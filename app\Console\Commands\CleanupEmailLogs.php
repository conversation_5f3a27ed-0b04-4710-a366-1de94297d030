<?php

namespace App\Console\Commands;

use App\Models\EmailLog;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupEmailLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:cleanup-logs {--days=90 : Number of days to keep email logs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old email logs to free up database space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Cleaning up email logs older than {$days} days (before {$cutoffDate->format('Y-m-d H:i:s')})...");

        // Count logs to be deleted
        $totalLogs = EmailLog::where('created_at', '<', $cutoffDate)->count();

        if ($totalLogs === 0) {
            $this->info('No email logs found to clean up.');
            return 0;
        }

        if (!$this->confirm("This will delete {$totalLogs} email log(s). Do you want to continue?")) {
            $this->info('Cleanup cancelled.');
            return 0;
        }

        // Get breakdown by status before deletion
        $breakdown = EmailLog::where('created_at', '<', $cutoffDate)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Delete old logs in chunks to avoid memory issues
        $deleted = 0;
        $chunkSize = 1000;

        EmailLog::where('created_at', '<', $cutoffDate)
            ->chunkById($chunkSize, function ($logs) use (&$deleted) {
                $chunkDeleted = $logs->count();
                EmailLog::whereIn('id', $logs->pluck('id'))->delete();
                $deleted += $chunkDeleted;
                $this->line("Deleted {$chunkDeleted} logs... (Total: {$deleted})");
            });

        $this->info("\nEmail log cleanup completed:");
        $this->table(
            ['Status', 'Deleted Count'],
            collect($breakdown)->map(function ($count, $status) {
                return [ucfirst($status), number_format($count)];
            })->toArray()
        );

        $this->info("Total deleted: " . number_format($deleted) . " email logs");

        Log::info('Email logs cleanup completed', [
            'days_kept' => $days,
            'cutoff_date' => $cutoffDate,
            'deleted_count' => $deleted,
            'breakdown' => $breakdown,
        ]);

        return 0;
    }
}
