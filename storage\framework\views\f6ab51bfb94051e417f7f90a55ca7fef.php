<?php $__env->startSection('title', 'Ride Reminder'); ?>

<?php $__env->startSection('content'); ?>
<h2>Upcoming Ride Reminder</h2>

<p>Dear <?php echo e($driver->name); ?>,</p>

<p>This is a reminder about your upcoming ride assignment with <?php echo e($companyName); ?>.</p>

<div class="booking-details">
    <h3>Ride Assignment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value"><?php echo e($user->name); ?></span>
    </div>
    
    <?php if($user->phone): ?>
    <div class="detail-row">
        <span class="detail-label">Client Phone:</span>
        <span class="detail-value"><?php echo e($user->phone); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Fare:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?></span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">Driver Instructions:</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>Contact the client 15-30 minutes before pickup time</li>
        <li>Arrive at the pickup location 5 minutes early</li>
        <li>Confirm the client's identity before starting the ride</li>
        <li>Update the ride status in your driver app</li>
        <li>Follow all safety protocols and company guidelines</li>
    </ul>
</div>

<?php if($booking->notes): ?>
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <strong style="color: #856404;">Special Instructions from Client:</strong><br>
    <span style="color: #856404;"><?php echo e($booking->notes); ?></span>
</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('driver.rides.show', $booking->id)); ?>" class="btn">View Ride Details</a>
</div>

<h3>Need Assistance?</h3>
<p>If you have any questions or need to report an issue, please contact dispatch:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or use the driver app support feature</li>
</ul>

<p>Thank you for being a valued member of our driver team!</p>

<p>Safe driving,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\reminder-driver.blade.php ENDPATH**/ ?>