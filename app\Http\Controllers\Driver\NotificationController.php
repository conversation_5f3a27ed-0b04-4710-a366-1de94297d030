<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Display the notification settings page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function settings()
    {
        $userId = Auth::id();
        
        // Get the driver's notification settings or create default ones
        $settings = NotificationSetting::firstOrCreate(
            ['user_id' => $userId],
            [
                'email_ride_updates' => true,
                'email_earnings' => true,
                'email_announcements' => true,
                'sms_ride_updates' => true,
                'sms_earnings' => false,
                'sms_announcements' => false,
                'push_ride_updates' => true,
                'push_earnings' => true,
                'push_announcements' => false,
            ]
        );
        
        return view('driver.notifications.settings', compact('settings'));
    }

    /**
     * Update the notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request)
    {
        $userId = Auth::id();
        
        $request->validate([
            'email_ride_updates' => 'boolean',
            'email_earnings' => 'boolean',
            'email_announcements' => 'boolean',
            'sms_ride_updates' => 'boolean',
            'sms_earnings' => 'boolean',
            'sms_announcements' => 'boolean',
            'push_ride_updates' => 'boolean',
            'push_earnings' => 'boolean',
            'push_announcements' => 'boolean',
        ]);
        
        // Update or create notification settings
        NotificationSetting::updateOrCreate(
            ['user_id' => $userId],
            [
                'email_ride_updates' => $request->has('email_ride_updates'),
                'email_earnings' => $request->has('email_earnings'),
                'email_announcements' => $request->has('email_announcements'),
                'sms_ride_updates' => $request->has('sms_ride_updates'),
                'sms_earnings' => $request->has('sms_earnings'),
                'sms_announcements' => $request->has('sms_announcements'),
                'push_ride_updates' => $request->has('push_ride_updates'),
                'push_earnings' => $request->has('push_earnings'),
                'push_announcements' => $request->has('push_announcements'),
            ]
        );
        
        return redirect()->route('driver.notifications.settings')
            ->with('success', 'Notification settings updated successfully.');
    }
}
