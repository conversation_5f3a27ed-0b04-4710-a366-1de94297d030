<?php $__env->startSection('title', 'Payment History'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .payment-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .payment-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .payment-card .card-body {
        padding: 30px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto 15px;
    }

    .stat-title {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #343a40;
        margin-bottom: 0;
    }

    .filter-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .filter-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #343a40;
    }

    .payment-method-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        background-color: #f8f9fa;
        color: #343a40;
    }

    .payment-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .payment-status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .payment-status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .payment-status-failed {
        background-color: #f8d7da;
        color: #721c24;
    }

    .welcome-banner {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        color: white;
        padding: 30px;
        margin-bottom: 30px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(248, 193, 44, 0.05);
    }

    .table th {
        font-weight: 600;
        color: #343a40;
        border-top: none;
        border-bottom: 2px solid #ee393d;
    }

    .empty-state {
        text-align: center;
        padding: 50px 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 20px;
    }

    .empty-state h4 {
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        margin-right: 5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
    }

    .action-btn-view {
        background-color: rgba(13, 110, 253, 0.1);
        color: #ee393d;
    }

    .action-btn-view:hover {
        background-color: #ee393d;
        color: #fff;
    }

    .action-btn-invoice {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .action-btn-invoice:hover {
        background-color: #6c757d;
        color: #fff;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="welcome-banner" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">Payment History & Analytics</h2>
            <p class="mb-0">View detailed analytics and history of all your payment transactions</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?php echo e(route('client.payments.index')); ?>" class="btn btn-light">
                <i class="fas fa-arrow-left me-2"></i> Back to Payments
            </a>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Payment Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-title">Total Payments</div>
            <div class="stat-value"><?php echo e($payments->total()); ?></div>
        </div>
    </div>
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="200">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-title">Total Spent</div>
            <div class="stat-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($totalAmount ?? 0, 2)); ?></div>
        </div>
    </div>
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="300">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-title">Completed</div>
            <div class="stat-value"><?php echo e($completedPayments ?? 0); ?></div>
        </div>
    </div>
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-title">Average</div>
            <div class="stat-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($averageAmount ?? 0, 2)); ?></div>
        </div>
    </div>
</div>

<!-- Filter Options -->
<div class="filter-card" data-aos="fade-up" data-aos-delay="500">
    <div class="filter-title">
        <i class="fas fa-filter me-2"></i> Filter Options
    </div>
    <form action="<?php echo e(route('client.payments.history')); ?>" method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="date_from" class="form-label">From Date</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
        </div>
        <div class="col-md-3">
            <label for="date_to" class="form-label">To Date</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
        </div>
        <div class="col-md-3">
            <label for="payment_method" class="form-label">Payment Method</label>
            <select class="form-select" id="payment_method" name="payment_method">
                <option value="">All Methods</option>
                <option value="paypal" <?php echo e(request('payment_method') == 'paypal' ? 'selected' : ''); ?>>PayPal</option>
                <option value="credit_card" <?php echo e(request('payment_method') == 'credit_card' ? 'selected' : ''); ?>>Credit Card</option>
                <option value="cash" <?php echo e(request('payment_method') == 'cash' ? 'selected' : ''); ?>>Cash</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="">All Statuses</option>
                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                <option value="failed" <?php echo e(request('status') == 'failed' ? 'selected' : ''); ?>>Failed</option>
            </select>
        </div>
        <div class="col-12 d-flex justify-content-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i> Apply Filters
            </button>
            <a href="<?php echo e(route('client.payments.history')); ?>" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i> Reset
            </a>
        </div>
    </form>
</div>

<!-- Monthly Payment Chart -->
<div class="card payment-card mb-4" data-aos="fade-up" data-aos-delay="600">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Monthly Payment Summary</h5>
    </div>
    <div class="card-body">
        <?php if($monthlyStats->isEmpty()): ?>
            <div class="empty-state">
                <i class="fas fa-chart-line"></i>
                <h4>No payment data available</h4>
                <p>Your payment history will appear here once you make payments.</p>
                <a href="<?php echo e(route('booking.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-car me-1"></i> Book a Ride
                </a>
            </div>
        <?php else: ?>
            <div class="chart-container">
                <canvas id="monthlyPaymentsChart"></canvas>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Payment Methods Chart -->
<div class="card payment-card mb-4" data-aos="fade-up" data-aos-delay="700">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i> Payment Methods Distribution</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Payment Methods</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fab fa-paypal me-2 text-primary"></i> PayPal</span>
                                <span class="badge bg-primary rounded-pill"><?php echo e($paymentMethodStats['paypal'] ?? 0); ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="far fa-credit-card me-2 text-success"></i> Credit Card</span>
                                <span class="badge bg-success rounded-pill"><?php echo e($paymentMethodStats['credit_card'] ?? 0); ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-money-bill-wave me-2 text-warning"></i> Cash</span>
                                <span class="badge bg-warning rounded-pill"><?php echo e($paymentMethodStats['cash'] ?? 0); ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-money-check me-2 text-info"></i> Other</span>
                                <span class="badge bg-info rounded-pill"><?php echo e($paymentMethodStats['other'] ?? 0); ?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment History Table -->
<div class="card payment-card" data-aos="fade-up" data-aos-delay="800">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i> Payment History</h5>
        <div>
            <button class="btn btn-sm btn-light" onclick="window.print()">
                <i class="fas fa-print me-1"></i> Print
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if($payments->isEmpty()): ?>
            <div class="empty-state">
                <i class="fas fa-receipt"></i>
                <h4>No payment records found</h4>
                <p>You haven't made any payments yet.</p>
                <a href="<?php echo e(route('booking.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-car me-1"></i> Book a Ride
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Booking</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($payment->created_at->format('M d, Y')); ?></td>
                                <td>
                                    <a href="<?php echo e(route('client.bookings.show', $payment->booking_id)); ?>" class="text-decoration-none">
                                        <div class="d-flex align-items-center">
                                            <?php if($payment->booking->vehicle && $payment->booking->vehicle->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $payment->booking->vehicle->image)); ?>" class="me-2 rounded" width="40" height="30" alt="<?php echo e($payment->booking->vehicle->name); ?>">
                                            <?php else: ?>
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center me-2 rounded" style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-bold"><?php echo e($payment->booking->booking_number); ?></div>
                                                <small class="text-muted"><?php echo e($payment->booking->vehicle ? $payment->booking->vehicle->name : 'N/A'); ?></small>
                                            </div>
                                        </div>
                                    </a>
                                </td>
                                <td class="fw-bold"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($payment->amount, 2)); ?></td>
                                <td>
                                    <span class="payment-method-badge">
                                        <?php if($payment->payment_method == 'paypal'): ?>
                                            <i class="fab fa-paypal me-1"></i> PayPal
                                        <?php elseif($payment->payment_method == 'credit_card'): ?>
                                            <i class="far fa-credit-card me-1"></i> Credit Card
                                        <?php elseif($payment->payment_method == 'cash'): ?>
                                            <i class="fas fa-money-bill-wave me-1"></i> Cash
                                        <?php else: ?>
                                            <i class="fas fa-money-check me-1"></i> <?php echo e(ucfirst($payment->payment_method)); ?>

                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="payment-status payment-status-<?php echo e($payment->status); ?>">
                                        <?php echo e(ucfirst($payment->status)); ?>

                                    </span>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('client.payments.show', $payment->id)); ?>" class="action-btn action-btn-view" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('client.payments.invoice', $payment->id)); ?>" class="action-btn action-btn-invoice" title="View Invoice">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <?php echo e($payments->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        <?php if(!$monthlyStats->isEmpty()): ?>
        // Monthly Payments Chart
        const monthlyCtx = document.getElementById('monthlyPaymentsChart').getContext('2d');

        // Prepare data from the monthlyStats
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];

        const labels = [];
        const data = [];
        const counts = [];

        <?php $__currentLoopData = $monthlyStats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            labels.push(months[<?php echo e($stat->month - 1); ?>] + ' <?php echo e($stat->year); ?>');
            data.push(<?php echo e($stat->total); ?>);
            counts.push(<?php echo e($stat->count); ?>);
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        const monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Total Amount ($)',
                        data: data,
                        backgroundColor: 'rgba(248, 193, 44, 0.7)',
                        borderColor: 'rgba(248, 193, 44, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Number of Payments',
                        data: counts,
                        backgroundColor: 'rgba(108, 117, 125, 0.5)',
                        borderColor: 'rgba(108, 117, 125, 1)',
                        borderWidth: 1,
                        type: 'line',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.dataset.label.includes('Amount')) {
                                    label += '$' + context.parsed.y.toFixed(2);
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount ($)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: 'Count'
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        // Payment Methods Chart
        const methodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');

        // Prepare data for payment methods
        const methodLabels = ['PayPal', 'Credit Card', 'Cash', 'Other'];
        const methodData = [
            <?php echo e($paymentMethodStats['paypal'] ?? 0); ?>,
            <?php echo e($paymentMethodStats['credit_card'] ?? 0); ?>,
            <?php echo e($paymentMethodStats['cash'] ?? 0); ?>,
            <?php echo e($paymentMethodStats['other'] ?? 0); ?>

        ];

        const methodsChart = new Chart(methodsCtx, {
            type: 'doughnut',
            data: {
                labels: methodLabels,
                datasets: [{
                    data: methodData,
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                const percentage = Math.round((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100);
                                return label + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\client\payments\history.blade.php ENDPATH**/ ?>