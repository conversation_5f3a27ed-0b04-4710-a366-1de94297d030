<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transaction_id' => $this->transaction_id,
            'payment_method' => $this->payment_method,
            'amount' => $this->amount,
            'formatted_amount' => \App\Helpers\SettingsHelper::formatPrice($this->amount),
            'status' => $this->status,
            'payment_details' => $this->payment_details,
            'refund_info' => [
                'refunded_at' => $this->refunded_at?->toISOString(),
                'refunded_amount' => $this->refunded_amount,
                'formatted_refunded_amount' => $this->refunded_amount ? \App\Helpers\SettingsHelper::formatPrice($this->refunded_amount) : null,
            ],
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'relationships' => [
                'booking' => new BookingResource($this->whenLoaded('booking')),
                'user' => new UserResource($this->whenLoaded('user')),
            ],
        ];
    }
}
