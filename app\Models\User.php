<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'address',
        'profile_photo',
        'license_number',
        'vehicle_info',
        'vehicle_make',
        'vehicle_model',
        'vehicle_color',
        'vehicle_reg_number',
        'insurance_expiry',
        'mot_expiry',
        'is_active',
        'is_available',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'insurance_expiry' => 'date',
        'mot_expiry' => 'date',
        'is_active' => 'boolean',
        'is_available' => 'boolean',
    ];

    /**
     * Check if user is admin
     *
     * @return bool
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is client
     *
     * @return bool
     */
    public function isClient()
    {
        return $this->role === 'client';
    }

    /**
     * Check if user is driver
     *
     * @return bool
     */
    public function isDriver()
    {
        return $this->role === 'driver';
    }

    /**
     * Check if user has a specific role
     *
     * @param string $role
     * @return bool
     */
    public function hasRole($role)
    {
        return $this->role === $role;
    }

    /**
     * Get bookings made by this user
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get bookings assigned to this driver
     */
    public function driverBookings()
    {
        return $this->hasMany(Booking::class, 'driver_id');
    }

    /**
     * Get payments made by this user
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get blog posts created by this user
     */
    public function blogPosts()
    {
        return $this->hasMany(BlogPost::class);
    }

    /**
     * Get comments made by this user
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get documents belonging to this user
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get driver documents
     */
    public function driverDocuments()
    {
        return $this->hasMany(DriverDocument::class, 'driver_id');
    }

    /**
     * Get driver schedules
     */
    public function driverSchedules()
    {
        return $this->hasMany(DriverSchedule::class, 'driver_id');
    }

    /**
     * Get driver rides (alias for driverBookings)
     */
    public function driverRides()
    {
        return $this->driverBookings();
    }

    /**
     * Get driver earnings
     */
    public function driverEarnings()
    {
        return $this->hasMany(Booking::class, 'driver_id')
            ->where('status', 'completed');
    }

    /**
     * Check if driver is available
     *
     * @return bool
     */
    public function isAvailable()
    {
        return $this->is_available && $this->is_active;
    }

    /**
     * Scope a query to only include users with a specific role.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $role
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope a query to only include active users.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include available users.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Get addresses belonging to this user
     */
    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    /**
     * Get time off requests for this driver
     */
    public function timeOffRequests()
    {
        return $this->hasMany(TimeOffRequest::class, 'driver_id');
    }

    /**
     * Get maintenance logs created by this driver
     */
    public function maintenanceLogs()
    {
        return $this->hasMany(MaintenanceLog::class, 'driver_id');
    }


}
