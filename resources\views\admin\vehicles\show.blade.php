@extends('layouts.admin')

@section('title', 'Vehicle Details')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }

    .detail-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .detail-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .detail-card .card-body {
        padding: 30px;
    }

    .vehicle-image {
        width: 100%;
        height: 300px;
        object-fit: cover;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
    }

    .detail-label {
        width: 150px;
        font-weight: 600;
    }

    .detail-value {
        flex: 1;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Vehicle Details</h2>
                <a href="{{ route('admin.vehicles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Vehicles
                </a>
            </div>

            <div class="card detail-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">{{ $vehicle->name }}</h4>
                        <div>
                            @if ($vehicle->is_active)
                                <span class="status-badge status-active">Active</span>
                            @else
                                <span class="status-badge status-inactive">Inactive</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            @if ($vehicle->image)
                                <img src="{{ asset('storage/' . $vehicle->image) }}" alt="{{ $vehicle->name }}" class="vehicle-image">
                            @else
                                <img src="https://via.placeholder.com/600x300?text=No+Image" alt="No Image" class="vehicle-image">
                            @endif
                        </div>
                        <div class="col-md-6">
                            <div class="detail-row">
                                <div class="detail-label">Type:</div>
                                <div class="detail-value">{{ $vehicle->type }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Model:</div>
                                <div class="detail-value">{{ $vehicle->model }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Category:</div>
                                <div class="detail-value">{{ $vehicle->category }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Seats:</div>
                                <div class="detail-value">{{ $vehicle->seats }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Luggage Capacity:</div>
                                <div class="detail-value">{{ $vehicle->luggage_capacity }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Transmission:</div>
                                <div class="detail-value">{{ $vehicle->transmission }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Price Per {{ \App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Mile' : 'KM' }}:</div>
                                <div class="detail-value">@currency(){{ number_format($vehicle->price_per_km, 2) }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Price Per Hour:</div>
                                <div class="detail-value">@currency(){{ number_format($vehicle->price_per_hour, 2) }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Base Fare:</div>
                                <div class="detail-value">@currency(){{ number_format($vehicle->base_fare ?? 5.00, 2) }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Tax Rate:</div>
                                <div class="detail-value">{{ number_format($vehicle->tax_rate ?? 0.00, 2) }}%</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Created At:</div>
                                <div class="detail-value">{{ $vehicle->created_at->format('M d, Y h:i A') }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Last Updated:</div>
                                <div class="detail-value">{{ $vehicle->updated_at->format('M d, Y h:i A') }}</div>
                            </div>
                        </div>
                    </div>

                    @if ($vehicle->description)
                        <div class="mt-4">
                            <h5>Description</h5>
                            <p>{{ $vehicle->description }}</p>
                        </div>
                    @endif

                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ route('admin.vehicles.edit', $vehicle->id) }}" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <form action="{{ route('admin.vehicles.destroy', $vehicle->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this vehicle?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
