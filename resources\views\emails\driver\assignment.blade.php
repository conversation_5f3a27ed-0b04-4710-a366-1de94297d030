@extends('emails.layouts.master')

@section('title', 'New Ride Assignment')

@section('content')
<h2>New Ride Assignment</h2>

<p>Dear {{ $driver->name }},</p>

<p>You have been assigned a new ride! Please review the details below and prepare for pickup.</p>

<div class="booking-details">
    <h3>Ride Assignment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong>{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value">{{ $user->name }}</span>
    </div>
    
    @if($user->phone)
    <div class="detail-row">
        <span class="detail-label">Client Phone:</span>
        <span class="detail-value"><a href="tel:{{ $user->phone }}">{{ $user->phone }}</a></span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Earnings:</span>
        <span class="detail-value"><strong>{{ $currencySymbol }}{{ number_format($booking->amount * 0.8, 2) }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Trip Type:</span>
        <span class="detail-value">{{ ucfirst($booking->trip_type ?? 'one_way') }}</span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">📋 Driver Checklist</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>✓ Review pickup and dropoff locations</li>
        <li>✓ Plan your route and check traffic conditions</li>
        <li>✓ Ensure your vehicle is clean and fueled</li>
        <li>✓ Contact client 15-30 minutes before pickup</li>
        <li>✓ Arrive 5 minutes early at pickup location</li>
        <li>✓ Confirm client identity before starting trip</li>
    </ul>
</div>

@if($booking->notes)
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <h3 style="color: #856404; margin-top: 0;">📝 Special Instructions from Client</h3>
    <p style="color: #856404; margin-bottom: 0;">{{ $booking->notes }}</p>
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('driver.rides.show', $booking->id) }}" class="btn">View Full Ride Details</a>
</div>

<h3>🚗 Pre-Trip Preparation:</h3>
<ul>
    <li><strong>Vehicle Check:</strong> Ensure your vehicle is clean, fueled, and in good condition</li>
    <li><strong>Route Planning:</strong> Review the pickup and dropoff locations, plan your route</li>
    <li><strong>Traffic Check:</strong> Check current traffic conditions and allow extra time if needed</li>
    <li><strong>Client Contact:</strong> Call the client 15-30 minutes before pickup time</li>
    <li><strong>Identification:</strong> Be prepared to confirm the client's name and destination</li>
</ul>

<h3>📱 During the Trip:</h3>
<ul>
    <li>Update trip status in your driver app</li>
    <li>Follow GPS navigation and traffic rules</li>
    <li>Provide professional and courteous service</li>
    <li>Assist with luggage if needed</li>
    <li>Complete the trip in your driver app upon arrival</li>
</ul>

<h3>💰 Earnings Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Trip Fare:</strong> {{ $currencySymbol }}{{ number_format($booking->amount, 2) }}</p>
    <p><strong>Your Earnings (80%):</strong> {{ $currencySymbol }}{{ number_format($booking->amount * 0.8, 2) }}</p>
    <p><strong>Payment Method:</strong> {{ $booking->payment_method ?? 'To be determined' }}</p>
    <p class="mb-0"><strong>Payout:</strong> Included in your next weekly payout</p>
</div>

<h3>📞 Client Contact Information:</h3>
<div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Name:</strong> {{ $user->name }}</p>
    @if($user->phone)
    <p><strong>Phone:</strong> <a href="tel:{{ $user->phone }}">{{ $user->phone }}</a></p>
    @endif
    <p class="mb-0"><strong>Pickup:</strong> {{ $booking->pickup_address }}</p>
</div>

<h3>❓ Need Help?</h3>
<p>If you have any questions or encounter issues, contact our dispatch team immediately:</p>
<ul>
    <li><strong>Dispatch Phone:</strong> {{ $companyPhone }}</li>
    <li><strong>Email:</strong> {{ $companyEmail }}</li>
    <li><strong>Driver App:</strong> Use the support feature for immediate assistance</li>
</ul>

<p>Thank you for being a valued member of our driver team. Drive safely and provide excellent service!</p>

<p>Safe driving,<br>
The {{ $companyName }} Dispatch Team</p>
@endsection
