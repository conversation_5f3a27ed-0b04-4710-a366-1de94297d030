<?php $__env->startSection('title', 'Manage Addresses'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .address-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .address-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .address-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .address-card .card-body {
        padding: 30px;
    }

    .address-item {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        position: relative;
        transition: all 0.3s;
        border-left: 5px solid #ee393d;
    }

    .address-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-3px);
    }

    .address-item:last-child {
        margin-bottom: 0;
    }

    .address-type {
        font-weight: 600;
        margin-bottom: 10px;
        color: #343a40;
        display: flex;
        align-items: center;
    }

    .address-type i {
        margin-right: 10px;
        color: #ee393d;
    }

    .address-text {
        color: #6c757d;
        margin-bottom: 15px;
    }

    .address-actions {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e0a800;
        border-color: #e0a800;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-danger {
        color: #dc3545;
        border-color: #dc3545;
        transition: all 0.3s;
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .empty-addresses {
        text-align: center;
        padding: 50px 0;
    }

    .empty-addresses i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 20px;
    }

    .empty-addresses h4 {
        margin-bottom: 10px;
    }

    .empty-addresses p {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        border-radius: 8px 0 0 8px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Manage Addresses</h2>
    <a href="<?php echo e(route('client.profile.index')); ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Profile
    </a>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8" data-aos="fade-up">
        <div class="card address-card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> Your Saved Addresses</h4>
            </div>
            <div class="card-body">
                <?php if($addresses->isEmpty()): ?>
                    <div class="empty-addresses">
                        <i class="fas fa-map-marked-alt"></i>
                        <h4>No addresses saved yet</h4>
                        <p>Add your frequently used addresses for faster checkout</p>
                    </div>
                <?php else: ?>
                    <?php $__currentLoopData = $addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="address-item" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                            <div class="address-actions">
                                <form action="<?php echo e(route('client.profile.delete-address', $address->id)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this address?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>

                            <div class="address-type">
                                <?php if($address->type == 'home'): ?>
                                    <i class="fas fa-home"></i>
                                <?php elseif($address->type == 'work'): ?>
                                    <i class="fas fa-briefcase"></i>
                                <?php elseif($address->type == 'other'): ?>
                                    <i class="fas fa-map-pin"></i>
                                <?php endif; ?>
                                <?php echo e(ucfirst($address->type)); ?> Address
                            </div>

                            <div class="address-text"><?php echo e($address->address); ?></div>

                            <?php if($address->is_default): ?>
                                <div class="badge bg-success mt-2">Default Address</div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
        <div class="card address-card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-plus me-2"></i> Add New Address</h4>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('client.profile.add-address')); ?>" method="POST">
                    <?php echo csrf_field(); ?>

                    <div class="mb-3">
                        <label for="type" class="form-label">Address Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select Type</option>
                            <option value="home" <?php echo e(old('type') == 'home' ? 'selected' : ''); ?>>Home</option>
                            <option value="work" <?php echo e(old('type') == 'work' ? 'selected' : ''); ?>>Work</option>
                            <option value="other" <?php echo e(old('type') == 'other' ? 'selected' : ''); ?>>Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Street Address</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-home"></i></span>
                            <input type="text" class="form-control" id="address" name="address" value="<?php echo e(old('address')); ?>" required placeholder="Enter street address">
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_default" name="is_default" <?php echo e(old('is_default') ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="is_default">Set as default address</label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> Add Address
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\client\profile\addresses.blade.php ENDPATH**/ ?>