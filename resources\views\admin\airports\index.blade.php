@extends('layouts.admin')

@section('title', 'Airport Management')

@section('content')
<div class="content-wrapper">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plane me-2 text-primary"></i>Airport Management
            </h1>
            <a href="{{ route('admin.airports.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Add Airport
            </a>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.airports.index') }}" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" 
                               placeholder="Search by name, code, city, or country...">
                    </div>
                    <div class="col-md-3">
                        <label for="country" class="form-label">Country</label>
                        <select class="form-select" id="country" name="country">
                            <option value="">All Countries</option>
                            @foreach($countries as $country)
                                <option value="{{ $country->country_code }}" 
                                        {{ request('country') == $country->country_code ? 'selected' : '' }}>
                                    {{ $country->country }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Airports Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Airports 
                    <span class="badge bg-primary ms-2">{{ $airports->total() }}</span>
                </h5>
            </div>
            <div class="card-body">
                @if($airports->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>City</th>
                                    <th>Country</th>
                                    <th>Coordinates</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($airports as $airport)
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">{{ $airport->code }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ $airport->name }}</strong>
                                        </td>
                                        <td>{{ $airport->city }}</td>
                                        <td>
                                            <span class="flag-icon flag-icon-{{ strtolower($airport->country_code) }}"></span>
                                            {{ $airport->country }}
                                        </td>
                                        <td>
                                            @if($airport->latitude && $airport->longitude)
                                                <small class="text-muted">
                                                    {{ number_format($airport->latitude, 4) }}, 
                                                    {{ number_format($airport->longitude, 4) }}
                                                </small>
                                            @else
                                                <span class="text-muted">Not set</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.airports.show', $airport) }}" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.airports.edit', $airport) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.airports.destroy', $airport) }}" 
                                                      method="POST" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this airport?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            Showing {{ $airports->firstItem() }} to {{ $airports->lastItem() }} 
                            of {{ $airports->total() }} results
                        </div>
                        {{ $airports->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-plane fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No airports found</h5>
                        <p class="text-muted">Start by adding your first airport to the system.</p>
                        <a href="{{ route('admin.airports.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add First Airport
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Auto-submit form on country change
    document.getElementById('country').addEventListener('change', function() {
        this.form.submit();
    });
</script>
@endsection
