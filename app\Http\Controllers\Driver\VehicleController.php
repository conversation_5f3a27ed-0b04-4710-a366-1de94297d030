<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\MaintenanceLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VehicleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Display a listing of vehicles assigned to the driver.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $driverId = Auth::id();

        // Get vehicles assigned to this driver
        $vehicles = Vehicle::whereHas('bookings', function($query) use ($driverId) {
                $query->where('driver_id', $driverId);
            })
            ->distinct()
            ->get();

        return view('driver.vehicles.index', compact('vehicles'));
    }

    /**
     * Display the specified vehicle.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $driverId = Auth::id();

        // Ensure the vehicle has been assigned to this driver
        $vehicle = Vehicle::whereHas('bookings', function($query) use ($driverId) {
                $query->where('driver_id', $driverId);
            })
            ->findOrFail($id);

        // Get maintenance logs for this vehicle
        $maintenanceLogs = MaintenanceLog::where('vehicle_id', $id)
            ->orderBy('date', 'desc')
            ->get();

        return view('driver.vehicles.show', compact('vehicle', 'maintenanceLogs'));
    }

    /**
     * Display the maintenance log for vehicles.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function maintenanceLog()
    {
        $driverId = Auth::id();

        // Get vehicles assigned to this driver
        $vehicles = Vehicle::whereHas('bookings', function($query) use ($driverId) {
                $query->where('driver_id', $driverId);
            })
            ->distinct()
            ->get();

        // Get maintenance logs for these vehicles
        $maintenanceLogs = MaintenanceLog::whereIn('vehicle_id', $vehicles->pluck('id'))
            ->with('vehicle')
            ->orderBy('date', 'desc')
            ->get();

        return view('driver.vehicles.maintenance-log', compact('vehicles', 'maintenanceLogs'));
    }

    /**
     * Add a maintenance log entry.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addMaintenanceLog(Request $request)
    {
        $driverId = Auth::id();

        $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'date' => 'required|date',
            'type' => 'required|string|max:255',
            'description' => 'required|string',
            'odometer' => 'required|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
        ]);

        // Ensure the vehicle has been assigned to this driver
        $vehicleExists = Vehicle::whereHas('bookings', function($query) use ($driverId) {
                $query->where('driver_id', $driverId);
            })
            ->where('id', $request->vehicle_id)
            ->exists();

        if (!$vehicleExists) {
            return redirect()->back()->with('error', 'You are not authorized to add maintenance logs for this vehicle.');
        }

        // Create maintenance log
        $maintenanceLog = new MaintenanceLog();
        $maintenanceLog->vehicle_id = $request->vehicle_id;
        $maintenanceLog->driver_id = $driverId;
        $maintenanceLog->date = $request->date;
        $maintenanceLog->type = $request->type;
        $maintenanceLog->description = $request->description;
        $maintenanceLog->odometer = $request->odometer;
        $maintenanceLog->cost = $request->cost;
        $maintenanceLog->save();

        return redirect()->route('driver.vehicles.maintenance-log')
            ->with('success', 'Maintenance log added successfully.');
    }

    /**
     * Update the driver's vehicle information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'vehicle_make' => 'required|string|max:50',
            'vehicle_model' => 'required|string|max:50',
            'vehicle_color' => 'required|string|max:30',
            'vehicle_reg_number' => 'required|string|max:20',
            'insurance_expiry' => 'required|date',
            'mot_expiry' => 'required|date',
            'vehicle_info' => 'nullable|string|max:500',
        ]);

        // Update user's vehicle information
        $user->vehicle_make = $request->vehicle_make;
        $user->vehicle_model = $request->vehicle_model;
        $user->vehicle_color = $request->vehicle_color;
        $user->vehicle_reg_number = $request->vehicle_reg_number;
        $user->insurance_expiry = $request->insurance_expiry;
        $user->mot_expiry = $request->mot_expiry;
        $user->vehicle_info = $request->vehicle_info;
        $user->save();

        return redirect()->route('driver.profile.edit')
            ->with('success', 'Vehicle information updated successfully.');
    }
}
