<?php $__env->startSection('title', 'YNR Cars - Transportation Services | Trusted UK Taxi Service'); ?>

<?php $__env->startSection('styles'); ?>
<!-- Additional styles for the home page -->
<style>
    /* Hero section enhancements */
    .hero-section {
        position: relative;
        background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1550355291-bbee04a92027?ixlib=rb-4.0.3&auto=format&fit=crop&w=1936&q=80');
        background-size: cover;
        background-position: center;
        min-height: 100vh;
        display: flex;
        align-items: center;
        padding: 150px 0 100px;
    }

    .hero-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto 40px;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 2px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 20px;
        font-weight: 300;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    }

    .hero-description {
        font-size: 1.2rem;
        margin-bottom: 30px;
        font-weight: 300;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        line-height: 1.6;
    }

    .hero-buttons {
        margin-bottom: 40px;
    }

    /* Booking form enhancements */
    .booking-form-container {
        background-color: rgba(0, 0, 0, 0.8);
        border-radius: 10px;
        padding: 30px;
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        z-index: 10;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }

    .booking-form .nav-tabs {
        border-bottom: none;
        margin-bottom: 25px;
        justify-content: center;
    }

    .booking-form .nav-link {
        color: var(--white);
        border: none;
        padding: 12px 25px;
        border-radius: 30px;
        margin-right: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .booking-form .nav-link:hover {
        background-color: rgba(238, 57, 61, 0.7);
        color: white;
    }

    .booking-form .nav-link.active {
        background-color: #ee393d;
        color: white;
    }

    .booking-form .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 12px 15px;
        height: auto;
        border-radius: 5px;
    }

    .booking-form .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(238, 57, 61, 0.5);
        box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
    }

    .booking-form .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .booking-form .form-label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    /* Form validation styles */
    .booking-form .is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }

    .booking-form .is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }

    /* Autocomplete styling removed - API key not configured */

    /* Responsive adjustments for the booking form */
    @media (max-width: 768px) {
        .hero-section {
            padding: 100px 0 50px;
            min-height: auto;
        }

        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
        }

        .hero-description {
            font-size: 1rem;
            line-height: 1.5;
        }

        .booking-form-container {
            padding: 20px;
        }

        .booking-form .nav-link {
            padding: 8px 15px;
            font-size: 0.9rem;
        }

        .hero-buttons .btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
        }

        .hero-buttons .btn:first-child {
            margin-right: 0;
        }
    }

    /* Autocomplete styling for home form */
    .hero-form .form-control.autocomplete-enabled {
        border-left: 3px solid #28a745;
        transition: all 0.3s ease;
    }

    .hero-form .form-control.autocomplete-disabled {
        border-left: 3px solid #ffc107;
        background-color: #fff9e6;
    }

    /* Google Maps autocomplete dropdown styling */
    .pac-container {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid #dee2e6;
        margin-top: 2px;
        z-index: 9999;
        font-family: 'Inter', sans-serif;
    }

    .pac-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-size: 14px;
    }

    .pac-item:hover {
        background-color: #f8f9fa;
    }

    .pac-item-selected {
        background-color: #e3f2fd;
    }

    .pac-matched {
        font-weight: 600;
        color: #ee393d;
    }

    .pac-icon {
        margin-right: 8px;
    }

    /* Loading indicator for autocomplete */
    .hero-form .form-control.loading {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23007bff' viewBox='0 0 16 16'%3E%3Cpath d='M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM7 3a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0V3zm0 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 16px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Integrated Booking Form -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Welcome to YNR Cars</h1>
            <p class="hero-subtitle">Your trusted provider of reliable and affordable taxi transfers to and from all major London airports. Whether you're travelling alone or in a group, for business or leisure, we're here to ensure a smooth, comfortable, and stress-free journey—24 hours a day, 7 days a week.</p>
            <p class="hero-description">Our fleet of modern, air-conditioned vehicles is tailored to meet your specific needs, and our experienced drivers are committed to delivering friendly, punctual service with excellent knowledge. Get an instant quote online or call for instant quote—what we quote is what you pay. Let us take the hassle out of airport travel.</p>
        </div>

        <!-- Integrated Booking Form -->
        <div class="booking-form-container hero-booking-form" data-aos="fade-up">
            <div class="booking-form">
                <ul class="nav nav-tabs" id="bookingTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="one-way-tab" data-bs-toggle="tab" data-bs-target="#one-way" type="button" role="tab" aria-controls="one-way" aria-selected="true">
                            <i class="fas fa-arrow-right me-2"></i>One Way
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="return-tab" data-bs-toggle="tab" data-bs-target="#return" type="button" role="tab" aria-controls="return" aria-selected="false">
                            <i class="fas fa-exchange-alt me-2"></i>Round Trip
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="airport-tab" data-bs-toggle="tab" data-bs-target="#airport" type="button" role="tab" aria-controls="airport" aria-selected="false">
                            <i class="fas fa-plane me-2"></i>Airport Transfer
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="bookingTabContent">
                    <!-- One Way Tab -->
                    <div class="tab-pane fade show active" id="one-way" role="tabpanel" aria-labelledby="one-way-tab">
                        <form action="<?php echo e(route('booking.index')); ?>" method="GET" class="booking-form-validate">
                            <input type="hidden" name="type" value="one_way">
                            <input type="hidden" name="booking_type" value="one_way">
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3">
                                    <label for="pickup" class="form-label">Pickup Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="pickup" name="pickup" placeholder="Enter pickup address" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="dropoff" class="form-label">Drop-off Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="dropoff" name="dropoff" placeholder="Enter destination address" required>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3">
                                    <label for="date" class="form-label">Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control required" id="date" name="date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e(date('Y-m-d')); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="time" class="form-label">Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control required" id="time" name="time" value="<?php echo e(date('H:i', strtotime('+2 hours'))); ?>" required>
                                </div>
                                <input type="hidden" name="passengers" value="2">
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Book Now</button>
                            </div>
                        </form>
                    </div>

                    <!-- Round Trip Tab -->
                    <div class="tab-pane fade" id="return" role="tabpanel" aria-labelledby="return-tab">
                        <form action="<?php echo e(route('booking.index')); ?>" method="GET" class="booking-form-validate">
                            <input type="hidden" name="type" value="round_trip">
                            <input type="hidden" name="booking_type" value="return">
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3">
                                    <label for="pickup_rt" class="form-label">Pickup Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="pickup_rt" name="pickup" placeholder="Enter pickup address" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="dropoff_rt" class="form-label">Drop-off Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="dropoff_rt" name="dropoff" placeholder="Enter destination address" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-3 mb-3">
                                    <label for="outbound_date" class="form-label">Outbound Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control required" id="outbound_date" name="date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e(date('Y-m-d')); ?>" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="outbound_time" class="form-label">Outbound Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control required" id="outbound_time" name="time" value="<?php echo e(date('H:i', strtotime('+2 hours'))); ?>" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="return_date" class="form-label">Return Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control required" id="return_date" name="return_date" min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>" value="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="return_time" class="form-label">Return Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control required" id="return_time" name="return_time" value="<?php echo e(date('H:i', strtotime('+2 hours'))); ?>" required>
                                </div>
                            </div>
                            <input type="hidden" name="passengers" value="2">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Book Now</button>
                            </div>
                        </form>
                    </div>

                    <!-- Airport Transfer Tab -->
                    <div class="tab-pane fade" id="airport" role="tabpanel" aria-labelledby="airport-tab">
                        <form action="<?php echo e(route('booking.index')); ?>" method="GET" class="booking-form-validate">
                            <input type="hidden" name="type" value="airport_transfer">
                            <input type="hidden" name="booking_type" value="airport">
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3">
                                    <label for="airport_direction" class="form-label">Transfer Direction <span class="text-danger">*</span></label>
                                    <select class="form-control required" id="airport_direction" name="airport_direction" required>
                                        <option value="to_airport">To Airport</option>
                                        <option value="from_airport">From Airport</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="airport_name" class="form-label">Airport <span class="text-danger">*</span></label>
                                    <select class="form-control required" id="airport_name" name="airport_name" required>
                                        <option value="">Select Airport</option>
                                        <option value="Heathrow Airport (LHR)">Heathrow Airport (LHR)</option>
                                        <option value="Gatwick Airport (LGW)">Gatwick Airport (LGW)</option>
                                        <option value="Stansted Airport (STN)">Stansted Airport (STN)</option>
                                        <option value="Luton Airport (LTN)">Luton Airport (LTN)</option>
                                        <option value="London City Airport (LCY)">London City Airport (LCY)</option>
                                        <option value="Manchester Airport (MAN)">Manchester Airport (MAN)</option>
                                        <option value="Birmingham Airport (BHX)">Birmingham Airport (BHX)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3 to-airport-field">
                                    <label for="airport_pickup_address" class="form-label">Pickup Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="airport_pickup_address" name="pickup" placeholder="Enter pickup location" required>
                                </div>
                                <div class="col-md-6 mb-3 from-airport-field" style="display: none;">
                                    <label for="airport_dropoff_address" class="form-label">Dropoff Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="airport_dropoff_address" name="dropoff" placeholder="Enter dropoff location">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="airport_date" class="form-label">Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control required" id="airport_date" name="date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e(date('Y-m-d')); ?>" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3">
                                    <label for="airport_time" class="form-label">Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control required" id="airport_time" name="time" value="<?php echo e(date('H:i', strtotime('+2 hours'))); ?>" required>
                                </div>
                                <input type="hidden" name="passengers" value="2">
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Book Now</button>
                            </div>
                        </form>
                    </div>


                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Transportation Services</h2>
            <p>Experience luxury, comfort, and reliability with our professional chauffeur services</p>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-plane-arrival"></i>
                    </div>
                    <h4>Airport Transfers</h4>
                    <p>Reliable and punctual airport pickup and drop-off services with flight tracking and meet & greet options</p>
                </div>
            </div>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h4>Corporate Travel</h4>
                    <p>Professional transportation solutions for executives, meetings, conferences, and corporate events</p>
                </div>
            </div>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-glass-cheers"></i>
                    </div>
                    <h4>Special Events</h4>
                    <p>Elegant transportation for weddings, proms, anniversaries, and other special occasions</p>
                </div>
            </div>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <h4>Point-to-Point</h4>
                    <p>Direct transportation between any two locations with professional chauffeurs and luxury vehicles</p>
                </div>
            </div>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>Hourly Charter</h4>
                    <p>Flexible transportation services charged by the hour for meetings, shopping, or sightseeing</p>
                </div>
            </div>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h4>City Tours</h4>
                    <p>Customized city tours with knowledgeable chauffeurs and comfortable luxury vehicles</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Fleet Section -->
<section class="py-5 bg-light fleet-section">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Extensive Fleet</h2>
            <p>We provide access to a large fleet of vehicle sizes and types. Regardless of your party size, luggage or special requirements, we can usually provide the perfect vehicle.</p>
        </div>

        <div class="row">
            <?php $__currentLoopData = $vehicles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <div class="card vehicle-card">
                    <?php if($vehicle->image): ?>
                        <img src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" class="card-img-top vehicle-img" alt="<?php echo e($vehicle->name); ?>">
                    <?php else: ?>
                        <img src="https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80" class="card-img-top vehicle-img" alt="<?php echo e($vehicle->name); ?>">
                    <?php endif; ?>
                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($vehicle->name); ?></h5>
                        <p class="card-text text-muted"><?php echo e($vehicle->type); ?></p>

                        <?php if($vehicle->price_per_hour): ?>
                        <div class="vehicle-price">
                            From <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($vehicle->price_per_hour, 2)); ?>/hour
                        </div>
                        <?php endif; ?>

                        <div class="vehicle-details">
                            <div class="vehicle-detail">
                                <i class="fas fa-user"></i>
                                <p><?php echo e($vehicle->seats); ?> Passengers</p>
                            </div>
                            <div class="vehicle-detail">
                                <i class="fas fa-suitcase"></i>
                                <p><?php echo e($vehicle->luggage_capacity); ?> Luggage</p>
                            </div>
                            <div class="vehicle-detail">
                                <i class="fas fa-car"></i>
                                <p><?php echo e($vehicle->transmission); ?></p>
                            </div>
                        </div>
                        <div class="d-grid mt-3">
                            <a href="<?php echo e(route('booking.index', ['vehicle_id' => $vehicle->id])); ?>" class="btn btn-primary">Book Now</a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-4">
            <a href="<?php echo e(route('fleet')); ?>" class="btn btn-outline-dark btn-lg">View Complete Fleet</a>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-5 why-choose-us">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Why Choose YNR Cars</h2>
            <p>Your trusted UK taxi service focused on safety, reliability, and exceptional customer experience</p>
        </div>

        <div class="row">
            <div class="col-lg-6" data-aos="fade-right" data-aos-delay="100">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="feature-content">
                        <h4>Professional Chauffeurs</h4>
                        <p>Our chauffeurs are professionally trained, background-checked, and committed to providing exceptional service.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left" data-aos-delay="100">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="feature-content">
                        <h4>Luxury Vehicles</h4>
                        <p>Our fleet consists of late-model luxury vehicles that are meticulously maintained and cleaned.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-right" data-aos-delay="200">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="feature-content">
                        <h4>Punctuality</h4>
                        <p>We understand the importance of time, which is why we guarantee on-time pickups and arrivals.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h4>Safety First</h4>
                        <p>Your safety is our top priority. All our vehicles are regularly inspected and maintained to the highest standards.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-right" data-aos-delay="300">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="feature-content">
                        <h4>24/7 Customer Support</h4>
                        <p>Our dedicated customer service team is available around the clock to assist you with any inquiries or concerns.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left" data-aos-delay="300">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="feature-content">
                        <h4>Transparent Pricing</h4>
                        <p>No hidden fees or surprises. We provide clear, upfront pricing for all our services.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <h2 class="cta-title" data-aos="fade-up">Experience Transportation with YNR Cars</h2>
        <p class="cta-subtitle" data-aos="fade-up" data-aos-delay="100">Book your ride today and experience the safety, reliability, and exceptional customer service that makes YNR Cars your trusted UK transportation partner.</p>
        <a href="<?php echo e(route('booking.index')); ?>" class="btn btn-primary btn-lg cta-btn" data-aos="fade-up" data-aos-delay="200">Book Your Ride Now</a>
    </div>
</section>



<!-- Testimonials Section -->
<section class="py-5 testimonial-section">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>What Our Clients Say</h2>
            <p>Read testimonials from our satisfied customers</p>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="testimonial-card">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Client" class="testimonial-img">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"The service was impeccable. The chauffeur was professional, punctual, and the vehicle was immaculate. I highly recommend YNR Cars for any transportation needs."</p>
                    <h5 class="testimonial-author">Michael Johnson</h5>
                    <p class="testimonial-position">Business Executive</p>
                </div>
            </div>

            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="testimonial-card">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Client" class="testimonial-img">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"We used YNR Cars for our wedding transportation and couldn't be happier. The vehicles were beautiful, the drivers were courteous, and everything went smoothly on our special day."</p>
                    <h5 class="testimonial-author">Sarah Williams</h5>
                    <p class="testimonial-position">Bride</p>
                </div>
            </div>

            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="testimonial-card">
                    <img src="https://randomuser.me/api/portraits/men/62.jpg" alt="Client" class="testimonial-img">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <p class="testimonial-text">"I regularly use YNR Cars for airport transfers. Their flight tracking service ensures they're always there when I arrive, no matter if my flight is early or delayed. Reliable and professional."</p>
                    <h5 class="testimonial-author">David Thompson</h5>
                    <p class="testimonial-position">Frequent Traveler</p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php
    $googleMapsApiKey = \App\Services\SettingsService::get('google_maps_api_key', config('services.google_maps.key', ''));
    $countryCode = \App\Services\SettingsService::get('country_code', 'GB');
    $autocompleteSettings = \App\Services\SettingsService::getAutocompleteSettings();
?>

<?php if($googleMapsApiKey): ?>
<!-- Google Maps API with Places library -->
<script>
// Global variables for Google Maps
window.googleMapsApiKey = '<?php echo e($googleMapsApiKey); ?>';
window.countryCode = '<?php echo e($countryCode); ?>';

// Google Maps autocomplete settings
window.autocompleteSettings = {
    enabled: true,
    restrictCountry: <?php echo e(\App\Services\SettingsService::get('google_maps_restrict_country', 'true') === 'true' ? 'true' : 'false'); ?>,
    country: '<?php echo e(\App\Services\SettingsService::get('google_maps_country_code', $countryCode)); ?>',
    types: "<?php echo e($autocompleteSettings['types'] ?? 'geocode'); ?>",
    fields: "<?php echo e($autocompleteSettings['fields'] ?? 'address_components,geometry,name'); ?>"
};

// Initialize Google Maps API callback
function initGoogleMapsApi() {
    console.log('Google Maps API loaded successfully');

    // Initialize autocomplete for all address inputs
    initializeAutocomplete();

    // Dispatch event for other scripts
    window.dispatchEvent(new Event('google-maps-loaded'));
}

// Initialize autocomplete for address inputs
function initializeAutocomplete() {
    try {
        // Check if Google Maps API is loaded
        if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
            console.error('Google Maps API not loaded. Autocomplete will not work.');
            return;
        }

        // Enhanced autocomplete options for better diversity
        const options = {
            fields: ['address_components', 'geometry', 'name', 'formatted_address', 'place_id'],
            types: ['geocode']
        };

        // Add country restriction if enabled (but make it less restrictive)
        if (window.autocompleteSettings.restrictCountry && window.autocompleteSettings.country) {
            options.componentRestrictions = {
                country: window.autocompleteSettings.country
            };
        }

        // Address input IDs to initialize
        const addressInputs = [
            'pickup',
            'dropoff',
            'pickup_rt',
            'dropoff_rt',
            'airport_pickup_address',
            'airport_dropoff_address'
        ];

        addressInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                try {
                    const autocomplete = new google.maps.places.Autocomplete(input, options);

                    // Set broader bounds for more diverse results
                    if (window.autocompleteSettings.country === 'GB' || window.autocompleteSettings.country === 'UK') {
                        const ukBounds = new google.maps.LatLngBounds(
                            new google.maps.LatLng(49.9, -8.2), // Southwest
                            new google.maps.LatLng(60.9, 1.8)   // Northeast
                        );
                        autocomplete.setBounds(ukBounds);
                    }

                    // Store autocomplete instance
                    window.autocompleteInstances = window.autocompleteInstances || {};
                    window.autocompleteInstances[inputId] = autocomplete;

                    // Add place changed listener
                    autocomplete.addListener('place_changed', function() {
                        const place = autocomplete.getPlace();

                        if (!place.geometry) {
                            console.log('No geometry found for place');
                            return;
                        }

                        // Store coordinates if needed (for future use)
                        input.dataset.lat = place.geometry.location.lat();
                        input.dataset.lng = place.geometry.location.lng();

                        // Update input value with formatted address
                        input.value = place.formatted_address || place.name;

                        console.log('Place selected:', {
                            name: place.name,
                            formatted_address: place.formatted_address,
                            lat: place.geometry.location.lat(),
                            lng: place.geometry.location.lng()
                        });
                    });

                    // Add visual indicator that autocomplete is working
                    input.classList.add('autocomplete-enabled');
                    input.style.borderLeft = '3px solid #28a745';
                    input.title = 'Autocomplete enabled - start typing to see suggestions';

                    // Add loading indicator functionality
                    input.addEventListener('input', function() {
                        if (this.value.length > 2) {
                            this.classList.add('loading');
                        } else {
                            this.classList.remove('loading');
                        }
                    });

                    // Clear loading indicator when place is selected or input loses focus
                    input.addEventListener('blur', function() {
                        this.classList.remove('loading');
                    });

                } catch (error) {
                    console.error('Error initializing autocomplete for', inputId, error);
                }
            }
        });

        console.log('Home form autocomplete initialized for', addressInputs.length, 'inputs');

    } catch (error) {
        console.error('Error in initializeAutocomplete:', error);
    }
}
</script>

<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places&callback=initGoogleMapsApi" async defer></script>
<?php else: ?>
<script>
    console.warn('Google Maps API key is not configured. Autocomplete functionality will not be available.');

    // Add visual indicator that autocomplete is not available
    document.addEventListener('DOMContentLoaded', function() {
        const addressInputs = document.querySelectorAll('input[type="text"][id*="pickup"], input[type="text"][id*="dropoff"], input[type="text"][id*="address"]');
        addressInputs.forEach(input => {
            input.classList.add('autocomplete-disabled');
            input.placeholder = input.placeholder + ' (autocomplete not available)';
            input.style.borderLeft = '3px solid #ffc107';
            input.title = 'Google Maps API key not configured - autocomplete not available';
        });

        // Show a subtle notification about autocomplete not being available
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning alert-dismissible fade show mt-3';
        notification.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Note:</strong> Address autocomplete is currently not available. Please enter addresses manually.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert notification after the first booking form
        const firstForm = document.querySelector('.booking-form-container');
        if (firstForm) {
            firstForm.appendChild(notification);

            // Auto-dismiss after 8 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 8000);
        }
    });
</script>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Price calculation removed - users will see prices on booking page

        // Handle airport direction toggle and flight information
        const airportDirectionSelect = document.getElementById('airport_direction');
        if (airportDirectionSelect) {
            // Set initial state
            const initialDirection = airportDirectionSelect.value;
            if (initialDirection === 'to_airport') {
                document.querySelectorAll('.to-airport-field').forEach(el => el.style.display = 'block');
                document.querySelectorAll('.from-airport-field').forEach(el => el.style.display = 'none');

                // Make pickup required and dropoff not required
                const pickupInput = document.getElementById('airport_pickup_address');
                const dropoffInput = document.getElementById('airport_dropoff_address');

                if (pickupInput && dropoffInput) {
                    pickupInput.required = true;
                    pickupInput.classList.add('required');
                    dropoffInput.required = false;
                    dropoffInput.classList.remove('required');
                }

            } else {
                document.querySelectorAll('.to-airport-field').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.from-airport-field').forEach(el => el.style.display = 'block');

                // Make dropoff required and pickup not required
                const pickupInput = document.getElementById('airport_pickup_address');
                const dropoffInput = document.getElementById('airport_dropoff_address');

                if (pickupInput && dropoffInput) {
                    pickupInput.required = false;
                    pickupInput.classList.remove('required');
                    dropoffInput.required = true;
                    dropoffInput.classList.add('required');
                }
            }

            // Add change event listener
            airportDirectionSelect.addEventListener('change', function() {
                const direction = this.value;

                // Toggle visibility of fields based on direction
                if (direction === 'to_airport') {
                    document.querySelectorAll('.to-airport-field').forEach(el => el.style.display = 'block');
                    document.querySelectorAll('.from-airport-field').forEach(el => el.style.display = 'none');

                    // Make pickup required and dropoff not required
                    const pickupInput = document.getElementById('airport_pickup_address');
                    const dropoffInput = document.getElementById('airport_dropoff_address');

                    if (pickupInput && dropoffInput) {
                        pickupInput.required = true;
                        pickupInput.classList.add('required');
                        dropoffInput.required = false;
                        dropoffInput.classList.remove('required');
                    }

                } else {
                    document.querySelectorAll('.to-airport-field').forEach(el => el.style.display = 'none');
                    document.querySelectorAll('.from-airport-field').forEach(el => el.style.display = 'block');

                    // Make dropoff required and pickup not required
                    const pickupInput = document.getElementById('airport_pickup_address');
                    const dropoffInput = document.getElementById('airport_dropoff_address');

                    if (pickupInput && dropoffInput) {
                        pickupInput.required = false;
                        pickupInput.classList.remove('required');
                        dropoffInput.required = true;
                        dropoffInput.classList.add('required');
                    }
                }
            });
        }

        // Autocomplete will be initialized by the global initAutocomplete function
        // when Google Maps API loads

        // Enhanced date and time handling
        const dateInputs = document.querySelectorAll('input[type="date"]');
        const timeInputs = document.querySelectorAll('input[type="time"]');

        // Get current date and time
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowStr = tomorrow.toISOString().split('T')[0];

        // Calculate default time (current time + 2 hours, rounded to nearest 30 min)
        const defaultTime = calculateDefaultTime();

        function calculateDefaultTime(addHours = 2) {
            const timeNow = new Date();
            timeNow.setHours(timeNow.getHours() + addHours);
            const minutes = Math.ceil(timeNow.getMinutes() / 30) * 30;
            timeNow.setMinutes(minutes % 60);
            if (minutes === 60) {
                timeNow.setHours(timeNow.getHours() + 1);
            }

            const hours = String(timeNow.getHours()).padStart(2, '0');
            const mins = String(timeNow.getMinutes()).padStart(2, '0');
            return `${hours}:${mins}`;
        }

        // Set minimum dates and default values for date inputs
        dateInputs.forEach(input => {
            // Set minimum date to today
            if (!input.min) {
                input.min = today;
            }

            // Set default value if not already set
            if (!input.value) {
                // For return date, set to tomorrow by default
                if (input.id === 'return_date') {
                    input.value = tomorrowStr;
                    input.min = today; // Return can be same day
                } else {
                    input.value = today;
                }
            }

            // Add change event listener for date validation
            input.addEventListener('change', function() {
                validateDateTimeSelection(this);
            });
        });

        // Set default values for time inputs
        timeInputs.forEach(input => {
            if (!input.value) {
                input.value = defaultTime;
            }

            // Add change event listener for time validation
            input.addEventListener('change', function() {
                validateDateTimeSelection(this);
            });
        });

        // Validate date and time selection
        function validateDateTimeSelection(inputField) {
            const form = inputField.closest('form');
            if (!form) return true;

            // For return trip, validate return date is after pickup date
            if (inputField.id === 'return_date' || inputField.id === 'outbound_date') {
                const outboundDate = form.querySelector('#outbound_date');
                const returnDate = form.querySelector('#return_date');

                if (outboundDate && returnDate && outboundDate.value && returnDate.value) {
                    if (returnDate.value < outboundDate.value) {
                        returnDate.classList.add('is-invalid');

                        // Create or update validation message
                        let feedbackElement = returnDate.nextElementSibling;
                        if (!feedbackElement || !feedbackElement.classList.contains('invalid-feedback')) {
                            feedbackElement = document.createElement('div');
                            feedbackElement.classList.add('invalid-feedback');
                            returnDate.parentNode.insertBefore(feedbackElement, returnDate.nextSibling);
                        }
                        feedbackElement.textContent = 'Return date must be on or after outbound date';
                        return false;
                    } else {
                        returnDate.classList.remove('is-invalid');

                        // Remove validation message if it exists
                        const feedbackElement = returnDate.nextElementSibling;
                        if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                            feedbackElement.textContent = '';
                        }
                    }
                }
            }

            // Validate that date is not in the past
            if (inputField.type === 'date') {
                const selectedDate = new Date(inputField.value);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0); // Reset time part for comparison

                if (selectedDate < currentDate) {
                    inputField.classList.add('is-invalid');

                    // Create or update validation message
                    let feedbackElement = inputField.nextElementSibling;
                    if (!feedbackElement || !feedbackElement.classList.contains('invalid-feedback')) {
                        feedbackElement = document.createElement('div');
                        feedbackElement.classList.add('invalid-feedback');
                        inputField.parentNode.insertBefore(feedbackElement, inputField.nextSibling);
                    }
                    feedbackElement.textContent = 'Date cannot be in the past';
                    return false;
                }
            }

            // For same-day bookings, validate time is not in the past
            if (inputField.type === 'time') {
                const dateInput = form.querySelector('input[type="date"]');
                if (dateInput && dateInput.value === today) {
                    const selectedTime = inputField.value;
                    const currentTime = new Date();
                    const currentHour = currentTime.getHours();
                    const currentMinute = currentTime.getMinutes();

                    const [hours, minutes] = selectedTime.split(':').map(Number);

                    if (hours < currentHour || (hours === currentHour && minutes < currentMinute)) {
                        inputField.classList.add('is-invalid');

                        // Create or update validation message
                        let feedbackElement = inputField.nextElementSibling;
                        if (!feedbackElement || !feedbackElement.classList.contains('invalid-feedback')) {
                            feedbackElement = document.createElement('div');
                            feedbackElement.classList.add('invalid-feedback');
                            inputField.parentNode.insertBefore(feedbackElement, inputField.nextSibling);
                        }
                        feedbackElement.textContent = 'Time cannot be in the past';
                        return false;
                    }
                }
            }

            // If we got here, validation passed
            inputField.classList.remove('is-invalid');

            // Remove validation message if it exists
            const feedbackElement = inputField.nextElementSibling;
            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                feedbackElement.textContent = '';
            }

            return true;
        }



        // Form validation
        const forms = document.querySelectorAll('.booking-form-validate');

        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('.required');

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('is-invalid');
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                // Special validation for airport transfer
                if (form.querySelector('#airport_direction')) {
                    const direction = form.querySelector('#airport_direction').value;
                    const airportName = form.querySelector('#airport_name').value;

                    if (!airportName) {
                        isValid = false;
                        form.querySelector('#airport_name').classList.add('is-invalid');
                    }

                    if (direction === 'to_airport') {
                        const pickup = form.querySelector('#airport_pickup_address').value;
                        if (!pickup.trim()) {
                            isValid = false;
                            form.querySelector('#airport_pickup_address').classList.add('is-invalid');
                        }
                    } else {
                        const dropoff = form.querySelector('#airport_dropoff_address').value;
                        if (!dropoff.trim()) {
                            isValid = false;
                            form.querySelector('#airport_dropoff_address').classList.add('is-invalid');
                        }
                    }
                }



                if (!isValid) {
                    event.preventDefault();
                    // Scroll to the first invalid field
                    const firstInvalid = form.querySelector('.is-invalid');
                    if (firstInvalid) {
                        firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstInvalid.focus();
                    }
                }
            });
        });

        // Add input event listeners to remove is-invalid class when user types
        document.querySelectorAll('.required').forEach(field => {
            field.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.classList.remove('is-invalid');
                }
            });
        });

        // Enhanced address validation
        function validateAddress(inputField) {
            // Basic validation - check if the address has at least 5 characters
            if (inputField.value.trim().length < 5) {
                inputField.classList.add('is-invalid');

                // Create or update validation message
                let feedbackElement = inputField.nextElementSibling;
                if (!feedbackElement || !feedbackElement.classList.contains('invalid-feedback')) {
                    feedbackElement = document.createElement('div');
                    feedbackElement.classList.add('invalid-feedback');
                    inputField.parentNode.insertBefore(feedbackElement, inputField.nextSibling);
                }
                feedbackElement.textContent = 'Please enter a complete address';
                return false;
            } else {
                inputField.classList.remove('is-invalid');

                // Remove validation message if it exists
                const feedbackElement = inputField.nextElementSibling;
                if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                    feedbackElement.textContent = '';
                }
                return true;
            }
        }

        // Add address validation to all address fields
        const addressFields = [
            'pickup', 'dropoff', 'pickup_rt', 'dropoff_rt', 'pickup_hourly',
            'airport_pickup_address', 'airport_dropoff_address'
        ];

        addressFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', function() {
                    validateAddress(this);
                });
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/welcome.blade.php ENDPATH**/ ?>