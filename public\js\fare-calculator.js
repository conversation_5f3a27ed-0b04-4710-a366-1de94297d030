/**
 * Fare Calculator for Ynr Cars
 * This file contains functions for calculating fares and updating the summary
 */

// Get the currency symbol from the page or use existing window variable
let currencySymbol;
if (typeof window.currencySymbol === 'undefined') {
    currencySymbol = document.querySelector('meta[name="currency-symbol"]')?.getAttribute('content') || '$';
} else {
    currencySymbol = window.currencySymbol;
}

// Helper function to safely update element text content
function safelyUpdateElement(id, value, defaultValue = '-') {
    try {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value || defaultValue;
        }
    } catch (error) {
        console.warn(`Error updating element ${id}:`, error);
    }
}

// Helper function to safely update element visibility
function safelyToggleElement(id, show) {
    try {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = show ? 'flex' : 'none';
        }
    } catch (error) {
        console.warn(`Error toggling element ${id}:`, error);
    }
}

// Helper function to format date and time
function formatDateTime(date, time) {
    try {
        if (!date || !time) return '';

        const dateObj = new Date(date + 'T' + time);

        // Format: "Jan 1, 2023 12:00 PM"
        return dateObj.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        }) + ' ' + dateObj.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    } catch (error) {
        console.warn('Error formatting date and time:', error);
        return date + ' ' + time;
    }
}

// Global function to update the booking summary
window.updateSummary = function() {
    console.log('Updating summary with latest information');

    try {
        // Get booking type
        const bookingType = document.getElementById('bookingType')?.value || 'one_way';

        // Update trip type
        let bookingTypeText = 'One Way';
        if (bookingType === 'return') {
            bookingTypeText = 'Round Trip';
        } else if (bookingType === 'hourly') {
            bookingTypeText = 'Hourly';
        } else if (bookingType === 'airport') {
            bookingTypeText = 'Airport Transfer';
        }
        safelyUpdateElement('summary-trip-type', bookingTypeText);

        // Get pickup address
        let pickupAddress = '';
        try {
            if (bookingType === 'one_way') {
                pickupAddress = document.getElementById('pickup_address')?.value || '';
            } else if (bookingType === 'return') {
                pickupAddress = document.getElementById('return_pickup_address')?.value || '';
            } else if (bookingType === 'hourly') {
                pickupAddress = document.getElementById('hourly_pickup_address')?.value || '';
            } else if (bookingType === 'airport') {
                const direction = document.getElementById('airport_direction')?.value || 'to_airport';
                if (direction === 'to_airport') {
                    pickupAddress = document.getElementById('airport_pickup_address')?.value || '';
                } else {
                    pickupAddress = document.getElementById('airport_name')?.value || '';
                }
            }
        } catch (error) {
            console.warn('Error getting pickup address:', error);
        }

        // Truncate long addresses for better display
        if (pickupAddress && pickupAddress.length > 50) {
            const shortAddress = pickupAddress.substring(0, 47) + '...';
            safelyUpdateElement('summary-pickup', shortAddress);
        } else {
            safelyUpdateElement('summary-pickup', pickupAddress);
        }

        // Get dropoff address
        safelyToggleElement('summary-dropoff-container', bookingType !== 'hourly');

        if (bookingType !== 'hourly') {
            let dropoffAddress = '';
            try {
                if (bookingType === 'one_way') {
                    dropoffAddress = document.getElementById('dropoff_address')?.value || '';
                } else if (bookingType === 'return') {
                    dropoffAddress = document.getElementById('return_dropoff_address')?.value || '';
                } else if (bookingType === 'airport') {
                    const direction = document.getElementById('airport_direction')?.value || 'to_airport';
                    if (direction === 'to_airport') {
                        dropoffAddress = document.getElementById('airport_name')?.value || '';
                    } else {
                        dropoffAddress = document.getElementById('airport_dropoff_address')?.value || '';
                    }
                }
            } catch (error) {
                console.warn('Error getting dropoff address:', error);
            }

            // Truncate long addresses for better display
            if (dropoffAddress && dropoffAddress.length > 50) {
                const shortAddress = dropoffAddress.substring(0, 47) + '...';
                safelyUpdateElement('summary-dropoff', shortAddress);
            } else {
                safelyUpdateElement('summary-dropoff', dropoffAddress);
            }
        }

        // Get date and time
        let dateTime = '';
        try {
            if (bookingType === 'one_way') {
                const date = document.getElementById('pickup_date')?.value || '';
                const time = document.getElementById('pickup_time')?.value || '';
                if (date && time) {
                    dateTime = formatDateTime(date, time);
                }
            } else if (bookingType === 'return') {
                const date = document.getElementById('return_pickup_date')?.value || '';
                const time = document.getElementById('return_pickup_time')?.value || '';
                if (date && time) {
                    dateTime = formatDateTime(date, time);
                }
            } else if (bookingType === 'hourly') {
                const date = document.getElementById('hourly_pickup_date')?.value || '';
                const time = document.getElementById('hourly_pickup_time')?.value || '';
                if (date && time) {
                    dateTime = formatDateTime(date, time);
                }
            } else if (bookingType === 'airport') {
                const date = document.getElementById('airport_pickup_date')?.value || '';
                const time = document.getElementById('airport_pickup_time')?.value || '';
                if (date && time) {
                    dateTime = formatDateTime(date, time);
                }
            }
        } catch (error) {
            console.warn('Error getting date and time:', error);
        }
        safelyUpdateElement('summary-datetime', dateTime);

        // Get return date and time for round trip
        safelyToggleElement('summary-return-container', bookingType === 'return');

        if (bookingType === 'return') {
            try {
                const date = document.getElementById('return_date')?.value || '';
                const time = document.getElementById('return_time')?.value || '';
                if (date && time) {
                    safelyUpdateElement('summary-return-datetime', formatDateTime(date, time));
                }
            } catch (error) {
                console.warn('Error getting return date and time:', error);
            }
        }

        // Get duration for hourly booking
        safelyToggleElement('summary-duration-container', bookingType === 'hourly');

        if (bookingType === 'hourly') {
            try {
                const duration = document.getElementById('duration_hours')?.value || '';
                if (duration) {
                    safelyUpdateElement('summary-duration', duration + (parseInt(duration) > 1 ? ' hours' : ' hour'));
                }
            } catch (error) {
                console.warn('Error getting duration:', error);
            }
        }

        // Get airport direction for airport transfers
        safelyToggleElement('summary-airport-direction-container', bookingType === 'airport');

        if (bookingType === 'airport') {
            try {
                const direction = document.getElementById('airport_direction')?.value || 'to_airport';
                safelyUpdateElement('summary-airport-direction', direction === 'to_airport' ? 'To Airport' : 'From Airport');
            } catch (error) {
                console.warn('Error getting airport direction:', error);
            }
        }

        // Get selected vehicle
        try {
            const selectedVehicleCard = document.querySelector('.vehicle-select-card.selected');
            if (selectedVehicleCard) {
                const vehicleName = selectedVehicleCard.querySelector('.vehicle-name')?.textContent || 'Selected Vehicle';
                safelyUpdateElement('summary-vehicle', vehicleName);

                // Update vehicle type
                const vehicleType = selectedVehicleCard.querySelector('.text-muted')?.textContent || '';
                safelyUpdateElement('summary-vehicle-type', vehicleType);

                // Update capacity
                const seatsText = selectedVehicleCard.querySelector('.vehicle-detail p')?.textContent || '';
                safelyUpdateElement('summary-capacity', seatsText);
            } else {
                safelyUpdateElement('summary-vehicle', 'Not selected');
                safelyUpdateElement('summary-vehicle-type', 'Not specified');
                safelyUpdateElement('summary-capacity', 'Not specified');
            }
        } catch (error) {
            console.warn('Error getting vehicle information:', error);
        }

        // Update fare details in summary
        const distanceText = document.getElementById('distanceText')?.textContent || '';
        safelyUpdateElement('summary-distance', distanceText);

        const baseFare = document.getElementById('baseFare')?.textContent || '';
        safelyUpdateElement('summary-base-fare', baseFare);

        const distanceFare = document.getElementById('distanceFare')?.textContent || '';
        safelyUpdateElement('summary-distance-fare', distanceFare);

        const bookingFee = document.getElementById('bookingFee')?.textContent || '';
        safelyUpdateElement('summary-booking-fee', bookingFee);

        const totalFareText = document.getElementById('totalFareText')?.textContent || '';
        safelyUpdateElement('summary-total-fare', totalFareText);
    } catch (error) {
        console.error('Error in updateSummary function:', error);
    }
};
