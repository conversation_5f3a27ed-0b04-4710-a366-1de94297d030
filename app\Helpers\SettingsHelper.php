<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class SettingsHelper
{
    /**
     * Get all settings as an array.
     *
     * @return array
     */
    public static function getAllSettings()
    {
        try {
            // Check if the settings table exists
            if (!\Illuminate\Support\Facades\Schema::hasTable('settings')) {
                return [];
            }

            return Cache::remember('settings', 86400, function () {
                return Setting::all()->pluck('value', 'key')->toArray();
            });
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting all settings: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return [];
        }
    }

    /**
     * Get a specific setting value.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        try {
            // Check if the settings table exists
            if (!\Illuminate\Support\Facades\Schema::hasTable('settings')) {
                return $default;
            }

            $settings = self::getAllSettings();
            return $settings[$key] ?? $default;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting setting: ' . $e->getMessage(), [
                'key' => $key,
                'exception' => $e,
            ]);
            return $default;
        }
    }

    /**
     * Set a specific setting value.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set($key, $value)
    {
        Setting::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        // Clear the settings cache
        Cache::forget('settings');
    }

    /**
     * Get the company name.
     *
     * @return string
     */
    public static function getCompanyName()
    {
        return self::get('company_name', 'Ynr Cars');
    }

    /**
     * Get the company email.
     *
     * @return string
     */
    public static function getCompanyEmail()
    {
        return self::get('company_email', '<EMAIL>');
    }

    /**
     * Get the company phone.
     *
     * @return string
     */
    public static function getCompanyPhone()
    {
        return self::get('company_phone', '+****************');
    }

    /**
     * Get the company address.
     *
     * @return string
     */
    public static function getCompanyAddress()
    {
        return self::get('company_address', '123 Main St, New York, NY 10001');
    }

    /**
     * Get the currency symbol (legacy method).
     *
     * @return string
     */
    public static function getCurrency()
    {
        // First try to get the new currency_symbol setting
        $symbol = self::get('currency_symbol', null);
        if ($symbol !== null) {
            return $symbol;
        }

        // Fall back to the old currency setting
        return self::get('currency', '$');
    }

    /**
     * Get the tax rate.
     *
     * @return float
     */
    public static function getTaxRate()
    {
        return (float) self::get('tax_rate', 0);
    }

    /**
     * Get the base fare.
     *
     * @return float
     */
    public static function getBaseFare()
    {
        return (float) self::get('base_fare', 5.00);
    }

    /**
     * Get the price per kilometer.
     *
     * @return float
     */
    public static function getPricePerKm()
    {
        return (float) self::get('price_per_km', 2.50);
    }

    /**
     * Get the Google Maps API key.
     *
     * @return string|null
     */
    public static function getGoogleMapsApiKey()
    {
        // First try to get from settings, then fallback to env
        $apiKey = self::get('google_maps_api_key');
        if (empty($apiKey)) {
            $apiKey = env('GOOGLE_MAPS_API_KEY');
        }

        return $apiKey;
    }

    /**
     * Get the PayPal client ID.
     *
     * @return string|null
     */
    public static function getPaypalClientId()
    {
        $mode = self::getPaypalMode();
        if ($mode === 'sandbox') {
            return env('PAYPAL_SANDBOX_CLIENT_ID', self::get('paypal_client_id'));
        } else {
            return env('PAYPAL_LIVE_CLIENT_ID', self::get('paypal_client_id'));
        }
    }

    /**
     * Get the PayPal secret.
     *
     * @return string|null
     */
    public static function getPaypalSecret()
    {
        $mode = self::getPaypalMode();
        if ($mode === 'sandbox') {
            return env('PAYPAL_SANDBOX_CLIENT_SECRET', self::get('paypal_secret'));
        } else {
            return env('PAYPAL_LIVE_CLIENT_SECRET', self::get('paypal_secret'));
        }
    }

    /**
     * Format a price with currency symbol.
     *
     * @param float $price
     * @return string
     */
    public static function formatPrice($price)
    {
        $currencySymbol = self::getCurrencySymbol();
        return $currencySymbol . number_format($price, 2);
    }

    /**
     * Get the currency symbol.
     *
     * @return string
     */
    public static function getCurrencySymbol()
    {
        // First try to get the new currency_symbol setting
        $symbol = self::get('currency_symbol', null);
        if ($symbol !== null) {
            return $symbol;
        }

        // Fall back to the old currency setting
        return self::get('currency', '$');
    }

    /**
     * Get the currency code.
     *
     * @return string
     */
    public static function getCurrencyCode()
    {
        return self::get('currency_code', 'USD');
    }

    /**
     * Get the PayPal mode.
     *
     * @return string
     */
    public static function getPaypalMode()
    {
        return env('PAYPAL_MODE', self::get('paypal_mode', 'sandbox'));
    }

    /**
     * Get the Stripe publishable key.
     *
     * @return string|null
     */
    public static function getStripePublishableKey()
    {
        return env('STRIPE_PUBLISHABLE_KEY', self::get('stripe_publishable_key'));
    }

    /**
     * Get the Stripe secret key.
     *
     * @return string|null
     */
    public static function getStripeSecretKey()
    {
        return env('STRIPE_SECRET_KEY', self::get('stripe_secret_key'));
    }

    /**
     * Get social media URLs.
     *
     * @return array
     */
    public static function getSocialMediaUrls()
    {
        return [
            'facebook' => self::get('facebook_url'),
            'twitter' => self::get('twitter_url'),
            'instagram' => self::get('instagram_url'),
            'linkedin' => self::get('linkedin_url'),
        ];
    }

    /**
     * Get a specific social media URL.
     *
     * @param string $platform
     * @return string
     */
    public static function getSocialMediaUrl($platform)
    {
        $urls = self::getSocialMediaUrls();
        return $urls[$platform] ?? '#';
    }

    /**
     * Get the about us content.
     *
     * @return string|null
     */
    public static function getAboutUs()
    {
        return self::get('about_us');
    }

    /**
     * Get the privacy policy content.
     *
     * @return string|null
     */
    public static function getPrivacyPolicy()
    {
        return self::get('privacy_policy');
    }

    /**
     * Get the terms and conditions content.
     *
     * @return string|null
     */
    public static function getTermsAndConditions()
    {
        return self::get('terms_and_conditions');
    }

    /**
     * Get the company description.
     *
     * @return string
     */
    public static function getCompanyDescription()
    {
        return self::get('company_description', 'Professional car rental and chauffeur service');
    }
}
