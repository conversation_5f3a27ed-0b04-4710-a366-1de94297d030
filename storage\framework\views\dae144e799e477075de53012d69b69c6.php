<?php $__env->startSection('title', 'Earnings Analytics'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .analytics-container {
        padding: 20px;
    }

    .analytics-header {
        margin-bottom: 30px;
    }

    .analytics-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .analytics-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 20px;
    }

    .analytics-card .card-body {
        padding: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 25px;
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: #343a40;
    }

    .stat-label {
        color: #6c757d;
        font-size: 1rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 30px;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #343a40;
    }

    .insights-list {
        list-style-type: none;
        padding: 0;
    }

    .insights-list li {
        padding: 15px;
        border-bottom: 1px solid #eee;
    }

    .insights-list li:last-child {
        border-bottom: none;
    }

    .insight-icon {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        background-color: #f8f9fa;
        color: #ee393d;
        margin-right: 10px;
    }

    .insight-text {
        display: inline;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="analytics-container">
    <div class="analytics-header">
        <h2><i class="fas fa-chart-line me-2"></i> Earnings Analytics</h2>
        <p class="text-muted">Detailed analysis of your earnings patterns and trends</p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($totalEarnings, 2)); ?></div>
                <div class="stat-label">Total Earnings</div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-value">
                    <?php
                        $avgPerDay = 0;
                        if (count($formattedEarningsByDay) > 0) {
                            $avgPerDay = array_sum($formattedEarningsByDay) / count(array_filter($formattedEarningsByDay));
                        }
                    ?>
                    <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($avgPerDay, 2)); ?>

                </div>
                <div class="stat-label">Average Daily Earnings</div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-value">
                    <?php
                        $bestDay = 'N/A';
                        $maxEarnings = 0;
                        foreach ($formattedEarningsByDay as $day => $amount) {
                            if ($amount > $maxEarnings) {
                                $maxEarnings = $amount;
                                $bestDay = $day;
                            }
                        }
                    ?>
                    <?php echo e($bestDay); ?>

                </div>
                <div class="stat-label">Best Earning Day</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-week me-2"></i> Earnings by Day of Week</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dayOfWeekChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i> Earnings by Time of Day</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="timeOfDayChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i> Earnings by Booking Type</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="bookingTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i> Earnings Insights</h5>
                </div>
                <div class="card-body">
                    <ul class="insights-list">
                        <li>
                            <span class="insight-icon"><i class="fas fa-calendar-day"></i></span>
                            <span class="insight-text">Your most profitable day is <strong><?php echo e($bestDay); ?></strong> with average earnings of <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($maxEarnings, 2)); ?>.</span>
                        </li>
                        <li>
                            <span class="insight-icon"><i class="fas fa-clock"></i></span>
                            <span class="insight-text">
                                <?php
                                    $bestHour = array_search(max($formattedEarningsByHour), $formattedEarningsByHour);
                                ?>
                                Your peak earning time is around <strong><?php echo e($bestHour); ?></strong>.
                            </span>
                        </li>
                        <li>
                            <span class="insight-icon"><i class="fas fa-route"></i></span>
                            <span class="insight-text">
                                <?php
                                    $bestType = 'N/A';
                                    $maxTypeEarnings = 0;
                                    foreach ($earningsByType as $type => $amount) {
                                        if ($amount > $maxTypeEarnings) {
                                            $maxTypeEarnings = $amount;
                                            $bestType = $type;
                                        }
                                    }
                                    $bestType = str_replace('_', ' ', ucfirst($bestType));
                                ?>
                                <strong><?php echo e($bestType); ?></strong> bookings are your most profitable type.
                            </span>
                        </li>
                        <li>
                            <span class="insight-icon"><i class="fas fa-chart-line"></i></span>
                            <span class="insight-text">Consider focusing on <?php echo e($bestDay); ?> bookings to maximize your earnings.</span>
                        </li>
                        <li>
                            <span class="insight-icon"><i class="fas fa-money-bill-wave"></i></span>
                            <span class="insight-text">Your average earnings per day is <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($avgPerDay, 2)); ?>.</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Day of Week Chart
        const dayLabels = Object.keys(<?php echo json_encode($formattedEarningsByDay); ?>);
        const dayData = Object.values(<?php echo json_encode($formattedEarningsByDay); ?>);

        const dayCtx = document.getElementById('dayOfWeekChart').getContext('2d');
        new Chart(dayCtx, {
            type: 'bar',
            data: {
                labels: dayLabels,
                datasets: [{
                    label: 'Earnings by Day of Week',
                    data: dayData,
                    backgroundColor: 'rgba(238, 57, 61, 0.7)',
                    borderColor: '#ee393d',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?php echo e($currencySymbol ?? '£'); ?>' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Earnings: <?php echo e($currencySymbol ?? '£'); ?>' + context.raw.toFixed(2);
                            }
                        }
                    }
                }
            }
        });

        // Time of Day Chart
        const timeLabels = Object.keys(<?php echo json_encode($formattedEarningsByHour); ?>);
        const timeData = Object.values(<?php echo json_encode($formattedEarningsByHour); ?>);

        const timeCtx = document.getElementById('timeOfDayChart').getContext('2d');
        new Chart(timeCtx, {
            type: 'line',
            data: {
                labels: timeLabels,
                datasets: [{
                    label: 'Earnings by Time of Day',
                    data: timeData,
                    backgroundColor: 'rgba(238, 57, 61, 0.1)',
                    borderColor: '#ee393d',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?php echo e($currencySymbol ?? '£'); ?>' + value;
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 90,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Earnings: <?php echo e($currencySymbol ?? '£'); ?>' + context.raw.toFixed(2);
                            }
                        }
                    }
                }
            }
        });

        // Booking Type Chart
        const typeData = <?php echo json_encode($earningsByType); ?>;
        const typeLabels = Object.keys(typeData).map(type => type.replace('_', ' ').charAt(0).toUpperCase() + type.replace('_', ' ').slice(1));
        const typeValues = Object.values(typeData);

        const typeCtx = document.getElementById('bookingTypeChart').getContext('2d');
        new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: typeLabels,
                datasets: [{
                    data: typeValues,
                    backgroundColor: [
                        'rgba(238, 57, 61, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)'
                    ],
                    borderColor: [
                        '#ee393d',
                        '#36a2eb',
                        '#ffce56',
                        '#4bc0c0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return label + ': <?php echo e($currencySymbol ?? '£'); ?>' + value.toFixed(2) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.driver', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\driver\earnings\analytics.blade.php ENDPATH**/ ?>