<?php $__env->startSection('title', 'Driver Reports'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users me-2 text-warning"></i> Driver Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-warning" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Driver Statistics -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Available Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($availableDrivers); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unavailable Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($unavailableDrivers); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($inactiveDrivers); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Drivers by Rides -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Drivers by Completed Rides</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Driver</th>
                                    <th>Completed Rides</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topDriversByRides; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($driver->profile_photo): ?>
                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" 
                                                     class="rounded-circle me-2" width="32" height="32">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="font-weight-bold"><?php echo e($driver->name); ?></div>
                                                <small class="text-muted"><?php echo e($driver->email); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary"><?php echo e($driver->driver_rides_count); ?></span>
                                    </td>
                                    <td>
                                        <?php if($driver->is_active && $driver->is_available): ?>
                                            <span class="badge badge-success">Available</span>
                                        <?php elseif($driver->is_active): ?>
                                            <span class="badge badge-warning">Busy</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Drivers by Revenue -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Drivers by Revenue Generated</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Driver</th>
                                    <th>Total Revenue</th>
                                    <th>Rating</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topDriversByRevenue; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($driver->profile_photo): ?>
                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" 
                                                     class="rounded-circle me-2" width="32" height="32">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="font-weight-bold"><?php echo e($driver->name); ?></div>
                                                <small class="text-muted"><?php echo e($driver->phone); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-success font-weight-bold">
                                            $<?php echo e(number_format($driver->driver_rides_sum_amount ?? 0, 2)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                            $avgRating = $driver->driverRides()->whereNotNull('rating')->avg('rating');
                                        ?>
                                        <?php if($avgRating): ?>
                                            <div class="d-flex align-items-center">
                                                <span class="me-1"><?php echo e(number_format($avgRating, 1)); ?></span>
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= $avgRating): ?>
                                                        <i class="fas fa-star text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star text-warning"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">No ratings</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reports\drivers.blade.php ENDPATH**/ ?>