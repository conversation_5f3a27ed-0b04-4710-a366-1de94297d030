@extends('layouts.driver')

@section('title', 'Track Ride')

@section('styles')
<style>
    #map {
        height: 500px;
        width: 100%;
        border-radius: 10px;
    }

    .tracking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .tracking-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .tracking-info {
        padding: 15px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        margin-bottom: 15px;
    }

    .tracking-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .tracking-value {
        color: #6c757d;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-in-progress {
        background-color: #cce5ff;
        color: #004085;
    }

    .timer {
        font-size: 1.5rem;
        font-weight: 600;
        text-align: center;
        margin-bottom: 15px;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .action-buttons .btn {
        flex: 1;
    }

    .client-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .client-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 15px;
    }

    .client-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .client-phone {
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Track Ride</h2>
                <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Ride Details
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card tracking-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Live Tracking</h5>
                            <span class="status-badge status-in-progress">In Progress</span>
                        </div>
                        <div class="card-body">
                            <div id="map"></div>

                            <div class="timer mt-3">
                                <div>Ride Duration: <span id="duration">00:00:00</span></div>
                            </div>

                            <div class="action-buttons">
                                <form action="{{ route('driver.rides.complete', $ride->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-check-circle me-2"></i> Complete Ride
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card tracking-card">
                        <div class="card-header">
                            <h5 class="mb-0">Ride Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="client-info">
                                @if($ride->user->profile_photo)
                                    <img src="{{ asset('storage/' . $ride->user->profile_photo) }}" class="client-img" alt="Client">
                                @else
                                    <img src="https://via.placeholder.com/50x50?text=Client" class="client-img" alt="Client">
                                @endif
                                <div>
                                    <div class="client-name">Client #{{ substr($ride->user->id, 0, 5) }}</div>
                                    <div class="client-phone">
                                        <span class="badge bg-info">Booking #{{ $ride->booking_number }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Booking #</div>
                                <div class="tracking-value">{{ $ride->booking_number }}</div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Pickup</div>
                                <div class="tracking-value">{{ $ride->pickup_address }}</div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Dropoff</div>
                                <div class="tracking-value">{{ $ride->dropoff_address }}</div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Vehicle</div>
                                <div class="tracking-value">{{ $ride->vehicle->name }} ({{ $ride->vehicle->model }})</div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Fare</div>
                                <div class="tracking-value">@currency(){{ number_format($ride->amount, 2) }}</div>
                            </div>

                            <div class="tracking-info">
                                <div class="tracking-label">Started At</div>
                                <div class="tracking-value">{{ $ride->started_at ? $ride->started_at->format('M d, Y h:i A') : 'N/A' }}</div>
                            </div>

                            <div class="mt-4">
                                <button type="button" class="btn btn-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#addNoteModal">
                                    <i class="fas fa-sticky-note me-2"></i> Add Note
                                </button>

                                <button type="button" class="btn btn-danger w-100" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                    <i class="fas fa-times-circle me-2"></i> Cancel Ride
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Note Modal -->
<div class="modal fade" id="addNoteModal" tabindex="-1" aria-labelledby="addNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('driver.rides.add-note', $ride->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="addNoteModalLabel">Add Note</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="note" class="form-label">Note</label>
                        <textarea class="form-control" id="note" name="note" rows="4" required></textarea>
                        <small class="form-text text-muted">Add any important information about this ride.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Note</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('driver.rides.cancel', $ride->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelModalLabel">Cancel Ride</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to cancel this ride? This action cannot be undone.</p>
                    <div class="mb-3">
                        <label for="cancel_reason" class="form-label">Reason for Cancellation</label>
                        <textarea class="form-control" id="cancel_reason" name="cancel_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Cancel Ride</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&libraries=places&callback=initMap" async defer></script>
<script>
    let map;
    let directionsService;
    let directionsRenderer;
    let originMarker;
    let destinationMarker;
    let driverMarker;
    let watchId;
    let startTime = {{ $ride->started_at ? "new Date('" . $ride->started_at->toIso8601String() . "')" : "new Date()" }};

    function initMap() {
        // Initialize map
        map = new google.maps.Map(document.getElementById("map"), {
            zoom: 14,
            center: { lat: {{ $ride->pickup_lat }}, lng: {{ $ride->pickup_lng }} },
        });

        // Initialize directions service
        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            map: map,
            suppressMarkers: true
        });

        // Create markers
        originMarker = new google.maps.Marker({
            position: { lat: {{ $ride->pickup_lat }}, lng: {{ $ride->pickup_lng }} },
            map: map,
            icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
                scaledSize: new google.maps.Size(40, 40)
            },
            title: 'Pickup Location'
        });

        destinationMarker = new google.maps.Marker({
            position: { lat: {{ $ride->dropoff_lat }}, lng: {{ $ride->dropoff_lng }} },
            map: map,
            icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                scaledSize: new google.maps.Size(40, 40)
            },
            title: 'Dropoff Location'
        });

        // Add info windows
        const originInfo = new google.maps.InfoWindow({
            content: '<div><strong>Pickup:</strong> {{ $ride->pickup_address }}</div>'
        });

        const destinationInfo = new google.maps.InfoWindow({
            content: '<div><strong>Dropoff:</strong> {{ $ride->dropoff_address }}</div>'
        });

        originMarker.addListener('click', () => {
            originInfo.open(map, originMarker);
        });

        destinationMarker.addListener('click', () => {
            destinationInfo.open(map, destinationMarker);
        });

        // Calculate and display route
        calculateRoute();

        // Start tracking driver location
        startTracking();

        // Start timer
        updateTimer();
    }

    function calculateRoute() {
        const origin = { lat: {{ $ride->pickup_lat }}, lng: {{ $ride->pickup_lng }} };
        const destination = { lat: {{ $ride->dropoff_lat }}, lng: {{ $ride->dropoff_lng }} };

        directionsService.route(
            {
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING,
            },
            (response, status) => {
                if (status === "OK") {
                    directionsRenderer.setDirections(response);
                } else {
                    console.error("Directions request failed due to " + status);
                }
            }
        );
    }

    function startTracking() {
        if (navigator.geolocation) {
            driverMarker = new google.maps.Marker({
                map: map,
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
                    scaledSize: new google.maps.Size(40, 40)
                },
                title: 'Your Location'
            });

            watchId = navigator.geolocation.watchPosition(
                (position) => {
                    const pos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    };

                    driverMarker.setPosition(pos);
                    map.setCenter(pos);

                    // Send location update to server
                    updateDriverLocation(pos.lat, pos.lng);
                },
                () => {
                    console.error("Error: The Geolocation service failed.");
                }
            );
        } else {
            console.error("Error: Your browser doesn't support geolocation.");
        }
    }

    function updateDriverLocation(lat, lng) {
        fetch('{{ route('driver.rides.update-location', $ride->id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ latitude: lat, longitude: lng })
        })
        .then(response => response.json())
        .then(data => console.log('Location updated'))
        .catch(error => console.error('Error updating location:', error));
    }

    function updateTimer() {
        const now = new Date();
        const diff = now - startTime;

        // Calculate hours, minutes, seconds
        const hours = Math.floor(diff / 3600000);
        const minutes = Math.floor((diff % 3600000) / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);

        // Format time
        const formattedTime =
            (hours < 10 ? '0' + hours : hours) + ':' +
            (minutes < 10 ? '0' + minutes : minutes) + ':' +
            (seconds < 10 ? '0' + seconds : seconds);

        document.getElementById('duration').textContent = formattedTime;

        setTimeout(updateTimer, 1000);
    }

    // Clean up when leaving the page
    window.addEventListener('beforeunload', function() {
        if (watchId) {
            navigator.geolocation.clearWatch(watchId);
        }
    });
</script>
@endsection
