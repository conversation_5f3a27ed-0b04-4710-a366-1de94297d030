@extends('layouts.client')

@section('title', 'Edit Profile')

@section('styles')
<style>
    .profile-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .profile-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .profile-card .card-body {
        padding: 30px;
    }

    .profile-img-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 0 auto 20px;
    }

    .profile-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid #fff;
        box-shadow: 0 0 15px rgba(0,0,0,0.2);
    }

    .profile-img-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s;
        cursor: pointer;
    }

    .profile-img-container:hover .profile-img-overlay {
        opacity: 1;
    }

    .profile-img-overlay i {
        color: #fff;
        font-size: 1.5rem;
    }

    .preview-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 10px;
        display: none;
        margin-top: 15px;
        border: 3px solid #ee393d;
    }

    .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e0a800;
        border-color: #e0a800;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        border-radius: 8px 0 0 8px;
    }
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Edit Profile</h2>
    <a href="{{ route('client.profile.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Profile
    </a>
</div>

@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<div class="card profile-card" data-aos="fade-up">
    <div class="card-header">
        <h4 class="mb-0"><i class="fas fa-user-edit me-2"></i> Edit Profile Information</h4>
    </div>
    <div class="card-body">
        <form action="{{ route('client.profile.update') }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="text-center mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="profile-img-container" onclick="document.getElementById('profile_photo').click();">
                    @if ($user->profile_photo)
                        <img src="{{ asset('storage/' . $user->profile_photo) }}" class="profile-img" alt="{{ $user->name }}" id="preview-current">
                    @else
                        <img src="https://via.placeholder.com/150x150?text=Profile" class="profile-img" alt="{{ $user->name }}" id="preview-current">
                    @endif
                    <div class="profile-img-overlay">
                        <i class="fas fa-camera"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="file" class="form-control d-none" id="profile_photo" name="profile_photo" accept="image/*" onchange="previewImage(this)">
                    <img id="preview-new" class="preview-image mx-auto" src="#" alt="Profile Preview">
                </div>
                <small class="text-muted">Click on the image to change your profile photo</small>
            </div>

            <div class="form-section" data-aos="fade-up" data-aos-delay="200">
                <h5 class="form-section-title"><i class="fas fa-user-circle me-2"></i> Personal Information</h5>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Full Name</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $user->name) }}" required placeholder="Enter your full name">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $user->email) }}" required placeholder="Enter your email address">
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="phone" class="form-label">Phone Number</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="text" class="form-control" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" placeholder="Enter your phone number">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth', $user->date_of_birth ? $user->date_of_birth->format('Y-m-d') : '') }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-section" data-aos="fade-up" data-aos-delay="300">
                <h5 class="form-section-title"><i class="fas fa-map-marker-alt me-2"></i> Address Information</h5>

                <div class="mb-3">
                    <label for="address" class="form-label">Street Address</label>
                    <div class="input-group mb-3">
                        <span class="input-group-text"><i class="fas fa-home"></i></span>
                        <input type="text" class="form-control" id="address" name="address" value="{{ old('address', $user->address) }}" placeholder="Enter your street address">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="city" class="form-label">City</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-city"></i></span>
                            <input type="text" class="form-control" id="city" name="city" value="{{ old('city', $user->city) }}" placeholder="Enter your city">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="state" class="form-label">State/Province</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-map"></i></span>
                            <input type="text" class="form-control" id="state" name="state" value="{{ old('state', $user->state) }}" placeholder="Enter your state">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="postal_code" class="form-label">Postal Code</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="fas fa-mail-bulk"></i></span>
                            <input type="text" class="form-control" id="postal_code" name="postal_code" value="{{ old('postal_code', $user->postal_code) }}" placeholder="Enter your postal code">
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4" data-aos="fade-up" data-aos-delay="400">
                <a href="{{ route('client.profile.index') }}" class="btn btn-secondary me-md-2">
                    <i class="fas fa-times me-1"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function previewImage(input) {
        var previewCurrent = document.getElementById('preview-current');
        var previewNew = document.getElementById('preview-new');

        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                previewCurrent.style.display = 'none';
                previewNew.src = e.target.result;
                previewNew.style.display = 'block';
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            previewCurrent.style.display = 'block';
            previewNew.style.display = 'none';
        }
    }
</script>
@endsection
