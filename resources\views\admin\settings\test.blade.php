@extends('layouts.admin')

@section('title', 'Settings Test')

@section('content')
<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Settings Integration Test</h3>
                        <p class="card-subtitle">Verify all settings are properly connected to the website</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Company Settings -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-building"></i> Company Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Company Name:</strong></td>
                                                <td>{{ $companyName }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Company Email:</strong></td>
                                                <td>{{ $companyEmail }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Company Phone:</strong></td>
                                                <td>{{ $companyPhone }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Company Address:</strong></td>
                                                <td>{{ $companyAddress }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Appearance Settings -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-palette"></i> Appearance Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Primary Color:</strong></td>
                                                <td>
                                                    <span class="badge" style="background-color: {{ $primaryColor }}">{{ $primaryColor }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Secondary Color:</strong></td>
                                                <td>
                                                    <span class="badge" style="background-color: {{ $secondaryColor }}">{{ $secondaryColor }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Logo:</strong></td>
                                                <td>
                                                    @if($logoExists)
                                                        <img src="{{ asset('storage/' . $logoPath) }}" alt="Logo" style="max-height: 30px;">
                                                    @else
                                                        <span class="text-muted">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Language:</strong></td>
                                                <td>{{ $appearanceSettings['default_language'] }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Currency & Localization -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><i class="fas fa-globe"></i> Currency & Localization</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Currency Symbol:</strong></td>
                                                <td>{{ $currencySymbol }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Currency Code:</strong></td>
                                                <td>{{ $currencyCode }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Country Code:</strong></td>
                                                <td>{{ $countryCode }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Distance Unit:</strong></td>
                                                <td>{{ $distanceUnit }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Social Media Settings -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="fas fa-share-alt"></i> Social Media Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Facebook:</strong></td>
                                                <td>
                                                    @if($socialMediaSettings['facebook_url'])
                                                        <a href="{{ $socialMediaSettings['facebook_url'] }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fab fa-facebook-f"></i> View
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Twitter:</strong></td>
                                                <td>
                                                    @if($socialMediaSettings['twitter_url'])
                                                        <a href="{{ $socialMediaSettings['twitter_url'] }}" target="_blank" class="btn btn-sm btn-outline-info">
                                                            <i class="fab fa-twitter"></i> View
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Instagram:</strong></td>
                                                <td>
                                                    @if($socialMediaSettings['instagram_url'])
                                                        <a href="{{ $socialMediaSettings['instagram_url'] }}" target="_blank" class="btn btn-sm btn-outline-danger">
                                                            <i class="fab fa-instagram"></i> View
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>LinkedIn:</strong></td>
                                                <td>
                                                    @if($socialMediaSettings['linkedin_url'])
                                                        <a href="{{ $socialMediaSettings['linkedin_url'] }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fab fa-linkedin-in"></i> View
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- API Settings -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0"><i class="fas fa-key"></i> API Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Google Maps API Key:</strong></td>
                                                <td>
                                                    @if($googleMapsApiKey)
                                                        <span class="badge bg-success">Configured</span>
                                                        <small class="text-muted d-block">{{ substr($googleMapsApiKey, 0, 10) }}...</small>
                                                    @else
                                                        <span class="badge bg-danger">Not configured</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                        
                                        <h6 class="mt-3">Autocomplete Settings:</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Enabled:</strong></td>
                                                <td>
                                                    <span class="badge bg-{{ $autocompleteSettings['enabled'] ? 'success' : 'danger' }}">
                                                        {{ $autocompleteSettings['enabled'] ? 'Yes' : 'No' }}
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Types:</strong></td>
                                                <td>{{ $autocompleteSettings['types'] }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Country Restriction:</strong></td>
                                                <td>
                                                    @if($autocompleteSettings['restrict_country'])
                                                        <span class="badge bg-info">{{ $autocompleteSettings['country'] }}</span>
                                                    @else
                                                        <span class="text-muted">None</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Booking Settings -->
                            <div class="col-md-6">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0"><i class="fas fa-calendar-check"></i> Booking Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Advance Booking:</strong></td>
                                                <td>{{ $bookingSettings['advance_booking_time'] }} days</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Min Hourly Duration:</strong></td>
                                                <td>{{ $bookingSettings['minimum_hourly_duration'] }} hours</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Cancellation Window:</strong></td>
                                                <td>{{ $bookingSettings['cancellation_time_window'] }} hours</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Cancellation Fee:</strong></td>
                                                <td>{{ $bookingSettings['cancellation_fee_percentage'] }}%</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Guest Bookings:</strong></td>
                                                <td>
                                                    <span class="badge bg-{{ $bookingSettings['allow_guest_bookings'] ? 'success' : 'danger' }}">
                                                        {{ $bookingSettings['allow_guest_bookings'] ? 'Allowed' : 'Not Allowed' }}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-dark">
                                    <div class="card-header bg-dark text-white">
                                        <h5 class="mb-0"><i class="fas fa-vial"></i> Test Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('booking.index') }}" class="btn btn-primary" target="_blank">
                                                <i class="fas fa-external-link-alt"></i> Test Booking Page
                                            </a>
                                            <a href="{{ route('home') }}" class="btn btn-success" target="_blank">
                                                <i class="fas fa-home"></i> Test Homepage
                                            </a>
                                            <a href="{{ route('admin.settings.index') }}" class="btn btn-warning">
                                                <i class="fas fa-cog"></i> Back to Settings
                                            </a>
                                            <button type="button" class="btn btn-info" onclick="location.reload()">
                                                <i class="fas fa-sync-alt"></i> Refresh Test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add success animation to cards
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, 100 * index);
        });
    });
</script>
@endsection
