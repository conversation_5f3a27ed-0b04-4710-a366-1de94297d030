<?php

namespace App\Notifications;

use App\Models\Booking;
use App\Notifications\Channels\LogChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The booking instance.
     *
     * @var \App\Models\Booking
     */
    protected $booking;

    /**
     * The old status.
     *
     * @var string
     */
    protected $oldStatus;

    /**
     * Create a new notification instance.
     *
     * @param \App\Models\Booking $booking
     * @param string|null $oldStatus
     * @return void
     */
    public function __construct(Booking $booking, $oldStatus = null)
    {
        $this->booking = $booking;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [LogChannel::class, 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $message = (new MailMessage)
            ->subject('Booking Status Update - #' . $this->booking->booking_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your booking status has been updated.');

        switch ($this->booking->status) {
            case 'confirmed':
                $message->line('Your booking has been confirmed.')
                    ->line('A driver will be assigned to your booking soon.');
                break;
            case 'assigned':
                $message->line('A driver has been assigned to your booking.')
                    ->line('Driver Name: ' . ($this->booking->driver->name ?? 'Not available'));
                break;
            case 'in_progress':
                $message->line('Your ride is now in progress.')
                    ->line('The driver has started the journey.');
                break;
            case 'completed':
                $message->line('Your ride has been completed.')
                    ->line('Thank you for choosing our service!')
                    ->line('We would appreciate if you could leave a review for your ride.');
                break;
            case 'cancelled':
                $message->line('Your booking has been cancelled.')
                    ->line('Reason: ' . ($this->booking->cancellation_reason ?? 'Not provided'));
                break;
            default:
                $message->line('Status: ' . ucfirst($this->booking->status));
                break;
        }

        return $message
            ->line('Booking Details:')
            ->line('Pickup: ' . $this->booking->pickup_address)
            ->line('Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->action('View Booking Details', url(route('client.bookings.show', $this->booking->id)))
            ->line('Thank you for using our service!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'old_status' => $this->oldStatus,
            'new_status' => $this->booking->status,
            'message' => $this->getStatusMessage(),
        ];
    }

    /**
     * Get status message based on current status.
     *
     * @return string
     */
    protected function getStatusMessage()
    {
        switch ($this->booking->status) {
            case 'confirmed':
                return 'Your booking has been confirmed.';
            case 'assigned':
                return 'A driver has been assigned to your booking.';
            case 'in_progress':
                return 'Your ride is now in progress.';
            case 'completed':
                return 'Your ride has been completed.';
            case 'cancelled':
                return 'Your booking has been cancelled.';
            default:
                return 'Your booking status has been updated to ' . ucfirst($this->booking->status) . '.';
        }
    }
}
