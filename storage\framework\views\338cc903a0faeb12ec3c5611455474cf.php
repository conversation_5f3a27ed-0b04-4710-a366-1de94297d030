<?php $__env->startSection('title', 'Cash Payment Confirmed'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .success-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .success-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .success-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .success-card .card-body {
        padding: 30px;
    }

    .success-icon {
        font-size: 5rem;
        color: #28a745;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .payment-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .payment-detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .payment-detail-row:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .payment-detail-row span:first-child {
        font-weight: 600;
        color: #495057;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #000;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e5b429;
        border-color: #e5b429;
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="success-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card success-card">
                    <div class="card-header">
                        <h3 class="mb-0">Cash Payment Confirmed</h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-check-circle success-icon"></i>
                            <h4 class="mb-3">Your Cash Payment Has Been Confirmed!</h4>
                            <p class="mb-4">Your booking has been confirmed with cash payment. You will pay the driver in cash at the time of service.</p>
                        </div>

                        <div class="payment-details">
                            <h5 class="mb-3">Booking Details</h5>
                            <div class="payment-detail-row">
                                <span>Booking Number:</span>
                                <span><?php echo e($booking->booking_number); ?></span>
                            </div>
                            <div class="payment-detail-row">
                                <span>Payment Method:</span>
                                <span><?php echo e(ucfirst($payment->payment_method)); ?></span>
                            </div>
                            <div class="payment-detail-row">
                                <span>Transaction ID:</span>
                                <span><?php echo e($payment->transaction_id); ?></span>
                            </div>
                            <div class="payment-detail-row">
                                <span>Amount to Pay:</span>
                                <span><?php echo e($currencySymbol); ?><?php echo e(number_format($payment->amount, 2)); ?></span>
                            </div>
                            <div class="payment-detail-row">
                                <span>Status:</span>
                                <span><?php echo e(ucfirst($payment->status)); ?> (Pay to driver)</span>
                            </div>
                            <div class="payment-detail-row">
                                <span>Date:</span>
                                <span><?php echo e($payment->created_at->format('M d, Y h:i A')); ?></span>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Please have the exact amount ready to pay the driver when they arrive.
                        </div>

                        <div class="text-center">
                            <p class="mb-4">Your booking has been confirmed. You can view your booking details and track your ride from your dashboard.</p>

                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <a href="<?php echo e(route('booking.track', $booking->id)); ?>" class="btn btn-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i>Next: Track Your Booking
                                </a>
                                <a href="<?php echo e(route('booking.confirmation', $booking->id)); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-info-circle me-2"></i>View Booking Details
                                </a>
                                <a href="<?php echo e(route('client.dashboard')); ?>" class="btn btn-outline-secondary">Go to Dashboard</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\booking\cash-payment-success.blade.php ENDPATH**/ ?>