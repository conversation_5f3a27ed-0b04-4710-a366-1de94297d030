<?php $__env->startSection('title', 'Your Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="guest-review-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="guest-review-card">
                    <div class="card-header">
                        <h3 class="mb-0">Your Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <h5 class="mb-4">Please provide your contact information</h5>

                                <form action="<?php echo e(route('booking.client-details.save')); ?>" method="POST" id="clientDetailsForm" class="guest-form">
                                    <?php echo csrf_field(); ?>

                                    <div class="form-group mb-3">
                                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-text">Please enter your full name as it appears on your ID.</div>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email')); ?>" required>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-text">We'll send your booking confirmation to this email.</div>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone" name="phone" value="<?php echo e(old('phone')); ?>" required>
                                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-text">The driver may contact you on this number.</div>
                                    </div>

                                    <div class="form-group mb-4">
                                        <label for="special_requests" class="form-label">Special Requests</label>
                                        <textarea class="form-control <?php $__errorArgs = ['special_requests'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="special_requests" name="special_requests" rows="3"><?php echo e(old('special_requests')); ?></textarea>
                                        <?php $__errorArgs = ['special_requests'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-text">Any special requirements or information for your driver.</div>
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input <?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> <span class="text-danger">*</span>
                                        </label>
                                        <?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="booking-actions">
                                        <a href="<?php echo e(route('booking.guest.review')); ?>" class="btn btn-back">
                                            <i class="fas fa-arrow-left me-2"></i> Back
                                        </a>
                                        <button type="submit" class="btn btn-next">
                                            Continue <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="col-md-5">
                                <div class="booking-summary">
                                    <h4>Booking Summary</h4>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Booking Type:</div>
                                        <div class="booking-summary-value">
                                            <?php if(session('guest_booking.booking_type') == 'one_way'): ?>
                                                One Way
                                            <?php elseif(session('guest_booking.booking_type') == 'return'): ?>
                                                Return
                                            <?php elseif(session('guest_booking.booking_type') == 'airport'): ?>
                                                Airport Transfer
                                            <?php else: ?>
                                                Hourly (<?php echo e(session('guest_booking.duration_hours')); ?> hours)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup:</div>
                                        <div class="booking-summary-value"><?php echo e(session('guest_booking.pickup_address')); ?></div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Date & Time:</div>
                                        <div class="booking-summary-value"><?php echo e(\Carbon\Carbon::parse(session('guest_booking.pickup_date'))->format('M d, Y h:i A')); ?></div>
                                    </div>

                                    <?php if(session('guest_booking.booking_type') != 'hourly'): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Dropoff:</div>
                                            <div class="booking-summary-value"><?php echo e(session('guest_booking.dropoff_address')); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(session('guest_booking.booking_type') == 'return' && session('guest_booking.return_date')): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Return Date:</div>
                                            <div class="booking-summary-value"><?php echo e(\Carbon\Carbon::parse(session('guest_booking.return_date'))->format('M d, Y h:i A')); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="booking-total">
                                        <div class="booking-total-label">Total:</div>
                                        <div class="booking-total-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format(session('guest_booking.amount'), 2)); ?></div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> After providing your details, you'll be able to sign in or create an account to complete your booking.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Booking and Cancellation Policy</h6>
                <p>All bookings are subject to availability. Cancellations made less than 24 hours before the scheduled pickup time may incur a cancellation fee.</p>

                <h6>2. Payment Terms</h6>
                <p>Payment is required at the time of booking. We accept credit/debit cards and PayPal.</p>

                <h6>3. Privacy Policy</h6>
                <p>Your personal information will be used only for the purpose of providing our service and will be handled in accordance with our privacy policy.</p>

                <h6>4. Liability</h6>
                <p>We strive to provide reliable service, but cannot be held responsible for delays caused by traffic, weather, or other circumstances beyond our control.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('clientDetailsForm');

        if (form) {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...';
                }
            });
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\booking\client-details.blade.php ENDPATH**/ ?>