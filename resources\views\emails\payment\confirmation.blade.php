@extends('emails.layouts.master')

@section('title', 'Payment Confirmation')

@section('content')
<h2>Payment Confirmation</h2>

<p>Dear {{ $user->name }},</p>

<p>Thank you! We have successfully received your payment for booking #{{ $booking->booking_number }}.</p>

<div class="booking-details">
    <h3>Payment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Transaction ID:</span>
        <span class="detail-value">{{ $payment->transaction_id }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value">{{ ucfirst($payment->payment_method) }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Amount Paid:</span>
        <span class="detail-value"><strong>{{ $currencySymbol }}{{ number_format($payment->amount, 2) }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Date:</span>
        <span class="detail-value">{{ $payment->created_at->format('F j, Y \a\t g:i A') }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Status:</span>
        <span class="detail-value" style="color: #28a745;"><strong>Completed</strong></span>
    </div>
</div>

<div class="booking-details">
    <h3>Booking Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value">{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name }}</span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">✓ Your Booking is Confirmed!</h3>
    <p style="color: #155724; margin-bottom: 0;">
        Your payment has been processed successfully and your booking is now fully confirmed. 
        You will receive a reminder email 24 hours before your pickup time.
    </p>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn">View Booking Details</a>
    <a href="{{ route('client.payments.invoice', $payment->id) }}" class="btn" style="background-color: #6c757d; margin-left: 10px;">Download Invoice</a>
</div>

<h3>What's Next?</h3>
<ul>
    <li>You will receive a reminder email 24 hours before your pickup</li>
    <li>Our driver will contact you 15-30 minutes before arrival</li>
    <li>Please be ready at your pickup location at the scheduled time</li>
    <li>Keep your booking confirmation number handy: <strong>#{{ $booking->booking_number }}</strong></li>
</ul>

<h3>Need Help?</h3>
<p>If you have any questions about your payment or booking, please don't hesitate to contact us:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or visit your account dashboard online</li>
</ul>

<p>Thank you for choosing {{ $companyName }}!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>
@endsection
