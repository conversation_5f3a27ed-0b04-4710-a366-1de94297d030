<?php

namespace App\Console\Commands;

use App\Models\EmailLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class ProcessEmailQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:process-queue {--timeout=60 : Maximum execution time in seconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process queued emails and update their status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $timeout = (int) $this->option('timeout');
        $this->info("Processing email queue for {$timeout} seconds...");

        $startTime = time();
        $processed = 0;
        $updated = 0;
        $errors = 0;

        // Get queued emails that might need status updates
        $queuedEmails = EmailLog::where('status', 'queued')
            ->where('created_at', '<', now()->subMinutes(5)) // Only check emails older than 5 minutes
            ->orderBy('created_at', 'asc')
            ->limit(100)
            ->get();

        foreach ($queuedEmails as $emailLog) {
            if (time() - $startTime >= $timeout) {
                $this->info("Timeout reached. Stopping processing.");
                break;
            }

            try {
                $processed++;
                
                // Check if email is still in queue or has been processed
                $jobExists = $this->checkIfJobExists($emailLog);
                
                if (!$jobExists) {
                    // Email might have been processed but status not updated
                    // Check if it's been more than 10 minutes since creation
                    if ($emailLog->created_at->diffInMinutes(now()) > 10) {
                        // Assume it failed if no status update after 10 minutes
                        $emailLog->markAsFailed('Email processing timeout - no status update received');
                        $updated++;
                        $this->line("✗ Marked email #{$emailLog->id} as failed due to timeout");
                    }
                } else {
                    // Job still exists in queue, leave it alone
                    $this->line("⏳ Email #{$emailLog->id} still in queue");
                }

            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Error processing email #{$emailLog->id}: " . $e->getMessage());
                Log::error('Email queue processing error', [
                    'email_log_id' => $emailLog->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Also check for very old queued emails (more than 1 hour)
        $staleEmails = EmailLog::where('status', 'queued')
            ->where('created_at', '<', now()->subHour())
            ->get();

        foreach ($staleEmails as $emailLog) {
            try {
                $emailLog->markAsFailed('Email processing timeout - stale queue entry');
                $updated++;
                $this->line("✗ Marked stale email #{$emailLog->id} as failed");
            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Error marking stale email #{$emailLog->id} as failed: " . $e->getMessage());
            }
        }

        $this->info("\nEmail queue processing completed:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Emails processed', $processed],
                ['Status updates', $updated],
                ['Errors', $errors],
                ['Stale emails cleaned', $staleEmails->count()],
            ]
        );

        Log::info('Email queue processing completed', [
            'processed' => $processed,
            'updated' => $updated,
            'errors' => $errors,
            'stale_cleaned' => $staleEmails->count(),
        ]);

        return $errors > 0 ? 1 : 0;
    }

    /**
     * Check if a job exists in the queue for this email
     *
     * @param EmailLog $emailLog
     * @return bool
     */
    private function checkIfJobExists(EmailLog $emailLog)
    {
        // This is a simplified check - in a real implementation,
        // you might want to check the actual queue tables or use
        // a more sophisticated method to track job status
        
        try {
            // Check if there are any jobs in the queue
            // This is a basic implementation - you might need to adjust
            // based on your queue driver and setup
            
            $queueSize = Queue::size();
            
            // If queue is empty and email is still marked as queued,
            // it's likely been processed but status not updated
            return $queueSize > 0;
            
        } catch (\Exception $e) {
            Log::warning('Could not check queue status', [
                'error' => $e->getMessage()
            ]);
            return true; // Assume job exists if we can't check
        }
    }
}
