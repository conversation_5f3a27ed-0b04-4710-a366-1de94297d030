@extends('layouts.admin')

@section('title', 'Add New Driver')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-plus text-primary mr-2"></i>Add New Driver
        </h1>
        <a href="{{ route('admin.drivers.index') }}" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Drivers
        </a>
    </div>

    <form action="{{ route('admin.drivers.store') }}" method="POST" enctype="multipart/form-data" id="driverForm">
        @csrf

        <!-- Personal Information -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user mr-2"></i>Personal Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}"
                                   placeholder="Enter driver's full name" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email') }}"
                                   placeholder="<EMAIL>" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone">Phone Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                   id="phone" name="phone" value="{{ old('phone') }}"
                                   placeholder="+****************" required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="license_number">Driver License Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('license_number') is-invalid @enderror"
                                   id="license_number" name="license_number" value="{{ old('license_number') }}"
                                   placeholder="DL123456789" required>
                            @error('license_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password">Password <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                       id="password" name="password" placeholder="Enter secure password" required>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Minimum 8 characters</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password_confirmation">Confirm Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password_confirmation"
                                   name="password_confirmation" placeholder="Confirm password" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="address">Address <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('address') is-invalid @enderror"
                                      id="address" name="address" rows="3"
                                      placeholder="Enter complete address" required>{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="profile_photo">Profile Photo</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input @error('profile_photo') is-invalid @enderror"
                                       id="profile_photo" name="profile_photo" accept="image/*">
                                <label class="custom-file-label" for="profile_photo">Choose photo</label>
                                @error('profile_photo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <small class="form-text text-muted">Max size: 2MB</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vehicle Details -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-car mr-2"></i>Vehicle Details
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_make">Vehicle Make <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('vehicle_make') is-invalid @enderror"
                                   id="vehicle_make" name="vehicle_make" value="{{ old('vehicle_make') }}"
                                   placeholder="e.g. Toyota, BMW, Mercedes" required>
                            @error('vehicle_make')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_model">Vehicle Model <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('vehicle_model') is-invalid @enderror"
                                   id="vehicle_model" name="vehicle_model" value="{{ old('vehicle_model') }}"
                                   placeholder="e.g. Camry, X5, E-Class" required>
                            @error('vehicle_model')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_color">Vehicle Color <span class="text-danger">*</span></label>
                            <select class="form-control @error('vehicle_color') is-invalid @enderror"
                                    id="vehicle_color" name="vehicle_color" required>
                                <option value="">Select Color</option>
                                <option value="Black" {{ old('vehicle_color') == 'Black' ? 'selected' : '' }}>Black</option>
                                <option value="White" {{ old('vehicle_color') == 'White' ? 'selected' : '' }}>White</option>
                                <option value="Silver" {{ old('vehicle_color') == 'Silver' ? 'selected' : '' }}>Silver</option>
                                <option value="Gray" {{ old('vehicle_color') == 'Gray' ? 'selected' : '' }}>Gray</option>
                                <option value="Blue" {{ old('vehicle_color') == 'Blue' ? 'selected' : '' }}>Blue</option>
                                <option value="Red" {{ old('vehicle_color') == 'Red' ? 'selected' : '' }}>Red</option>
                                <option value="Green" {{ old('vehicle_color') == 'Green' ? 'selected' : '' }}>Green</option>
                                <option value="Other" {{ old('vehicle_color') == 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('vehicle_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_reg_number">Registration Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('vehicle_reg_number') is-invalid @enderror"
                                   id="vehicle_reg_number" name="vehicle_reg_number" value="{{ old('vehicle_reg_number') }}"
                                   placeholder="e.g. ABC123, XY12 ABC" style="text-transform: uppercase;" required>
                            @error('vehicle_reg_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="insurance_expiry">Insurance Expiry <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('insurance_expiry') is-invalid @enderror"
                                   id="insurance_expiry" name="insurance_expiry" value="{{ old('insurance_expiry') }}"
                                   min="{{ date('Y-m-d') }}" required>
                            @error('insurance_expiry')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Must be valid for at least 30 days</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mot_expiry">MOT Expiry <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('mot_expiry') is-invalid @enderror"
                                   id="mot_expiry" name="mot_expiry" value="{{ old('mot_expiry') }}"
                                   min="{{ date('Y-m-d') }}" required>
                            @error('mot_expiry')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">MOT certificate must be valid</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="vehicle_info">Additional Vehicle Information</label>
                    <textarea class="form-control @error('vehicle_info') is-invalid @enderror"
                              id="vehicle_info" name="vehicle_info" rows="3"
                              placeholder="Any additional information about the vehicle">{{ old('vehicle_info') }}</textarea>
                    @error('vehicle_info')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Documents Upload -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-alt mr-2"></i>Documents Upload
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>Required Documents:</strong> Driver License, Driver PHD License, Vehicle PHD License, Insurance Certificate, MOT Certificate
                </div>

                <div id="documents-container">
                    <div class="document-upload-item border rounded p-3 mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Document Type</label>
                                    <select class="form-control document-type" name="document_types[]">
                                        <option value="">Select Document Type</option>
                                        <option value="Driver License">Driver License</option>
                                        <option value="Driver PHD License">Driver PHD License</option>
                                        <option value="Vehicle PHD License">Vehicle PHD License</option>
                                        <option value="Insurance">Insurance Certificate</option>
                                        <option value="MOT Certificate">MOT Certificate</option>
                                        <option value="V5C Logbook">V5C Logbook</option>
                                        <option value="Vehicle Photos">Vehicle Photos</option>
                                        <option value="Background Check">Background Check</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Document File</label>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input document-file" name="documents[]"
                                               accept=".pdf,.jpg,.jpeg,.png">
                                        <label class="custom-file-label">Choose file</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Expiry Date</label>
                                    <input type="date" class="form-control" name="expiry_dates[]" min="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-danger btn-sm d-block remove-document" style="display: none;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea class="form-control" name="document_notes[]" rows="2"
                                      placeholder="Any additional notes about this document"></textarea>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-outline-primary btn-sm mb-3" id="add-document">
                    <i class="fas fa-plus mr-1"></i> Add Another Document
                </button>
            </div>
        </div>

        <!-- Driver Settings -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cog mr-2"></i>Driver Settings
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" checked>
                                <label class="custom-control-label" for="is_active">
                                    <strong>Active Status</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Driver will be active in the system</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_available" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_available" name="is_available" value="1" checked>
                                <label class="custom-control-label" for="is_available">
                                    <strong>Available for Bookings</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Driver will be available for new bookings</small>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    The driver will receive login credentials via email after creation.
                </div>

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-user-plus mr-2"></i> Create Driver
                    </button>
                    <a href="{{ route('admin.drivers.index') }}" class="btn btn-secondary btn-lg ml-2">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                </div>
            </div>
        </div>
    </form>

</div>
@endsection

@section('styles')
<style>
    .document-upload-item {
        background-color: #f8f9fc;
        transition: all 0.3s ease;
    }

    .document-upload-item:hover {
        background-color: #eaecf4;
    }
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // File upload handlers
    $('.custom-file-input').on('change', function() {
        const fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').text(fileName || 'Choose file');
    });

    // Password toggle
    $('#togglePassword').on('click', function() {
        const passwordField = $('#password');
        const type = passwordField.attr('type') === 'password' ? 'text' : 'password';
        passwordField.attr('type', type);
        $(this).find('i').toggleClass('fa-eye fa-eye-slash');
    });

    // Password confirmation validation
    $('#password_confirmation').on('input', function() {
        const password = $('#password').val();
        const confirmation = $(this).val();

        if (confirmation && password !== confirmation) {
            $(this).addClass('is-invalid').removeClass('is-valid');
        } else if (confirmation && password === confirmation) {
            $(this).addClass('is-valid').removeClass('is-invalid');
        }
    });

    // Registration number uppercase
    $('#vehicle_reg_number').on('input', function() {
        $(this).val($(this).val().toUpperCase());
    });

    // Document management
    $('#add-document').on('click', function() {
        const template = `
            <div class="document-upload-item border rounded p-3 mb-3">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Document Type</label>
                            <select class="form-control document-type" name="document_types[]">
                                <option value="">Select Document Type</option>
                                <option value="Driver License">Driver License</option>
                                <option value="Driver PHD License">Driver PHD License</option>
                                <option value="Vehicle PHD License">Vehicle PHD License</option>
                                <option value="Insurance">Insurance Certificate</option>
                                <option value="MOT Certificate">MOT Certificate</option>
                                <option value="V5C Logbook">V5C Logbook</option>
                                <option value="Vehicle Photos">Vehicle Photos</option>
                                <option value="Background Check">Background Check</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Document File</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input document-file" name="documents[]"
                                       accept=".pdf,.jpg,.jpeg,.png">
                                <label class="custom-file-label">Choose file</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Expiry Date</label>
                            <input type="date" class="form-control" name="expiry_dates[]" min="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm d-block remove-document">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Notes</label>
                    <textarea class="form-control" name="document_notes[]" rows="2"
                              placeholder="Any additional notes about this document"></textarea>
                </div>
            </div>
        `;

        $('#documents-container').append(template);
        updateRemoveButtons();
    });

    // Remove document
    $(document).on('click', '.remove-document', function() {
        $(this).closest('.document-upload-item').remove();
        updateRemoveButtons();
    });

    function updateRemoveButtons() {
        const items = $('.document-upload-item');
        if (items.length > 1) {
            $('.remove-document').show();
        } else {
            $('.remove-document').hide();
        }
    }

    // Initialize remove buttons
    updateRemoveButtons();

    // Form submission loading state
    $('#driverForm').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating Driver...');
    });
});
</script>
@endsection
