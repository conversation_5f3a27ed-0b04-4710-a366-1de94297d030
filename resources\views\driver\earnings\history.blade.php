@extends('layouts.driver')

@section('title', 'Earnings History')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }



    .earnings-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .earnings-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .earnings-card .card-body {
        padding: 20px;
    }

    .filter-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .earnings-total {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .earnings-total-label {
        font-weight: 600;
    }

    .earnings-total-amount {
        font-size: 1.2rem;
        font-weight: 700;
        color: #28a745;
    }

    .earnings-table th,
    .earnings-table td {
        vertical-align: middle;
    }

    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Earnings History</h2>
                <a href="{{ route('driver.earnings.index') }}" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i> Earnings Dashboard
                </a>
            </div>

            <div class="filter-form">
                <form action="{{ route('driver.earnings.history') }}" method="GET">
                    <div class="row">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate ?? '' }}">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate ?? '' }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">Filter</button>
                            <a href="{{ route('driver.earnings.history') }}" class="btn btn-secondary">Reset</a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="earnings-total">
                <div class="earnings-total-label">Total Earnings{{ $startDate || $endDate ? ' (Filtered)' : '' }}:</div>
                <div class="earnings-total-amount">{{ $currencySymbol }}{{ number_format($totalFiltered, 2) }}</div>
            </div>

            <div class="card earnings-card">
                <div class="card-header">
                    <h5 class="mb-0">Earnings History</h5>
                </div>
                <div class="card-body">
                    @if ($earnings->isEmpty())
                        <p class="text-center text-muted py-5">No earnings found for the selected period</p>
                    @else
                        <div class="table-responsive">
                            <table class="table table-hover earnings-table">
                                <thead>
                                    <tr>
                                        <th>Booking #</th>
                                        <th>Client</th>
                                        <th>Date</th>
                                        <th>Pickup</th>
                                        <th>Dropoff</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($earnings as $earning)
                                        <tr>
                                            <td>{{ $earning->booking_number }}</td>
                                            <td>{{ $earning->user->name }}</td>
                                            <td>{{ $earning->completed_at ? $earning->completed_at->format('M d, Y') : 'N/A' }}</td>
                                            <td>{{ Str::limit($earning->pickup_address, 20) }}</td>
                                            <td>{{ Str::limit($earning->dropoff_address, 20) }}</td>
                                            <td>{{ ucfirst($earning->booking_type) }}</td>
                                            <td>{{ $currencySymbol }}{{ number_format($earning->amount, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            {{ $earnings->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
