<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:client']);
    }

    /**
     * Show the client dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $userId = Auth::id();

        // Get booking statistics
        $totalBookings = Booking::where('user_id', $userId)->count();
        $activeBookings = Booking::where('user_id', $userId)
            ->whereIn('status', ['pending', 'confirmed', 'assigned', 'in_progress'])
            ->count();
        $totalSpent = Booking::where('user_id', $userId)
            ->whereIn('status', ['confirmed', 'assigned', 'in_progress', 'completed'])
            ->sum('amount');

        // Get recent bookings
        $recentBookings = Booking::where('user_id', $userId)
            ->with(['vehicle'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('client.dashboard', compact(
            'totalBookings',
            'activeBookings',
            'totalSpent',
            'recentBookings'
        ));
    }
}
