<?php

namespace Tests\Feature;

use App\Models\Booking;
use Tests\TestCase;

class BookingAmountTest extends TestCase
{
    /**
     * Test that the amount field is cast correctly.
     *
     * @return void
     */
    public function test_amount_field_is_cast_correctly()
    {
        // Create a new booking instance
        $booking = new Booking();

        // Test with a decimal amount
        $booking->amount = 123.45;
        $this->assertEquals(123.45, $booking->amount);
        $this->assertIsNumeric($booking->amount);

        // Test with a whole number
        $booking->amount = 100;
        $this->assertEquals(100.00, $booking->amount);

        // Test with a very small decimal
        $booking->amount = 0.01;
        $this->assertEquals(0.01, $booking->amount);

        // Test with a very large number
        $booking->amount = 9999999.99;
        $this->assertEquals(9999999.99, $booking->amount);
    }

    /**
     * Test that the formatted amount attribute works correctly.
     *
     * @return void
     */
    public function test_formatted_amount_attribute()
    {
        // Mock the SettingsHelper to return a fixed currency symbol
        $this->mock('App\Helpers\SettingsHelper', function ($mock) {
            $mock->shouldReceive('formatPrice')
                ->andReturnUsing(function ($price) {
                    return '$' . number_format($price, 2);
                });
        });

        // Create a new booking instance
        $booking = new Booking();

        // Test with a decimal amount
        $booking->amount = 123.45;
        $this->assertEquals('$123.45', $booking->formatted_amount);

        // Test with a whole number
        $booking->amount = 100;
        $this->assertEquals('$100.00', $booking->formatted_amount);

        // Test with a very small decimal
        $booking->amount = 0.01;
        $this->assertEquals('$0.01', $booking->formatted_amount);

        // Test with a very large number
        $booking->amount = 9999999.99;
        $this->assertEquals('$9,999,999.99', $booking->formatted_amount);
    }
}
