<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
        ]);
    }

    /**
     * Indicate that the user is a driver.
     */
    public function driver(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'driver',
            'phone' => $this->faker->phoneNumber,
            'license_number' => $this->faker->regexify('[A-Z]{2}[0-9]{6}'),
            'vehicle_make' => $this->faker->randomElement(['Toyota', 'Honda', 'Ford', 'BMW', 'Mercedes']),
            'vehicle_model' => $this->faker->randomElement(['Camry', 'Accord', 'Focus', 'X3', 'C-Class']),
            'vehicle_color' => $this->faker->colorName,
            'vehicle_reg_number' => $this->faker->regexify('[A-Z]{3}[0-9]{3}'),
            'insurance_expiry' => $this->faker->dateTimeBetween('now', '+2 years'),
            'mot_expiry' => $this->faker->dateTimeBetween('now', '+1 year'),
            'is_active' => true,
            'is_available' => $this->faker->boolean(80),
        ]);
    }

    /**
     * Indicate that the user is a client.
     */
    public function client(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'client',
            'phone' => $this->faker->phoneNumber,
        ]);
    }
}
