@extends('layouts.admin')

@section('title', 'Manage Bookings')

@section('styles')
<style>
    .booking-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-assigned {
        background-color: #e2e3e5;
        color: #41464b;
    }

    .status-in_progress {
        background-color: #d3d3fc;
        color: #3a3a91;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .booking-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .booking-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .booking-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #4e73df;
    }

    .booking-card .card-body {
        padding: 25px;
    }

    .filter-form {
        background-color: #f8f9fc;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.03);
    }

    .filter-form label {
        font-weight: 600;
        color: #4e73df;
        font-size: 0.85rem;
        margin-bottom: 8px;
    }

    .filter-form .form-control,
    .filter-form .form-select {
        border-radius: 10px;
        border: 1px solid #e3e6f0;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .filter-form .form-control:focus,
    .filter-form .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-form .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
        border-radius: 10px;
        padding: 10px 15px;
    }

    .filter-form .btn-primary:hover {
        background-color: #2e59d9;
        border-color: #2653d4;
    }

    .stats-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 100%;
        text-align: center;
        padding: 25px;
        background-color: #fff;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background-color: #4e73df;
    }

    .stats-card.pending::before {
        background-color: #ffc107;
    }

    .stats-card.confirmed::before {
        background-color: #0dcaf0;
    }

    .stats-card.completed::before {
        background-color: #198754;
    }

    .stats-card.cancelled::before {
        background-color: #dc3545;
    }

    .stats-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        display: inline-block;
        width: 70px;
        height: 70px;
        line-height: 70px;
        border-radius: 50%;
        background-color: #f8f9fc;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: #5a5c69;
    }

    .stats-label {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 500;
    }

    .pending-icon {
        color: #ffc107;
    }

    .confirmed-icon {
        color: #0dcaf0;
    }

    .completed-icon {
        color: #198754;
    }

    .cancelled-icon {
        color: #dc3545;
    }

    .total-icon {
        color: #4e73df;
    }

    .bookings-table {
        margin-bottom: 0;
    }

    .bookings-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .bookings-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .bookings-table tr {
        transition: all 0.3s ease;
    }

    .bookings-table tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state i {
        font-size: 4rem;
        color: #d1d3e2;
        margin-bottom: 20px;
    }

    .empty-state h4 {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #858796;
        max-width: 500px;
        margin: 0 auto;
    }

    .pagination {
        margin-top: 30px;
    }

    .page-item.active .page-link {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .page-link {
        color: #4e73df;
    }

    .page-link:hover {
        color: #2e59d9;
    }
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Manage Bookings</h2>
    <div>
        <a href="{{ route('admin.bookings.create') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i> Create New Booking
        </a>
        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Booking Statistics -->
<div class="row mb-4">
    <div class="col">
        <div class="stats-card">
            <div class="stats-icon total-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-number">{{ $bookingStats['total'] }}</div>
            <div class="stats-label">Total Bookings</div>
        </div>
    </div>
    <div class="col">
        <div class="stats-card pending">
            <div class="stats-icon pending-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ $bookingStats['pending'] }}</div>
            <div class="stats-label">Pending</div>
        </div>
    </div>
    <div class="col">
        <div class="stats-card confirmed">
            <div class="stats-icon confirmed-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">{{ $bookingStats['confirmed'] }}</div>
            <div class="stats-label">Confirmed</div>
        </div>
    </div>
    <div class="col">
        <div class="stats-card completed">
            <div class="stats-icon completed-icon">
                <i class="fas fa-flag-checkered"></i>
            </div>
            <div class="stats-number">{{ $bookingStats['completed'] }}</div>
            <div class="stats-label">Completed</div>
        </div>
    </div>
    <div class="col">
        <div class="stats-card cancelled">
            <div class="stats-icon cancelled-icon">
                <i class="fas fa-ban"></i>
            </div>
            <div class="stats-number">{{ $bookingStats['cancelled'] }}</div>
            <div class="stats-label">Cancelled</div>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="filter-form">
    <form action="{{ route('admin.bookings.index') }}" method="GET">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="confirmed" {{ $status == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                    <option value="completed" {{ $status == 'completed' ? 'selected' : '' }}>Completed</option>
                    <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="date_from" class="form-label">Date From</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
            </div>
            <div class="col-md-3 mb-3">
                <label for="date_to" class="form-label">Date To</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
            </div>
            <div class="col-md-3 mb-3">
                <label for="search" class="form-label">Search</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="Booking #, Address, Client..." value="{{ $search }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Bookings Table -->
<div class="booking-card card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i> Booking List</h5>
    </div>
    <div class="card-body">
        @if ($bookings->isEmpty())
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h4>No Bookings Found</h4>
                <p>Try adjusting your search or filter criteria to find the bookings you're looking for.</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table bookings-table">
                    <thead>
                        <tr>
                            <th>Booking #</th>
                            <th>Client</th>
                            <th>Vehicle</th>
                            <th>Pickup Date</th>
                            <th>From</th>
                            <th>To</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($bookings as $booking)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $booking->booking_number }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($booking->user->profile_photo)
                                            <img src="{{ asset('storage/' . $booking->user->profile_photo) }}" alt="{{ $booking->user->name }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                        @else
                                            <div class="bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 0.8rem;">
                                                {{ strtoupper(substr($booking->user->name, 0, 1)) }}
                                            </div>
                                        @endif
                                        <div>{{ $booking->user->name }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div><i class="fas fa-car me-1 text-primary"></i> {{ $booking->vehicle->name }}</div>
                                </td>
                                <td>
                                    <div><i class="fas fa-calendar-alt me-1 text-primary"></i> {{ $booking->pickup_date->format('M d, Y') }}</div>
                                    <div><i class="fas fa-clock me-1 text-primary"></i> {{ $booking->pickup_date->format('h:i A') }}</div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 150px;" title="{{ $booking->pickup_address }}">
                                        <i class="fas fa-map-marker-alt me-1 text-primary"></i> {{ $booking->pickup_address }}
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 150px;" title="{{ $booking->dropoff_address }}">
                                        <i class="fas fa-map-marker-alt me-1 text-danger"></i> {{ $booking->dropoff_address }}
                                    </div>
                                </td>
                                <td>
                                    <span class="booking-status status-{{ strtolower($booking->status) }}">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-sm btn-outline-primary" title="View Booking Details">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $bookings->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
