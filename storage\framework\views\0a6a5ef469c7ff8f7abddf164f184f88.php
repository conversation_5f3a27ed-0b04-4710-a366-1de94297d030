<?php $__env->startSection('title', 'Weekly Earnings Report'); ?>

<?php $__env->startSection('content'); ?>
<h2>Weekly Earnings Report</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>Here's your weekly earnings summary for <?php echo e($reportPeriod ?? 'this week'); ?>.</p>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: #155724; margin-top: 0;">💰 Total Earnings This Week</h3>
    <div style="font-size: 36px; font-weight: bold; color: #155724; margin: 10px 0;">
        <?php echo e($currencySymbol); ?><?php echo e(number_format($totalEarnings ?? 0, 2)); ?>

    </div>
    <p style="color: #155724; margin-bottom: 0;">
        Great work! You've completed <?php echo e($totalRides ?? 0); ?> rides this week.
    </p>
</div>

<div class="booking-details">
    <h3>Earnings Breakdown</h3>
    
    <div class="detail-row">
        <span class="detail-label">Total Rides Completed:</span>
        <span class="detail-value"><?php echo e($totalRides ?? 0); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Gross Earnings:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($grossEarnings ?? 0, 2)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Platform Fee (20%):</span>
        <span class="detail-value">-<?php echo e($currencySymbol); ?><?php echo e(number_format($platformFee ?? 0, 2)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Tips Received:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($tipsReceived ?? 0, 2)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Bonuses:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($bonuses ?? 0, 2)); ?></span>
    </div>
    
    <div class="detail-row" style="border-top: 2px solid #28a745; padding-top: 10px; margin-top: 10px;">
        <span class="detail-label"><strong>Net Earnings:</strong></span>
        <span class="detail-value"><strong><?php echo e($currencySymbol); ?><?php echo e(number_format($totalEarnings ?? 0, 2)); ?></strong></span>
    </div>
</div>

<h3>📊 Performance Metrics:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <div style="display: flex; justify-content: space-around; text-align: center;">
        <div>
            <strong style="font-size: 20px; color: #007bff;"><?php echo e(number_format($averageRating ?? 0, 1)); ?></strong><br>
            <span style="color: #6c757d;">Average Rating</span>
        </div>
        <div>
            <strong style="font-size: 20px; color: #28a745;"><?php echo e($totalHours ?? 0); ?>h</strong><br>
            <span style="color: #6c757d;">Hours Driven</span>
        </div>
        <div>
            <strong style="font-size: 20px; color: #ffc107;"><?php echo e($totalDistance ?? 0); ?>km</strong><br>
            <span style="color: #6c757d;">Distance Covered</span>
        </div>
    </div>
</div>

<h3>🏆 Achievements This Week:</h3>
<ul>
    <?php if(($totalRides ?? 0) >= 20): ?>
        <li>✅ <strong>High Performer:</strong> Completed 20+ rides</li>
    <?php endif; ?>
    <?php if(($averageRating ?? 0) >= 4.8): ?>
        <li>⭐ <strong>5-Star Service:</strong> Maintained excellent rating</li>
    <?php endif; ?>
    <?php if(($onTimePercentage ?? 0) >= 95): ?>
        <li>⏰ <strong>Punctuality Pro:</strong> 95%+ on-time arrivals</li>
    <?php endif; ?>
    <?php if(($totalEarnings ?? 0) >= 500): ?>
        <li>💰 <strong>Top Earner:</strong> Earned $500+ this week</li>
    <?php endif; ?>
    <?php if(empty($achievements ?? [])): ?>
        <li>Keep up the great work! More achievements coming your way.</li>
    <?php endif; ?>
</ul>

<h3>📈 Earning Opportunities:</h3>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h4 style="color: #856404; margin-top: 0;">💡 Tips to Increase Your Earnings:</h4>
    <ul style="color: #856404; margin-bottom: 0;">
        <li><strong>Peak Hours:</strong> Drive during rush hours (7-9 AM, 5-7 PM)</li>
        <li><strong>Weekend Bonus:</strong> Higher rates on Friday-Sunday</li>
        <li><strong>Customer Service:</strong> Maintain high ratings for bonus eligibility</li>
        <li><strong>Referral Program:</strong> Earn $50 for each new driver you refer</li>
        <li><strong>Consecutive Rides:</strong> Complete 5+ rides in a row for bonus</li>
    </ul>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('driver.dashboard')); ?>" class="btn">View Dashboard</a>
    <a href="<?php echo e(route('driver.earnings.index')); ?>" class="btn" style="background-color: #28a745; margin-left: 10px;">Detailed Earnings</a>
</div>

<h3>💳 Payment Information:</h3>
<div class="booking-details">
    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value"><?php echo e($paymentMethod ?? 'Bank Transfer'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Next Payout Date:</span>
        <span class="detail-value"><?php echo e($nextPayoutDate ?? 'Every Friday'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payout Status:</span>
        <span class="detail-value"><?php echo e($payoutStatus ?? 'Scheduled'); ?></span>
    </div>
</div>

<h3>📱 Driver App Features:</h3>
<p>Make the most of your driving experience with our app features:</p>
<ul>
    <li><strong>Real-time Earnings:</strong> Track your earnings in real-time</li>
    <li><strong>Navigation:</strong> Built-in GPS navigation for optimal routes</li>
    <li><strong>Customer Communication:</strong> Easy communication with passengers</li>
    <li><strong>Ride History:</strong> Complete history of all your rides</li>
    <li><strong>Support:</strong> 24/7 driver support through the app</li>
</ul>

<h3>🎯 Goals for Next Week:</h3>
<div style="background-color: #e3f2fd; border: 1px solid #bbdefb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <ul style="color: #1976d2; margin-bottom: 0;">
        <li>Complete <?php echo e(($totalRides ?? 0) + 5); ?> rides (<?php echo e(($totalRides ?? 0) > 0 ? '+5 from this week' : 'aim for 15+'); ?>)</li>
        <li>Maintain rating above 4.7</li>
        <li>Earn <?php echo e($currencySymbol); ?><?php echo e(number_format(($totalEarnings ?? 0) * 1.1, 0)); ?> (10% increase)</li>
        <li>Try driving during peak hours for higher earnings</li>
    </ul>
</div>

<h3>📞 Driver Support:</h3>
<p>Need help or have questions about your earnings?</p>
<ul>
    <li><strong>Driver Support:</strong> <?php echo e($companyPhone); ?></li>
    <li><strong>Email:</strong> <?php echo e($companyEmail); ?></li>
    <li><strong>Driver Portal:</strong> Access help articles and FAQs</li>
    <li><strong>Emergency Line:</strong> Available 24/7 for urgent issues</li>
</ul>

<p>Thank you for being a valued member of the <?php echo e($companyName); ?> driver team. Keep up the excellent work!</p>

<p>Drive safely and earn well,<br>
The <?php echo e($companyName); ?> Driver Relations Team</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 12px; color: #6c757d; text-align: center;">
    This earnings report is sent weekly to all active drivers. 
    You can <a href="<?php echo e(route('driver.email-preferences.index')); ?>" style="color: #6c757d;">update your email preferences</a> 
    to change the frequency or disable these reports.
</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\driver\earnings-report.blade.php ENDPATH**/ ?>