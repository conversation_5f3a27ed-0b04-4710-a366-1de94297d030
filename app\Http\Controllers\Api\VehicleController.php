<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use Illuminate\Http\Request;

class VehicleController extends Controller
{
    /**
     * Get all active vehicles
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $vehicles = Vehicle::where('is_active', true)
            ->orderBy('price_per_km', 'asc')
            ->get();
            
        return response()->json($vehicles);
    }
}
