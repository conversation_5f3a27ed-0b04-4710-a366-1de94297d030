<?php

namespace App\Jobs;

use App\Models\EmailLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The email log instance.
     */
    protected $emailLog;

    /**
     * The mailable instance.
     */
    protected $mailable;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailLog $emailLog, $mailable)
    {
        $this->emailLog = $emailLog;
        $this->mailable = $mailable;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Mail::to($this->emailLog->recipient_email)->send($this->mailable);
            
            // Mark as sent
            $this->emailLog->markAsSent();
            
            Log::info('Email sent successfully', [
                'email_log_id' => $this->emailLog->id,
                'recipient' => $this->emailLog->recipient_email
            ]);
            
        } catch (\Exception $e) {
            // Mark as failed
            $this->emailLog->markAsFailed($e->getMessage());
            
            Log::error('Failed to send email', [
                'email_log_id' => $this->emailLog->id,
                'recipient' => $this->emailLog->recipient_email,
                'error' => $e->getMessage()
            ]);
            
            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $this->emailLog->markAsFailed($exception->getMessage());
        
        Log::error('Email job failed permanently', [
            'email_log_id' => $this->emailLog->id,
            'recipient' => $this->emailLog->recipient_email,
            'error' => $exception->getMessage()
        ]);
    }
}
