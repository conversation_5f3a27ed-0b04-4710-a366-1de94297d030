<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Company Information
        Setting::updateOrCreate(
            ['key' => 'company_name'],
            [
                'value' => 'Ynr Cars',
                'group' => 'company',
                'type' => 'text',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'company_email'],
            [
                'value' => '<EMAIL>',
                'group' => 'company',
                'type' => 'email',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'company_phone'],
            [
                'value' => '+****************',
                'group' => 'company',
                'type' => 'text',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'company_address'],
            [
                'value' => '123 Main St, New York, NY 10001',
                'group' => 'company',
                'type' => 'textarea',
                'is_public' => true
            ]
        );

        // Pricing and Localization
        Setting::updateOrCreate(
            ['key' => 'currency_code'],
            [
                'value' => 'USD',
                'group' => 'pricing',
                'type' => 'select',
                'options' => json_encode([
                    'USD' => 'US Dollar ($)',
                    'EUR' => 'Euro (€)',
                    'GBP' => 'British Pound (£)',
                    'CAD' => 'Canadian Dollar (C$)',
                    'AUD' => 'Australian Dollar (A$)',
                    'JPY' => 'Japanese Yen (¥)',
                    'INR' => 'Indian Rupee (₹)',
                    'CNY' => 'Chinese Yuan (¥)',
                    'AED' => 'UAE Dirham (د.إ)',
                    'SAR' => 'Saudi Riyal (﷼)'
                ]),
                'label' => 'Currency',
                'description' => 'Select the currency to use for all transactions',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'currency_symbol'],
            [
                'value' => '$',
                'group' => 'pricing',
                'type' => 'text',
                'label' => 'Currency Symbol',
                'description' => 'Symbol to display before prices',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'country_code'],
            [
                'value' => 'US',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'US' => 'United States',
                    'GB' => 'United Kingdom',
                    'CA' => 'Canada',
                    'AU' => 'Australia',
                    'DE' => 'Germany',
                    'FR' => 'France',
                    'IT' => 'Italy',
                    'ES' => 'Spain',
                    'IN' => 'India',
                    'AE' => 'United Arab Emirates',
                    'SA' => 'Saudi Arabia'
                ]),
                'label' => 'Country',
                'description' => 'Select the primary country for operations',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'selected_country_name'],
            [
                'value' => 'United States',
                'group' => 'localization',
                'type' => 'text',
                'label' => 'Selected Country Name',
                'description' => 'Full name of the selected country',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'distance_unit'],
            [
                'value' => 'miles',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'miles' => 'Miles',
                    'kilometers' => 'Kilometers'
                ]),
                'label' => 'Distance Unit',
                'description' => 'Unit to use for displaying distances',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'timezone'],
            [
                'value' => 'UTC',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'UTC' => 'UTC',
                    'America/New_York' => 'Eastern Time (US & Canada)',
                    'America/Chicago' => 'Central Time (US & Canada)',
                    'America/Denver' => 'Mountain Time (US & Canada)',
                    'America/Los_Angeles' => 'Pacific Time (US & Canada)',
                    'Europe/London' => 'London',
                    'Europe/Paris' => 'Paris',
                    'Asia/Dubai' => 'Dubai',
                    'Asia/Kolkata' => 'India'
                ]),
                'label' => 'Default Timezone',
                'description' => 'Default timezone for date and time display',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'date_format'],
            [
                'value' => 'Y-m-d',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'Y-m-d' => 'YYYY-MM-DD (2023-12-31)',
                    'm/d/Y' => 'MM/DD/YYYY (12/31/2023)',
                    'd/m/Y' => 'DD/MM/YYYY (31/12/2023)',
                    'd.m.Y' => 'DD.MM.YYYY (31.12.2023)'
                ]),
                'label' => 'Date Format',
                'description' => 'Format for displaying dates throughout the application',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'tax_rate'],
            [
                'value' => '8.5',
                'group' => 'pricing',
                'type' => 'number',
                'label' => 'Tax Rate (%)',
                'description' => 'Percentage tax rate to apply to bookings',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'base_fare'],
            [
                'value' => '5.00',
                'group' => 'pricing',
                'type' => 'number',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'price_per_km'],
            [
                'value' => '2.50',
                'group' => 'pricing',
                'type' => 'number',
                'is_public' => true
            ]
        );

        // Google Maps API Settings
        Setting::updateOrCreate(
            ['key' => 'google_maps_api_key'],
            [
                'value' => '',
                'group' => 'api',
                'type' => 'text',
                'label' => 'Google Maps API Key',
                'description' => 'Required for address autocomplete, distance calculation, and map display',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_default_zoom'],
            [
                'value' => '10',
                'group' => 'api',
                'type' => 'number',
                'label' => 'Default Map Zoom Level',
                'description' => 'Default zoom level for maps (1-20)',
                'options' => json_encode(['min' => 1, 'max' => 20, 'step' => 1]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_libraries'],
            [
                'value' => 'places,geometry,drawing',
                'group' => 'api',
                'type' => 'text',
                'label' => 'Google Maps Libraries',
                'description' => 'Comma-separated list of Google Maps libraries to load',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_default_lat'],
            [
                'value' => '51.5074',
                'group' => 'api',
                'type' => 'text',
                'label' => 'Default Map Center (Latitude)',
                'description' => 'Default latitude for map center',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_default_lng'],
            [
                'value' => '-0.1278',
                'group' => 'api',
                'type' => 'text',
                'label' => 'Default Map Center (Longitude)',
                'description' => 'Default longitude for map center',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_use_distance_matrix'],
            [
                'value' => 'true',
                'group' => 'api',
                'type' => 'boolean',
                'label' => 'Use Distance Matrix API',
                'description' => 'Enable to use Google\'s Distance Matrix API for more accurate distance and duration calculations',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_use_directions'],
            [
                'value' => 'true',
                'group' => 'api',
                'type' => 'boolean',
                'label' => 'Use Directions API',
                'description' => 'Enable to use Google\'s Directions API for route visualization and navigation',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_restrict_country'],
            [
                'value' => 'false',
                'group' => 'api',
                'type' => 'boolean',
                'label' => 'Restrict to Country',
                'description' => 'Enable to restrict Google Maps results to the selected country',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'google_maps_country_code'],
            [
                'value' => '',
                'group' => 'api',
                'type' => 'select',
                'options' => json_encode([
                    'US' => 'United States',
                    'GB' => 'United Kingdom',
                    'CA' => 'Canada',
                    'AU' => 'Australia',
                    'DE' => 'Germany',
                    'FR' => 'France',
                    'IT' => 'Italy',
                    'ES' => 'Spain',
                    'IN' => 'India',
                    'AE' => 'United Arab Emirates',
                    'SA' => 'Saudi Arabia'
                ]),
                'label' => 'Country for Google Maps',
                'description' => 'Select the country to restrict Google Maps results to',
                'is_public' => true
            ]
        );

        // Payment Gateways
        Setting::updateOrCreate(
            ['key' => 'paypal_client_id'],
            [
                'value' => env('PAYPAL_SANDBOX_CLIENT_ID', ''),
                'group' => 'payment',
                'type' => 'text',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'paypal_secret'],
            [
                'value' => env('PAYPAL_SANDBOX_CLIENT_SECRET', ''),
                'group' => 'payment',
                'type' => 'text',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'paypal_mode'],
            [
                'value' => env('PAYPAL_MODE', 'sandbox'),
                'group' => 'payment',
                'type' => 'select',
                'options' => json_encode(['sandbox' => 'Sandbox', 'live' => 'Live']),
                'is_public' => false
            ]
        );

        // Social Media
        Setting::updateOrCreate(
            ['key' => 'facebook_url'],
            [
                'value' => 'https://facebook.com/ynrcars',
                'group' => 'social',
                'type' => 'url',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'twitter_url'],
            [
                'value' => 'https://twitter.com/ynrcars',
                'group' => 'social',
                'type' => 'url',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'instagram_url'],
            [
                'value' => 'https://instagram.com/ynrcars',
                'group' => 'social',
                'type' => 'url',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'linkedin_url'],
            [
                'value' => 'https://linkedin.com/company/ynrcars',
                'group' => 'social',
                'type' => 'url',
                'is_public' => true
            ]
        );

        // Content
        Setting::updateOrCreate(
            ['key' => 'about_us'],
            [
                'value' => 'Ynr Cars is a Transportation service providing luxury rides for all your needs. We pride ourselves on reliability, comfort, and exceptional service.',
                'group' => 'content',
                'type' => 'textarea',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'privacy_policy'],
            [
                'value' => 'This is the privacy policy for Ynr Cars. We respect your privacy and are committed to protecting your personal data.',
                'group' => 'content',
                'type' => 'textarea',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'terms_and_conditions'],
            [
                'value' => 'These are the terms and conditions for using Ynr Cars services. By using our services, you agree to these terms.',
                'group' => 'content',
                'type' => 'textarea',
                'is_public' => true
            ]
        );

        // Booking Settings
        Setting::updateOrCreate(
            ['key' => 'advance_booking_time'],
            [
                'value' => '30',
                'group' => 'booking',
                'type' => 'number',
                'options' => json_encode(['min' => 1, 'max' => 90, 'step' => 1]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'minimum_hourly_duration'],
            [
                'value' => '2',
                'group' => 'booking',
                'type' => 'number',
                'options' => json_encode(['min' => 1, 'max' => 12, 'step' => 1]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'cancellation_time_window'],
            [
                'value' => '24',
                'group' => 'booking',
                'type' => 'number',
                'options' => json_encode(['min' => 1, 'max' => 72, 'step' => 1]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'cancellation_fee_percentage'],
            [
                'value' => '20',
                'group' => 'booking',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 100, 'step' => 5]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'allow_guest_bookings'],
            [
                'value' => 'true',
                'group' => 'booking',
                'type' => 'boolean',
                'is_public' => true
            ]
        );

        // Extra Services Settings
        Setting::updateOrCreate(
            ['key' => 'meet_and_greet_enabled'],
            [
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Meet & Greet Service',
                'description' => 'Enable/disable meet and greet service option',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'meet_and_greet_fee'],
            [
                'value' => '10.00',
                'group' => 'extra_services',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 100, 'step' => 0.01]),
                'label' => 'Meet & Greet Fee',
                'description' => 'Additional fee for meet and greet service',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'child_seat_enabled'],
            [
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Child Seat Service',
                'description' => 'Enable/disable child seat service option',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'child_seat_fee'],
            [
                'value' => '15.00',
                'group' => 'extra_services',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 100, 'step' => 0.01]),
                'label' => 'Child Seat Fee',
                'description' => 'Additional fee for child seat service',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'wheelchair_enabled'],
            [
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Wheelchair Accessible',
                'description' => 'Enable/disable wheelchair accessible vehicle option',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'wheelchair_fee'],
            [
                'value' => '0.00',
                'group' => 'extra_services',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 100, 'step' => 0.01]),
                'label' => 'Wheelchair Accessible Fee',
                'description' => 'Additional fee for wheelchair accessible vehicle (usually free)',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'extra_luggage_enabled'],
            [
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Extra Luggage Space',
                'description' => 'Enable/disable extra luggage space option',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'extra_luggage_fee'],
            [
                'value' => '5.00',
                'group' => 'extra_services',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 100, 'step' => 0.01]),
                'label' => 'Extra Luggage Fee',
                'description' => 'Additional fee for extra luggage space',
                'is_public' => true
            ]
        );

        // Driver Settings
        Setting::updateOrCreate(
            ['key' => 'driver_commission_percentage'],
            [
                'value' => '80',
                'group' => 'driver',
                'type' => 'number',
                'options' => json_encode(['min' => 50, 'max' => 95, 'step' => 5]),
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'minimum_payout_amount'],
            [
                'value' => '50',
                'group' => 'driver',
                'type' => 'number',
                'options' => json_encode(['min' => 10, 'max' => 200, 'step' => 10]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'payout_schedule'],
            [
                'value' => 'weekly',
                'group' => 'driver',
                'type' => 'select',
                'options' => json_encode(['weekly' => 'Weekly', 'biweekly' => 'Bi-weekly', 'monthly' => 'Monthly']),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'driver_verification_requirements'],
            [
                'value' => json_encode(['license' => true, 'insurance' => true, 'background_check' => true, 'vehicle_inspection' => true]),
                'group' => 'driver',
                'type' => 'json',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'maximum_active_hours'],
            [
                'value' => '12',
                'group' => 'driver',
                'type' => 'number',
                'options' => json_encode(['min' => 4, 'max' => 16, 'step' => 1]),
                'is_public' => true
            ]
        );

        // Email Settings
        Setting::updateOrCreate(
            ['key' => 'mail_driver'],
            [
                'value' => env('MAIL_MAILER', 'smtp'),
                'group' => 'email',
                'type' => 'select',
                'options' => json_encode(['smtp' => 'SMTP', 'sendmail' => 'Sendmail', 'mailgun' => 'Mailgun', 'ses' => 'Amazon SES']),
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_host'],
            [
                'value' => env('MAIL_HOST', 'smtp.mailgun.org'),
                'group' => 'email',
                'type' => 'text',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_port'],
            [
                'value' => env('MAIL_PORT', '587'),
                'group' => 'email',
                'type' => 'number',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_username'],
            [
                'value' => env('MAIL_USERNAME', ''),
                'group' => 'email',
                'type' => 'text',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_password'],
            [
                'value' => env('MAIL_PASSWORD', ''),
                'group' => 'email',
                'type' => 'password',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_encryption'],
            [
                'value' => env('MAIL_ENCRYPTION', 'tls'),
                'group' => 'email',
                'type' => 'select',
                'options' => json_encode(['tls' => 'TLS', 'ssl' => 'SSL', 'none' => 'None']),
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_from_address'],
            [
                'value' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'group' => 'email',
                'type' => 'email',
                'is_public' => false
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'mail_from_name'],
            [
                'value' => env('MAIL_FROM_NAME', 'Ynr Cars'),
                'group' => 'email',
                'type' => 'text',
                'is_public' => false
            ]
        );

        // App Appearance
        Setting::updateOrCreate(
            ['key' => 'primary_color'],
            [
                'value' => '#ee393d',
                'group' => 'appearance',
                'type' => 'color',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'secondary_color'],
            [
                'value' => '#343a40',
                'group' => 'appearance',
                'type' => 'color',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'logo'],
            [
                'value' => 'logo.png',
                'group' => 'appearance',
                'type' => 'file',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'favicon'],
            [
                'value' => 'favicon.ico',
                'group' => 'appearance',
                'type' => 'file',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'default_language'],
            [
                'value' => 'en',
                'group' => 'appearance',
                'type' => 'select',
                'options' => json_encode(['en' => 'English', 'es' => 'Spanish', 'fr' => 'French', 'de' => 'German', 'it' => 'Italian']),
                'is_public' => true
            ]
        );

        // Autocomplete Settings
        Setting::updateOrCreate(
            ['key' => 'autocomplete_enabled'],
            [
                'value' => 'true',
                'group' => 'autocomplete',
                'type' => 'boolean',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_restrict_country'],
            [
                'value' => 'false',
                'group' => 'autocomplete',
                'type' => 'boolean',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_country'],
            [
                'value' => '',
                'group' => 'autocomplete',
                'type' => 'text',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_types'],
            [
                'value' => 'geocode',
                'group' => 'autocomplete',
                'type' => 'select',
                'options' => json_encode(['geocode' => 'Geocode (Recommended for Postal Codes)', 'address' => 'Address Only', 'establishment' => 'Establishments', '(cities)' => 'Cities', '(regions)' => 'Regions', '' => 'All Types']),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_bias_radius'],
            [
                'value' => '50',
                'group' => 'autocomplete',
                'type' => 'number',
                'options' => json_encode(['min' => 0, 'max' => 500, 'step' => 1]),
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_use_strict_bounds'],
            [
                'value' => 'false',
                'group' => 'autocomplete',
                'type' => 'boolean',
                'is_public' => true
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'autocomplete_fields'],
            [
                'value' => 'formatted_address',
                'group' => 'autocomplete',
                'type' => 'select',
                'options' => json_encode(['formatted_address' => 'Formatted Address Only', 'address_components' => 'Address Components', 'geometry' => 'Geometry (with coordinates)']),
                'is_public' => true
            ]
        );
    }
}
