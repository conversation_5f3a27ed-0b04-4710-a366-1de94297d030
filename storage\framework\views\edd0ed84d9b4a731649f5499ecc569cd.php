<?php $__env->startSection('title', 'Payment Details'); ?>

<?php $__env->startSection('payment-logs-styles'); ?>
<style>
    .payment-info {
        margin-bottom: 30px;
    }

    .payment-info-item {
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .payment-info-item:hover {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 5px;
        transform: translateX(5px);
    }

    .payment-info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .payment-info-value {
        font-weight: 500;
        font-size: 1.1rem;
    }

    .booking-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        transition: all 0.3s;
    }

    .booking-details:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-5px);
    }

    .booking-details h5 {
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #ee393d;
        padding-bottom: 10px;
        display: inline-block;
    }

    .booking-info-item {
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .booking-info-item:hover {
        background-color: rgba(255,255,255,0.7);
        border-radius: 5px;
        padding: 5px;
    }

    .booking-info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .booking-info-value {
        font-weight: 500;
    }

    .payment-actions {
        margin-top: 30px;
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .payment-actions h5 {
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #ee393d;
        padding-bottom: 10px;
        display: inline-block;
    }

    .payment-details-json {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .payment-details-json pre {
        margin: 0;
        white-space: pre-wrap;
    }

    .transaction-id {
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        padding: 5px 10px;
        border-radius: 5px;
        border: 1px solid #e9ecef;
        font-size: 0.9rem;
    }

    .payment-amount {
        font-weight: 700;
        color: #198754;
        font-size: 1.2rem;
    }

    .payment-refunded {
        color: #0dcaf0;
    }

    .payment-failed {
        color: #dc3545;
    }

    .payment-pending {
        color: #ffc107;
    }

    .payment-header {
        background-color: #343a40;
        color: white;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }

    .payment-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .payment-header .payment-id {
        background-color: rgba(255,255,255,0.2);
        padding: 3px 10px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-left: 10px;
    }

    .action-buttons {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }

    .action-buttons .btn {
        flex: 1;
        transition: all 0.3s;
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-title', 'Payment Details'); ?>

<?php $__env->startSection('payment-logs-actions'); ?>
<div class="btn-group">
    <?php if($payment->status == 'completed'): ?>
    <a href="<?php echo e(route('admin.payments.invoice', $payment->id)); ?>" class="btn btn-outline-primary">
        <i class="fas fa-file-invoice me-1"></i> View Invoice
    </a>
    <?php endif; ?>
    <a href="<?php echo e(route('admin.bookings.show', $payment->booking->id)); ?>" class="btn btn-outline-info">
        <i class="fas fa-car me-1"></i> View Booking
    </a>
    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-1"></i> Print
    </button>
    <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to List
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-content'); ?>
<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <!-- Payment Information -->
        <div class="card payment-log-card">
            <div class="payment-header d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0 d-flex align-items-center">
                        Payment Information
                        <span class="payment-id">#<?php echo e($payment->id); ?></span>
                    </h4>
                </div>
                <span class="status-badge status-<?php echo e($payment->status); ?>">
                    <?php echo e(ucfirst($payment->status)); ?>

                </span>
            </div>
            <div class="payment-log-body">
                <div class="payment-info">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Transaction ID</div>
                                <div class="payment-info-value">
                                    <span class="transaction-id"><?php echo e($payment->transaction_id); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Payment Method</div>
                                <div class="payment-info-value">
                                    <span class="method-badge method-<?php echo e($payment->payment_method); ?>">
                                        <?php if($payment->payment_method == 'paypal'): ?>
                                            <i class="fab fa-paypal me-1"></i>
                                        <?php elseif($payment->payment_method == 'credit_card'): ?>
                                            <i class="fas fa-credit-card me-1"></i>
                                        <?php elseif($payment->payment_method == 'cash'): ?>
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                        <?php elseif($payment->payment_method == 'bank_transfer'): ?>
                                            <i class="fas fa-university me-1"></i>
                                        <?php elseif($payment->payment_method == 'paypal_card'): ?>
                                            <i class="fab fa-paypal me-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-money-check me-1"></i>
                                        <?php endif; ?>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Amount</div>
                                <div class="payment-info-value">
                                    <span class="payment-amount <?php echo e($payment->status == 'refunded' ? 'payment-refunded' : ($payment->status == 'failed' ? 'payment-failed' : ($payment->status == 'pending' ? 'payment-pending' : ''))); ?>">
                                        <?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Date</div>
                                <div class="payment-info-value">
                                    <i class="far fa-calendar-alt me-1 text-muted"></i>
                                    <?php echo e($payment->created_at ? $payment->created_at->format('F d, Y H:i:s') : 'N/A'); ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if($payment->paid_at): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Paid At</div>
                                <div class="payment-info-value">
                                    <i class="far fa-clock me-1 text-muted"></i>
                                    <?php echo e($payment->paid_at ? $payment->paid_at->format('F d, Y H:i:s') : 'N/A'); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($payment->refunded_at): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Refunded At</div>
                                <div class="payment-info-value">
                                    <i class="fas fa-undo-alt me-1 text-muted"></i>
                                    <?php echo e($payment->refunded_at ? $payment->refunded_at->format('F d, Y H:i:s') : 'N/A'); ?>

                                </div>
                            </div>
                        </div>
                        <?php if($payment->refunded_amount): ?>
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Refunded Amount</div>
                                <div class="payment-info-value payment-refunded">
                                    <?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->refunded_amount)); ?>

                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Payment Actions -->
                <div class="payment-actions">
                    <h5>Payment Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <form action="<?php echo e(route('admin.payments.update-status', $payment->id)); ?>" method="POST" id="updateStatusForm">
                                <?php echo csrf_field(); ?>
                                <div class="mb-3">
                                    <label for="status" class="form-label">Update Payment Status</label>
                                    <div class="input-group">
                                        <select class="form-select" name="status" id="status">
                                            <option value="pending" <?php echo e($payment->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                            <option value="processing" <?php echo e($payment->status == 'processing' ? 'selected' : ''); ?>>Processing</option>
                                            <option value="completed" <?php echo e($payment->status == 'completed' ? 'selected' : ''); ?>>Completed</option>
                                            <option value="failed" <?php echo e($payment->status == 'failed' ? 'selected' : ''); ?>>Failed</option>
                                            <option value="refunded" <?php echo e($payment->status == 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                                            <option value="cancelled" <?php echo e($payment->status == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                                        </select>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-check me-1"></i> Update
                                        </button>
                                    </div>
                                    <div class="form-text">Changing the payment status will also update the booking payment status.</div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <?php if($payment->status === 'completed'): ?>
                                <form action="<?php echo e(route('admin.payments.refund', $payment->id)); ?>" method="POST" id="refundForm">
                                    <?php echo csrf_field(); ?>
                                    <div class="mb-3">
                                        <label for="refund_amount" class="form-label">Refund Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo e(\App\Helpers\SettingsHelper::getCurrencySymbol()); ?></span>
                                            <input type="number" class="form-control" id="refund_amount" name="refund_amount" value="<?php echo e($payment->amount); ?>" step="0.01" min="0" max="<?php echo e($payment->amount); ?>" required>
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-undo me-1"></i> Process Refund
                                            </button>
                                        </div>
                                        <div class="form-text">Enter the amount to refund. This can be partial or full.</div>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <a href="<?php echo e(route('admin.bookings.show', $payment->booking->id)); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-car me-1"></i> View Booking
                        </a>
                        <?php if($payment->status == 'completed'): ?>
                        <a href="<?php echo e(route('admin.payments.invoice', $payment->id)); ?>" class="btn btn-outline-info">
                            <i class="fas fa-file-invoice me-1"></i> View Invoice
                        </a>
                        <?php endif; ?>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Details
                        </button>
                    </div>
                </div>

                <!-- Payment Details JSON -->
                <?php if($payment->payment_details): ?>
                    <div class="mt-4">
                        <h5>Payment Details</h5>
                        <div class="payment-details-json">
                            <pre><?php echo e(json_encode(json_decode($payment->payment_details), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)); ?></pre>
                        </div>
                        <div class="form-text mt-2">
                            <i class="fas fa-info-circle me-1"></i> This is the raw payment data received from the payment processor.
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($payment->refund_notes): ?>
                    <div class="mt-4">
                        <h5>Refund Notes</h5>
                        <div class="payment-details-json">
                            <div class="p-3"><?php echo e($payment->refund_notes); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Booking Information -->
        <div class="card payment-log-card">
            <div class="payment-header">
                <h4 class="mb-0">Booking Information</h4>
            </div>
            <div class="payment-log-body">
                <div class="booking-info-item">
                    <div class="booking-info-label">Booking Number</div>
                    <div class="booking-info-value">
                        <a href="<?php echo e(route('admin.bookings.show', $payment->booking->id)); ?>" class="fw-bold">
                            <?php echo e($payment->booking->booking_number); ?>

                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Client</div>
                    <div class="booking-info-value">
                        <a href="<?php echo e(route('admin.users.show', $payment->booking->user->id)); ?>">
                            <i class="fas fa-user me-1 text-muted"></i>
                            <?php echo e($payment->booking->user->name); ?>

                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Email</div>
                    <div class="booking-info-value">
                        <a href="mailto:<?php echo e($payment->booking->user->email); ?>">
                            <i class="fas fa-envelope me-1 text-muted"></i>
                            <?php echo e($payment->booking->user->email); ?>

                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Vehicle</div>
                    <div class="booking-info-value">
                        <i class="fas fa-car me-1 text-muted"></i>
                        <?php echo e($payment->booking->vehicle->name); ?>

                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Pickup Date</div>
                    <div class="booking-info-value">
                        <i class="fas fa-calendar-alt me-1 text-muted"></i>
                        <?php echo e($payment->booking->pickup_date ? $payment->booking->pickup_date->format('F d, Y H:i') : 'N/A'); ?>

                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Pickup Location</div>
                    <div class="booking-info-value text-truncate" title="<?php echo e($payment->booking->pickup_address); ?>">
                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                        <?php echo e($payment->booking->pickup_address); ?>

                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Dropoff Location</div>
                    <div class="booking-info-value text-truncate" title="<?php echo e($payment->booking->dropoff_address); ?>">
                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                        <?php echo e($payment->booking->dropoff_address); ?>

                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Booking Status</div>
                    <div class="booking-info-value">
                        <span class="status-badge status-<?php echo e($payment->booking->status); ?>">
                            <?php echo e(ucfirst($payment->booking->status)); ?>

                        </span>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Payment Status</div>
                    <div class="booking-info-value">
                        <span class="status-badge status-<?php echo e($payment->booking->payment_status === 'paid' ? 'completed' : ($payment->booking->payment_status === 'refunded' ? 'refunded' : 'pending')); ?>">
                            <?php echo e(ucfirst($payment->booking->payment_status)); ?>

                        </span>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Total Amount</div>
                    <div class="booking-info-value fw-bold">
                        <?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->booking->total_amount)); ?>

                    </div>
                </div>

                <div class="mt-4">
                    <a href="<?php echo e(route('admin.bookings.show', $payment->booking->id)); ?>" class="btn btn-outline-primary w-100">
                        <i class="fas fa-external-link-alt me-1"></i> View Full Booking Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle update status form submission
        const updateStatusForm = document.getElementById('updateStatusForm');
        if (updateStatusForm) {
            updateStatusForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const status = document.getElementById('status').value;
                let confirmMessage = 'You are about to update the payment status to ' + status + '.';
                let confirmIcon = 'info';

                if (status === 'refunded') {
                    confirmMessage = 'You are about to mark this payment as refunded. Consider using the refund form instead to process an actual refund.';
                    confirmIcon = 'warning';
                }

                Swal.fire({
                    title: 'Update Payment Status?',
                    text: confirmMessage,
                    icon: confirmIcon,
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, update status'
                }).then((result) => {
                    if (result.isConfirmed) {
                        updateStatusForm.removeEventListener('submit', arguments.callee);
                        updateStatusForm.submit();
                    }
                });
            });
        }

        // Handle refund form submission
        const refundForm = document.getElementById('refundForm');
        if (refundForm) {
            refundForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const refundAmount = parseFloat(document.getElementById('refund_amount').value);
                const maxAmount = parseFloat(document.getElementById('refund_amount').getAttribute('max'));

                if (isNaN(refundAmount) || refundAmount <= 0) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please enter a valid refund amount greater than zero',
                        icon: 'error'
                    });
                    return;
                }

                if (refundAmount > maxAmount) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Refund amount cannot exceed the original payment amount',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Process Refund?',
                    text: 'You are about to process a refund of ' + refundAmount.toFixed(2) + '. This action cannot be undone!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, process refund'
                }).then((result) => {
                    if (result.isConfirmed) {
                        refundForm.removeEventListener('submit', arguments.callee);
                        refundForm.submit();
                    }
                });
            });
        }

        // Format JSON for better readability
        const jsonPre = document.querySelector('.payment-details-json pre');
        if (jsonPre) {
            try {
                const jsonObj = JSON.parse(jsonPre.textContent);
                jsonPre.textContent = JSON.stringify(jsonObj, null, 2);
            } catch (e) {
                console.error('Error parsing JSON:', e);
            }
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.payment-logs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\payments\show.blade.php ENDPATH**/ ?>