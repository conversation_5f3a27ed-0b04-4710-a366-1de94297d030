<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentReceived extends Notification implements ShouldQueue
{
    use Queueable;

    protected $payment;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Payment  $payment
     * @return void
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $booking = $this->payment->booking;
        
        return (new MailMessage)
            ->subject('Payment Confirmation - Booking #' . $booking->booking_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We have successfully received your payment for the following booking:')
            ->line('Booking Number: ' . $booking->booking_number)
            ->line('Amount Paid: ' . \App\Helpers\SettingsHelper::formatPrice($this->payment->amount))
            ->line('Payment Method: ' . ucfirst($this->payment->payment_method))
            ->line('Transaction ID: ' . $this->payment->transaction_id)
            ->line('Pickup Date: ' . $booking->pickup_date->format('M d, Y h:i A'))
            ->line('Pickup Address: ' . $booking->pickup_address)
            ->when($booking->dropoff_address, function ($mail) use ($booking) {
                return $mail->line('Dropoff Address: ' . $booking->dropoff_address);
            })
            ->line('Vehicle: ' . $booking->vehicle->name)
            ->action('View Booking Details', url('/client/bookings/' . $booking->id))
            ->line('Your booking is now confirmed. We will assign a driver and notify you shortly.')
            ->line('Thank you for choosing our service!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $booking = $this->payment->booking;
        
        return [
            'payment_id' => $this->payment->id,
            'booking_id' => $booking->id,
            'booking_number' => $booking->booking_number,
            'amount' => $this->payment->amount,
            'payment_method' => $this->payment->payment_method,
            'transaction_id' => $this->payment->transaction_id,
            'message' => 'Payment of ' . \App\Helpers\SettingsHelper::formatPrice($this->payment->amount) . ' received for booking #' . $booking->booking_number,
            'type' => 'payment_received'
        ];
    }
}
