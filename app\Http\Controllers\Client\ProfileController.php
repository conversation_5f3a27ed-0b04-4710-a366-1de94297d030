<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:client']);
    }

    /**
     * Show the client profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();
        $addresses = $user->addresses()->orderBy('is_default', 'desc')->get();
        return view('client.profile.index', compact('user', 'addresses'));
    }

    /**
     * Show the form for editing the client profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit()
    {
        $user = Auth::user();
        return view('client.profile.edit', compact('user'));
    }

    /**
     * Update the client profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->address = $request->address;

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->profile_photo) {
                Storage::disk('public')->delete($user->profile_photo);
            }

            $image = $request->file('profile_photo');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('profile-photos', $imageName, 'public');
            $user->profile_photo = $path;
        }

        $user->save();

        return redirect()->route('client.profile.index')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Show the form for changing password.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showChangePasswordForm()
    {
        return view('client.profile.change-password');
    }

    /**
     * Change the client's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()
                ->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->route('client.profile.index')
            ->with('success', 'Password changed successfully.');
    }

    /**
     * Show the addresses page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function addresses()
    {
        $user = Auth::user();
        $addresses = $user->addresses()->orderBy('is_default', 'desc')->get();

        return view('client.profile.addresses', compact('addresses'));
    }

    /**
     * Store a new address.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeAddress(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:home,work,other',
            'address' => 'required|string|max:255',
        ]);

        $user = Auth::user();

        // If this is the first address or is_default is checked, make it default
        $isDefault = $request->has('is_default') || $user->addresses()->count() === 0;

        // If setting this address as default, unset any existing default
        if ($isDefault) {
            $user->addresses()->update(['is_default' => false]);
        }

        // Create the address
        $user->addresses()->create([
            'type' => $request->type,
            'address' => $request->address,
            'is_default' => $isDefault,
        ]);

        return redirect()->route('client.profile.addresses')
            ->with('success', 'Address added successfully.');
    }

    /**
     * Delete an address.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteAddress($id)
    {
        $user = Auth::user();
        $address = $user->addresses()->findOrFail($id);

        // Check if this was the default address
        $wasDefault = $address->is_default;

        // Delete the address
        $address->delete();

        // If the deleted address was the default, set another address as default if available
        if ($wasDefault) {
            $newDefault = $user->addresses()->first();
            if ($newDefault) {
                $newDefault->is_default = true;
                $newDefault->save();
            }
        }

        return redirect()->route('client.profile.addresses')
            ->with('success', 'Address deleted successfully.');
    }

    /**
     * Update avatar.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user = Auth::user();

        // Handle profile photo upload
        if ($request->hasFile('avatar')) {
            // Delete old photo if exists
            if ($user->profile_photo) {
                Storage::disk('public')->delete($user->profile_photo);
            }

            $image = $request->file('avatar');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('profile-photos', $imageName, 'public');
            $user->profile_photo = $path;
            $user->save();
        }

        return redirect()->route('client.profile.index')
            ->with('success', 'Profile photo updated successfully.');
    }
}
