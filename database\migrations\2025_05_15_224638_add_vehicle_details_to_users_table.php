<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add vehicle details fields
            if (!Schema::hasColumn('users', 'vehicle_make')) {
                $table->string('vehicle_make')->nullable()->after('vehicle_info');
            }
            if (!Schema::hasColumn('users', 'vehicle_model')) {
                $table->string('vehicle_model')->nullable()->after('vehicle_make');
            }
            if (!Schema::hasColumn('users', 'vehicle_color')) {
                $table->string('vehicle_color')->nullable()->after('vehicle_model');
            }
            if (!Schema::hasColumn('users', 'vehicle_reg_number')) {
                $table->string('vehicle_reg_number')->nullable()->after('vehicle_color');
            }

            // Insurance details
            if (!Schema::hasColumn('users', 'insurance_expiry')) {
                $table->date('insurance_expiry')->nullable()->after('vehicle_reg_number');
            }

            // MOT details
            if (!Schema::hasColumn('users', 'mot_expiry')) {
                $table->date('mot_expiry')->nullable()->after('insurance_expiry');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop columns if they exist
            $columns = [
                'vehicle_make',
                'vehicle_model',
                'vehicle_color',
                'vehicle_reg_number',
                'insurance_expiry',
                'mot_expiry'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
