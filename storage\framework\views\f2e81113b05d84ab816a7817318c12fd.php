

<?php $__env->startSection('title', 'Review Your Booking'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/booking.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="guest-review-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="guest-review-card">
                    <div class="card-header">
                        <h3 class="mb-0">Review Your Booking</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <h5 class="mb-4">Booking Summary</h5>

                                <div class="booking-summary">
                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Booking Type:</div>
                                        <div class="booking-summary-value">
                                            <?php if($bookingData['booking_type'] == 'one_way'): ?>
                                                One Way
                                            <?php elseif($bookingData['booking_type'] == 'return'): ?>
                                                Return
                                            <?php elseif($bookingData['booking_type'] == 'airport'): ?>
                                                Airport Transfer
                                            <?php else: ?>
                                                Hourly (<?php echo e($bookingData['duration_hours']); ?> hours)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup Address:</div>
                                        <div class="booking-summary-value"><?php echo e($bookingData['pickup_address']); ?></div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup Date & Time:</div>
                                        <div class="booking-summary-value"><?php echo e(\Carbon\Carbon::parse($bookingData['pickup_date'])->format('M d, Y h:i A')); ?></div>
                                    </div>

                                    <?php if($bookingData['booking_type'] != 'hourly'): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Dropoff Address:</div>
                                            <div class="booking-summary-value"><?php echo e($bookingData['dropoff_address']); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($bookingData['booking_type'] == 'return' && isset($bookingData['return_date'])): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Return Date & Time:</div>
                                            <div class="booking-summary-value"><?php echo e(\Carbon\Carbon::parse($bookingData['return_date'])->format('M d, Y h:i A')); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(isset($bookingData['notes']) && $bookingData['notes']): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Additional Notes:</div>
                                            <div class="booking-summary-value"><?php echo e($bookingData['notes']); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="booking-total">
                                        <div class="booking-total-label">Total Amount:</div>
                                        <div class="booking-total-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['amount'], 2)); ?></div>
                                    </div>
                                </div>

                                <?php if(isset($bookingData['fare_details']) && is_array($bookingData['fare_details'])): ?>
                                <div class="fare-details mt-4">
                                    <h6 class="mb-3">Fare Breakdown</h6>

                                    <div class="fare-row">
                                        <div class="fare-label">Base Fare:</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['base_fare'], 2)); ?></div>
                                    </div>

                                    <?php if(isset($bookingData['fare_details']['distance_km'])): ?>
                                    <div class="fare-row">
                                        <div class="fare-label">Distance (<?php echo e($bookingData['fare_details']['distance_km']); ?> ml):</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['distance_fare'], 2)); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if(isset($bookingData['fare_details']['booking_fee'])): ?>
                                    <div class="fare-row">
                                        <div class="fare-label">Booking Fee:</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['booking_fee'], 2)); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if(isset($bookingData['fare_details']['airport_surcharge']) && $bookingData['fare_details']['airport_surcharge'] > 0): ?>
                                    <div class="fare-row">
                                        <div class="fare-label">Airport Surcharge:</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['airport_surcharge'], 2)); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if(isset($bookingData['fare_details']['weekend_surcharge']) && $bookingData['fare_details']['weekend_surcharge'] > 0): ?>
                                    <div class="fare-row">
                                        <div class="fare-label">Weekend Surcharge:</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['weekend_surcharge'], 2)); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if(isset($bookingData['fare_details']['night_surcharge']) && $bookingData['fare_details']['night_surcharge'] > 0): ?>
                                    <div class="fare-row">
                                        <div class="fare-label">Night Surcharge:</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($bookingData['fare_details']['night_surcharge'], 2)); ?></div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div style="max-height: 600px;" class="col-md-5">
                                <h5 class="mb-4">Vehicle Details</h5>

                                <div class="vehicle-card">
                                    <div class="vehicle-image-container">
                                        <?php if($vehicle->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" class="vehicle-image" alt="<?php echo e($vehicle->name); ?>">
                                        <?php else: ?>
                                            <div class="vehicle-image bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-car fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="vehicle-price-tag">
                                            <?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($vehicle->price_per_km, 2)); ?>/ml
                                        </div>
                                    </div>

                                    <div class="vehicle-info">
                                        <h5 class="vehicle-name"><?php echo e($vehicle->name); ?></h5>
                                        <p class="vehicle-type"><?php echo e(ucfirst($vehicle->type)); ?></p>

                                        <div class="vehicle-features">
                                            <div class="vehicle-feature">
                                                <i class="fas fa-user"></i> <?php echo e($vehicle->seats); ?> seats
                                            </div>
                                            <div class="vehicle-feature">
                                                <i class="fas fa-suitcase"></i> <?php echo e($vehicle->luggage_capacity); ?> luggage
                                            </div>
                                        </div>

                                        <div class="vehicle-amenities">
                                            <?php if($vehicle->has_ac): ?>
                                                <span class="vehicle-amenity"><i class="fas fa-snowflake me-1"></i> AC</span>
                                            <?php endif; ?>
                                            <?php if($vehicle->has_wifi): ?>
                                                <span class="vehicle-amenity"><i class="fas fa-wifi me-1"></i> WiFi</span>
                                            <?php endif; ?>
                                            <?php if($vehicle->has_child_seat): ?>
                                                <span class="vehicle-amenity"><i class="fas fa-baby me-1"></i> Child Seat</span>
                                            <?php endif; ?>
                                            <?php if($vehicle->is_wheelchair_accessible): ?>
                                                <span class="vehicle-amenity"><i class="fas fa-wheelchair me-1"></i> Accessible</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <form action="<?php echo e(route('booking.guest.save')); ?>" method="POST" id="confirmBookingForm" class="mt-4">
                            <?php echo csrf_field(); ?>
                            <div class="booking-actions">
                                <a href="<?php echo e(route('booking.index')); ?>" class="btn btn-back">
                                    <i class="fas fa-arrow-left me-2"></i> Back
                                </a>
                                <button type="submit" id="confirmBtn" class="btn btn-next">
                                    <span class="button-text">Continue <i class="fas fa-arrow-right ms-2"></i></span>
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle confirmation form submission
        const confirmForm = document.getElementById('confirmBookingForm');
        const confirmBtn = document.getElementById('confirmBtn');

        if (confirmForm && confirmBtn) {
            confirmForm.addEventListener('submit', function(e) {
                // Show loading indicator
                const spinner = confirmBtn.querySelector('.spinner-border');
                const buttonText = confirmBtn.querySelector('.button-text');

                if (spinner && buttonText) {
                    spinner.classList.remove('d-none');
                    buttonText.innerHTML = 'Processing...';
                    confirmBtn.disabled = true;
                }

                // Form will submit normally
            });
        }

        // Add animation to vehicle card
        const vehicleCard = document.querySelector('.vehicle-card');
        if (vehicleCard) {
            setTimeout(() => {
                vehicleCard.classList.add('selected');
            }, 300);
        }

        // Add smooth scroll to booking summary
        const bookingSummary = document.querySelector('.booking-summary');
        if (bookingSummary) {
            bookingSummary.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\booking\guest-review.blade.php ENDPATH**/ ?>