<?php

namespace App\Listeners;

use App\Events\DriverAssigned;
use App\Services\EmailService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendDriverAssignmentEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DriverAssigned $event): void
    {
        try {
            // Send email to client
            EmailService::sendDriverAssignment($event->booking, 'client');
            
            // Send email to driver
            EmailService::sendDriverAssignment($event->booking, 'driver');
            
            Log::info('Driver assignment emails sent', [
                'booking_id' => $event->booking->id,
                'driver_id' => $event->booking->driver_id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send driver assignment emails', [
                'booking_id' => $event->booking->id,
                'driver_id' => $event->booking->driver_id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
