<?php $__env->startSection('title', 'Email Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Management</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Email Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title">
                                    <i class="fas fa-envelope"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Total Emails</h6>
                            <b><?php echo e(number_format($statistics['total'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title">
                                    <i class="fas fa-check-circle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Sent</h6>
                            <b><?php echo e(number_format($statistics['sent'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title">
                                    <i class="fas fa-clock"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Queued</h6>
                            <b><?php echo e(number_format($statistics['queued'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-danger">
                                <span class="avatar-title">
                                    <i class="fas fa-exclamation-circle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Failed</h6>
                            <b><?php echo e(number_format($statistics['failed'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">Email Logs</h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <a href="<?php echo e(route('admin.emails.bulk')); ?>" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Send Bulk Email
                                </a>
                                <a href="<?php echo e(route('admin.emails.settings')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-3">
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="sent" <?php echo e(request('status') === 'sent' ? 'selected' : ''); ?>>Sent</option>
                                <option value="queued" <?php echo e(request('status') === 'queued' ? 'selected' : ''); ?>>Queued</option>
                                <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="template" class="form-select">
                                <option value="">All Templates</option>
                                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($template); ?>" <?php echo e(request('template') === $template ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $template))); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>" placeholder="From Date">
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>" placeholder="To Date">
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                                <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Email Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Template</th>
                                    <th>Status</th>
                                    <th>Sent At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $emailLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($log->recipient_name ?: 'N/A'); ?></strong><br>
                                                <small class="text-muted"><?php echo e($log->recipient_email); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo e(Str::limit($log->subject, 50)); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $log->template))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo e($log->status_badge_class); ?>">
                                                <?php echo e($log->formatted_status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($log->sent_at): ?>
                                                <?php echo e($log->sent_at->format('M j, Y g:i A')); ?>

                                            <?php elseif($log->failed_at): ?>
                                                <span class="text-danger">Failed: <?php echo e($log->failed_at->format('M j, Y g:i A')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e($log->created_at->format('M j, Y g:i A')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('admin.emails.show', $log)); ?>" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($log->status === 'failed'): ?>
                                                    <form method="POST" action="<?php echo e(route('admin.emails.resend', $log)); ?>" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="btn btn-outline-warning" title="Resend">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <form method="POST" action="<?php echo e(route('admin.emails.destroy', $log)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-outline-danger"
                                                            onclick="return confirm('Are you sure?')" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-envelope fa-2x d-block mb-2"></i>
                                                No email logs found
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php echo e($emailLogs->withQueryString()->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/index.blade.php ENDPATH**/ ?>