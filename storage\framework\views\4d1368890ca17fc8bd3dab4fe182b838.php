<?php $__env->startSection('title', 'Email Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Management</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Email Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title">
                                    <i class="fas fa-envelope"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Total Emails</h6>
                            <b><?php echo e(number_format($statistics['total'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title">
                                    <i class="fas fa-check-circle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Sent</h6>
                            <b><?php echo e(number_format($statistics['sent'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title">
                                    <i class="fas fa-clock"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Queued</h6>
                            <b><?php echo e(number_format($statistics['queued'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-danger">
                                <span class="avatar-title">
                                    <i class="fas fa-exclamation-circle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Failed</h6>
                            <b><?php echo e(number_format($statistics['failed'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">Email Logs</h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <a href="<?php echo e(route('admin.emails.analytics')); ?>" class="btn btn-outline-info">
                                    <i class="fas fa-chart-bar"></i> Analytics
                                </a>
                                <a href="<?php echo e(route('admin.emails.templates')); ?>" class="btn btn-outline-success">
                                    <i class="fas fa-file-alt"></i> Templates
                                </a>
                                <a href="<?php echo e(route('admin.emails.bulk')); ?>" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Send Bulk Email
                                </a>
                                <a href="<?php echo e(route('admin.emails.settings')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-3">
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="sent" <?php echo e(request('status') === 'sent' ? 'selected' : ''); ?>>Sent</option>
                                <option value="queued" <?php echo e(request('status') === 'queued' ? 'selected' : ''); ?>>Queued</option>
                                <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="template" class="form-select">
                                <option value="">All Templates</option>
                                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($template); ?>" <?php echo e(request('template') === $template ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $template))); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="text" name="search" class="form-control" value="<?php echo e(request('search')); ?>" placeholder="Search emails...">
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>" placeholder="From Date">
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>" placeholder="To Date">
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                                <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-danger" id="bulkDeleteBtn" disabled>
                                    <i class="fas fa-trash"></i> Delete Selected
                                </button>
                                <button type="button" class="btn btn-outline-warning" id="bulkResendBtn" disabled>
                                    <i class="fas fa-redo"></i> Resend Failed
                                </button>
                                <button type="button" class="btn btn-outline-info" id="exportBtn">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <span id="selectedCount" class="text-muted">0 selected</span>
                        </div>
                    </div>

                    <!-- Email Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Template</th>
                                    <th>Status</th>
                                    <th>Sent At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $emailLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input email-checkbox" value="<?php echo e($log->id); ?>" data-status="<?php echo e($log->status); ?>">
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e($log->recipient_name ?: 'N/A'); ?></strong><br>
                                                <small class="text-muted"><?php echo e($log->recipient_email); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo e(Str::limit($log->subject, 50)); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $log->template))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo e($log->status_badge_class); ?>">
                                                <?php echo e($log->formatted_status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($log->sent_at): ?>
                                                <?php echo e($log->sent_at->format('M j, Y g:i A')); ?>

                                            <?php elseif($log->failed_at): ?>
                                                <span class="text-danger">Failed: <?php echo e($log->failed_at->format('M j, Y g:i A')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e($log->created_at->format('M j, Y g:i A')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('admin.emails.show', $log)); ?>" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($log->status === 'failed'): ?>
                                                    <form method="POST" action="<?php echo e(route('admin.emails.resend', $log)); ?>" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="btn btn-outline-warning" title="Resend">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <form method="POST" action="<?php echo e(route('admin.emails.destroy', $log)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-outline-danger"
                                                            onclick="return confirm('Are you sure?')" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-envelope fa-2x d-block mb-2"></i>
                                                No email logs found
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php echo e($emailLogs->withQueryString()->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const emailCheckboxes = document.querySelectorAll('.email-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkResendBtn = document.getElementById('bulkResendBtn');
    const exportBtn = document.getElementById('exportBtn');
    const selectedCount = document.getElementById('selectedCount');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        emailCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox change
    emailCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.email-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCount.textContent = `${count} selected`;

        // Enable/disable bulk action buttons
        bulkDeleteBtn.disabled = count === 0;

        // Only enable resend for failed emails
        const failedSelected = Array.from(checkedBoxes).some(cb => cb.dataset.status === 'failed');
        bulkResendBtn.disabled = count === 0 || !failedSelected;

        // Update select all checkbox state
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === emailCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // Bulk delete functionality
    bulkDeleteBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.email-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);

        if (ids.length === 0) return;

        if (confirm(`Are you sure you want to delete ${ids.length} email log(s)?`)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo e(route("admin.emails.bulk-delete")); ?>';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            form.appendChild(methodField);

            ids.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'ids[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // Bulk resend functionality
    bulkResendBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.email-checkbox:checked');
        const failedIds = Array.from(checkedBoxes)
            .filter(cb => cb.dataset.status === 'failed')
            .map(cb => cb.value);

        if (failedIds.length === 0) return;

        if (confirm(`Are you sure you want to resend ${failedIds.length} failed email(s)?`)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo e(route("admin.emails.bulk-resend")); ?>';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            failedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'ids[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // Export functionality
    exportBtn.addEventListener('click', function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        window.location.href = '<?php echo e(route("admin.emails.index")); ?>?' + params.toString();
    });

    // Initialize
    updateBulkActions();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/index.blade.php ENDPATH**/ ?>