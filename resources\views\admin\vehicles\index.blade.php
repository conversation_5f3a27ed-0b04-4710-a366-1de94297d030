@extends('layouts.admin')

@section('title', 'Manage Vehicles')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }

    .vehicle-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s;
        height: 100%;
    }

    .vehicle-card:hover {
        transform: translateY(-5px);
    }

    .vehicle-img {
        height: 200px;
        object-fit: cover;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

    .vehicle-details {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
    }

    .vehicle-detail {
        text-align: center;
        font-size: 0.8rem;
    }

    .vehicle-detail i {
        color: #ee393d;
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Manage Vehicles</h2>
                <a href="{{ route('admin.vehicles.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add New Vehicle
                </a>
            </div>

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="row">
                @forelse ($vehicles as $vehicle)
                    <div class="col-md-4 mb-4">
                        <div class="card vehicle-card">
                            @if ($vehicle->is_active)
                                <span class="status-badge status-active">Active</span>
                            @else
                                <span class="status-badge status-inactive">Inactive</span>
                            @endif

                            @if ($vehicle->image)
                                <img src="{{ asset('storage/' . $vehicle->image) }}" class="vehicle-img" alt="{{ $vehicle->name }}">
                            @else
                                <img src="https://via.placeholder.com/300x200?text=No+Image" class="vehicle-img" alt="{{ $vehicle->name }}">
                            @endif

                            <div class="card-body">
                                <h5 class="card-title">{{ $vehicle->name }}</h5>
                                <p class="text-muted mb-2">{{ $vehicle->type }} - {{ $vehicle->model }}</p>

                                <div class="vehicle-details mb-3">
                                    <div class="vehicle-detail">
                                        <i class="fas fa-user"></i>
                                        <p>{{ $vehicle->seats }} Seats</p>
                                    </div>
                                    <div class="vehicle-detail">
                                        <i class="fas fa-suitcase"></i>
                                        <p>{{ $vehicle->luggage_capacity }} Luggage</p>
                                    </div>
                                    <div class="vehicle-detail">
                                        <i class="fas fa-cog"></i>
                                        <p>{{ $vehicle->transmission }}</p>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between mb-2">
                                    <span>Per {{ \App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Mile' : 'KM' }}:</span>
                                    <span>@currency(){{ number_format($vehicle->price_per_km, 2) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Per Hour:</span>
                                    <span>@currency(){{ number_format($vehicle->price_per_hour, 2) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Base Fare:</span>
                                    <span>@currency(){{ number_format($vehicle->base_fare ?? 5.00, 2) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span>Tax Rate:</span>
                                    <span>{{ number_format($vehicle->tax_rate ?? 0.00, 2) }}%</span>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.vehicles.edit', $vehicle->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                    <a href="{{ route('admin.vehicles.show', $vehicle->id) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                    <form action="{{ route('admin.vehicles.destroy', $vehicle->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this vehicle?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash me-1"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="alert alert-info">
                            No vehicles found. <a href="{{ route('admin.vehicles.create') }}">Add a new vehicle</a>.
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection
