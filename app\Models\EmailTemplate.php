<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subject',
        'content',
        'description',
        'category',
        'is_active',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who last updated this template
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for templates by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get template categories
     */
    public static function getCategories()
    {
        return [
            'booking' => 'Booking Templates',
            'user' => 'User Templates',
            'system' => 'System Templates',
            'payment' => 'Payment Templates',
        ];
    }

    /**
     * Get default templates
     */
    public static function getDefaultTemplates()
    {
        return [
            'booking_confirmation' => [
                'name' => 'booking_confirmation',
                'subject' => 'Booking Confirmation - {{ $booking->id }}',
                'content' => self::getDefaultBookingConfirmationContent(),
                'description' => 'Sent when a booking is confirmed',
                'category' => 'booking',
            ],
            'booking_cancelled' => [
                'name' => 'booking_cancelled',
                'subject' => 'Booking Cancelled - {{ $booking->id }}',
                'content' => self::getDefaultBookingCancelledContent(),
                'description' => 'Sent when a booking is cancelled',
                'category' => 'booking',
            ],
            'booking_reminder' => [
                'name' => 'booking_reminder',
                'subject' => 'Booking Reminder - {{ $booking->id }}',
                'content' => self::getDefaultBookingReminderContent(),
                'description' => 'Sent as a reminder before pickup',
                'category' => 'booking',
            ],
            'driver_assigned' => [
                'name' => 'driver_assigned',
                'subject' => 'Driver Assigned - {{ $booking->id }}',
                'content' => self::getDefaultDriverAssignedContent(),
                'description' => 'Sent when a driver is assigned',
                'category' => 'booking',
            ],
            'payment_confirmation' => [
                'name' => 'payment_confirmation',
                'subject' => 'Payment Confirmation - {{ $payment->transaction_id }}',
                'content' => self::getDefaultPaymentConfirmationContent(),
                'description' => 'Sent when payment is processed',
                'category' => 'payment',
            ],
            'user_welcome' => [
                'name' => 'user_welcome',
                'subject' => 'Welcome to {{ config("app.name") }}',
                'content' => self::getDefaultUserWelcomeContent(),
                'description' => 'Sent to new users',
                'category' => 'user',
            ],
            'password_reset' => [
                'name' => 'password_reset',
                'subject' => 'Reset Your Password',
                'content' => self::getDefaultPasswordResetContent(),
                'description' => 'Sent for password reset requests',
                'category' => 'user',
            ],
            'email_verification' => [
                'name' => 'email_verification',
                'subject' => 'Verify Your Email Address',
                'content' => self::getDefaultEmailVerificationContent(),
                'description' => 'Sent for email verification',
                'category' => 'user',
            ],
        ];
    }

    private static function getDefaultBookingConfirmationContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>Your booking has been confirmed! Here are the details:</p>

<div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Booking Details</h3>
    <p><strong>Booking ID:</strong> {{ $booking->id }}</p>
    <p><strong>Pickup Location:</strong> {{ $booking->pickup_location }}</p>
    <p><strong>Drop-off Location:</strong> {{ $booking->dropoff_location }}</p>
    <p><strong>Pickup Date & Time:</strong> {{ $booking->pickup_datetime }}</p>
    <p><strong>Total Amount:</strong> {{ $booking->total_amount }}</p>
</div>

<p>We will send you driver details closer to your pickup time.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultBookingCancelledContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>Your booking {{ $booking->id }} has been cancelled.</p>

<p>If you have any questions, please contact our support team.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultBookingReminderContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>This is a reminder for your upcoming booking:</p>

<div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Booking Reminder</h3>
    <p><strong>Booking ID:</strong> {{ $booking->id }}</p>
    <p><strong>Pickup Time:</strong> {{ $booking->pickup_datetime }}</p>
    <p><strong>Pickup Location:</strong> {{ $booking->pickup_location }}</p>
</div>

<p>Please be ready at your pickup location.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultDriverAssignedContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>A driver has been assigned to your booking:</p>

<div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Driver Details</h3>
    <p><strong>Driver:</strong> {{ $driver->name }}</p>
    <p><strong>Phone:</strong> {{ $driver->phone }}</p>
    <p><strong>Vehicle:</strong> {{ $driver->vehicle }}</p>
</div>

<p>Your driver will contact you before pickup.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultPaymentConfirmationContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>Your payment has been processed successfully.</p>

<div style="background-color: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Payment Details</h3>
    <p><strong>Amount:</strong> {{ $payment->amount }}</p>
    <p><strong>Transaction ID:</strong> {{ $payment->transaction_id }}</p>
    <p><strong>Date:</strong> {{ $payment->date }}</p>
</div>

<p>Thank you for your payment!</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultUserWelcomeContent()
    {
        return '<h2>Welcome to {{ config("app.name") }}!</h2>

<p>Dear {{ $user->name }},</p>

<p>Welcome to {{ config("app.name") }}! We are excited to have you as part of our community.</p>

<p>You can now book rides, manage your account, and enjoy our premium transportation services.</p>

<p>If you have any questions, feel free to contact our support team.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultPasswordResetContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>You have requested to reset your password. Click the link below to reset it:</p>

<p style="text-align: center; margin: 30px 0;">
    <a href="{{ $reset_url }}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Reset Password</a>
</p>

<p>If you did not request this, please ignore this email.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }

    private static function getDefaultEmailVerificationContent()
    {
        return '<h2>{{ config("app.name") }}</h2>

<p>Dear {{ $user->name }},</p>

<p>Please verify your email address by clicking the link below:</p>

<p style="text-align: center; margin: 30px 0;">
    <a href="{{ $verification_url }}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Verify Email</a>
</p>

<p>If you did not create this account, please ignore this email.</p>

<p>Best regards,<br>
{{ config("app.name") }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config("app.name") }}. All rights reserved.</small></p>';
    }
}
