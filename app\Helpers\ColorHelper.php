<?php

namespace App\Helpers;

class ColorHelper
{
    /**
     * Adjust the brightness of a color
     *
     * @param string $hex Hex color code
     * @param int $steps Steps to adjust brightness (positive for lighter, negative for darker)
     * @return string Adjusted hex color
     */
    public static function adjustBrightness($hex, $steps)
    {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Parse the hex color
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Adjust the brightness
        $r = max(0, min(255, $r + $steps));
        $g = max(0, min(255, $g + $steps));
        $b = max(0, min(255, $b + $steps));
        
        // Convert back to hex
        return '#' . sprintf('%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Determine if a color is light or dark
     *
     * @param string $hex Hex color code
     * @return bool True if the color is light, false if it's dark
     */
    public static function isLight($hex)
    {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Parse the hex color
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Calculate the brightness
        $brightness = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
        
        // Return true if the color is light, false if it's dark
        return $brightness > 155;
    }
    
    /**
     * Get a contrasting color (black or white) for text on the given background color
     *
     * @param string $hex Hex color code
     * @return string '#000000' for black or '#ffffff' for white
     */
    public static function getContrastColor($hex)
    {
        return self::isLight($hex) ? '#000000' : '#ffffff';
    }
    
    /**
     * Convert a hex color to RGB
     *
     * @param string $hex Hex color code
     * @return array RGB values [r, g, b]
     */
    public static function hexToRgb($hex)
    {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Parse the hex color
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        return [$r, $g, $b];
    }
    
    /**
     * Convert a hex color to RGBA string
     *
     * @param string $hex Hex color code
     * @param float $alpha Alpha value (0-1)
     * @return string RGBA string
     */
    public static function hexToRgba($hex, $alpha = 1)
    {
        $rgb = self::hexToRgb($hex);
        return "rgba({$rgb[0]}, {$rgb[1]}, {$rgb[2]}, {$alpha})";
    }
}
