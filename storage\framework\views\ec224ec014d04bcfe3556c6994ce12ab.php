<?php $__env->startSection('title', 'Payment Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        Payment Details
        <span class="booking-status status-<?php echo e(strtolower($payment->status)); ?> ms-2">
            <?php echo e(ucfirst($payment->status)); ?>

        </span>
    </h2>
    <div>
        <a href="<?php echo e(route('client.payments.invoice', $payment->id)); ?>" class="btn btn-primary">
            <i class="fas fa-file-invoice me-1"></i> View Invoice
        </a>
        <a href="<?php echo e(route('client.payments.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Payments
        </a>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Payment Information -->
    <div class="col-md-7">
        <div class="card dashboard-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Payment Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Transaction ID</p>
                        <p class="fw-bold"><?php echo e($payment->transaction_id); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Amount</p>
                        <p class="fw-bold"><?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?></p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Payment Method</p>
                        <p class="fw-bold">
                            <?php if($payment->payment_method == 'paypal'): ?>
                                <i class="fab fa-paypal me-1"></i> PayPal
                            <?php elseif($payment->payment_method == 'credit_card'): ?>
                                <i class="far fa-credit-card me-1"></i> Credit Card
                            <?php elseif($payment->payment_method == 'cash'): ?>
                                <i class="fas fa-money-bill-wave me-1"></i> Cash
                            <?php else: ?>
                                <i class="fas fa-money-check me-1"></i> <?php echo e(ucfirst($payment->payment_method)); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Payment Date</p>
                        <p class="fw-bold"><?php echo e($payment->created_at->format('F d, Y h:i A')); ?></p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Status</p>
                        <p class="fw-bold">
                            <span class="booking-status status-<?php echo e(strtolower($payment->status)); ?>">
                                <?php echo e(ucfirst($payment->status)); ?>

                            </span>
                        </p>
                    </div>
                    <?php if($payment->paid_at): ?>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Paid At</p>
                        <p class="fw-bold"><?php echo e($payment->paid_at->format('F d, Y h:i A')); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Booking Information -->
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">Booking Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Booking Number</p>
                        <p class="fw-bold"><?php echo e($payment->booking->booking_number); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Vehicle</p>
                        <p class="fw-bold"><?php echo e($payment->booking->vehicle->name); ?></p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Pickup Date</p>
                        <p class="fw-bold"><?php echo e($payment->booking->pickup_date->format('F d, Y h:i A')); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Booking Status</p>
                        <p class="fw-bold">
                            <span class="badge bg-<?php echo e($payment->booking->status == 'completed' ? 'success' : ($payment->booking->status == 'cancelled' ? 'danger' : 'info')); ?>">
                                <?php echo e(ucfirst($payment->booking->status)); ?>

                            </span>
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Pickup Location</p>
                        <p class="fw-bold"><?php echo e($payment->booking->pickup_address); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Dropoff Location</p>
                        <p class="fw-bold"><?php echo e($payment->booking->dropoff_address); ?></p>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="<?php echo e(route('client.bookings.show', $payment->booking->id)); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-1"></i> View Booking Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Receipt and Actions -->
    <div class="col-md-5">
        <div class="card dashboard-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Payment Receipt</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <?php if($payment->status == 'completed'): ?>
                            <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
                            <h4>Payment Successful</h4>
                        <?php elseif($payment->status == 'pending'): ?>
                            <i class="fas fa-clock text-warning fa-5x mb-3"></i>
                            <h4>Payment Pending</h4>
                        <?php elseif($payment->status == 'failed'): ?>
                            <i class="fas fa-times-circle text-danger fa-5x mb-3"></i>
                            <h4>Payment Failed</h4>
                        <?php elseif($payment->status == 'refunded'): ?>
                            <i class="fas fa-undo text-info fa-5x mb-3"></i>
                            <h4>Payment Refunded</h4>
                        <?php endif; ?>
                    </div>
                    
                    <div class="border-top border-bottom py-3 mb-3">
                        <h3 class="mb-0"><?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?></h3>
                        <p class="text-muted"><?php echo e($payment->created_at->format('F d, Y')); ?></p>
                    </div>
                    
                    <p class="mb-4">
                        Thank you for your payment. This page serves as your official receipt.
                    </p>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('client.payments.invoice', $payment->id)); ?>" class="btn btn-primary">
                            <i class="fas fa-file-invoice me-1"></i> View Invoice
                        </a>
                        <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Receipt
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">Need Help?</h5>
            </div>
            <div class="card-body">
                <p>If you have any questions about this payment or need assistance, please contact our support team.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\client\payments\show.blade.php ENDPATH**/ ?>