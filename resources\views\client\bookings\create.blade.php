@extends('layouts.client')

@section('title', 'Create Booking')

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<style>
    .booking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .booking-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .booking-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .booking-card .card-body {
        padding: 30px;
    }

    .booking-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        position: relative;
    }

    .booking-steps::before {
        content: '';
        position: absolute;
        top: 24px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step {
        position: relative;
        z-index: 2;
        background-color: white;
        text-align: center;
        width: 33.333%;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.2rem;
        margin: 0 auto 10px;
        transition: all 0.3s;
    }

    .step-title {
        font-weight: 600;
        color: #6c757d;
        transition: all 0.3s;
    }

    .step.active .step-number {
        background-color: #ee393d;
        color: #343a40;
    }

    .step.active .step-title {
        color: #343a40;
    }

    .step.completed .step-number {
        background-color: #28a745;
        color: white;
    }

    .booking-type-selector {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .booking-type-card {
        flex: 1;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .booking-type-card:hover {
        border-color: #ee393d;
        transform: translateY(-5px);
    }

    .booking-type-card.selected {
        border-color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
    }

    .booking-type-icon {
        font-size: 2rem;
        color: #343a40;
        margin-bottom: 10px;
    }

    .booking-type-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .booking-type-description {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #343a40;
        font-weight: 600;
        padding: 12px 20px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e0a800;
        border-color: #e0a800;
        transform: translateY(-2px);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 20px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: white;
        transform: translateY(-2px);
    }

    .vehicle-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .vehicle-card:hover {
        border-color: #ee393d;
        transform: translateY(-5px);
    }

    .vehicle-card.selected {
        border-color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
    }

    .vehicle-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .vehicle-name {
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 5px;
    }

    .vehicle-type {
        color: #6c757d;
        margin-bottom: 10px;
    }

    .vehicle-features {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }

    .vehicle-feature {
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .vehicle-feature i {
        margin-right: 5px;
        color: #ee393d;
    }

    .vehicle-price {
        font-weight: 600;
        font-size: 1.1rem;
        color: #343a40;
    }

    .booking-summary {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-total {
        font-weight: 600;
        font-size: 1.1rem;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 2px solid #e9ecef;
    }
</style>
@endsection

@section('content')
<div class="welcome-banner mb-4 p-4 bg-dark text-white rounded-3" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">Book Your Ride</h2>
            <p class="mb-0">Complete the steps below to book your Transportation service.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ route('client.bookings.index') }}" class="btn btn-light">
                <i class="fas fa-arrow-left me-2"></i> Back to Bookings
            </a>
        </div>
    </div>
</div>

<div class="card booking-card" data-aos="fade-up">
    <div class="card-header">
        <h4 class="mb-0">New Booking</h4>
    </div>
    <div class="card-body">
        @if ($errors->any())
            <div class="alert alert-danger mb-4">
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Booking Steps Indicator -->
        <div class="booking-steps">
            <div class="step active" id="step1-indicator">
                <div class="step-number">1</div>
                <div class="step-title">Trip Details</div>
            </div>
            <div class="step" id="step2-indicator">
                <div class="step-number">2</div>
                <div class="step-title">Vehicle Selection</div>
            </div>
            <div class="step" id="step3-indicator">
                <div class="step-number">3</div>
                <div class="step-title">Review & Payment</div>
            </div>
        </div>

        <form id="bookingForm" action="{{ route('client.bookings.store') }}" method="POST">
            @csrf
            <input type="hidden" name="booking_type" id="bookingType" value="one_way">
            <input type="hidden" name="vehicle_id" id="selectedVehicle">
            <input type="hidden" name="amount" id="totalFare">

            <!-- Step 1: Trip Details -->
            <div id="step1" class="booking-step">
                <h5 class="mb-4">Select Booking Type</h5>

                <div class="booking-type-selector">
                    <div class="booking-type-card selected" data-type="one_way">
                        <div class="booking-type-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="booking-type-title">One Way</div>
                        <div class="booking-type-description">Travel from pickup to destination</div>
                    </div>

                    <div class="booking-type-card" data-type="return">
                        <div class="booking-type-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="booking-type-title">Return</div>
                        <div class="booking-type-description">Round trip with return journey</div>
                    </div>

                    <div class="booking-type-card" data-type="hourly">
                        <div class="booking-type-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="booking-type-title">Hourly</div>
                        <div class="booking-type-description">Book by the hour</div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6 mb-3">
                        <label for="pickup_address" class="form-label">Pickup Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="pickup_address" name="pickup_address" placeholder="Enter pickup address" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="pickup_datetime" class="form-label">Pickup Date & Time</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control" id="pickup_datetime" name="pickup_datetime" placeholder="Select date and time" required>
                        </div>
                    </div>
                </div>

                <div id="dropoff_section" class="row">
                    <div class="col-md-12 mb-3">
                        <label for="dropoff_address" class="form-label">Dropoff Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="dropoff_address" name="dropoff_address" placeholder="Enter dropoff address" required>
                        </div>
                    </div>
                </div>

                <div id="return_section" class="row d-none">
                    <div class="col-md-12 mb-3">
                        <label for="return_datetime" class="form-label">Return Date & Time</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control" id="return_datetime" name="return_datetime" placeholder="Select return date and time">
                        </div>
                    </div>
                </div>

                <div id="hourly_section" class="row d-none">
                    <div class="col-md-12 mb-3">
                        <label for="duration_hours" class="form-label">Duration (Hours)</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                            <input type="number" class="form-control" id="duration_hours" name="duration_hours" min="1" value="1">
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">Additional Notes (Optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special requirements or information"></textarea>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="button" id="step1Next" class="btn btn-primary">
                        Next: Select Vehicle <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>

            <!-- Step 2: Vehicle Selection (will be loaded via AJAX) -->
            <div id="step2" class="booking-step d-none">
                <h5 class="mb-4">Select a Vehicle</h5>

                <div id="vehiclesContainer" class="row">
                    <!-- Vehicles will be loaded here via AJAX -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading available vehicles...</p>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" id="step2Prev" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back: Trip Details
                    </button>
                    <button type="button" id="step2Next" class="btn btn-primary" disabled>
                        Next: Review & Payment <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>

            <!-- Step 3: Review & Payment -->
            <div id="step3" class="booking-step d-none">
                <h5 class="mb-4">Review Your Booking</h5>

                <div class="row">
                    <div class="col-md-6">
                        <div class="booking-summary mb-4">
                            <h6 class="mb-3">Trip Details</h6>

                            <div class="summary-item">
                                <span>Booking Type:</span>
                                <span id="summary_booking_type">One Way</span>
                            </div>

                            <div class="summary-item">
                                <span>Pickup Address:</span>
                                <span id="summary_pickup"></span>
                            </div>

                            <div class="summary-item">
                                <span>Pickup Date & Time:</span>
                                <span id="summary_pickup_datetime"></span>
                            </div>

                            <div id="summary_dropoff_section" class="summary-item">
                                <span>Dropoff Address:</span>
                                <span id="summary_dropoff"></span>
                            </div>

                            <div id="summary_return_section" class="summary-item d-none">
                                <span>Return Date & Time:</span>
                                <span id="summary_return_datetime"></span>
                            </div>

                            <div id="summary_hourly_section" class="summary-item d-none">
                                <span>Duration:</span>
                                <span id="summary_duration"></span>
                            </div>

                            <div class="summary-item">
                                <span>Additional Notes:</span>
                                <span id="summary_notes">None</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="booking-summary mb-4">
                            <h6 class="mb-3">Vehicle Details</h6>

                            <div id="summary_vehicle_details">
                                <!-- Vehicle details will be populated via JavaScript -->
                                <p class="text-center">Please select a vehicle first</p>
                            </div>

                            <div class="summary-total d-flex justify-content-between">
                                <span>Total Fare:</span>
                                <span id="summary_total_fare">$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i> By proceeding, you agree to our <a href="{{ route('terms-and-conditions') }}" target="_blank">Terms and Conditions</a>.
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" id="step3Prev" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back: Vehicle Selection
                    </button>
                    <button type="submit" class="btn btn-primary">
                        Proceed to Payment <i class="fas fa-credit-card ms-2"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date pickers
        flatpickr("#pickup_datetime", {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            minDate: "today",
            time_24hr: false
        });

        flatpickr("#return_datetime", {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            minDate: "today",
            time_24hr: false
        });

        // Booking type selection
        const bookingTypeCards = document.querySelectorAll('.booking-type-card');
        const bookingTypeInput = document.getElementById('bookingType');
        const dropoffSection = document.getElementById('dropoff_section');
        const returnSection = document.getElementById('return_section');
        const hourlySection = document.getElementById('hourly_section');
        const dropoffAddress = document.getElementById('dropoff_address');
        const returnDatetime = document.getElementById('return_datetime');
        const durationHours = document.getElementById('duration_hours');

        bookingTypeCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                bookingTypeCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Update hidden input
                const bookingType = this.dataset.type;
                bookingTypeInput.value = bookingType;

                // Show/hide relevant sections
                if (bookingType === 'one_way' || bookingType === 'return') {
                    dropoffSection.classList.remove('d-none');
                    dropoffAddress.required = true;

                    if (bookingType === 'return') {
                        returnSection.classList.remove('d-none');
                        returnDatetime.required = true;
                        hourlySection.classList.add('d-none');
                        durationHours.required = false;
                    } else {
                        returnSection.classList.add('d-none');
                        returnDatetime.required = false;
                        hourlySection.classList.add('d-none');
                        durationHours.required = false;
                    }
                } else if (bookingType === 'hourly') {
                    dropoffSection.classList.add('d-none');
                    dropoffAddress.required = false;
                    returnSection.classList.add('d-none');
                    returnDatetime.required = false;
                    hourlySection.classList.remove('d-none');
                    durationHours.required = true;
                }
            });
        });

        // Step navigation
        const step1 = document.getElementById('step1');
        const step2 = document.getElementById('step2');
        const step3 = document.getElementById('step3');

        const step1Indicator = document.getElementById('step1-indicator');
        const step2Indicator = document.getElementById('step2-indicator');
        const step3Indicator = document.getElementById('step3-indicator');

        const step1Next = document.getElementById('step1Next');
        const step2Prev = document.getElementById('step2Prev');
        const step2Next = document.getElementById('step2Next');
        const step3Prev = document.getElementById('step3Prev');

        step1Next.addEventListener('click', function() {
            // Validate step 1
            const pickupAddress = document.getElementById('pickup_address').value;
            const pickupDatetime = document.getElementById('pickup_datetime').value;
            const bookingType = bookingTypeInput.value;

            if (!pickupAddress) {
                Swal.fire('Error', 'Please enter pickup address', 'error');
                return;
            }

            if (!pickupDatetime) {
                Swal.fire('Error', 'Please select pickup date and time', 'error');
                return;
            }

            if ((bookingType === 'one_way' || bookingType === 'return') && !dropoffAddress.value) {
                Swal.fire('Error', 'Please enter dropoff address', 'error');
                return;
            }

            if (bookingType === 'return' && !returnDatetime.value) {
                Swal.fire('Error', 'Please select return date and time', 'error');
                return;
            }

            if (bookingType === 'hourly' && (!durationHours.value || durationHours.value < 1)) {
                Swal.fire('Error', 'Please enter a valid duration (minimum 1 hour)', 'error');
                return;
            }

            // Load vehicles via AJAX
            loadVehicles();

            // Move to step 2
            step1.classList.add('d-none');
            step2.classList.remove('d-none');

            step1Indicator.classList.remove('active');
            step1Indicator.classList.add('completed');
            step2Indicator.classList.add('active');
        });

        step2Prev.addEventListener('click', function() {
            step2.classList.add('d-none');
            step1.classList.remove('d-none');

            step2Indicator.classList.remove('active');
            step1Indicator.classList.remove('completed');
            step1Indicator.classList.add('active');
        });

        step2Next.addEventListener('click', function() {
            // Validate step 2
            const selectedVehicle = document.getElementById('selectedVehicle').value;

            if (!selectedVehicle) {
                Swal.fire('Error', 'Please select a vehicle', 'error');
                return;
            }

            // Update summary
            updateSummary();

            // Move to step 3
            step2.classList.add('d-none');
            step3.classList.remove('d-none');

            step2Indicator.classList.remove('active');
            step2Indicator.classList.add('completed');
            step3Indicator.classList.add('active');
        });

        step3Prev.addEventListener('click', function() {
            step3.classList.add('d-none');
            step2.classList.remove('d-none');

            step3Indicator.classList.remove('active');
            step2Indicator.classList.remove('completed');
            step2Indicator.classList.add('active');
        });

        // Load vehicles function
        function loadVehicles() {
            const vehiclesContainer = document.getElementById('vehiclesContainer');

            // Show loading
            vehiclesContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading available vehicles...</p>
                </div>
            `;

            // Make AJAX request to get vehicles
            fetch('/api/vehicles')
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        vehiclesContainer.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-info">
                                    No vehicles available for the selected criteria. Please try different options.
                                </div>
                            </div>
                        `;
                        return;
                    }

                    let html = '';

                    data.forEach(vehicle => {
                        html += `
                            <div class="col-md-6 mb-4">
                                <div class="vehicle-card" data-vehicle-id="${vehicle.id}" data-vehicle-name="${vehicle.name}"
                                     data-vehicle-type="${vehicle.type}" data-vehicle-price="${vehicle.price_per_km}"
                                     data-vehicle-image="${vehicle.image ? '/storage/' + vehicle.image : 'https://via.placeholder.com/300x200?text=Vehicle'}">
                                    <img src="${vehicle.image ? '/storage/' + vehicle.image : 'https://via.placeholder.com/300x200?text=Vehicle'}"
                                         class="vehicle-image" alt="${vehicle.name}">
                                    <div class="vehicle-name">${vehicle.name}</div>
                                    <div class="vehicle-type">${vehicle.type}</div>
                                    <div class="vehicle-features">
                                        <div class="vehicle-feature">
                                            <i class="fas fa-user"></i> ${vehicle.seats} seats
                                        </div>
                                        <div class="vehicle-feature">
                                            <i class="fas fa-suitcase"></i> ${vehicle.luggage_capacity} luggage
                                        </div>
                                    </div>
                                    <div class="vehicle-price">
                                        $${vehicle.price_per_km}/km
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    vehiclesContainer.innerHTML = html;

                    // Add click event to vehicle cards
                    const vehicleCards = document.querySelectorAll('.vehicle-card');
                    vehicleCards.forEach(card => {
                        card.addEventListener('click', function() {
                            // Remove selected class from all cards
                            vehicleCards.forEach(c => c.classList.remove('selected'));

                            // Add selected class to clicked card
                            this.classList.add('selected');

                            // Update hidden input
                            document.getElementById('selectedVehicle').value = this.dataset.vehicleId;

                            // Enable next button
                            document.getElementById('step2Next').disabled = false;

                            // Calculate fare
                            calculateFare(this.dataset.vehicleId);
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading vehicles:', error);
                    vehiclesContainer.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-danger">
                                Error loading vehicles. Please try again later.
                            </div>
                        </div>
                    `;
                });
        }

        // Calculate fare function
        function calculateFare(vehicleId) {
            const bookingType = document.getElementById('bookingType').value;
            const pickupAddress = document.getElementById('pickup_address').value;
            const dropoffAddress = document.getElementById('dropoff_address').value;
            const durationHours = document.getElementById('duration_hours').value;

            // Show loading
            Swal.fire({
                title: 'Calculating Fare',
                text: 'Please wait while we calculate your fare...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Prepare data for API call
            const data = {
                booking_type: bookingType,
                vehicle_id: vehicleId,
                pickup_address: pickupAddress
            };

            if (bookingType === 'one_way' || bookingType === 'return') {
                data.dropoff_address = dropoffAddress;
            }

            if (bookingType === 'hourly') {
                data.duration_hours = durationHours;
            }

            // Make AJAX request to calculate fare
            fetch('/booking/calculate-fare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                // Close loading
                Swal.close();

                if (data.success) {
                    // Update total fare
                    let fare = 0;

                    // Handle different response structures
                    if (data.data && data.data.fare) {
                        fare = data.data.fare;
                    } else if (data.fare) {
                        fare = data.fare;
                    } else if (data.data && data.data.fare_details && data.data.fare_details.total_fare) {
                        fare = data.data.fare_details.total_fare;
                    } else if (data.fare_details && data.fare_details.total_fare) {
                        fare = data.fare_details.total_fare;
                    }

                    document.getElementById('totalFare').value = fare;
                    document.getElementById('summary_total_fare').textContent = '$' + parseFloat(fare).toFixed(2);
                } else {
                    Swal.fire('Error', data.message || 'Error calculating fare. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error calculating fare:', error);
                Swal.fire('Error', 'Error calculating fare. Please try again.', 'error');
            });
        }

        // Update summary function
        function updateSummary() {
            const bookingType = document.getElementById('bookingType').value;
            const pickupAddress = document.getElementById('pickup_address').value;
            const pickupDatetime = document.getElementById('pickup_datetime').value;
            const dropoffAddress = document.getElementById('dropoff_address').value;
            const returnDatetime = document.getElementById('return_datetime').value;
            const durationHours = document.getElementById('duration_hours').value;
            const notes = document.getElementById('notes').value;

            // Update booking type
            let bookingTypeText = 'One Way';
            if (bookingType === 'return') bookingTypeText = 'Return';
            if (bookingType === 'hourly') bookingTypeText = 'Hourly';
            document.getElementById('summary_booking_type').textContent = bookingTypeText;

            // Update addresses and times
            document.getElementById('summary_pickup').textContent = pickupAddress;
            document.getElementById('summary_pickup_datetime').textContent = pickupDatetime;

            // Show/hide sections based on booking type
            if (bookingType === 'one_way' || bookingType === 'return') {
                document.getElementById('summary_dropoff_section').classList.remove('d-none');
                document.getElementById('summary_dropoff').textContent = dropoffAddress;

                if (bookingType === 'return') {
                    document.getElementById('summary_return_section').classList.remove('d-none');
                    document.getElementById('summary_return_datetime').textContent = returnDatetime;
                    document.getElementById('summary_hourly_section').classList.add('d-none');
                } else {
                    document.getElementById('summary_return_section').classList.add('d-none');
                    document.getElementById('summary_hourly_section').classList.add('d-none');
                }
            } else if (bookingType === 'hourly') {
                document.getElementById('summary_dropoff_section').classList.add('d-none');
                document.getElementById('summary_return_section').classList.add('d-none');
                document.getElementById('summary_hourly_section').classList.remove('d-none');
                document.getElementById('summary_duration').textContent = durationHours + ' hour(s)';
            }

            // Update notes
            document.getElementById('summary_notes').textContent = notes || 'None';

            // Update vehicle details
            const selectedVehicleCard = document.querySelector('.vehicle-card.selected');
            if (selectedVehicleCard) {
                const vehicleName = selectedVehicleCard.dataset.vehicleName;
                const vehicleType = selectedVehicleCard.dataset.vehicleType;
                const vehicleImage = selectedVehicleCard.dataset.vehicleImage;

                document.getElementById('summary_vehicle_details').innerHTML = `
                    <div class="text-center mb-3">
                        <img src="${vehicleImage}" alt="${vehicleName}" class="img-fluid rounded" style="max-height: 150px;">
                    </div>
                    <div class="summary-item">
                        <span>Vehicle:</span>
                        <span>${vehicleName}</span>
                    </div>
                    <div class="summary-item">
                        <span>Type:</span>
                        <span>${vehicleType}</span>
                    </div>
                `;
            }
        }
    });
</script>
@endsection
