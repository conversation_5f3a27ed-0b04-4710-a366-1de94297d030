<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;

use App\Http\Controllers\BookingController;
use App\Http\Controllers\Client\DashboardController as ClientDashboardController;
use App\Http\Controllers\Driver\DashboardController as DriverDashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/about', [App\Http\Controllers\HomeController::class, 'about'])->name('about');
Route::get('/services', [App\Http\Controllers\HomeController::class, 'services'])->name('services');
Route::get('/contact', [App\Http\Controllers\HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [App\Http\Controllers\HomeController::class, 'submitContact'])->name('contact.submit');
Route::get('/privacy-policy', [App\Http\Controllers\HomeController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-and-conditions', [App\Http\Controllers\HomeController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('/fleet', [App\Http\Controllers\HomeController::class, 'fleet'])->name('fleet');

Route::get('/faq', [App\Http\Controllers\HomeController::class, 'faq'])->name('faq');

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Dashboard Redirect
Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');

// Notification Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/mark-as-read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
});

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');

    // Vehicle Management
    Route::resource('vehicles', \App\Http\Controllers\Admin\VehicleController::class, ['as' => 'admin']);
    Route::post('vehicles/{id}/toggle-active', [\App\Http\Controllers\Admin\VehicleController::class, 'toggleActive'])->name('admin.vehicles.toggle-active');
    Route::get('vehicles-export', [\App\Http\Controllers\Admin\VehicleController::class, 'export'])->name('admin.vehicles.export');

    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class, ['as' => 'admin']);
    Route::post('users/{id}/toggle-active', [\App\Http\Controllers\Admin\UserController::class, 'toggleActive'])->name('admin.users.toggle-active');
    Route::get('users-export', [\App\Http\Controllers\Admin\UserController::class, 'export'])->name('admin.users.export');
    Route::get('clients', [\App\Http\Controllers\Admin\UserController::class, 'clients'])->name('admin.users.clients');
    Route::get('clients/search', [\App\Http\Controllers\Admin\UserController::class, 'searchClients'])->name('admin.clients.search');
    Route::get('clients/{id}/details', [\App\Http\Controllers\Admin\UserController::class, 'getClientDetails'])->name('admin.clients.details');
    Route::post('clients/quick-create', [\App\Http\Controllers\Admin\UserController::class, 'quickCreateClient'])->name('admin.clients.quick-create');

    // Driver Management
    Route::resource('drivers', \App\Http\Controllers\Admin\DriverController::class, ['as' => 'admin']);
    Route::post('drivers/{id}/toggle-active', [\App\Http\Controllers\Admin\DriverController::class, 'toggleActive'])->name('admin.drivers.toggle-active');
    Route::post('drivers/{id}/toggle-availability', [\App\Http\Controllers\Admin\DriverController::class, 'toggleAvailability'])->name('admin.drivers.toggle-availability');
    Route::get('drivers-export', [\App\Http\Controllers\Admin\DriverController::class, 'export'])->name('admin.drivers.export');

    // Driver Documents
    Route::get('drivers/{id}/documents', [\App\Http\Controllers\Admin\DriverController::class, 'documents'])->name('admin.drivers.documents');
    Route::post('drivers/{id}/documents', [\App\Http\Controllers\Admin\DriverController::class, 'uploadDocument'])->name('admin.drivers.upload-document');
    Route::delete('drivers/documents/{document_id}', [\App\Http\Controllers\Admin\DriverController::class, 'deleteDocument'])->name('admin.drivers.delete-document');
    Route::post('drivers/documents/{document_id}/verify', [\App\Http\Controllers\Admin\DriverController::class, 'verifyDocument'])->name('admin.drivers.verify-document');

    // Driver Schedule
    Route::get('drivers/{id}/schedule', [\App\Http\Controllers\Admin\DriverController::class, 'schedule'])->name('admin.drivers.schedule');
    Route::post('drivers/{id}/schedule', [\App\Http\Controllers\Admin\DriverController::class, 'updateSchedule'])->name('admin.drivers.update-schedule');

    // Payment Management
    Route::get('payments', [\App\Http\Controllers\Admin\PaymentController::class, 'index'])->name('admin.payments.index');
    Route::get('payments/{id}', [\App\Http\Controllers\Admin\PaymentController::class, 'show'])->name('admin.payments.show');
    Route::post('payments', [\App\Http\Controllers\Admin\PaymentController::class, 'store'])->name('admin.payments.store');
    Route::post('payments/process-refund', [\App\Http\Controllers\Admin\PaymentController::class, 'processRefund'])->name('admin.payments.process-refund');
    Route::post('payments/{id}/update-status', [\App\Http\Controllers\Admin\PaymentController::class, 'updateStatus'])->name('admin.payments.update-status');
    Route::post('payments/{id}/refund', [\App\Http\Controllers\Admin\PaymentController::class, 'refund'])->name('admin.payments.refund');
    Route::get('payments-report', [\App\Http\Controllers\Admin\PaymentController::class, 'report'])->name('admin.payments.report');
    Route::get('payments/{id}/invoice', [\App\Http\Controllers\Admin\PaymentController::class, 'invoice'])->name('admin.payments.invoice');

    // Payment Export
    Route::get('payments-export', [\App\Http\Controllers\Admin\PaymentExportController::class, 'export'])->name('admin.payments.export');
    Route::get('payments-export-report', [\App\Http\Controllers\Admin\PaymentExportController::class, 'exportReport'])->name('admin.payments.export-report');



    // Review Management
    Route::resource('reviews', \App\Http\Controllers\Admin\ReviewController::class, ['as' => 'admin']);

    // Booking Management
    Route::get('bookings', [\App\Http\Controllers\Admin\BookingController::class, 'index'])->name('admin.bookings.index');
    Route::get('bookings/create', [\App\Http\Controllers\Admin\BookingController::class, 'create'])->name('admin.bookings.create');
    Route::post('bookings/store', [\App\Http\Controllers\Admin\BookingController::class, 'store'])->name('admin.bookings.store');
    Route::get('bookings/{id}', [\App\Http\Controllers\Admin\BookingController::class, 'show'])->name('admin.bookings.show');
    Route::get('bookings/{id}/edit', [\App\Http\Controllers\Admin\BookingController::class, 'edit'])->name('admin.bookings.edit');
    Route::put('bookings/{id}', [\App\Http\Controllers\Admin\BookingController::class, 'update'])->name('admin.bookings.update');
    Route::post('bookings/{id}/update-status', [\App\Http\Controllers\Admin\BookingController::class, 'updateStatus'])->name('admin.bookings.update-status');
    Route::get('bookings/{id}/available-drivers', [\App\Http\Controllers\Admin\BookingController::class, 'showAvailableDrivers'])->name('admin.bookings.available-drivers');
    Route::post('bookings/{id}/assign-driver', [\App\Http\Controllers\Admin\BookingController::class, 'assignDriver'])->name('admin.bookings.assign-driver');
    Route::post('bookings/{id}/cancel', [\App\Http\Controllers\Admin\BookingController::class, 'cancel'])->name('admin.bookings.cancel');
    Route::get('bookings-export', [\App\Http\Controllers\Admin\BookingController::class, 'export'])->name('admin.bookings.export');
    Route::get('bookings-report', [\App\Http\Controllers\Admin\BookingController::class, 'report'])->name('admin.bookings.report');

    // Settings Management
    Route::get('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('admin.settings.index');
    Route::post('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('admin.settings.update');
    Route::get('settings/test', function() {
        return view('admin.settings.test');
    })->name('admin.settings.test');

    // Airport Management
    Route::resource('airports', \App\Http\Controllers\Admin\AirportController::class, ['as' => 'admin']);

    // Reports
    Route::get('reports/revenue', [\App\Http\Controllers\Admin\ReportController::class, 'revenue'])->name('admin.reports.revenue');
    Route::get('reports/bookings', [\App\Http\Controllers\Admin\ReportController::class, 'bookings'])->name('admin.reports.bookings');
    Route::get('reports/drivers', [\App\Http\Controllers\Admin\ReportController::class, 'drivers'])->name('admin.reports.drivers');
    Route::get('reports/vehicles', [\App\Http\Controllers\Admin\ReportController::class, 'vehicles'])->name('admin.reports.vehicles');
});

// Client Routes
Route::prefix('client')->middleware(['auth', 'role:client'])->group(function () {
    Route::get('/dashboard', [ClientDashboardController::class, 'index'])->name('client.dashboard');

    // Booking Routes
    Route::prefix('bookings')->group(function () {
        Route::get('/', [App\Http\Controllers\Client\BookingController::class, 'index'])->name('client.bookings.index');
        Route::get('/{id}', [App\Http\Controllers\Client\BookingController::class, 'show'])->name('client.bookings.show');
        Route::post('/{id}/cancel', [App\Http\Controllers\Client\BookingController::class, 'cancel'])->name('client.bookings.cancel');
        Route::get('/{id}/review', [App\Http\Controllers\Client\BookingController::class, 'showReviewForm'])->name('client.bookings.review');
        Route::post('/{id}/review', [App\Http\Controllers\Client\BookingController::class, 'storeReview'])->name('client.bookings.store-review');
        Route::get('/create', [App\Http\Controllers\Client\BookingController::class, 'create'])->name('client.bookings.create');
        Route::post('/store', [App\Http\Controllers\Client\BookingController::class, 'store'])->name('client.bookings.store');
        Route::get('/history', [App\Http\Controllers\Client\BookingController::class, 'history'])->name('client.bookings.history');
    });

    // Profile Routes
    Route::prefix('profile')->group(function () {
        Route::get('/', [App\Http\Controllers\Client\ProfileController::class, 'index'])->name('client.profile.index');
        Route::get('/edit', [App\Http\Controllers\Client\ProfileController::class, 'edit'])->name('client.profile.edit');
        Route::put('/update', [App\Http\Controllers\Client\ProfileController::class, 'update'])->name('client.profile.update');
        Route::get('/change-password', [App\Http\Controllers\Client\ProfileController::class, 'showChangePasswordForm'])->name('client.profile.change-password');
        Route::post('/change-password', [App\Http\Controllers\Client\ProfileController::class, 'changePassword'])->name('client.profile.update-password');
        Route::post('/update-avatar', [App\Http\Controllers\Client\ProfileController::class, 'updateAvatar'])->name('client.profile.update-avatar');
        Route::get('/addresses', [App\Http\Controllers\Client\ProfileController::class, 'addresses'])->name('client.profile.addresses');
        Route::post('/addresses', [App\Http\Controllers\Client\ProfileController::class, 'storeAddress'])->name('client.profile.add-address');
        Route::delete('/addresses/{id}', [App\Http\Controllers\Client\ProfileController::class, 'deleteAddress'])->name('client.profile.delete-address');
    });

    // Payment Routes
    Route::prefix('payments')->group(function () {
        Route::get('/', [App\Http\Controllers\Client\PaymentController::class, 'index'])->name('client.payments.index');
        Route::get('/{id}', [App\Http\Controllers\Client\PaymentController::class, 'show'])->name('client.payments.show');
        Route::get('/{id}/invoice', [App\Http\Controllers\Client\PaymentController::class, 'invoice'])->name('client.payments.invoice');
        Route::get('/history', [App\Http\Controllers\Client\PaymentController::class, 'history'])->name('client.payments.history');
        Route::post('/{id}/download-invoice', [App\Http\Controllers\Client\PaymentController::class, 'downloadInvoice'])->name('client.payments.download-invoice');
    });

    // Notification Settings
    Route::prefix('notifications')->group(function () {
        Route::get('/settings', [App\Http\Controllers\Client\NotificationController::class, 'settings'])->name('client.notifications.settings');
        Route::post('/settings', [App\Http\Controllers\Client\NotificationController::class, 'updateSettings'])->name('client.notifications.update-settings');
    });
});

// Driver Routes
Route::prefix('driver')->middleware(['auth', 'role:driver'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Driver\DashboardController::class, 'index'])->name('driver.dashboard');

    // Ride Routes
    Route::prefix('rides')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\RideController::class, 'index'])->name('driver.rides.index');
        Route::get('/my-rides', [App\Http\Controllers\Driver\RideController::class, 'myRides'])->name('driver.rides.my-rides');
        Route::get('/history', [App\Http\Controllers\Driver\RideController::class, 'history'])->name('driver.rides.history');
        Route::get('/{id}', [App\Http\Controllers\Driver\RideController::class, 'show'])->name('driver.rides.show');
        Route::get('/{id}/track', [App\Http\Controllers\Driver\RideController::class, 'track'])->name('driver.rides.track');
        Route::get('/{id}/summary', [App\Http\Controllers\Driver\RideController::class, 'summary'])->name('driver.rides.summary');
        Route::post('/{id}/accept', [App\Http\Controllers\Driver\RideController::class, 'accept'])->name('driver.rides.accept');
        Route::post('/{id}/start', [App\Http\Controllers\Driver\RideController::class, 'start'])->name('driver.rides.start');
        Route::post('/{id}/complete', [App\Http\Controllers\Driver\RideController::class, 'complete'])->name('driver.rides.complete');
        Route::post('/{id}/cancel', [App\Http\Controllers\Driver\RideController::class, 'cancel'])->name('driver.rides.cancel');
        Route::post('/{id}/update-location', [App\Http\Controllers\Driver\RideController::class, 'updateLocation'])->name('driver.rides.update-location');
        Route::post('/{id}/add-note', [App\Http\Controllers\Driver\RideController::class, 'addNote'])->name('driver.rides.add-note');
        Route::get('/{id}/download-receipt', [App\Http\Controllers\Driver\RideController::class, 'downloadReceipt'])->name('driver.rides.download-receipt');
        Route::post('/{id}/report-issue', [App\Http\Controllers\Driver\RideController::class, 'reportIssue'])->name('driver.rides.report-issue');
    });

    // Earnings Routes
    Route::prefix('earnings')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\EarningController::class, 'index'])->name('driver.earnings.index');
        Route::get('/history', [App\Http\Controllers\Driver\EarningController::class, 'history'])->name('driver.earnings.history');
        Route::get('/details/{month}/{year}', [App\Http\Controllers\Driver\EarningController::class, 'details'])->name('driver.earnings.details');
        Route::get('/statement', [App\Http\Controllers\Driver\EarningController::class, 'statement'])->name('driver.earnings.statement');
        Route::post('/statement/download', [App\Http\Controllers\Driver\EarningController::class, 'downloadStatement'])->name('driver.earnings.download-statement');
        Route::get('/reports', [App\Http\Controllers\Driver\EarningController::class, 'reports'])->name('driver.earnings.reports');
        Route::get('/analytics', [App\Http\Controllers\Driver\EarningController::class, 'analytics'])->name('driver.earnings.analytics');
        Route::get('/schedule', [App\Http\Controllers\Driver\EarningController::class, 'schedule'])->name('driver.earnings.schedule');
    });

    // Profile Routes
    Route::prefix('profile')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\ProfileController::class, 'index'])->name('driver.profile.index');
        Route::get('/edit', [App\Http\Controllers\Driver\ProfileController::class, 'edit'])->name('driver.profile.edit');
        Route::put('/update', [App\Http\Controllers\Driver\ProfileController::class, 'update'])->name('driver.profile.update');
        Route::get('/change-password', [App\Http\Controllers\Driver\ProfileController::class, 'showChangePasswordForm'])->name('driver.profile.change-password');
        Route::post('/change-password', [App\Http\Controllers\Driver\ProfileController::class, 'changePassword'])->name('driver.profile.update-password');
        Route::put('/password/update', [App\Http\Controllers\Driver\ProfileController::class, 'changePassword'])->name('driver.password.update');
        Route::get('/availability', [App\Http\Controllers\Driver\ProfileController::class, 'showAvailabilityForm'])->name('driver.profile.availability');
        Route::put('/availability', [App\Http\Controllers\Driver\ProfileController::class, 'updateAvailability'])->name('driver.profile.update-availability');
        Route::post('/update-avatar', [App\Http\Controllers\Driver\ProfileController::class, 'updateAvatar'])->name('driver.profile.update-avatar');
        Route::get('/documents', [App\Http\Controllers\Driver\ProfileController::class, 'documents'])->name('driver.profile.documents');
        Route::post('/documents', [App\Http\Controllers\Driver\ProfileController::class, 'uploadDocument'])->name('driver.profile.upload-document');
        Route::delete('/documents/{id}', [App\Http\Controllers\Driver\ProfileController::class, 'deleteDocument'])->name('driver.profile.delete-document');
    });

    // Vehicle Routes
    Route::prefix('vehicles')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\VehicleController::class, 'index'])->name('driver.vehicles.index');
        Route::get('/maintenance/log', [App\Http\Controllers\Driver\VehicleController::class, 'maintenanceLog'])->name('driver.vehicles.maintenance-log');
        Route::post('/maintenance/log', [App\Http\Controllers\Driver\VehicleController::class, 'addMaintenanceLog'])->name('driver.vehicles.add-maintenance-log');
        Route::get('/{id}', [App\Http\Controllers\Driver\VehicleController::class, 'show'])->name('driver.vehicles.show');
    });

    // Vehicle Update Route (separate from the vehicles prefix to match the form in the view)
    Route::put('/vehicle/update', [App\Http\Controllers\Driver\VehicleController::class, 'update'])->name('driver.vehicle.update');

    // Document Routes
    Route::prefix('documents')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\DocumentController::class, 'index'])->name('driver.documents.index');
        Route::get('/create', [App\Http\Controllers\Driver\DocumentController::class, 'create'])->name('driver.documents.create');
        Route::post('/', [App\Http\Controllers\Driver\DocumentController::class, 'store'])->name('driver.documents.store');
        Route::get('/{id}', [App\Http\Controllers\Driver\DocumentController::class, 'show'])->name('driver.documents.show');
        Route::get('/{id}/edit', [App\Http\Controllers\Driver\DocumentController::class, 'edit'])->name('driver.documents.edit');
        Route::put('/{id}', [App\Http\Controllers\Driver\DocumentController::class, 'update'])->name('driver.documents.update');
        Route::delete('/{id}', [App\Http\Controllers\Driver\DocumentController::class, 'destroy'])->name('driver.documents.destroy');
    });

    // Schedule Routes
    Route::prefix('schedule')->group(function () {
        Route::get('/', [App\Http\Controllers\Driver\ScheduleController::class, 'index'])->name('driver.schedule.index');
        Route::post('/update', [App\Http\Controllers\Driver\ScheduleController::class, 'update'])->name('driver.schedule.update');
        Route::post('/time-off', [App\Http\Controllers\Driver\ScheduleController::class, 'requestTimeOff'])->name('driver.schedule.request-time-off');
    });

    // Notification Settings
    Route::prefix('notifications')->group(function () {
        Route::get('/settings', [App\Http\Controllers\Driver\NotificationController::class, 'settings'])->name('driver.notifications.settings');
        Route::post('/settings', [App\Http\Controllers\Driver\NotificationController::class, 'updateSettings'])->name('driver.notifications.update-settings');
    });
});

// Booking Routes
Route::prefix('booking')->group(function () {
    // Public booking routes (no auth required)
    Route::get('/', [BookingController::class, 'index'])->name('booking.index');
    Route::post('/calculate-fare', [BookingController::class, 'calculateFare'])->name('booking.calculate-fare');
    Route::post('/store', [BookingController::class, 'store'])->name('booking.store');
    Route::get('/guest/review', [BookingController::class, 'guestReview'])->name('booking.guest.review');
    Route::post('/guest/save', [BookingController::class, 'saveGuestBooking'])->name('booking.guest.save');

    // Client details routes
    Route::get('/client-details', [BookingController::class, 'showClientDetails'])->name('booking.client-details');
    Route::post('/save-client-details', [BookingController::class, 'saveClientDetails'])->name('booking.save-client-details');

    // Auth check route
    Route::get('/auth-check', [BookingController::class, 'authCheck'])->name('booking.auth-check');

    // Guest login/register routes
    Route::get('/guest/login', [BookingController::class, 'showGuestLogin'])->name('booking.guest.login');
    Route::post('/guest/login', [BookingController::class, 'processGuestLogin'])->name('booking.guest.process-login');
    Route::get('/guest/register', [BookingController::class, 'showGuestRegister'])->name('booking.guest.register');
    Route::post('/guest/register', [BookingController::class, 'processGuestRegister'])->name('booking.guest.process-register');

    // Payment Routes (require authentication)
    Route::middleware(['auth'])->group(function () {
        Route::get('/{id}/payment', [BookingController::class, 'showPayment'])->name('booking.payment');
        Route::post('/{id}/payment', [BookingController::class, 'processPayment'])->name('booking.process-payment');
        Route::get('/{id}/payment/success', [BookingController::class, 'paymentSuccess'])->name('booking.payment.success');
        Route::get('/{id}/payment/cancel', [BookingController::class, 'paymentCancel'])->name('booking.payment.cancel');
        Route::get('/{id}/confirmation', [BookingController::class, 'confirmation'])->name('booking.confirmation');
        Route::get('/{id}/track', [BookingController::class, 'trackBooking'])->name('booking.track');
    });
});



// PayPal Test Routes
Route::get('/paypal-test', function () {
    $paypalService = new \App\Services\PayPalService();
    $clientId = \App\Helpers\SettingsHelper::getPaypalClientId();
    $clientSecret = \App\Helpers\SettingsHelper::getPaypalSecret();
    $mode = \App\Helpers\SettingsHelper::getPaypalMode();

    // Get all settings
    $settings = \App\Models\Setting::all();

    return response()->json([
        'client_id' => $clientId,
        'client_secret_masked' => $clientSecret ? substr($clientSecret, 0, 5) . '...' : null,
        'mode' => $mode,
        'env_client_id' => env('PAYPAL_SANDBOX_CLIENT_ID'),
        'env_client_secret_masked' => env('PAYPAL_SANDBOX_CLIENT_SECRET') ? substr(env('PAYPAL_SANDBOX_CLIENT_SECRET'), 0, 5) . '...' : null,
        'settings_count' => $settings->count(),
        'company_name' => \App\Helpers\SettingsHelper::getCompanyName(),
    ]);
});

Route::get('/test-paypal', function () {
    return view('test-paypal');
});
