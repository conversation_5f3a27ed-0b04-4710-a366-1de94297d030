/* Notification Settings Page Styles */

/* Main Container Styling */
.notification-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Page Header Styling */
.page-header {
    background: linear-gradient(135deg, #343a40 0%, #121416 100%);
    color: white;
    padding: 30px 0;
    border-radius: 10px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ee393d" opacity="0.1"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/></svg>') no-repeat center center;
    background-size: 150px;
    opacity: 0.2;
}

.page-header h2 {
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
}

.page-header p {
    opacity: 0.8;
    max-width: 600px;
    margin-bottom: 0;
    position: relative;
}

/* Card Styling */
.notification-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    transition: transform 0.3s, box-shadow 0.3s;
    overflow: hidden;
}

.notification-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.notification-card .card-header {
    background: linear-gradient(135deg, #343a40 0%, #121416 100%);
    color: white;
    border-bottom: none;
    padding: 20px 25px;
    font-weight: 600;
}

.notification-card .card-footer {
    background-color: #f8f9fa;
    border-top: none;
    padding: 20px 25px;
}

/* Notification Sections */
.notification-section {
    padding: 25px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color 0.3s;
}

.notification-section:hover {
    background-color: rgba(238, 57, 61, 0.03);
}

.notification-section:last-child {
    border-bottom: none;
}

.notification-title {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #343a40;
}

.notification-description {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

/* Form Controls */
.form-check-input:checked {
    background-color: #ee393d;
    border-color: #ee393d;
}

.form-switch .form-check-input {
    width: 3.5em;
    height: 1.8em;
    cursor: pointer;
    transition: all 0.3s;
}

.form-switch .form-check-input:focus {
    border-color: rgba(238, 57, 61, 0.25);
    box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
}

.form-check-label {
    font-weight: 600;
    font-size: 1rem;
    padding-left: 10px;
    cursor: pointer;
}

/* Notification Type Cards */
.notification-icon {
    font-size: 2.5rem;
    color: #ee393d;
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.notification-type {
    background-color: white;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(0,0,0,0.05);
}

.notification-type:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.notification-type:hover .notification-icon {
    transform: scale(1.1);
}

.notification-type h4 {
    font-weight: 700;
    margin-bottom: 15px;
    color: #343a40;
}

.notification-type p {
    color: #6c757d;
    margin-bottom: 0;
}

/* Button Styling */
.btn-primary {
    background-color: #ee393d;
    border-color: #ee393d;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #d62d31;
    border-color: #d62d31;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(238, 57, 61, 0.3);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus {
    background-color: #6c757d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

/* About Section */
.about-notifications {
    background-color: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
}

.about-notifications ul {
    padding-left: 20px;
}

.about-notifications li {
    margin-bottom: 10px;
}

/* Badge Styling */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 30px;
}

/* Alert Styling */
.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #055160;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .notification-type {
        margin-bottom: 20px;
    }
    
    .page-header {
        padding: 20px 0;
    }
    
    .notification-section {
        padding: 20px;
    }
}
