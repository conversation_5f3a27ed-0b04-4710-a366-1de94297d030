<?php

namespace App\Services;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;

class CardPaymentService
{
    /**
     * Stripe API key
     *
     * @var string
     */
    protected $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Get Stripe API key from settings
        $this->apiKey = SettingsHelper::getStripeSecretKey();
        
        // Set Stripe API key
        Stripe::setApiKey($this->apiKey);
    }

    /**
     * Create a payment intent
     *
     * @param float $amount
     * @param string $currency
     * @param array $metadata
     * @return array|null
     */
    public function createPaymentIntent($amount, $currency = 'USD', $metadata = [])
    {
        try {
            // Amount needs to be in cents for Stripe
            $amountInCents = (int)($amount * 100);
            
            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => $currency,
                'metadata' => $metadata,
                'payment_method_types' => ['card'],
            ]);

            return [
                'id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'amount' => $amount,
                'currency' => $currency,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment intent creation failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }

    /**
     * Retrieve a payment intent
     *
     * @param string $paymentIntentId
     * @return \Stripe\PaymentIntent|null
     */
    public function retrievePaymentIntent($paymentIntentId)
    {
        try {
            return PaymentIntent::retrieve($paymentIntentId);
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment intent retrieval failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }

    /**
     * Confirm a payment intent
     *
     * @param string $paymentIntentId
     * @param string $paymentMethodId
     * @return \Stripe\PaymentIntent|null
     */
    public function confirmPaymentIntent($paymentIntentId, $paymentMethodId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            $paymentIntent->confirm([
                'payment_method' => $paymentMethodId,
            ]);

            return $paymentIntent;
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment intent confirmation failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }

    /**
     * Cancel a payment intent
     *
     * @param string $paymentIntentId
     * @return \Stripe\PaymentIntent|null
     */
    public function cancelPaymentIntent($paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            $paymentIntent->cancel();

            return $paymentIntent;
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment intent cancellation failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }
}
