<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('airports', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('airports', 'country_code')) {
                $table->string('country_code', 2)->after('country');
            }
            if (!Schema::hasColumn('airports', 'timezone')) {
                $table->string('timezone')->nullable()->after('longitude');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('airports', function (Blueprint $table) {
            // Check if columns exist before dropping them
            $columnsToDrop = [];
            if (Schema::hasColumn('airports', 'country_code')) {
                $columnsToDrop[] = 'country_code';
            }
            if (Schema::hasColumn('airports', 'timezone')) {
                $columnsToDrop[] = 'timezone';
            }

            if (!empty($columnsToDrop)) {
                $table->dropColumn($columnsToDrop);
            }
        });
    }
};
