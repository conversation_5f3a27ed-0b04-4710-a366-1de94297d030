<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Vehicle;
use App\Models\Airport;
use App\Services\SettingsService;

class PricingController extends Controller
{
    /**
     * Calculate pricing for a journey
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function calculatePrice(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:one_way,round_trip,airport_transfer',
            'pickup' => 'required_unless:type,airport_transfer,airport_direction,from_airport',
            'dropoff' => 'required_unless:type,airport_transfer,airport_direction,to_airport',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required',
            'passengers' => 'required|integer|min:1',
            'airport_direction' => 'required_if:type,airport_transfer|in:to_airport,from_airport',
            'airport_name' => 'required_if:type,airport_transfer',
            'return_date' => 'required_if:type,round_trip|date|after_or_equal:date',
            'return_time' => 'required_if:type,round_trip',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get available vehicles based on passenger count
        $passengers = $request->passengers;
        $vehicles = Vehicle::where('passenger_capacity', '>=', $passengers)
                          ->where('status', 'active')
                          ->orderBy('base_price', 'asc')
                          ->get();

        if ($vehicles->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No suitable vehicles available for this number of passengers'
            ], 404);
        }

        // Calculate distance and duration using Google Maps API
        // For demo purposes, we'll use fixed values
        $distance = 15; // miles
        $duration = 45; // minutes

        // If it's an airport transfer, get airport details
        if ($request->type === 'airport_transfer' && $request->airport_name) {
            // In a real app, you would fetch this from the database
            $airportSurcharge = 10.00; // Additional fee for airport pickups/dropoffs
        } else {
            $airportSurcharge = 0;
        }

        // Calculate prices for each vehicle
        $pricedVehicles = [];
        foreach ($vehicles as $vehicle) {
            $basePrice = $vehicle->base_price;
            $pricePerMile = $vehicle->price_per_mile;

            // Calculate one-way price
            $oneWayPrice = $basePrice + ($distance * $pricePerMile) + $airportSurcharge;

            // For round trip, apply a discount
            if ($request->type === 'round_trip') {
                $returnPrice = $oneWayPrice * 0.9; // 10% discount on return journey
                $totalPrice = $oneWayPrice + $returnPrice;
            } else {
                $totalPrice = $oneWayPrice;
            }

            // Apply time-based surcharges (e.g., night time, peak hours)
            $hour = date('H', strtotime($request->time));
            if ($hour >= 22 || $hour < 6) {
                $totalPrice *= 1.2; // 20% surcharge for night time (10pm - 6am)
            } elseif (($hour >= 7 && $hour < 10) || ($hour >= 16 && $hour < 19)) {
                $totalPrice *= 1.1; // 10% surcharge for peak hours (7-10am, 4-7pm)
            }

            // Round to 2 decimal places
            $totalPrice = round($totalPrice, 2);

            $pricedVehicles[] = [
                'id' => $vehicle->id,
                'name' => $vehicle->name,
                'description' => $vehicle->description,
                'image' => $vehicle->image,
                'passenger_capacity' => $vehicle->passenger_capacity,
                'luggage_capacity' => $vehicle->luggage_capacity,
                'price' => $totalPrice,
                'currency' => SettingsService::getCurrencyCode(),
                'currency_symbol' => SettingsService::getCurrencySymbol(),
                'distance' => $distance,
                'duration' => $duration,
                'estimated_arrival' => date('H:i', strtotime('+10 minutes')),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'vehicles' => $pricedVehicles,
                'currency_code' => SettingsService::getCurrencyCode(),
                'currency_symbol' => SettingsService::getCurrencySymbol(),
                'journey' => [
                    'type' => $request->type,
                    'pickup' => $request->pickup,
                    'dropoff' => $request->dropoff,
                    'date' => $request->date,
                    'time' => $request->time,
                    'distance' => $distance,
                    'duration' => $duration,
                    'airport' => $request->airport_name ?? null,
                ]
            ]
        ]);
    }
}
