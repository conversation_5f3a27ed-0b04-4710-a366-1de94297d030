<?php $__env->startSection('title', 'Email Templates'); ?>



<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Templates</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.emails.index')); ?>">Email Management</a></li>
                        <li class="breadcrumb-item active">Templates</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Categories -->
    <div class="row">
        <!-- Booking Templates -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Booking Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Confirmation</h6>
                                <p class="mb-1 text-muted">Sent when a booking is confirmed</p>
                                <small class="text-muted">Template: booking_confirmation</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_confirmation')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_confirmation')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Cancelled</h6>
                                <p class="mb-1 text-muted">Sent when a booking is cancelled</p>
                                <small class="text-muted">Template: booking_cancelled</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_cancelled')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_cancelled')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Reminder</h6>
                                <p class="mb-1 text-muted">Sent as a reminder before pickup</p>
                                <small class="text-muted">Template: booking_reminder</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_reminder')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_reminder')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Driver Assigned</h6>
                                <p class="mb-1 text-muted">Sent when a driver is assigned to booking</p>
                                <small class="text-muted">Template: driver_assigned</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('driver_assigned')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('driver_assigned')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Templates -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">User Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Welcome Email</h6>
                                <p class="mb-1 text-muted">Sent to new users after registration</p>
                                <small class="text-muted">Template: user_welcome</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('user_welcome')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('user_welcome')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Password Reset</h6>
                                <p class="mb-1 text-muted">Sent when user requests password reset</p>
                                <small class="text-muted">Template: password_reset</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('password_reset')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('password_reset')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Email Verification</h6>
                                <p class="mb-1 text-muted">Sent to verify email address</p>
                                <small class="text-muted">Template: email_verification</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('email_verification')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('email_verification')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Account Activated</h6>
                                <p class="mb-1 text-muted">Sent when account is activated by admin</p>
                                <small class="text-muted">Template: account_activated</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('account_activated')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('account_activated')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Templates -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">System Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Payment Confirmation</h6>
                                <p class="mb-1 text-muted">Sent when payment is processed</p>
                                <small class="text-muted">Template: payment_confirmation</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('payment_confirmation')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('payment_confirmation')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">System Notification</h6>
                                <p class="mb-1 text-muted">General system notifications</p>
                                <small class="text-muted">Template: system_notification</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('system_notification')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('system_notification')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Variables -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Available Variables</h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="variablesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#userVars">
                                    User Variables
                                </button>
                            </h2>
                            <div id="userVars" class="accordion-collapse collapse show" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code>{{ '$user->name' }}</code></div>
                                        <div class="col-6">User's full name</div>
                                        <div class="col-6"><code>{{ '$user->email' }}</code></div>
                                        <div class="col-6">User's email address</div>
                                        <div class="col-6"><code>{{ '$user->phone' }}</code></div>
                                        <div class="col-6">User's phone number</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#bookingVars">
                                    Booking Variables
                                </button>
                            </h2>
                            <div id="bookingVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code>{{ '$booking->id' }}</code></div>
                                        <div class="col-6">Booking ID</div>
                                        <div class="col-6"><code>{{ '$booking->pickup_location' }}</code></div>
                                        <div class="col-6">Pickup location</div>
                                        <div class="col-6"><code>{{ '$booking->dropoff_location' }}</code></div>
                                        <div class="col-6">Drop-off location</div>
                                        <div class="col-6"><code>{{ '$booking->pickup_datetime' }}</code></div>
                                        <div class="col-6">Pickup date and time</div>
                                        <div class="col-6"><code>{{ '$booking->total_amount' }}</code></div>
                                        <div class="col-6">Total booking amount</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#systemVars">
                                    System Variables
                                </button>
                            </h2>
                            <div id="systemVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code>{{ 'config("app.name")' }}</code></div>
                                        <div class="col-6">Application name</div>
                                        <div class="col-6"><code>{{ 'config("app.url")' }}</code></div>
                                        <div class="col-6">Application URL</div>
                                        <div class="col-6"><code>{{ 'now()->format("Y")' }}</code></div>
                                        <div class="col-6">Current year</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="showTestEmailForm()">Send Test Email</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Test Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="testEmailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="testEmailAddress" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="testEmailAddress" required
                               placeholder="Enter email address to send test email">
                    </div>
                    <div class="mb-3">
                        <label for="testEmailTemplate" class="form-label">Template</label>
                        <input type="text" class="form-control" id="testEmailTemplate" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Test Data</label>
                        <div class="form-text">The email will be sent with sample data for testing purposes.</div>
                    </div>
                    <div id="testEmailAlert" class="alert d-none"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="sendTestBtn">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="testEmailSpinner"></span>
                        Send Test Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Template Editor Modal -->
<div class="modal fade" id="templateEditorModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Email Template: <span id="editorTemplateName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="templateEditorForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- Template Content Editor -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-code"></i> Template Content</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="templateSubject" class="form-label">Email Subject</label>
                                        <input type="text" class="form-control" id="templateSubject"
                                               placeholder="Enter email subject">
                                    </div>
                                    <div class="mb-3">
                                        <label for="templateContent" class="form-label">Email Content</label>
                                        <textarea class="form-control" id="templateContent" rows="15"
                                                  placeholder="Enter email template content..."></textarea>
                                    </div>
                                    <div class="form-text">
                                        <strong>Available Variables:</strong> Use variables like {{ '$user->name' }}, {{ '$booking->id' }}, etc.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Template Variables & Preview -->
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-eye"></i> Live Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div id="templatePreview" class="border rounded p-3" style="min-height: 200px; background-color: #f8f9fa;">
                                        <p class="text-muted">Preview will appear here as you type...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-tags"></i> Quick Variables</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>User Variables:</strong>
                                        <div class="d-flex flex-wrap gap-1 mt-1">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="insertVariable('$user->name')">Name</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="insertVariable('$user->email')">Email</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="insertVariable('$user->phone')">Phone</button>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Booking Variables:</strong>
                                        <div class="d-flex flex-wrap gap-1 mt-1">
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="insertVariable('$booking->id')">ID</button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="insertVariable('$booking->pickup_location')">Pickup</button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="insertVariable('$booking->dropoff_location')">Dropoff</button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="insertVariable('$booking->total_amount')">Amount</button>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>System Variables:</strong>
                                        <div class="d-flex flex-wrap gap-1 mt-1">
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="insertVariable('config(\"app.name\")')">App Name</button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="insertVariable('now()->year')">Year</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="templateEditorAlert" class="alert d-none mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-info" onclick="previewTemplate()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveTemplateBtn">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="templateSaveSpinner"></span>
                        <i class="fas fa-save"></i> Save Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editTemplate(templateName) {
    // Open template editor modal (function defined in template-editor.js)
    showTemplateEditor(templateName);
}

function showTestEmailForm() {
    const testModal = new bootstrap.Modal(document.getElementById('testEmailModal'));
    const templateInput = document.getElementById('testEmailTemplate');
    const emailInput = document.getElementById('testEmailAddress');
    const alert = document.getElementById('testEmailAlert');

    // Set current template
    templateInput.value = currentTemplate.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Clear previous values
    emailInput.value = '';
    alert.classList.add('d-none');

    // Hide preview modal and show test email modal
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('templatePreviewModal'));
    if (previewModal) {
        previewModal.hide();
    }

    testModal.show();
}

function sendTestEmail() {
    const form = document.getElementById('testEmailForm');
    const emailAddress = document.getElementById('testEmailAddress').value;
    const sendBtn = document.getElementById('sendTestBtn');
    const spinner = document.getElementById('testEmailSpinner');
    const alert = document.getElementById('testEmailAlert');

    if (!emailAddress) {
        showAlert('Please enter an email address.', 'danger');
        return;
    }

    // Show loading state
    sendBtn.disabled = true;
    spinner.classList.remove('d-none');
    alert.classList.add('d-none');

    // Prepare form data
    const formData = new FormData();
    formData.append('email', emailAddress);
    formData.append('template', currentTemplate);
    formData.append('_token', '<?php echo e(csrf_token()); ?>');

    // Send test email
    fetch('<?php echo e(route("admin.emails.test")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Test email sent successfully!', 'success');
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('testEmailModal'));
                modal.hide();
            }, 2000);
        } else {
            showAlert(data.message || 'Failed to send test email.', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while sending the test email.', 'danger');
    })
    .finally(() => {
        // Hide loading state
        sendBtn.disabled = false;
        spinner.classList.add('d-none');
    });
}

function showAlert(message, type) {
    const alert = document.getElementById('testEmailAlert');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.classList.remove('d-none');
}

// Update previewTemplate to store current template
function previewTemplate(templateName) {
    currentTemplate = templateName;
    const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    const content = document.getElementById('templatePreviewContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Simulate loading template preview
    setTimeout(() => {
        content.innerHTML = `
            <div class="border rounded p-3" style="background-color: #f8f9fa;">
                <h4>Preview: ${templateName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                <hr>
                <div class="email-preview">
                    <p><strong>Subject:</strong> Your ${templateName.includes('booking') ? 'Booking' : 'Account'} Update</p>
                    <div class="mt-3">
                        <p>Dear [User Name],</p>
                        <p>This is a preview of the ${templateName.replace('_', ' ')} email template.</p>
                        <p>Template variables will be replaced with actual values when sent.</p>
                        <p>Best regards,<br>YNR Cars Team</p>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

// Template editor functions
let currentTemplate = '';

function showTemplateEditor(templateName) {
    currentTemplate = templateName;
    const modal = new bootstrap.Modal(document.getElementById('templateEditorModal'));
    const templateNameSpan = document.getElementById('editorTemplateName');
    const subjectInput = document.getElementById('templateSubject');
    const contentTextarea = document.getElementById('templateContent');
    const alert = document.getElementById('templateEditorAlert');

    // Set template name
    templateNameSpan.textContent = templateName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Clear previous content
    subjectInput.value = '';
    contentTextarea.value = '';
    alert.classList.add('d-none');

    // Load template content
    loadTemplateContent(templateName);

    modal.show();
}

function loadTemplateContent(templateName) {
    const subjectInput = document.getElementById('templateSubject');
    const contentTextarea = document.getElementById('templateContent');

    // Show loading
    subjectInput.value = 'Loading...';
    contentTextarea.value = 'Loading template content...';

    // Fetch template content
    fetch('/admin/emails/templates/load/' + templateName, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            subjectInput.value = data.subject || getDefaultSubject(templateName);
            contentTextarea.value = data.content || getDefaultContent(templateName);
            updatePreview();
        } else {
            // Use default content if template doesn't exist
            subjectInput.value = getDefaultSubject(templateName);
            contentTextarea.value = getDefaultContent(templateName);
            updatePreview();
        }
    })
    .catch(error => {
        console.error('Error loading template:', error);
        // Use default content on error
        subjectInput.value = getDefaultSubject(templateName);
        contentTextarea.value = getDefaultContent(templateName);
        updatePreview();
    });
}

function getDefaultSubject(templateName) {
    const subjects = {
        'booking_confirmation': <?php echo json_encode('Booking Confirmation - <?php echo e($booking->id); ?>'); ?>,
        'booking_cancelled': <?php echo json_encode('Booking Cancelled - <?php echo e($booking->id); ?>'); ?>,
        'booking_reminder': <?php echo json_encode('Booking Reminder - <?php echo e($booking->id); ?>'); ?>,
        'driver_assigned': <?php echo json_encode('Driver Assigned - <?php echo e($booking->id); ?>'); ?>,
        'payment_confirmation': <?php echo json_encode('Payment Confirmation - <?php echo e($payment->transaction_id); ?>'); ?>,
        'user_welcome': <?php echo json_encode('Welcome to <?php echo e(config("app.name")); ?>'); ?>,
        'password_reset': 'Reset Your Password',
        'email_verification': 'Verify Your Email Address'
    };
    return subjects[templateName] || <?php echo json_encode('Email from <?php echo e(config("app.name")); ?>'); ?>;
}

function getDefaultContent(templateName) {
    const baseTemplate = <?php echo json_encode('<h2><?php echo e(config(\'app.name\')); ?></h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>This is a TEMPLATE_TYPE_PLACEHOLDER email.</p>

<p>Best regards,<br>
<?php echo e(config(\'app.name\')); ?> Team</p>

<hr>
<p><small>&copy; <?php echo e(now()->year); ?> <?php echo e(config(\'app.name\')); ?>. All rights reserved.</small></p>'); ?>;

    return baseTemplate.replace('TEMPLATE_TYPE_PLACEHOLDER', templateName.replace('_', ' '));
}

function insertVariable(variable) {
    const contentTextarea = document.getElementById('templateContent');
    const cursorPos = contentTextarea.selectionStart;
    const textBefore = contentTextarea.value.substring(0, cursorPos);
    const textAfter = contentTextarea.value.substring(contentTextarea.selectionEnd);

    contentTextarea.value = textBefore + '<?php echo "<?php echo e("; ?> ' + variable + ' <?php echo "); ?>"; ?>' + textAfter;
    contentTextarea.focus();
    contentTextarea.setSelectionRange(cursorPos + variable.length + 6, cursorPos + variable.length + 6);

    updatePreview();
}

function updatePreview() {
    const content = document.getElementById('templateContent').value;
    const preview = document.getElementById('templatePreview');

    // Simple preview - replace variables with sample data
    let previewContent = content
        .replace(/<?php echo "<?php echo e("; ?>\s*\$user->name\s*<?php echo "); ?>"; ?>/g, 'John Doe')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$user->email\s*<?php echo "); ?>"; ?>/g, '<EMAIL>')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$user->phone\s*<?php echo "); ?>"; ?>/g, '+****************')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->id\s*<?php echo "); ?>"; ?>/g, 'BK-1234')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->pickup_location\s*<?php echo "); ?>"; ?>/g, '123 Main Street')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->dropoff_location\s*<?php echo "); ?>"; ?>/g, '456 Oak Avenue')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->total_amount\s*<?php echo "); ?>"; ?>/g, '$85.00')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$payment->transaction_id\s*<?php echo "); ?>"; ?>/g, 'TXN-789012')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$payment->amount\s*<?php echo "); ?>"; ?>/g, '$85.00')
        .replace(/<?php echo "<?php echo e("; ?>\s*config\("app\.name"\)\s*<?php echo "); ?>"; ?>/g, 'YNR Cars')
        .replace(/<?php echo "<?php echo e("; ?>\s*now\(\)->year\s*<?php echo "); ?>"; ?>/g, new Date().getFullYear())
        // Also handle regular blade syntax for existing templates
        .replace(/\{\{\s*\$user->name\s*\}\}/g, 'John Doe')
        .replace(/\{\{\s*\$user->email\s*\}\}/g, '<EMAIL>')
        .replace(/\{\{\s*\$user->phone\s*\}\}/g, '+****************')
        .replace(/\{\{\s*\$booking->id\s*\}\}/g, 'BK-1234')
        .replace(/\{\{\s*\$booking->pickup_location\s*\}\}/g, '123 Main Street')
        .replace(/\{\{\s*\$booking->dropoff_location\s*\}\}/g, '456 Oak Avenue')
        .replace(/\{\{\s*\$booking->total_amount\s*\}\}/g, '$85.00')
        .replace(/\{\{\s*\$payment->transaction_id\s*\}\}/g, 'TXN-789012')
        .replace(/\{\{\s*\$payment->amount\s*\}\}/g, '$85.00')
        .replace(/\{\{\s*config\("app\.name"\)\s*\}\}/g, 'YNR Cars')
        .replace(/\{\{\s*now\(\)->year\s*\}\}/g, new Date().getFullYear());

    preview.innerHTML = previewContent || '<p class="text-muted">Preview will appear here as you type...</p>';
}

function previewTemplate() {
    const subject = document.getElementById('templateSubject').value;
    const content = document.getElementById('templateContent').value;

    // Hide editor modal temporarily
    const editorModal = bootstrap.Modal.getInstance(document.getElementById('templateEditorModal'));
    editorModal.hide();

    // Show preview in the main preview modal
    const previewModal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    const previewContent = document.getElementById('templatePreviewContent');

    // Apply same preview logic as updatePreview
    let processedContent = content
        .replace(/<?php echo "<?php echo e("; ?>\s*\$user->name\s*<?php echo "); ?>"; ?>/g, 'John Doe')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$user->email\s*<?php echo "); ?>"; ?>/g, '<EMAIL>')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->id\s*<?php echo "); ?>"; ?>/g, 'BK-1234')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->pickup_location\s*<?php echo "); ?>"; ?>/g, '123 Main Street')
        .replace(/<?php echo "<?php echo e("; ?>\s*\$booking->total_amount\s*<?php echo "); ?>"; ?>/g, '$85.00')
        .replace(/<?php echo "<?php echo e("; ?>\s*config\("app\.name"\)\s*<?php echo "); ?>"; ?>/g, 'YNR Cars')
        .replace(/<?php echo "<?php echo e("; ?>\s*now\(\)->year\s*<?php echo "); ?>"; ?>/g, new Date().getFullYear())
        // Also handle regular blade syntax
        .replace(/\{\{\s*\$user->name\s*\}\}/g, 'John Doe')
        .replace(/\{\{\s*\$user->email\s*\}\}/g, '<EMAIL>')
        .replace(/\{\{\s*\$booking->id\s*\}\}/g, 'BK-1234')
        .replace(/\{\{\s*\$booking->pickup_location\s*\}\}/g, '123 Main Street')
        .replace(/\{\{\s*\$booking->total_amount\s*\}\}/g, '$85.00')
        .replace(/\{\{\s*config\("app\.name"\)\s*\}\}/g, 'YNR Cars')
        .replace(/\{\{\s*now\(\)->year\s*\}\}/g, new Date().getFullYear());

    previewContent.innerHTML = `
        <div class="border rounded p-3" style="background-color: #f8f9fa;">
            <h4>Subject: ${subject}</h4>
            <hr>
            <div class="email-preview">
                ${processedContent}
            </div>
        </div>
    `;

    previewModal.show();

    // Re-show editor modal when preview is closed
    document.getElementById('templatePreviewModal').addEventListener('hidden.bs.modal', function() {
        editorModal.show();
    }, { once: true });
}

function saveTemplate() {
    const templateName = currentTemplate;
    const subject = document.getElementById('templateSubject').value;
    const content = document.getElementById('templateContent').value;
    const saveBtn = document.getElementById('saveTemplateBtn');
    const spinner = document.getElementById('templateSaveSpinner');
    const alert = document.getElementById('templateEditorAlert');

    if (!subject.trim() || !content.trim()) {
        showTemplateAlert('Please fill in both subject and content.', 'danger');
        return;
    }

    // Show loading state
    saveBtn.disabled = true;
    spinner.classList.remove('d-none');
    alert.classList.add('d-none');

    // Prepare form data
    const formData = new FormData();
    formData.append('template', templateName);
    formData.append('subject', subject);
    formData.append('content', content);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    // Save template
    fetch('/admin/emails/templates/save', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showTemplateAlert('Template saved successfully!', 'success');
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('templateEditorModal'));
                modal.hide();
            }, 2000);
        } else {
            showTemplateAlert(data.message || 'Failed to save template.', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showTemplateAlert('An error occurred while saving the template.', 'danger');
    })
    .finally(() => {
        // Hide loading state
        saveBtn.disabled = false;
        spinner.classList.add('d-none');
    });
}

function showTemplateAlert(message, type) {
    const alert = document.getElementById('templateEditorAlert');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.classList.remove('d-none');
}

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    const testEmailForm = document.getElementById('testEmailForm');
    if (testEmailForm) {
        testEmailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendTestEmail();
        });
    }

    const templateEditorForm = document.getElementById('templateEditorForm');
    if (templateEditorForm) {
        templateEditorForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveTemplate();
        });
    }

    // Add live preview update
    const contentTextarea = document.getElementById('templateContent');
    if (contentTextarea) {
        contentTextarea.addEventListener('input', updatePreview);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\emails\templates.blade.php ENDPATH**/ ?>