<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $client;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = User::factory()->client()->create();
        $vehicle = Vehicle::factory()->create();
        
        $this->booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $vehicle->id,
            'amount' => 50.00,
            'status' => 'confirmed',
            'payment_status' => 'pending',
        ]);
    }

    /** @test */
    public function client_can_initiate_paypal_payment()
    {
        $this->actingAs($this->client);

        $response = $this->post(route('payment.paypal.create'), [
            'booking_id' => $this->booking->id,
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();
        
        $this->assertArrayHasKey('approval_url', $responseData);
        $this->assertStringContains('paypal.com', $responseData['approval_url']);
    }

    /** @test */
    public function payment_success_updates_booking_status()
    {
        $this->actingAs($this->client);

        // Create a payment record
        $payment = Payment::create([
            'booking_id' => $this->booking->id,
            'user_id' => $this->client->id,
            'transaction_id' => 'TEST_TXN_' . time(),
            'payment_method' => 'paypal',
            'amount' => $this->booking->amount,
            'status' => 'completed',
            'payment_details' => json_encode(['test' => true]),
        ]);

        // Simulate payment success callback
        $response = $this->post(route('payment.paypal.success'), [
            'booking_id' => $this->booking->id,
            'payment_id' => $payment->id,
            'payer_id' => 'TEST_PAYER_ID',
        ]);

        $response->assertRedirect();
        
        $this->booking->refresh();
        $this->assertEquals('completed', $this->booking->payment_status);
        $this->assertNotNull($this->booking->payment_processed_at);
    }

    /** @test */
    public function payment_failure_keeps_booking_pending()
    {
        $this->actingAs($this->client);

        $response = $this->post(route('payment.paypal.cancel'), [
            'booking_id' => $this->booking->id,
        ]);

        $response->assertRedirect();
        
        $this->booking->refresh();
        $this->assertEquals('pending', $this->booking->payment_status);
    }

    /** @test */
    public function admin_can_record_manual_payment()
    {
        $admin = User::factory()->admin()->create();
        $this->actingAs($admin);

        $response = $this->post(route('admin.payments.store'), [
            'booking_id' => $this->booking->id,
            'payment_method' => 'cash',
            'amount' => $this->booking->amount,
            'transaction_id' => 'MANUAL_' . time(),
            'notes' => 'Cash payment received',
        ]);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('payments', [
            'booking_id' => $this->booking->id,
            'payment_method' => 'cash',
            'amount' => $this->booking->amount,
            'status' => 'completed',
        ]);

        $this->booking->refresh();
        $this->assertEquals('completed', $this->booking->payment_status);
    }

    /** @test */
    public function payment_amount_must_match_booking_amount()
    {
        $admin = User::factory()->admin()->create();
        $this->actingAs($admin);

        $response = $this->post(route('admin.payments.store'), [
            'booking_id' => $this->booking->id,
            'payment_method' => 'cash',
            'amount' => 25.00, // Different from booking amount
            'transaction_id' => 'MANUAL_' . time(),
        ]);

        $response->assertSessionHasErrors('amount');
    }

    /** @test */
    public function cannot_pay_for_already_paid_booking()
    {
        $this->actingAs($this->client);

        // Mark booking as already paid
        $this->booking->update(['payment_status' => 'completed']);

        $response = $this->post(route('payment.paypal.create'), [
            'booking_id' => $this->booking->id,
        ]);

        $response->assertStatus(400);
        $responseData = $response->json();
        $this->assertArrayHasKey('error', $responseData);
    }

    /** @test */
    public function payment_creates_booking_history()
    {
        $this->actingAs($this->client);

        // Create a successful payment
        $payment = Payment::create([
            'booking_id' => $this->booking->id,
            'user_id' => $this->client->id,
            'transaction_id' => 'TEST_TXN_' . time(),
            'payment_method' => 'paypal',
            'amount' => $this->booking->amount,
            'status' => 'completed',
            'payment_details' => json_encode(['test' => true]),
        ]);

        // Check if booking history was created
        $this->assertDatabaseHas('booking_histories', [
            'booking_id' => $this->booking->id,
            'action' => 'payment_completed',
        ]);
    }

    /** @test */
    public function admin_can_process_refund()
    {
        $admin = User::factory()->admin()->create();
        $this->actingAs($admin);

        // Create a completed payment
        $payment = Payment::create([
            'booking_id' => $this->booking->id,
            'user_id' => $this->client->id,
            'transaction_id' => 'TEST_TXN_' . time(),
            'payment_method' => 'paypal',
            'amount' => $this->booking->amount,
            'status' => 'completed',
        ]);

        $this->booking->update(['payment_status' => 'completed']);

        $response = $this->post(route('admin.payments.refund', $payment), [
            'refund_amount' => $this->booking->amount,
            'refund_reason' => 'Customer request',
        ]);

        $response->assertRedirect();
        
        $payment->refresh();
        $this->assertEquals('refunded', $payment->status);
        $this->assertEquals($this->booking->amount, $payment->refunded_amount);
        $this->assertNotNull($payment->refunded_at);
    }
}
