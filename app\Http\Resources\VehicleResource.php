<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'model' => $this->model,
            'category' => $this->category,
            'seats' => $this->seats,
            'luggage_capacity' => $this->luggage_capacity,
            'transmission' => $this->transmission,
            'image' => $this->image ? asset('storage/' . $this->image) : null,
            'is_active' => $this->is_active,
            'pricing' => [
                'price_per_km' => $this->price_per_km,
                'price_per_hour' => $this->price_per_hour,
                'base_fare' => $this->base_fare,
                'tax_rate' => $this->tax_rate,
                'airport_surcharge' => $this->airport_surcharge,
                'booking_fee' => $this->booking_fee,
                'waiting_fee_per_minute' => $this->waiting_fee_per_minute,
                'cancellation_fee' => $this->cancellation_fee,
                'night_surcharge' => $this->night_surcharge,
                'weekend_surcharge' => $this->weekend_surcharge,
                'holiday_surcharge' => $this->holiday_surcharge,
            ],
            'features' => $this->features,
            'description' => $this->description,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
