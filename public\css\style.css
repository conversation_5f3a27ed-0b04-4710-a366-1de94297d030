/*
* Ynr Cars - Main Stylesheet
* Version: 1.0
*/

/* ===== PRELOADER ===== */

* {
    box-sizing: border-box;
}

.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
}

.preloader-logo {
    width: 150px;
    margin-bottom: 20px;
}

.preloader-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin: 0 auto;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #ee393d;
    --primary-dark: #9e3133;
    --secondary-color: #343a40;
    --text-color: #333;
    --text-light: #6c757d;
    --white: #fff;
    --black: #000;
    --light-bg: #f8f9fa;
    --border-color: #eee;
    --shadow: 0 10px 30px rgba(0,0,0,0.08);
    --shadow-hover: 0 15px 35px rgba(0,0,0,0.15);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    background-color: var(--light-bg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

.section-padding {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.section-title p {
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    font-size: 1.1rem;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 20px auto 0;
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: 5px;
    padding: 10px 25px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--black);
}

.btn-lg {
    padding: 15px 40px;
    font-size: 1.1rem;
}

/* ===== HEADER & NAVIGATION ===== */
.navbar {
    background-color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: black;
    font-weight: 500;
    padding: 0 15px;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.dropdown-menu {
    background-color: var(--black);
    border: none;
    border-radius: 5px;
    padding: 10px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.dropdown-item {
    color: var(--white);
    padding: 8px 20px;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: #333;
    color: var(--primary-color);
}

/* ===== PAGE HEADER ===== */
.page-header {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 60px 0;
    margin-bottom: 40px;
    position: relative;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: var(--primary-color);
}

.page-header h1 {
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--white);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--white);
}

.content-section {
    padding: 60px 0;
}

/* ===== HERO SECTION ===== */
.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1550355291-bbee04a92027?ixlib=rb-4.0.3&auto=format&fit=crop&w=1936&q=80');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 150px 0 100px;
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 30px;
    font-weight: 300;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* ===== BOOKING FORM ===== */
.booking-form-container {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 30px;
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hero booking form specific styles */
.hero-booking-form {
    margin-top: 0;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.booking-form .nav-tabs {
    border-bottom: none;
    margin-bottom: 25px;
    justify-content: center;
}

.booking-form .nav-link {
    color: var(--white);
    border: none;
    padding: 12px 25px;
    border-radius: 30px;
    margin-right: 10px;
    font-weight: 500;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.1);
}

.booking-form .nav-link:hover {
    background-color: rgba(248, 193, 44, 0.7);
    color: var(--black);
}

.booking-form .nav-link.active {
    background-color: var(--primary-color);
    color: var(--black);
}

.booking-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 12px 15px;
    height: auto;
    border-radius: 5px;
}

.booking-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(248, 193, 44, 0.5);
    box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
}

.booking-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.booking-form .form-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.booking-form .btn-primary {
    padding: 12px 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 5px;
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 25px;
}

.card-title {
    font-weight: 700;
    margin-bottom: 15px;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--black);
    color: var(--white);
    padding: 50px 0 20px;
    margin-top: auto;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.footer ul {
    list-style: none;
    padding-left: 0;
}

.footer ul li {
    margin-bottom: 10px;
}

.footer ul li a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.footer ul li a:hover {
    color: var(--primary-color);
}

.social-icons a {
    color: var(--white);
    font-size: 20px;
    margin-right: 15px;
    transition: var(--transition);
}

.social-icons a:hover {
    color: var(--primary-color);
}

.copyright {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-us {
    background-color: var(--white);
}

.feature-card {
    display: flex;
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    background-color: var(--white);
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-right: 20px;
    min-width: 60px;
    text-align: center;
}

.feature-content h4 {
    font-weight: 600;
    margin-bottom: 10px;
}

.feature-content p {
    color: var(--text-light);
    margin-bottom: 0;
}

/* ===== SERVICE CARDS ===== */
.service-card {
    text-align: center;
    padding: 40px 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    background-color: var(--white);
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    font-size: 3.5rem;
    color: var(--primary-color);
    margin-bottom: 25px;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-card h4 {
    font-weight: 600;
    margin-bottom: 15px;
}

/* ===== VEHICLE CARDS ===== */
.vehicle-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.vehicle-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-hover);
}

.vehicle-img {
    height: 220px;
    object-fit: cover;
}

.vehicle-card .card-body {
    padding: 25px;
}

.vehicle-card .card-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.vehicle-details {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.vehicle-detail {
    text-align: center;
}

.vehicle-detail i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.vehicle-detail p {
    font-size: 0.9rem;
    margin-bottom: 0;
    color: var(--text-light);
}

/* ===== TESTIMONIALS ===== */
.testimonial-card {
    text-align: center;
    padding: 40px 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    background-color: var(--white);
    height: 100%;
    position: relative;
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.testimonial-card:before {
    content: '\201C';
    font-size: 5rem;
    color: var(--primary-color);
    opacity: 0.2;
    position: absolute;
    top: 10px;
    left: 20px;
    font-family: Georgia, serif;
}

.testimonial-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 20px;
    border: 3px solid var(--primary-color);
}

.testimonial-rating {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.testimonial-text {
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 20px;
}

.testimonial-author {
    font-weight: 600;
    margin-bottom: 5px;
}

.testimonial-position {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* ===== CTA SECTION ===== */
.cta-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('../images/Cars-image.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--white);
    text-align: center;
    padding: 100px 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 40px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 300;
}

.cta-btn {
    padding: 15px 40px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 5px;
    font-size: 1.1rem;
}

/* ===== BLOG CARDS ===== */
.blog-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-hover);
}

.blog-img {
    height: 200px;
    object-fit: cover;
}

.blog-card .card-body {
    padding: 25px;
}

.blog-category {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.blog-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.blog-title {
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.4;
}

.blog-excerpt {
    color: var(--text-light);
    margin-bottom: 20px;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .feature-card {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-padding {
        padding: 50px 0;
    }

    .navbar-nav .nav-link {
        padding: 10px 15px;
    }

    .vehicle-details {
        flex-wrap: wrap;
    }

    .vehicle-detail {
        width: 50%;
        margin-bottom: 15px;
    }
}

@media (max-width: 575.98px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn-lg {
        padding: 10px 25px;
        font-size: 1rem;
    }

    .cta-title {
    font-size: 1.4rem;
    }

    .testimonial-card {
        padding: 30px 20px;
    }
}
