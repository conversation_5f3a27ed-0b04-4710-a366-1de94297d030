<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'email_booking_updates',
        'email_promotions',
        'email_ride_updates',
        'email_earnings',
        'email_announcements',
        'sms_booking_updates',
        'sms_promotions',
        'sms_ride_updates',
        'sms_earnings',
        'sms_announcements',
        'push_booking_updates',
        'push_promotions',
        'push_ride_updates',
        'push_earnings',
        'push_announcements',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_booking_updates' => 'boolean',
        'email_promotions' => 'boolean',
        'email_ride_updates' => 'boolean',
        'email_earnings' => 'boolean',
        'email_announcements' => 'boolean',
        'sms_booking_updates' => 'boolean',
        'sms_promotions' => 'boolean',
        'sms_ride_updates' => 'boolean',
        'sms_earnings' => 'boolean',
        'sms_announcements' => 'boolean',
        'push_booking_updates' => 'boolean',
        'push_promotions' => 'boolean',
        'push_ride_updates' => 'boolean',
        'push_earnings' => 'boolean',
        'push_announcements' => 'boolean',
    ];

    /**
     * Get the user that owns the notification settings.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
