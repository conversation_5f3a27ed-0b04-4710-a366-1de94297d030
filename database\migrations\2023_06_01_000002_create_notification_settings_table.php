<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // Client notification settings
            $table->boolean('email_booking_updates')->default(true);
            $table->boolean('email_promotions')->default(true);
            $table->boolean('sms_booking_updates')->default(true);
            $table->boolean('sms_promotions')->default(false);
            $table->boolean('push_booking_updates')->default(true);
            $table->boolean('push_promotions')->default(false);
            
            // Driver notification settings
            $table->boolean('email_ride_updates')->default(true);
            $table->boolean('email_earnings')->default(true);
            $table->boolean('email_announcements')->default(true);
            $table->boolean('sms_ride_updates')->default(true);
            $table->boolean('sms_earnings')->default(false);
            $table->boolean('sms_announcements')->default(false);
            $table->boolean('push_ride_updates')->default(true);
            $table->boolean('push_earnings')->default(true);
            $table->boolean('push_announcements')->default(false);
            
            $table->timestamps();
            
            // Ensure a user can only have one notification settings record
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_settings');
    }
};
