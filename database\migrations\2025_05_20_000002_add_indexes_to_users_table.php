<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add indexes to improve query performance
            $table->index('role');
            $table->index('is_active');
            $table->index('is_available');
            $table->index('insurance_expiry');
            $table->index('mot_expiry');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['role']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['is_available']);
            $table->dropIndex(['insurance_expiry']);
            $table->dropIndex(['mot_expiry']);
        });
    }
};
