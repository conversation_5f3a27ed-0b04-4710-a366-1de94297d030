<?php $__env->startSection('title', 'Vehicle Reports'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-car me-2 text-primary"></i> Vehicle Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-primary" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Vehicle Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Vehicles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($totalVehicles); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-car fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Vehicles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($activeVehicles); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vehicle Type Distribution -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vehicle Type Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="vehicleTypeChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vehicle Types Summary</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle Type</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e(ucfirst($type->type)); ?></td>
                                    <td><?php echo e($type->count); ?></td>
                                    <td><?php echo e(number_format(($type->count / $totalVehicles) * 100, 1)); ?>%</td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Vehicles by Bookings -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Most Popular Vehicles</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Total Bookings</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topVehiclesByBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($vehicle->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" 
                                                     class="rounded me-2" width="40" height="30" style="object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="font-weight-bold"><?php echo e($vehicle->name); ?></div>
                                                <small class="text-muted"><?php echo e($vehicle->type); ?> - <?php echo e($vehicle->model); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary"><?php echo e($vehicle->bookings_count); ?></span>
                                    </td>
                                    <td>
                                        <?php if($vehicle->is_active): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Vehicles by Revenue -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Revenue Generating Vehicles</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Total Revenue</th>
                                    <th>Avg. per Booking</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topVehiclesByRevenue; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($vehicle->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" 
                                                     class="rounded me-2" width="40" height="30" style="object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="font-weight-bold"><?php echo e($vehicle->name); ?></div>
                                                <small class="text-muted"><?php echo e($vehicle->seats); ?> seats</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-success font-weight-bold">
                                            $<?php echo e(number_format($vehicle->bookings_sum_amount ?? 0, 2)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                            $completedBookings = $vehicle->bookings()->where('status', 'completed')->count();
                                            $avgPerBooking = $completedBookings > 0 ? ($vehicle->bookings_sum_amount ?? 0) / $completedBookings : 0;
                                        ?>
                                        <span class="text-info">
                                            $<?php echo e(number_format($avgPerBooking, 2)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Vehicle Type Distribution Chart
const vehicleTypeCtx = document.getElementById('vehicleTypeChart').getContext('2d');
const vehicleTypeChart = new Chart(vehicleTypeCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($vehicleTypes->pluck('type')->map(function($type) { return ucfirst($type); }), 15, 512) ?>,
        datasets: [{
            data: <?php echo json_encode($vehicleTypes->pluck('count'), 15, 512) ?>,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reports\vehicles.blade.php ENDPATH**/ ?>