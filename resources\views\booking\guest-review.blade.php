@extends('layouts.guest')

@section('title', 'Review Your Booking')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/booking.css') }}">
@endsection

@section('content')
<div class="guest-review-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="guest-review-card">
                    <div class="card-header">
                        <h3 class="mb-0">Review Your Booking</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <h5 class="mb-4">Booking Summary</h5>

                                <div class="booking-summary">
                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Booking Type:</div>
                                        <div class="booking-summary-value">
                                            @if($bookingData['booking_type'] == 'one_way')
                                                One Way
                                            @elseif($bookingData['booking_type'] == 'return')
                                                Return
                                            @elseif($bookingData['booking_type'] == 'airport')
                                                Airport Transfer
                                            @else
                                                Hourly ({{ $bookingData['duration_hours'] }} hours)
                                            @endif
                                        </div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup Address:</div>
                                        <div class="booking-summary-value">{{ $bookingData['pickup_address'] }}</div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup Date & Time:</div>
                                        <div class="booking-summary-value">{{ \Carbon\Carbon::parse($bookingData['pickup_date'])->format('M d, Y h:i A') }}</div>
                                    </div>

                                    @if($bookingData['booking_type'] != 'hourly')
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Dropoff Address:</div>
                                            <div class="booking-summary-value">{{ $bookingData['dropoff_address'] }}</div>
                                        </div>
                                    @endif

                                    @if(isset($bookingData['via_points']) && !empty($bookingData['via_points']))
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Via Points:</div>
                                            <div class="booking-summary-value">
                                                {{ count($bookingData['via_points']) }} stop{{ count($bookingData['via_points']) > 1 ? 's' : '' }}
                                                @if(isset($bookingData['via_surcharge']) && $bookingData['via_surcharge'] > 0)
                                                    <small class="text-muted">(+{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['via_surcharge'], 2) }})</small>
                                                @endif
                                            </div>
                                        </div>
                                        @foreach($bookingData['via_points'] as $index => $viaPoint)
                                            <div class="booking-summary-item" style="margin-left: 20px; font-size: 0.9em;">
                                                <div class="booking-summary-label">{{ $index + 1 }}. {{ $viaPoint['address'] }}</div>
                                                <div class="booking-summary-value">
                                                    @if(!empty($viaPoint['notes']))
                                                        <small class="text-muted">{{ $viaPoint['notes'] }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif

                                    @if($bookingData['booking_type'] == 'return' && isset($bookingData['return_date']))
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Return Date & Time:</div>
                                            <div class="booking-summary-value">{{ \Carbon\Carbon::parse($bookingData['return_date'])->format('M d, Y h:i A') }}</div>
                                        </div>
                                    @endif

                                    @if(isset($bookingData['notes']) && $bookingData['notes'])
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Additional Notes:</div>
                                            <div class="booking-summary-value">{{ $bookingData['notes'] }}</div>
                                        </div>
                                    @endif

                                    <div class="booking-total">
                                        <div class="booking-total-label">Total Amount:</div>
                                        <div class="booking-total-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['amount'], 2) }}</div>
                                    </div>
                                </div>

                                @if(isset($bookingData['fare_details']) && is_array($bookingData['fare_details']))
                                <div class="fare-details mt-4">
                                    <h6 class="mb-3">Fare Breakdown</h6>

                                    <div class="fare-row">
                                        <div class="fare-label">Base Fare:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['base_fare'], 2) }}</div>
                                    </div>

                                    @if(isset($bookingData['fare_details']['distance_km']))
                                    <div class="fare-row">
                                        <div class="fare-label">Distance ({{ $bookingData['fare_details']['distance_km'] }} ml):</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['distance_fare'], 2) }}</div>
                                    </div>
                                    @endif

                                    @if(isset($bookingData['fare_details']['booking_fee']))
                                    <div class="fare-row">
                                        <div class="fare-label">Booking Fee:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['booking_fee'], 2) }}</div>
                                    </div>
                                    @endif

                                    @if(isset($bookingData['fare_details']['airport_surcharge']) && $bookingData['fare_details']['airport_surcharge'] > 0)
                                    <div class="fare-row">
                                        <div class="fare-label">Airport Surcharge:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['airport_surcharge'], 2) }}</div>
                                    </div>
                                    @endif

                                    @if(isset($bookingData['fare_details']['weekend_surcharge']) && $bookingData['fare_details']['weekend_surcharge'] > 0)
                                    <div class="fare-row">
                                        <div class="fare-label">Weekend Surcharge:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['weekend_surcharge'], 2) }}</div>
                                    </div>
                                    @endif

                                    @if(isset($bookingData['fare_details']['night_surcharge']) && $bookingData['fare_details']['night_surcharge'] > 0)
                                    <div class="fare-row">
                                        <div class="fare-label">Night Surcharge:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['fare_details']['night_surcharge'], 2) }}</div>
                                    </div>
                                    @endif

                                    @if(isset($bookingData['via_surcharge']) && $bookingData['via_surcharge'] > 0)
                                    <div class="fare-row">
                                        <div class="fare-label">Via Points Surcharge:</div>
                                        <div class="fare-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingData['via_surcharge'], 2) }}</div>
                                    </div>
                                    @endif
                                </div>
                                @endif
                            </div>

                            <div style="max-height: 600px;" class="col-md-5">
                                <h5 class="mb-4">Vehicle Details</h5>

                                <div class="vehicle-card">
                                    <div class="vehicle-image-container">
                                        @if($vehicle->image)
                                            <img src="{{ asset('storage/' . $vehicle->image) }}" class="vehicle-image" alt="{{ $vehicle->name }}">
                                        @else
                                            <div class="vehicle-image bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-car fa-3x text-muted"></i>
                                            </div>
                                        @endif
                                        <div class="vehicle-price-tag">
                                            {{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($vehicle->price_per_km, 2) }}/ml
                                        </div>
                                    </div>

                                    <div class="vehicle-info">
                                        <h5 class="vehicle-name">{{ $vehicle->name }}</h5>
                                        <p class="vehicle-type">{{ ucfirst($vehicle->type) }}</p>

                                        <div class="vehicle-features">
                                            <div class="vehicle-feature">
                                                <i class="fas fa-user"></i> {{ $vehicle->seats }} seats
                                            </div>
                                            <div class="vehicle-feature">
                                                <i class="fas fa-suitcase"></i> {{ $vehicle->luggage_capacity }} luggage
                                            </div>
                                        </div>

                                        <div class="vehicle-amenities">
                                            @if($vehicle->has_ac)
                                                <span class="vehicle-amenity"><i class="fas fa-snowflake me-1"></i> AC</span>
                                            @endif
                                            @if($vehicle->has_wifi)
                                                <span class="vehicle-amenity"><i class="fas fa-wifi me-1"></i> WiFi</span>
                                            @endif
                                            @if($vehicle->has_child_seat)
                                                <span class="vehicle-amenity"><i class="fas fa-baby me-1"></i> Child Seat</span>
                                            @endif
                                            @if($vehicle->is_wheelchair_accessible)
                                                <span class="vehicle-amenity"><i class="fas fa-wheelchair me-1"></i> Accessible</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <form action="{{ route('booking.guest.save') }}" method="POST" id="confirmBookingForm" class="mt-4">
                            @csrf
                            <div class="booking-actions">
                                <a href="{{ route('booking.index') }}" class="btn btn-back">
                                    <i class="fas fa-arrow-left me-2"></i> Back
                                </a>
                                <button type="submit" id="confirmBtn" class="btn btn-next">
                                    <span class="button-text">Continue <i class="fas fa-arrow-right ms-2"></i></span>
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle confirmation form submission
        const confirmForm = document.getElementById('confirmBookingForm');
        const confirmBtn = document.getElementById('confirmBtn');

        if (confirmForm && confirmBtn) {
            confirmForm.addEventListener('submit', function(e) {
                // Show loading indicator
                const spinner = confirmBtn.querySelector('.spinner-border');
                const buttonText = confirmBtn.querySelector('.button-text');

                if (spinner && buttonText) {
                    spinner.classList.remove('d-none');
                    buttonText.innerHTML = 'Processing...';
                    confirmBtn.disabled = true;
                }

                // Form will submit normally
            });
        }

        // Add animation to vehicle card
        const vehicleCard = document.querySelector('.vehicle-card');
        if (vehicleCard) {
            setTimeout(() => {
                vehicleCard.classList.add('selected');
            }, 300);
        }

        // Add smooth scroll to booking summary
        const bookingSummary = document.querySelector('.booking-summary');
        if (bookingSummary) {
            bookingSummary.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
</script>
@endsection