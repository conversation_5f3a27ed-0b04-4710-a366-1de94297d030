<?php $__env->startSection('title', 'Update Availability'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }



    .availability-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .availability-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .availability-card .card-body {
        padding: 30px;
    }

    .availability-toggle {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .toggle-label {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .toggle-slider {
        background-color: #28a745;
    }

    input:focus + .toggle-slider {
        box-shadow: 0 0 1px #28a745;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

    .toggle-status {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }

    .toggle-status-text {
        font-weight: 600;
    }

    .toggle-status-available {
        color: #28a745;
    }

    .toggle-status-unavailable {
        color: #dc3545;
    }

    .availability-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .availability-info h5 {
        margin-bottom: 15px;
    }

    .availability-info p {
        margin-bottom: 10px;
    }

    .availability-info ul {
        padding-left: 20px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Update Availability</h2>
                <a href="<?php echo e(route('driver.profile.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Profile
                </a>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card availability-card">
                        <div class="card-header">
                            <h4 class="mb-0">Availability Status</h4>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('driver.profile.update-availability')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>

                                <div class="availability-toggle">
                                    <div class="text-center">
                                        <div class="toggle-label">Are you available to accept rides?</div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="is_available" value="1" <?php echo e($user->is_available ? 'checked' : ''); ?> onchange="updateToggleStatus(this)">
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <div class="toggle-status">
                                            <span class="toggle-status-text toggle-status-unavailable" id="status-unavailable">Unavailable</span>
                                            <span class="toggle-status-text toggle-status-available" id="status-available">Available</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="submit" class="btn btn-primary">Update Availability</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="availability-info">
                        <h5>What does this mean?</h5>
                        <p>When you set your status to <strong>Available</strong>:</p>
                        <ul>
                            <li>You will be able to see and accept new ride requests</li>
                            <li>Clients can book rides that will be assigned to you</li>
                            <li>You will appear in the list of available drivers</li>
                        </ul>

                        <p>When you set your status to <strong>Unavailable</strong>:</p>
                        <ul>
                            <li>You will not receive new ride requests</li>
                            <li>You will not appear in the list of available drivers</li>
                            <li>Your existing assigned rides will not be affected</li>
                        </ul>
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Current Status</h5>
                            <p class="card-text">You are currently <strong class="<?php echo e($user->is_available ? 'text-success' : 'text-danger'); ?>"><?php echo e($user->is_available ? 'Available' : 'Unavailable'); ?></strong> to accept new rides.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function updateToggleStatus(checkbox) {
        const availableStatus = document.getElementById('status-available');
        const unavailableStatus = document.getElementById('status-unavailable');

        if (checkbox.checked) {
            availableStatus.style.fontWeight = '700';
            unavailableStatus.style.fontWeight = '400';
        } else {
            availableStatus.style.fontWeight = '400';
            unavailableStatus.style.fontWeight = '700';
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        const checkbox = document.querySelector('input[name="is_available"]');
        updateToggleStatus(checkbox);
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.driver', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\driver\profile\availability.blade.php ENDPATH**/ ?>