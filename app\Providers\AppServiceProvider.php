<?php

namespace App\Providers;

use App\Services\SettingsService;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register the CardPaymentService
        $this->app->singleton(\App\Services\CardPaymentService::class, function ($app) {
            return new \App\Services\CardPaymentService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Use Bootstrap 5 for pagination
        Paginator::useBootstrapFive();

        // Apply settings to the application
        SettingsService::applySettings();

        // Share common settings with all views
        View::composer('*', function ($view) {
            // Company information
            $view->with('companyName', SettingsService::getCompanyName());
            $view->with('companyEmail', SettingsService::getCompanyEmail());
            $view->with('companyPhone', SettingsService::getCompanyPhone());
            $view->with('companyAddress', SettingsService::getCompanyAddress());
            $view->with('companyDescription', SettingsService::getCompanyDescription());
            $view->with('companyLogo', SettingsService::get('logo', 'logo.png'));

            // Check if logo exists in storage
            $logoPath = SettingsService::get('logo', 'logo.png');
            $logoExists = \Illuminate\Support\Facades\Storage::disk('public')->exists($logoPath);
            $view->with('logoPath', $logoPath);
            $view->with('logoExists', $logoExists);

            // Colors and appearance
            $view->with('primaryColor', SettingsService::getPrimaryColor());
            $view->with('secondaryColor', SettingsService::getSecondaryColor());
            $view->with('appearanceSettings', SettingsService::getAppearanceSettings());

            // Currency and localization settings
            $view->with('currencySymbol', SettingsService::getCurrencySymbol());
            $view->with('currencyCode', SettingsService::getCurrencyCode());
            $view->with('countryCode', SettingsService::getCountryCode());
            $view->with('distanceUnit', SettingsService::getDistanceUnit());

            // API and autocomplete settings
            $view->with('autocompleteSettings', SettingsService::getAutocompleteSettings());
            $view->with('googleMapsApiKey', SettingsService::getGoogleMapsApiKey());

            // Social media settings
            $view->with('socialMediaSettings', SettingsService::getSocialMediaSettings());

            // Booking settings
            $view->with('bookingSettings', SettingsService::getBookingSettings());

            // Share color helper functions
            $view->with('colorHelper', app(\App\Helpers\ColorHelper::class));
        });

        // Add custom Blade directives for settings
        Blade::directive('setting', function ($expression) {
            return "<?php echo \\App\\Services\\SettingsService::get({$expression}); ?>";
        });

        // Add price formatting directive
        Blade::directive('price', function ($expression) {
            return "<?php echo \\App\\Services\\SettingsService::formatPrice({$expression}); ?>";
        });

        // Add currency symbol directive
        Blade::directive('currency', function () {
            return "<?php echo \\App\\Services\\SettingsService::getCurrencySymbol(); ?>";
        });

        // Add helper functions for Blade templates
        Blade::directive('colorContrast', function ($expression) {
            return "<?php echo \\App\\Helpers\\ColorHelper::getContrastColor({$expression}); ?>";
        });

        // Add a function to adjust color brightness
        Blade::if('isLightColor', function ($color) {
            return \App\Helpers\ColorHelper::isLight($color);
        });

        // Register a helper function for adjusting brightness
        Blade::directive('adjustBrightness', function ($expression) {
            $args = explode(',', $expression);
            $color = trim($args[0]);
            $steps = trim($args[1]);
            return "<?php echo \\App\\Helpers\\ColorHelper::adjustBrightness({$color}, {$steps}); ?>";
        });
    }
}
