<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add airport transfer fields
            $table->foreignId('pickup_airport_id')->nullable()->constrained('airports')->onDelete('set null');
            $table->foreignId('dropoff_airport_id')->nullable()->constrained('airports')->onDelete('set null');
            $table->enum('airport_direction', ['to_airport', 'from_airport'])->nullable();
            $table->decimal('airport_surcharge', 8, 2)->nullable()->default(0);

            // Update booking_type enum to include airport_transfer
            $table->dropColumn('booking_type');
        });

        // Add the updated enum column
        Schema::table('bookings', function (Blueprint $table) {
            $table->enum('booking_type', ['one_way', 'return', 'hourly', 'airport_transfer'])->default('one_way')->after('booking_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Remove airport fields
            $table->dropForeign(['pickup_airport_id']);
            $table->dropForeign(['dropoff_airport_id']);
            $table->dropColumn(['pickup_airport_id', 'dropoff_airport_id', 'airport_direction', 'airport_surcharge']);

            // Revert booking_type enum
            $table->dropColumn('booking_type');
        });

        // Add back the original enum column
        Schema::table('bookings', function (Blueprint $table) {
            $table->enum('booking_type', ['one_way', 'return', 'hourly'])->default('one_way')->after('booking_number');
        });
    }
};
