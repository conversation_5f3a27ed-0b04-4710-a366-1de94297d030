<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the payments.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        $query = Payment::with(['booking.user', 'booking.vehicle']);

        // Filter by payment method
        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by transaction ID or booking number
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('booking', function($q) use ($search) {
                      $q->where('booking_number', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get payment statistics
        $stats = [
            'total_payments' => Payment::count(),
            'total_amount' => Payment::where('status', 'completed')->sum('amount'),
            'completed_payments' => Payment::where('status', 'completed')->count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'payment_methods' => Payment::select('payment_method', DB::raw('count(*) as count'))
                ->groupBy('payment_method')
                ->get()
                ->pluck('count', 'payment_method')
                ->toArray(),
        ];

        return view('admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $payment = Payment::with(['booking.user', 'booking.vehicle', 'booking.driver'])->findOrFail($id);

        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Update the payment status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded',
        ]);

        $payment = Payment::findOrFail($id);
        $payment->status = $request->status;
        $payment->save();

        // Update booking payment status if payment is completed
        if ($request->status === 'completed') {
            $booking = $payment->booking;
            $booking->payment_status = 'paid';
            $booking->save();
        }

        // Update booking payment status if payment is refunded
        if ($request->status === 'refunded') {
            $booking = $payment->booking;
            $booking->payment_status = 'refunded';
            $booking->save();
        }

        return redirect()->route('admin.payments.show', $payment->id)
            ->with('success', 'Payment status updated successfully.');
    }

    /**
     * Process a refund for the payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processRefund(Request $request)
    {
        $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'refund_amount' => 'required|numeric|min:0',
            'refund_reason' => 'required|string',
        ]);

        $payment = Payment::findOrFail($request->payment_id);
        $booking = $payment->booking;

        // Check if payment is already refunded
        if ($payment->status === 'refunded') {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Payment has already been refunded.');
        }

        // Check if payment is completed
        if ($payment->status !== 'completed') {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Only completed payments can be refunded.');
        }

        // Check if refund amount is valid
        if ($request->refund_amount > $payment->amount) {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Refund amount cannot be greater than the payment amount.');
        }

        // In a real application, you would integrate with the payment gateway's API
        // to process the refund. For now, we'll just update the status.

        // Update payment record
        $payment->status = 'refunded';
        $payment->refunded_at = now();
        $payment->refunded_amount = $request->refund_amount;
        $payment->refund_notes = $request->refund_reason;
        $payment->save();

        // Update booking payment status
        $booking->payment_status = 'refunded';
        $booking->save();

        // Add booking history for refund
        if (method_exists($booking, 'addHistory')) {
            $booking->addHistory('payment_refunded', [
                'amount' => $request->refund_amount,
                'payment_id' => $payment->id,
                'reason' => $request->refund_reason,
                'refunded_by' => auth()->user()->name
            ]);
        }

        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Payment refunded successfully.');
    }

    /**
     * Process a refund for a specific payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function refund(Request $request, $id)
    {
        $payment = Payment::findOrFail($id);

        $request->validate([
            'refund_amount' => 'required|numeric|min:0',
            'refund_reason' => 'required|string',
        ]);

        $booking = $payment->booking;

        // Check if payment is already refunded
        if ($payment->status === 'refunded') {
            return redirect()->route('admin.payments.show', $payment->id)
                ->with('error', 'Payment has already been refunded.');
        }

        // Check if payment is completed
        if ($payment->status !== 'completed') {
            return redirect()->route('admin.payments.show', $payment->id)
                ->with('error', 'Only completed payments can be refunded.');
        }

        // Check if refund amount is valid
        if ($request->refund_amount > $payment->amount) {
            return redirect()->route('admin.payments.show', $payment->id)
                ->with('error', 'Refund amount cannot be greater than the payment amount.');
        }

        // In a real application, you would integrate with the payment gateway's API
        // to process the refund. For now, we'll just update the status.

        // Update payment record
        $payment->status = 'refunded';
        $payment->refunded_at = now();
        $payment->refunded_amount = $request->refund_amount;
        $payment->refund_notes = $request->refund_reason;
        $payment->save();

        // Update booking payment status
        $booking->payment_status = 'refunded';
        $booking->save();

        // Add booking history for refund
        if (method_exists($booking, 'addHistory')) {
            $booking->addHistory('payment_refunded', [
                'amount' => $request->refund_amount,
                'payment_id' => $payment->id,
                'reason' => $request->refund_reason,
                'refunded_by' => auth()->user()->name
            ]);
        }

        return redirect()->route('admin.payments.show', $payment->id)
            ->with('success', 'Payment refunded successfully.');
    }

    /**
     * Export payments data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $query = Payment::with(['booking.user', 'booking.vehicle']);

        // Apply filters
        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->orderBy('created_at', 'desc')->get();

        // Generate CSV file
        $filename = 'payments_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Transaction ID',
                'Booking Number',
                'Client',
                'Amount',
                'Payment Method',
                'Status',
                'Date',
            ]);

            // Add payment data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->transaction_id,
                    $payment->booking->booking_number,
                    $payment->booking->user->name,
                    $payment->amount,
                    $payment->payment_method,
                    $payment->status,
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Generate payment report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function report(Request $request)
    {
        // Get payment statistics by date range
        $startDate = $request->has('start_date') ? $request->start_date : now()->subDays(30)->format('Y-m-d');
        $endDate = $request->has('end_date') ? $request->end_date : now()->format('Y-m-d');

        // Daily payments
        $dailyPayments = Payment::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_amount')
            )
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();

        // Payment methods distribution
        $paymentMethods = Payment::select('payment_method', DB::raw('count(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('payment_method')
            ->get();

        // Payment status distribution
        $paymentStatuses = Payment::select('status', DB::raw('count(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('status')
            ->get();

        // Top clients by payment amount
        $topClients = Payment::select(
                'booking_id',
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(*) as count')
            )
            ->with('booking.user')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->where('status', 'completed')
            ->groupBy('booking_id')
            ->orderBy('total_amount', 'desc')
            ->take(5)
            ->get();

        return view('admin.payments.report', compact(
            'dailyPayments',
            'paymentMethods',
            'paymentStatuses',
            'topClients',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display the invoice for the specified payment.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function invoice($id)
    {
        $payment = Payment::with(['booking.user', 'booking.vehicle'])->findOrFail($id);

        return view('admin.payments.invoice', compact('payment'));
    }

    /**
     * Store a manually recorded payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|string',
            'transaction_id' => 'nullable|string',
            'payment_note' => 'nullable|string',
        ]);

        // Find the booking
        $booking = Booking::findOrFail($request->booking_id);

        // Create a new payment record
        $payment = new Payment();
        $payment->booking_id = $booking->id;
        $payment->user_id = $booking->user_id;
        $payment->transaction_id = $request->transaction_id ?? 'MANUAL-' . time();
        $payment->payment_method = $request->payment_method;
        $payment->amount = $request->amount;
        $payment->status = 'completed';
        $payment->payment_details = json_encode([
            'note' => $request->payment_note,
            'recorded_by' => auth()->user()->name,
            'recorded_at' => now()->format('Y-m-d H:i:s'),
            'manual_payment' => true,
        ]);
        $payment->save();

        // Update booking status if it's pending
        if ($booking->status === 'pending') {
            $oldStatus = $booking->status;
            $booking->status = 'confirmed';
            $booking->payment_status = 'completed';
            $booking->save();

            // Add booking history for status change
            if (method_exists($booking, 'addHistory')) {
                $booking->addHistory('status_changed', [
                    'status_before' => $oldStatus,
                    'status_after' => 'confirmed',
                    'reason' => 'Payment recorded manually by admin'
                ]);
            }
        } else {
            // Just update the payment status
            $booking->payment_status = 'completed';
            $booking->save();
        }

        // Add booking history for payment
        if (method_exists($booking, 'addHistory')) {
            $booking->addHistory('payment_completed', [
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method,
                'transaction_id' => $payment->transaction_id,
                'recorded_by' => auth()->user()->name
            ]);
        }

        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Payment recorded successfully.');
    }
}
