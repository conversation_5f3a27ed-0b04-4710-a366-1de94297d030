/*
* Ynr Cars - Booking Stylesheet
* Version: 1.1
* Updated styling for consistent booking flow
*/

/* ===== VARIABLES ===== */
:root {
    --primary-color: #ee393d;
    --primary-hover: #d62d31;
    --secondary-color: #343a40;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #212529;
    --text-muted: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition-speed: 0.3s;
    --border-radius: 10px;
    --input-padding: 0.75rem 1rem;
}

/* ===== GLOBAL STYLES ===== */
.booking-container {
    background-color: var(--light-bg);
    padding: 5rem 0;
}

.section-title {
    font-weight: 700;
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

/* Card styles */
.booking-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: none;
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.booking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.booking-card .card-header {
    background-color: var(--secondary-color);
    color: #fff;
    padding: 1.5rem;
    border-bottom: none;
}

.booking-card .card-body {
    padding: 2rem;
}

/* Form elements */
.form-control {
    padding: var(--input-padding);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-secondary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(52, 58, 64, 0.3);
}

/* ===== BOOKING PROCESS ===== */
.booking-process {
    padding: 5rem 0;
    background-color: var(--light-bg);
}

/* Booking Steps */
.booking-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3rem;
    position: relative;
}

.booking-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--border-color);
    z-index: 1;
}

.step {
    position: relative;
    z-index: 2;
    text-align: center;
    width: 33.333%;
}

.step-number {
    width: 60px;
    height: 60px;
    background-color: #fff;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-muted);
    transition: all var(--transition-speed);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.step.active .step-number {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
    transform: scale(1.1);
}

.step.completed .step-number {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: #fff;
}

.step-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--text-muted);
    transition: all var(--transition-speed);
}

.step.active .step-title {
    color: var(--primary-color);
    font-weight: 700;
}

.step.completed .step-title {
    color: var(--success-color);
}

/* Step content */
.step-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.step-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Booking Form Card */
.booking-form-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all var(--transition-speed);
}

.booking-form-card .card-header {
    background-color: var(--secondary-color);
    color: #fff;
    padding: 1.5rem;
    border-bottom: none;
}

.booking-form-card .card-body {
    padding: 2rem;
}

.booking-form-card h3 {
    font-weight: 700;
    margin-bottom: 0;
    font-size: 1.5rem;
}

.booking-form .form-group {
    margin-bottom: 1.5rem;
}

.booking-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.booking-form .form-control {
    padding: var(--input-padding);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    transition: all var(--transition-speed);
}

.booking-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
}

.booking-form .form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Booking Summary */
.booking-summary {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    position: sticky;
    top: 2rem;
}

.booking-summary h4 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
    color: var(--secondary-color);
}

.booking-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px dashed var(--border-color);
}

.booking-summary-item:last-of-type {
    border-bottom: none;
    margin-bottom: 1.5rem;
}

.booking-summary-label {
    font-weight: 500;
    color: var(--text-color);
}

.booking-summary-value {
    font-weight: 600;
    color: var(--text-color);
}

.booking-total {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-color);
    font-size: 1.25rem;
    display: flex;
    justify-content: space-between;
}

.booking-total-label {
    font-weight: 700;
    color: var(--text-color);
}

.booking-total-value {
    font-weight: 700;
    color: var(--primary-color);
}

/* Booking Actions */
.booking-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    gap: 1rem;
}

.booking-actions .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.booking-actions .btn-next {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
    flex-grow: 1;
}

.booking-actions .btn-next:hover {
    background-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.booking-actions .btn-back {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-muted);
}

.booking-actions .btn-back:hover {
    background-color: var(--light-bg);
    color: var(--text-color);
    transform: translateY(-3px);
}

/* ===== VEHICLE SELECTION ===== */
.vehicle-selection {
    margin-bottom: 2.5rem;
}

/* Vehicle cards */
.vehicle-card {
    background-color: #fff;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all var(--transition-speed);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

.vehicle-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.5rem 1.5rem rgba(238, 57, 61, 0.15);
    transform: translateY(-5px);
}

.vehicle-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(238, 57, 61, 0.03);
    box-shadow: 0 0.5rem 1.5rem rgba(238, 57, 61, 0.2);
}

.vehicle-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 50px 50px 0;
    border-color: transparent var(--primary-color) transparent transparent;
}

.vehicle-card.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 8px;
    right: 8px;
    color: #fff;
    font-size: 1rem;
}

.vehicle-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    margin-bottom: 1.25rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.vehicle-image {
    width: 100%;
    height: auto;
    transition: transform var(--transition-speed);
}

.vehicle-card:hover .vehicle-image {
    transform: scale(1.05);
}

.vehicle-price-tag {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: var(--primary-color);
    color: #fff;
    padding: 0.5rem 1rem;
    font-weight: 700;
    border-top-left-radius: 0.5rem;
}

.vehicle-info {
    padding: 0.5rem 0;
}

.vehicle-name {
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    transition: color var(--transition-speed);
}

.vehicle-card:hover .vehicle-name {
    color: var(--primary-color);
}

.vehicle-type {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.vehicle-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.vehicle-feature {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-color);
}

.vehicle-feature i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-size: 1rem;
}

.vehicle-amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
}

.vehicle-amenity {
    background-color: var(--light-bg);
    color: var(--text-muted);
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.vehicle-card:hover .vehicle-amenity {
    background-color: rgba(238, 57, 61, 0.1);
    color: var(--primary-color);
}

.vehicle-select-btn {
    width: 100%;
    padding: 0.75rem 0;
    background-color: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.vehicle-select-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.vehicle-card.selected .vehicle-select-btn {
    background-color: var(--primary-color);
}

/* Fare calculation section */
.fare-calculation-section {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--card-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.fare-calculation-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.fare-calculation-title i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    font-size: 1.5rem;
}

.selected-vehicle-info {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.selected-vehicle-image {
    width: 80px;
    height: 80px;
    border-radius: 0.5rem;
    object-fit: cover;
    margin-right: 1.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.selected-vehicle-details h5 {
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.selected-vehicle-details p {
    color: var(--text-muted);
    margin-bottom: 0;
    font-size: 0.875rem;
}

.fare-details {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all var(--transition-speed);
}

.fare-row {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px dashed var(--border-color);
}

.fare-row:last-child {
    border-bottom: none;
}

.fare-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.fare-value {
    font-weight: 600;
    color: var(--text-color);
}

.fare-total {
    display: flex;
    justify-content: space-between;
    font-weight: 700;
    color: var(--text-color);
    font-size: 1.25rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid var(--border-color);
}

.fare-total-value {
    color: var(--primary-color);
}

/* Button animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(238, 57, 61, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(238, 57, 61, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(238, 57, 61, 0);
    }
}

.btn-pulse {
    animation: pulse 1.5s infinite;
}

/* ===== PAYMENT SECTION ===== */
.payment-section {
    padding: 5rem 0;
    background-color: var(--light-bg);
}

.payment-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
}

.payment-card .card-header {
    background-color: var(--secondary-color);
    color: #fff;
    padding: 1.5rem;
    border-bottom: none;
}

.payment-card .card-header h3 {
    margin-bottom: 0;
    font-weight: 700;
}

.payment-card .card-body {
    padding: 2rem;
}

.payment-methods {
    margin-bottom: 2rem;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all var(--transition-speed);
    background-color: #fff;
}

.payment-method:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.1);
    transform: translateY(-3px);
}

.payment-method.selected {
    border-color: var(--primary-color);
    background-color: rgba(238, 57, 61, 0.03);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.15);
}

.payment-method-radio {
    margin-right: 1.25rem;
}

.payment-method-radio .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
}

.payment-method-radio .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.payment-method-logo {
    width: 70px;
    height: 40px;
    object-fit: contain;
    margin-right: 1.25rem;
}

.payment-method-details {
    flex: 1;
}

.payment-method-title {
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--text-color);
    font-size: 1.1rem;
}

.payment-method-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0;
}

.payment-form {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.payment-form .form-group {
    margin-bottom: 1.5rem;
}

.payment-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.payment-form .form-control {
    padding: var(--input-padding);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    transition: all var(--transition-speed);
}

.payment-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
}

.payment-form .form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.card-input-group {
    display: flex;
    gap: 1rem;
}

.card-input-group .form-group {
    flex: 1;
}

.payment-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.payment-actions .btn-pay {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
    font-weight: 600;
    padding: 0.75rem 2rem;
    flex-grow: 1;
    transition: all var(--transition-speed);
}

.payment-actions .btn-pay:hover {
    background-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.payment-actions .btn-back {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-muted);
}

.payment-actions .btn-back:hover {
    background-color: var(--light-bg);
    color: var(--text-color);
    transform: translateY(-3px);
}

.payment-security {
    display: flex;
    align-items: center;
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--light-bg);
    border-radius: 0.5rem;
}

.payment-security i {
    font-size: 1.5rem;
    color: var(--success-color);
    margin-right: 1rem;
}

.payment-security-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0;
}

/* ===== CONFIRMATION PAGE ===== */
.confirmation-section {
    padding: 5rem 0;
    background-color: var(--light-bg);
}

.confirmation-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    text-align: center;
    padding: 3rem 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.confirmation-icon {
    font-size: 5rem;
    color: var(--success-color);
    margin-bottom: 2rem;
    display: inline-block;
    position: relative;
}

.confirmation-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 50%;
    z-index: -1;
}

.confirmation-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-size: 2rem;
}

.confirmation-message {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: var(--text-muted);
    line-height: 1.6;
}

.booking-details-card {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto 2.5rem;
    text-align: left;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

.booking-details-card h4 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
    color: var(--secondary-color);
}

.booking-detail-item {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px dashed var(--border-color);
}

.booking-detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.booking-detail-label {
    font-weight: 600;
    width: 40%;
    color: var(--text-color);
}

.booking-detail-value {
    width: 60%;
    color: var(--text-muted);
}

.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.confirmation-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.confirmation-actions .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.confirmation-actions .btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(238, 57, 61, 0.3);
}

.confirmation-actions .btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--text-muted);
}

.confirmation-actions .btn-outline-secondary:hover {
    background-color: var(--light-bg);
    color: var(--text-color);
    transform: translateY(-3px);
}

/* Button pulse animation */
@keyframes btnPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(238, 57, 61, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(238, 57, 61, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(238, 57, 61, 0);
    }
}

.btn-pulse {
    animation: btnPulse 1.5s infinite;
}

/* ===== GUEST REVIEW PAGE ===== */
.guest-review-section {
    padding: 5rem 0;
    background-color: var(--light-bg);
}

.guest-review-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
}

.guest-review-card .card-header {
    background-color: var(--secondary-color);
    color: #fff;
    padding: 1.5rem;
    border-bottom: none;
}

.guest-review-card .card-header h3 {
    margin-bottom: 0;
    font-weight: 700;
}

.guest-review-card .card-body {
    padding: 2rem;
}

.guest-form .form-group {
    margin-bottom: 1.5rem;
}

.guest-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.guest-form .form-control {
    padding: var(--input-padding);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    transition: all var(--transition-speed);
}

.guest-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
}

.guest-form .form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 991.98px) {
    .booking-steps {
        flex-wrap: wrap;
    }

    .step {
        width: 33.33%;
        margin-bottom: 1.5rem;
    }

    .booking-summary {
        position: static;
        margin-top: 2rem;
    }

    .card-input-group {
        flex-direction: column;
        gap: 0;
    }
}

@media (max-width: 767.98px) {
    .booking-process {
        padding: 3rem 0;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .step-title {
        font-size: 0.875rem;
    }

    .booking-form-card .card-body,
    .payment-card .card-body {
        padding: 1.5rem;
    }

    .booking-actions,
    .payment-actions,
    .confirmation-actions {
        flex-direction: column;
    }

    .booking-actions .btn,
    .payment-actions .btn,
    .confirmation-actions .btn {
        margin-bottom: 0.75rem;
    }

    .vehicle-card {
        padding: 1rem;
    }

    .fare-calculation-section {
        padding: 1.5rem;
    }

    .selected-vehicle-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .selected-vehicle-image {
        margin-bottom: 1rem;
        margin-right: 0;
    }

    .booking-detail-item {
        flex-direction: column;
    }

    .booking-detail-label,
    .booking-detail-value {
        width: 100%;
    }

    .booking-detail-label {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .step {
        width: 100%;
        margin-bottom: 1rem;
    }

    .booking-steps::before {
        display: none;
    }

    .step-number {
        margin-bottom: 0.5rem;
    }

    .payment-method {
        flex-direction: column;
        align-items: flex-start;
        padding: 1rem;
    }

    .payment-method-radio {
        margin-bottom: 0.75rem;
        margin-right: 0;
    }

    .payment-method-logo {
        margin-bottom: 0.75rem;
        margin-right: 0;
    }
}
