<?php

namespace Database\Seeders;

use App\Models\Airport;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AirportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $airports = [
            [
                'name' => 'Los Angeles International Airport',
                'code' => 'LAX',
                'city' => 'Los Angeles',
                'country' => 'United States',
                'country_code' => 'US',
                'latitude' => 33.9425,
                'longitude' => -118.4081,
                'timezone' => 'America/Los_Angeles',
                'address' => '1 World Way, Los Angeles, CA 90045, USA',
            ],
            [
                'name' => 'John F. Kennedy International Airport',
                'code' => 'JFK',
                'city' => 'New York',
                'country' => 'United States',
                'country_code' => 'US',
                'latitude' => 40.6413,
                'longitude' => -73.7781,
                'timezone' => 'America/New_York',
                'address' => 'Queens, NY 11430, USA',
            ],
            [
                'name' => 'Heathrow Airport',
                'code' => 'LHR',
                'city' => 'London',
                'country' => 'United Kingdom',
                'country_code' => 'GB',
                'latitude' => 51.4700,
                'longitude' => -0.4543,
                'timezone' => 'Europe/London',
                'address' => 'Longford TW6, UK',
            ],
            [
                'name' => 'Charles de Gaulle Airport',
                'code' => 'CDG',
                'city' => 'Paris',
                'country' => 'France',
                'country_code' => 'FR',
                'latitude' => 49.0097,
                'longitude' => 2.5479,
                'timezone' => 'Europe/Paris',
                'address' => '95700 Roissy-en-France, France',
            ],
            [
                'name' => 'Dubai International Airport',
                'code' => 'DXB',
                'city' => 'Dubai',
                'country' => 'United Arab Emirates',
                'country_code' => 'AE',
                'latitude' => 25.2532,
                'longitude' => 55.3657,
                'timezone' => 'Asia/Dubai',
                'address' => 'Dubai - United Arab Emirates',
            ],
        ];

        foreach ($airports as $airport) {
            Airport::updateOrCreate(
                ['code' => $airport['code']],
                $airport
            );
        }
    }
}
