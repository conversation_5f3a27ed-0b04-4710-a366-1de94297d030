
<?php
    $metaTags = \App\Services\SettingsService::getAutocompleteMetaTags();

    // Add via booking settings
    $viaBookingSettings = [
        'via-booking-enabled' => \App\Services\SettingsService::get('via_booking_enabled', 'true'),
        'max-via-points' => \App\Services\SettingsService::get('max_via_points', '5'),
        'via-point-surcharge' => \App\Services\SettingsService::get('via_point_surcharge', '5.00'),
        'default-stop-duration' => \App\Services\SettingsService::get('default_stop_duration', '5'),
        'min-stop-duration' => \App\Services\SettingsService::get('min_stop_duration', '1'),
        'max-stop-duration' => \App\Services\SettingsService::get('max_stop_duration', '60'),
    ];

    $metaTags = array_merge($metaTags, $viaBookingSettings);
?>

<!-- Autocomplete Settings Meta Tags -->
<?php $__currentLoopData = $metaTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <meta name="<?php echo e($name); ?>" content="<?php echo e($content); ?>">
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Google Maps API Configuration -->
<?php
    $googleMapsApiKey = \App\Services\SettingsService::get('google_maps_api_key', config('services.google_maps.key', ''));
    $jsConfig = \App\Services\SettingsService::getAutocompleteJavaScriptConfig();
?>

<?php if($googleMapsApiKey): ?>
    <script>
        // Global autocomplete configuration
        window.autocompleteSettings = <?php echo json_encode($jsConfig, 15, 512) ?>;
        window.googleMapsApiKey = '<?php echo e($googleMapsApiKey); ?>';

        // Initialize Google Maps API callback
        function initGoogleMapsApi() {
            window.dispatchEvent(new Event('google-maps-loaded'));
        }
    </script>

    <!-- Load Google Maps API with Places library -->
    <script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places&callback=initGoogleMapsApi" async defer></script>

    <!-- Load Unified Autocomplete Handler -->
    <script src="<?php echo e(asset('js/unified-autocomplete.js')); ?>" defer></script>
<?php else: ?>
    <script>
        console.error('Google Maps API key is not configured. Please set it in the admin settings.');

        // Show user-friendly message
        document.addEventListener('DOMContentLoaded', function() {
            const addressInputs = document.querySelectorAll('input[id*="address"], .address-autocomplete');
            addressInputs.forEach(input => {
                input.placeholder = 'Address autocomplete unavailable - API key not configured';
                input.style.borderColor = '#ffc107';
                input.style.backgroundColor = '#fff3cd';
            });
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/components/autocomplete-meta.blade.php ENDPATH**/ ?>