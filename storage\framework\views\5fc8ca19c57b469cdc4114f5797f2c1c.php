
<?php
    $metaTags = \App\Services\SettingsService::getAutocompleteMetaTags();
?>

<!-- Autocomplete Settings Meta Tags -->
<?php $__currentLoopData = $metaTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <meta name="<?php echo e($name); ?>" content="<?php echo e($content); ?>">
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Google Maps API Configuration -->
<?php
    $googleMapsApiKey = \App\Services\SettingsService::get('google_maps_api_key', config('services.google_maps.key', ''));
    $jsConfig = \App\Services\SettingsService::getAutocompleteJavaScriptConfig();
?>

<?php if($googleMapsApiKey): ?>
    <script>
        // Global autocomplete configuration
        window.autocompleteSettings = <?php echo json_encode($jsConfig, 15, 512) ?>;
        window.googleMapsApiKey = '<?php echo e($googleMapsApiKey); ?>';
        
        // Initialize Google Maps API callback
        function initGoogleMapsApi() {
            window.dispatchEvent(new Event('google-maps-loaded'));
        }
    </script>
    
    <!-- Load Google Maps API with Places library -->
    <script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places&callback=initGoogleMapsApi" async defer></script>
    
    <!-- Load Unified Autocomplete Handler -->
    <script src="<?php echo e(asset('js/unified-autocomplete.js')); ?>" defer></script>
<?php else: ?>
    <script>
        console.error('Google Maps API key is not configured. Please set it in the admin settings.');
        
        // Show user-friendly message
        document.addEventListener('DOMContentLoaded', function() {
            const addressInputs = document.querySelectorAll('input[id*="address"], .address-autocomplete');
            addressInputs.forEach(input => {
                input.placeholder = 'Address autocomplete unavailable - API key not configured';
                input.style.borderColor = '#ffc107';
                input.style.backgroundColor = '#fff3cd';
            });
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/components/autocomplete-meta.blade.php ENDPATH**/ ?>