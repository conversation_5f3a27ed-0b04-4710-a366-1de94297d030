<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class OptimizeAutocomplete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autocomplete:optimize {--reset : Reset to default optimized settings}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize autocomplete settings for better address diversity';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔧 Optimizing autocomplete settings for better address diversity...');

        if ($this->option('reset')) {
            $this->resetToDefaults();
        } else {
            $this->optimizeSettings();
        }

        $this->info('✅ Autocomplete optimization completed!');
        $this->displayCurrentSettings();

        return 0;
    }

    /**
     * Optimize autocomplete settings
     */
    private function optimizeSettings()
    {
        $optimizations = [
            // Enable autocomplete
            'autocomplete_enabled' => 'true',
            
            // Reduce country restriction impact
            'autocomplete_restrict_country' => 'false',
            
            // Use geocode for best results
            'autocomplete_types' => 'geocode',
            
            // Increase bias radius for more diverse results
            'autocomplete_bias_radius' => '100',
            
            // Disable strict bounds to allow diverse results
            'autocomplete_use_strict_bounds' => 'false',
            
            // Include more fields for better matching
            'autocomplete_fields' => 'address_components,geometry,name,formatted_address,place_id',
            
            // Google Maps settings
            'google_maps_restrict_country' => 'false',
        ];

        foreach ($optimizations as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                $oldValue = $setting->value;
                $setting->value = $value;
                $setting->save();
                
                $this->line("✓ Updated {$key}: {$oldValue} → {$value}");
            } else {
                Setting::create([
                    'key' => $key,
                    'value' => $value,
                    'group' => 'autocomplete',
                    'type' => 'string',
                    'is_public' => true
                ]);
                
                $this->line("✓ Created {$key}: {$value}");
            }
        }
    }

    /**
     * Reset to default optimized settings
     */
    private function resetToDefaults()
    {
        $this->info('🔄 Resetting to default optimized settings...');

        $defaults = [
            'autocomplete_enabled' => 'true',
            'autocomplete_restrict_country' => 'true',
            'autocomplete_country' => 'GB',
            'autocomplete_types' => 'geocode',
            'autocomplete_bias_radius' => '50',
            'autocomplete_use_strict_bounds' => 'false',
            'autocomplete_fields' => 'address_components,geometry,name,formatted_address',
            'google_maps_restrict_country' => 'true',
            'google_maps_country_code' => 'GB',
        ];

        foreach ($defaults as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => 'autocomplete',
                    'type' => 'string',
                    'is_public' => true
                ]
            );
            
            $this->line("✓ Reset {$key}: {$value}");
        }
    }

    /**
     * Display current settings
     */
    private function displayCurrentSettings()
    {
        $this->info('📋 Current Autocomplete Settings:');
        
        $settings = [
            'autocomplete_enabled',
            'autocomplete_restrict_country',
            'autocomplete_country',
            'autocomplete_types',
            'autocomplete_bias_radius',
            'autocomplete_use_strict_bounds',
            'autocomplete_fields',
            'google_maps_restrict_country',
            'google_maps_country_code',
        ];

        $table = [];
        foreach ($settings as $key) {
            $setting = Setting::where('key', $key)->first();
            $table[] = [
                'Setting' => $key,
                'Value' => $setting ? $setting->value : 'Not set',
                'Impact' => $this->getSettingImpact($key, $setting ? $setting->value : null)
            ];
        }

        $this->table(['Setting', 'Value', 'Impact on Diversity'], $table);

        $this->info('💡 Tips for better autocomplete diversity:');
        $this->line('• Set autocomplete_restrict_country to false for global results');
        $this->line('• Increase autocomplete_bias_radius to 100+ for wider coverage');
        $this->line('• Keep autocomplete_use_strict_bounds as false');
        $this->line('• Use geocode type for comprehensive address matching');
        $this->line('• Include place_id in fields for better result quality');
    }

    /**
     * Get setting impact description
     */
    private function getSettingImpact($key, $value)
    {
        $impacts = [
            'autocomplete_enabled' => $value === 'true' ? '✅ Autocomplete active' : '❌ Autocomplete disabled',
            'autocomplete_restrict_country' => $value === 'true' ? '⚠️ Limited to country' : '✅ Global results',
            'autocomplete_types' => $value === 'geocode' ? '✅ Best for addresses' : '⚠️ May limit results',
            'autocomplete_bias_radius' => (int)$value > 50 ? '✅ Wide coverage' : '⚠️ Local bias',
            'autocomplete_use_strict_bounds' => $value === 'false' ? '✅ Allows diverse results' : '❌ Restricts results',
            'google_maps_restrict_country' => $value === 'true' ? '⚠️ Country restricted' : '✅ Global coverage',
        ];

        return $impacts[$key] ?? 'ℹ️ Setting configured';
    }
}
