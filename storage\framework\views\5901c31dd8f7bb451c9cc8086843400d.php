<?php $__env->startSection('title', 'Booking Reminder'); ?>

<?php $__env->startSection('content'); ?>
<h2>Booking Reminder</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>This is a friendly reminder about your upcoming ride with <?php echo e($companyName); ?>.</p>

<div class="booking-details">
    <h3>Your Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name ?? 'To be assigned'); ?></span>
    </div>
    
    <?php if($driver): ?>
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value"><?php echo e($driver->name); ?></span>
    </div>
    <?php if($driver->phone): ?>
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value"><?php echo e($driver->phone); ?></span>
    </div>
    <?php endif; ?>
    <?php endif; ?>
</div>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #856404; margin-top: 0;">Important Reminders:</h3>
    <ul style="color: #856404; margin-bottom: 0;">
        <li>Please be ready at your pickup location 5 minutes before the scheduled time</li>
        <li>Our driver will contact you 15-30 minutes before arrival</li>
        <li>Have your booking confirmation number ready: <strong>#<?php echo e($booking->booking_number); ?></strong></li>
        <?php if($booking->payment_status === 'pending'): ?>
        <li style="color: #dc3545;"><strong>Payment Required:</strong> Please complete your payment before the ride</li>
        <?php endif; ?>
    </ul>
</div>

<?php if($booking->notes): ?>
<div style="margin: 20px 0;">
    <strong>Special Instructions:</strong><br>
    <?php echo e($booking->notes); ?>

</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('client.bookings.show', $booking->id)); ?>" class="btn">View Full Booking Details</a>
</div>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us as soon as possible:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or manage your booking online in your account</li>
</ul>

<p><strong>Cancellation Policy:</strong> Free cancellation up to 24 hours before pickup. Cancellations within 24 hours may incur a fee.</p>

<p>We look forward to serving you!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\reminder-client.blade.php ENDPATH**/ ?>