<?php

namespace App\Services;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoogleMapsService
{
    /**
     * Google Maps API Key
     *
     * @var string
     */
    protected $apiKey;

    /**
     * Default map zoom level
     *
     * @var int
     */
    protected $defaultZoom;

    /**
     * Default map center latitude
     *
     * @var float
     */
    protected $defaultLat;

    /**
     * Default map center longitude
     *
     * @var float
     */
    protected $defaultLng;

    /**
     * Google Maps libraries to load
     *
     * @var string
     */
    protected $libraries;

    /**
     * Whether to use Distance Matrix API
     *
     * @var bool
     */
    protected $useDistanceMatrix;

    /**
     * Whether to use Directions API
     *
     * @var bool
     */
    protected $useDirections;

    /**
     * Whether to restrict to a specific country
     *
     * @var bool
     */
    protected $restrictCountry;

    /**
     * Country code to restrict to
     *
     * @var string
     */
    protected $countryCode;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = SettingsHelper::get('google_maps_api_key', config('services.google_maps.key', ''));
        $this->defaultZoom = (int) SettingsHelper::get('google_maps_default_zoom', 10);
        $this->defaultLat = (float) SettingsHelper::get('google_maps_default_lat', 51.5074);
        $this->defaultLng = (float) SettingsHelper::get('google_maps_default_lng', -0.1278);
        $this->libraries = SettingsHelper::get('google_maps_libraries', 'places,geometry,drawing');
        $this->useDistanceMatrix = SettingsHelper::get('google_maps_use_distance_matrix', 'true') === 'true';
        $this->useDirections = SettingsHelper::get('google_maps_use_directions', 'true') === 'true';
        $this->restrictCountry = SettingsHelper::get('google_maps_restrict_country', 'false') === 'true';
        $this->countryCode = SettingsHelper::get('google_maps_country_code', '');
    }

    /**
     * Get distance and duration between two addresses
     *
     * @param string $origin
     * @param string $destination
     * @return array|null
     */
    /**
     * Get API key
     *
     * @return string
     */
    public function getApiKey()
    {
        return $this->apiKey;
    }

    /**
     * Get default zoom level
     *
     * @return int
     */
    public function getDefaultZoom()
    {
        return $this->defaultZoom;
    }

    /**
     * Get default latitude
     *
     * @return float
     */
    public function getDefaultLat()
    {
        return $this->defaultLat;
    }

    /**
     * Get default longitude
     *
     * @return float
     */
    public function getDefaultLng()
    {
        return $this->defaultLng;
    }

    /**
     * Get libraries
     *
     * @return string
     */
    public function getLibraries()
    {
        return $this->libraries;
    }

    /**
     * Check if Distance Matrix API should be used
     *
     * @return bool
     */
    public function useDistanceMatrix()
    {
        return $this->useDistanceMatrix;
    }

    /**
     * Check if Directions API should be used
     *
     * @return bool
     */
    public function useDirections()
    {
        return $this->useDirections;
    }

    /**
     * Check if country restriction is enabled
     *
     * @return bool
     */
    public function restrictCountry()
    {
        return $this->restrictCountry;
    }

    /**
     * Get country code for restriction
     *
     * @return string
     */
    public function getCountryCode()
    {
        return $this->countryCode;
    }

    /**
     * Get Google Maps API URL with all required parameters
     *
     * @return string
     */
    public function getApiUrl()
    {
        $url = "https://maps.googleapis.com/maps/api/js?key={$this->apiKey}&libraries={$this->libraries}";

        // Add country restriction if enabled
        if ($this->restrictCountry && !empty($this->countryCode)) {
            $url .= "&region={$this->countryCode}";
        }

        return $url;
    }

    /**
     * Get distance and duration between two addresses
     *
     * @param string $origin
     * @param string $destination
     * @return array|null
     */
    public function getDistanceMatrix($origin, $destination)
    {
        // Skip API call if Distance Matrix API is disabled
        if (!$this->useDistanceMatrix) {
            Log::info('Distance Matrix API is disabled. Using fallback calculation.');
            return $this->calculateDistanceHaversine($origin, $destination);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', [
                'origins' => $origin,
                'destinations' => $destination,
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($response->successful() && $data['status'] === 'OK') {
                $element = $data['rows'][0]['elements'][0];

                if ($element['status'] === 'OK') {
                    return [
                        'distance' => [
                            'value' => $element['distance']['value'], // in meters
                            'text' => $element['distance']['text'],
                        ],
                        'duration' => [
                            'value' => $element['duration']['value'], // in seconds
                            'text' => $element['duration']['text'],
                        ],
                    ];
                }
            }

            Log::warning('Google Maps Distance Matrix API error', [
                'status' => $data['status'] ?? 'Unknown',
                'error_message' => $data['error_message'] ?? 'No error message',
            ]);

            // If API call fails, try the fallback calculation
            Log::info('Trying fallback distance calculation using Haversine formula');
            return $this->calculateDistanceHaversine($origin, $destination);
        } catch (\Exception $e) {
            Log::error('Google Maps Distance Matrix API exception', [
                'message' => $e->getMessage(),
            ]);

            // If exception occurs, try the fallback calculation
            Log::info('Trying fallback distance calculation using Haversine formula after exception');
            return $this->calculateDistanceHaversine($origin, $destination);
        }
    }

    /**
     * Geocode an address to get latitude and longitude
     *
     * @param string $address
     * @return array|null
     */
    public function geocodeAddress($address)
    {
        // If address is empty or just whitespace, return null
        if (empty(trim($address))) {
            Log::warning('Empty address provided for geocoding');
            return null;
        }

        try {
            $params = [
                'address' => $address,
                'key' => $this->apiKey,
            ];

            // Add country restriction if enabled
            if ($this->restrictCountry && !empty($this->countryCode)) {
                $params['components'] = 'country:' . $this->countryCode;
            }

            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', $params);

            $data = $response->json();

            if ($response->successful() && $data['status'] === 'OK' && !empty($data['results'])) {
                $location = $data['results'][0]['geometry']['location'];

                return [
                    'lat' => $location['lat'],
                    'lng' => $location['lng'],
                    'formatted_address' => $data['results'][0]['formatted_address'],
                ];
            }

            Log::warning('Google Maps Geocoding API error', [
                'address' => $address,
                'status' => $data['status'] ?? 'Unknown',
                'error_message' => $data['error_message'] ?? 'No error message',
            ]);

            // If we're geocoding a known location like an airport, we could have fallback coordinates
            if (stripos($address, 'airport') !== false || stripos($address, 'heathrow') !== false ||
                stripos($address, 'gatwick') !== false || stripos($address, 'stansted') !== false) {
                Log::info('Using fallback coordinates for airport');
                return [
                    'lat' => 51.4700, // Default London airport coordinates
                    'lng' => -0.4543,
                    'formatted_address' => $address,
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Google Maps Geocoding API exception', [
                'address' => $address,
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get coordinates for an address
     *
     * @param string $address
     * @return array|null
     */
    public function getCoordinates($address)
    {
        return $this->geocodeAddress($address);
    }

    /**
     * Calculate fare based on distance
     *
     * @param float $distance Distance in kilometers
     * @param float $baseRate Base rate in dollars
     * @param float $ratePerKm Rate per kilometer in dollars
     * @param string $vehicleType Type of vehicle
     * @param string $bookingType Type of booking (one_way, return, hourly)
     * @param int $durationHours Duration in hours (for hourly bookings)
     * @param array $vehiclePricing Vehicle-specific pricing details
     * @param string|null $pickupDateTime Pickup date and time (Y-m-d H:i:s format)
     * @param array $extraServices Extra services selected
     * @param int $passengers Number of passengers
     * @return array
     */
    public function calculateFare($distance, $baseRate = 5.0, $ratePerKm = 2.5, $vehicleType = 'sedan', $bookingType = 'one_way', $durationHours = 1, $vehiclePricing = [], $pickupDateTime = null, $extraServices = [], $passengers = 2)
    {
        // Convert distance from meters to kilometers
        $distanceInKm = $distance / 1000;

        // Get vehicle pricing details or use defaults
        $baseFare = $vehiclePricing['base_fare'] ?? ($baseRate * $this->getVehicleMultiplier($vehicleType));
        $adjustedRatePerKm = $vehiclePricing['price_per_km'] ?? ($ratePerKm * $this->getVehicleMultiplier($vehicleType));
        $bookingFee = $vehiclePricing['booking_fee'] ?? 2.50;
        $taxRate = $vehiclePricing['tax_rate'] ?? 0;
        $airportSurcharge = $vehiclePricing['airport_surcharge'] ?? 15.00;
        $nightSurcharge = $vehiclePricing['night_surcharge'] ?? 0;
        $weekendSurcharge = $vehiclePricing['weekend_surcharge'] ?? 0;
        $holidaySurcharge = $vehiclePricing['holiday_surcharge'] ?? 0;

        // Calculate fare based on booking type
        switch ($bookingType) {
            case 'return':
                // For return trips, double the distance fare
                $distanceFare = ($distanceInKm * $adjustedRatePerKm) * 2;
                break;
            case 'hourly':
                // For hourly bookings, use hourly rate
                $hourlyRate = 30 * $vehicleMultiplier; // $30 per hour base rate
                $distanceFare = $hourlyRate * $durationHours;
                break;
            case 'airport':
                // For airport transfers, add the airport surcharge
                $distanceFare = ($distanceInKm * $adjustedRatePerKm) + $airportSurcharge;
                break;
            default: // one_way
                $distanceFare = $distanceInKm * $adjustedRatePerKm;
                break;
        }

        // Calculate subtotal
        $subtotal = $baseFare + $distanceFare;

        // Add booking fee
        $subtotal += $bookingFee;

        // Check if it's a weekend and add weekend surcharge if applicable
        $isWeekend = false;
        $isNightTime = false;

        if ($pickupDateTime) {
            try {
                // Parse the pickup date time
                $pickupDate = new \DateTime($pickupDateTime);

                // Check if it's a weekend (Saturday or Sunday)
                $dayOfWeek = (int)$pickupDate->format('N');
                $isWeekend = ($dayOfWeek >= 6); // 6 = Saturday, 7 = Sunday

                // Check if it's night time (10 PM - 6 AM)
                $hour = (int)$pickupDate->format('H');
                $isNightTime = ($hour >= 22 || $hour < 6);

                Log::info('Surcharge verification', [
                    'pickup_datetime' => $pickupDateTime,
                    'day_of_week' => $dayOfWeek,
                    'hour' => $hour,
                    'is_weekend' => $isWeekend,
                    'is_night_time' => $isNightTime
                ]);
            } catch (\Exception $e) {
                Log::error('Error parsing pickup date time for surcharge verification', [
                    'pickup_datetime' => $pickupDateTime,
                    'error' => $e->getMessage()
                ]);

                // Fallback to current date/time if parsing fails
                $isWeekend = (date('N') >= 6);
                $hour = (int)date('H');
                $isNightTime = ($hour >= 22 || $hour < 6);
            }
        } else {
            // Fallback to current date/time if no pickup date/time provided
            $isWeekend = (date('N') >= 6);
            $hour = (int)date('H');
            $isNightTime = ($hour >= 22 || $hour < 6);
        }

        // Add weekend surcharge if applicable
        if ($isWeekend && $weekendSurcharge > 0) {
            $subtotal += $weekendSurcharge;
        }

        // Add night surcharge if applicable
        if ($isNightTime && $nightSurcharge > 0) {
            $subtotal += $nightSurcharge;
        }

        // Calculate extra services total
        $extraServicesTotal = $this->calculateExtraServicesTotal($extraServices);
        $subtotal += $extraServicesTotal;

        // Check if it's a holiday and add holiday surcharge if applicable
        // This is a simplified check - in a real app, you'd have a list of holidays
        $isHoliday = false; // Placeholder for holiday check
        if ($isHoliday) {
            $subtotal += $holidaySurcharge;
        }

        // Calculate tax if applicable
        $taxAmount = 0;
        if ($taxRate > 0) {
            $taxAmount = $subtotal * ($taxRate / 100);
        }

        // Calculate total fare with tax
        $totalFare = $subtotal + $taxAmount;

        // Round to 2 decimal places
        $totalFare = round($totalFare, 2);
        $subtotal = round($subtotal, 2);
        $taxAmount = round($taxAmount, 2);

        $result = [
            'base_fare' => round($baseFare, 2),
            'distance_fare' => round($distanceFare, 2),
            'booking_fee' => $bookingFee,
            'extra_services_total' => $extraServicesTotal,
            'extra_services' => $this->getExtraServicesDetails($extraServices),
            'subtotal' => $subtotal,
            'tax_rate' => $taxRate,
            'tax_amount' => $taxAmount,
            'total_fare' => $totalFare,
            'distance_km' => round($distanceInKm, 2),
            'booking_type' => $bookingType,
            'passengers' => $passengers
        ];

        // Add surcharges to fare details if applicable
        if ($bookingType === 'airport') {
            $result['airport_surcharge'] = $airportSurcharge;
        }

        if ($isWeekend) {
            $result['weekend_surcharge'] = $weekendSurcharge;
        }

        if ($isNightTime) {
            $result['night_surcharge'] = $nightSurcharge;
        }

        if ($isHoliday) {
            $result['holiday_surcharge'] = $holidaySurcharge;
        }

        return $result;
    }

    /**
     * Get multiplier based on vehicle type
     *
     * @param string $vehicleType
     * @return float
     */
    protected function getVehicleMultiplier($vehicleType)
    {
        switch (strtolower($vehicleType)) {
            case 'economy':
                return 0.8;
            case 'sedan':
                return 1.0;
            case 'suv':
                return 1.3;
            case 'luxury':
                return 1.8;
            case 'van':
                return 1.5;
            case 'limo':
                return 2.5;
            default:
                return 1.0;
        }
    }

    /**
     * Get directions between two addresses
     *
     * @param string $origin
     * @param string $destination
     * @return array|null
     */
    public function getDirections($origin, $destination)
    {
        // Skip API call if Directions API is disabled
        if (!$this->useDirections) {
            Log::info('Directions API is disabled.');
            return null;
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/directions/json', [
                'origin' => $origin,
                'destination' => $destination,
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($response->successful() && $data['status'] === 'OK') {
                $route = $data['routes'][0];
                $leg = $route['legs'][0];

                $steps = [];
                foreach ($leg['steps'] as $step) {
                    $steps[] = [
                        'distance' => $step['distance']['text'],
                        'duration' => $step['duration']['text'],
                        'instructions' => strip_tags($step['html_instructions']),
                        'start_location' => $step['start_location'],
                        'end_location' => $step['end_location'],
                    ];
                }

                return [
                    'distance' => [
                        'value' => $leg['distance']['value'], // in meters
                        'text' => $leg['distance']['text'],
                    ],
                    'duration' => [
                        'value' => $leg['duration']['value'], // in seconds
                        'text' => $leg['duration']['text'],
                    ],
                    'start_address' => $leg['start_address'],
                    'end_address' => $leg['end_address'],
                    'steps' => $steps,
                    'overview_polyline' => $route['overview_polyline']['points'],
                ];
            }

            Log::warning('Google Maps Directions API error', [
                'status' => $data['status'] ?? 'Unknown',
                'error_message' => $data['error_message'] ?? 'No error message',
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Google Maps Directions API exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Calculate distance between two points using Haversine formula (fallback)
     *
     * @param string $origin
     * @param string $destination
     * @return array
     */
    protected function calculateDistanceHaversine($origin, $destination)
    {
        // First, we need to geocode the addresses to get coordinates
        $originCoords = $this->geocodeAddress($origin);
        $destCoords = $this->geocodeAddress($destination);

        // If geocoding fails, use a default distance
        if (!$originCoords || !$destCoords) {
            Log::warning('Geocoding failed for Haversine calculation. Using default distance.', [
                'origin' => $origin,
                'destination' => $destination
            ]);

            // Return a default distance of 10 km (or equivalent in miles)
            $defaultDistance = 10000; // 10 km in meters
            $defaultDuration = 1200; // 20 minutes in seconds

            return [
                'distance' => [
                    'value' => $defaultDistance,
                    'text' => $this->formatDistance($defaultDistance)
                ],
                'duration' => [
                    'value' => $defaultDuration,
                    'text' => $this->formatDuration($defaultDuration)
                ]
            ];
        }

        // Calculate distance using Haversine formula
        $earthRadius = 6371000; // meters

        $latFrom = deg2rad($originCoords['lat']);
        $lonFrom = deg2rad($originCoords['lng']);
        $latTo = deg2rad($destCoords['lat']);
        $lonTo = deg2rad($destCoords['lng']);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) + cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
        $distance = $angle * $earthRadius;

        // Ensure minimum distance of 1 km
        $distance = max($distance, 1000);

        // Estimate duration (assuming average speed of 50 km/h in urban areas)
        $durationSeconds = ($distance / 1000) * (60 * 60 / 50);

        return [
            'distance' => [
                'value' => round($distance), // in meters
                'text' => $this->formatDistance($distance)
            ],
            'duration' => [
                'value' => round($durationSeconds), // in seconds
                'text' => $this->formatDuration($durationSeconds)
            ]
        ];
    }

    /**
     * Format distance for display
     *
     * @param float $distance Distance in meters
     * @return string
     */
    protected function formatDistance($distance)
    {
        $distanceUnit = SettingsHelper::get('distance_unit', 'miles');

        if ($distanceUnit === 'miles') {
            $miles = $distance * 0.000621371; // Convert meters to miles
            if ($miles < 0.1) {
                return round($distance * 3.28084) . ' ft'; // Show feet for very short distances
            } else {
                return round($miles, 1) . ' mi';
            }
        } else {
            if ($distance < 1000) {
                return round($distance) . ' m';
            } else {
                return round($distance / 1000, 1) . ' km';
            }
        }
    }

    /**
     * Format duration for display
     *
     * @param float $seconds Duration in seconds
     * @return string
     */
    protected function formatDuration($seconds)
    {
        $minutes = round($seconds / 60);

        if ($minutes < 60) {
            return $minutes . ' mins';
        } else {
            $hours = floor($minutes / 60);
            $mins = $minutes % 60;
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ($mins > 0 ? ' ' . $mins . ' min' . ($mins > 1 ? 's' : '') : '');
        }
    }

    /**
     * Calculate total cost for extra services
     *
     * @param array $extraServices
     * @return float
     */
    protected function calculateExtraServicesTotal($extraServices)
    {
        $total = 0;
        $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();

        foreach ($extraServices as $service => $enabled) {
            if ($enabled && isset($extraServicesSettings[$service]) && $extraServicesSettings[$service]['enabled']) {
                $total += $extraServicesSettings[$service]['fee'];
            }
        }

        return $total;
    }

    /**
     * Get detailed breakdown of extra services with pricing
     *
     * @param array $extraServices
     * @return array
     */
    protected function getExtraServicesDetails($extraServices)
    {
        $details = [];
        $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();

        foreach ($extraServices as $service => $enabled) {
            if ($enabled && isset($extraServicesSettings[$service]) && $extraServicesSettings[$service]['enabled']) {
                $details[$service] = $extraServicesSettings[$service]['fee'];
            }
        }

        return $details;
    }
}
