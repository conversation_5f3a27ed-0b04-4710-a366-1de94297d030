<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\User;
use App\Models\Payment;
use App\Models\EmailLog;
use App\Models\EmailPreference;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use App\Mail\BookingConfirmation;
use App\Mail\BookingReminder;
use App\Mail\BookingCancellation;
use App\Mail\PaymentConfirmation;
use App\Mail\DriverAssignment;
use App\Mail\WelcomeEmail;
use App\Mail\ContactFormSubmission;
use App\Mail\PasswordReset;
use App\Mail\BookingStatusUpdate;

class EmailService
{
    /**
     * Send booking confirmation email
     *
     * @param Booking $booking
     * @return bool
     */
    public static function sendBookingConfirmation(Booking $booking)
    {
        try {
            $user = $booking->user;
            if (!$user || !$user->email) {
                Log::warning('Cannot send booking confirmation: No user email', ['booking_id' => $booking->id]);
                return false;
            }

            // Check user email preferences
            if (!EmailPreference::canReceiveEmail($user, 'booking_confirmations')) {
                Log::info('Booking confirmation email skipped due to user preferences', [
                    'booking_id' => $booking->id,
                    'user_id' => $user->id
                ]);
                return false;
            }

            Mail::to($user->email)->queue(new BookingConfirmation($booking));

            self::logEmail([
                'recipient_email' => $user->email,
                'recipient_name' => $user->name,
                'subject' => 'Booking Confirmation - #' . $booking->booking_number,
                'template' => 'booking_confirmation',
                'booking_id' => $booking->id,
                'user_id' => $user->id,
                'status' => 'queued'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking confirmation email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send booking reminder email
     *
     * @param Booking $booking
     * @param string $reminderType (client|driver)
     * @return bool
     */
    public static function sendBookingReminder(Booking $booking, $reminderType = 'client')
    {
        try {
            $recipient = $reminderType === 'driver' ? $booking->driver : $booking->user;

            if (!$recipient || !$recipient->email) {
                Log::warning('Cannot send booking reminder: No recipient email', [
                    'booking_id' => $booking->id,
                    'reminder_type' => $reminderType
                ]);
                return false;
            }

            // Check user email preferences
            $preferenceType = $reminderType === 'driver' ? 'ride_requests' : 'booking_reminders';
            if (!EmailPreference::canReceiveEmail($recipient, $preferenceType)) {
                Log::info('Booking reminder email skipped due to user preferences', [
                    'booking_id' => $booking->id,
                    'reminder_type' => $reminderType,
                    'user_id' => $recipient->id
                ]);
                return false;
            }

            Mail::to($recipient->email)->queue(new BookingReminder($booking, $reminderType));

            self::logEmail([
                'recipient_email' => $recipient->email,
                'recipient_name' => $recipient->name,
                'subject' => 'Booking Reminder - #' . $booking->booking_number,
                'template' => 'booking_reminder_' . $reminderType,
                'booking_id' => $booking->id,
                'user_id' => $recipient->id,
                'status' => 'queued'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking reminder email', [
                'booking_id' => $booking->id,
                'reminder_type' => $reminderType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send booking cancellation email
     *
     * @param Booking $booking
     * @param string $recipientType (client|driver)
     * @return bool
     */
    public static function sendBookingCancellation(Booking $booking, $recipientType = 'client')
    {
        try {
            $recipient = $recipientType === 'driver' ? $booking->driver : $booking->user;

            if (!$recipient || !$recipient->email) {
                Log::warning('Cannot send booking cancellation: No recipient email', [
                    'booking_id' => $booking->id,
                    'recipient_type' => $recipientType
                ]);
                return false;
            }

            Mail::to($recipient->email)->queue(new BookingCancellation($booking, $recipientType));

            self::logEmail([
                'recipient_email' => $recipient->email,
                'recipient_name' => $recipient->name,
                'subject' => 'Booking Cancellation - #' . $booking->booking_number,
                'template' => 'booking_cancellation_' . $recipientType,
                'booking_id' => $booking->id,
                'user_id' => $recipient->id,
                'status' => 'queued'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking cancellation email', [
                'booking_id' => $booking->id,
                'recipient_type' => $recipientType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send payment confirmation email
     *
     * @param Payment $payment
     * @return bool
     */
    public static function sendPaymentConfirmation(Payment $payment)
    {
        try {
            $user = $payment->user;
            if (!$user || !$user->email) {
                Log::warning('Cannot send payment confirmation: No user email', ['payment_id' => $payment->id]);
                return false;
            }

            // Check user email preferences
            if (!EmailPreference::canReceiveEmail($user, 'payment_confirmations')) {
                Log::info('Payment confirmation email skipped due to user preferences', [
                    'payment_id' => $payment->id,
                    'user_id' => $user->id
                ]);
                return false;
            }

            Mail::to($user->email)->queue(new PaymentConfirmation($payment));

            self::logEmail([
                'recipient_email' => $user->email,
                'recipient_name' => $user->name,
                'subject' => 'Payment Confirmation - #' . $payment->booking->booking_number,
                'template' => 'payment_confirmation',
                'booking_id' => $payment->booking_id,
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'status' => 'queued'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation email', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }



    /**
     * Send welcome email to new user
     *
     * @param User $user
     * @return bool
     */
    public static function sendWelcomeEmail(User $user)
    {
        try {
            if (!$user->email) {
                Log::warning('Cannot send welcome email: No user email', ['user_id' => $user->id]);
                return false;
            }

            Mail::to($user->email)->queue(new WelcomeEmail($user));

            self::logEmail([
                'recipient_email' => $user->email,
                'recipient_name' => $user->name,
                'subject' => 'Welcome to ' . config('app.name'),
                'template' => 'welcome_' . $user->role,
                'user_id' => $user->id,
                'status' => 'queued'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send welcome email', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send booking status update email
     *
     * @param Booking $booking
     * @param string $oldStatus
     * @param string $newStatus
     * @return bool
     */
    public static function sendBookingStatusUpdate(Booking $booking, string $oldStatus, string $newStatus)
    {
        try {
            $user = $booking->user;
            if (!$user || !$user->email) {
                Log::warning('Cannot send status update email: No user email', ['booking_id' => $booking->id]);
                return false;
            }

            // Check user email preferences
            if (!EmailPreference::canReceiveEmail($user, 'status_updates')) {
                Log::info('Status update email skipped due to user preferences', [
                    'booking_id' => $booking->id,
                    'user_id' => $user->id
                ]);
                return false;
            }

            Mail::to($user->email)->queue(new BookingStatusUpdate($booking, $oldStatus, $newStatus));

            self::logEmail([
                'recipient_email' => $user->email,
                'recipient_name' => $user->name,
                'subject' => 'Booking Status Update - #' . $booking->booking_number,
                'template' => 'booking_status_update',
                'user_id' => $user->id,
                'booking_id' => $booking->id,
                'status' => 'queued',
                'metadata' => json_encode([
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus
                ])
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking status update email', [
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send driver assignment email
     *
     * @param Booking $booking
     * @param string $recipient (client|driver)
     * @return bool
     */
    public static function sendDriverAssignment(Booking $booking, string $recipient = 'client')
    {
        try {
            if ($recipient === 'client') {
                $user = $booking->user;
                if (!$user || !$user->email) {
                    Log::warning('Cannot send driver assignment email: No client email', ['booking_id' => $booking->id]);
                    return false;
                }

                // Check user email preferences
                if (!EmailPreference::canReceiveEmail($user, 'driver_assignments')) {
                    Log::info('Driver assignment email skipped due to user preferences', [
                        'booking_id' => $booking->id,
                        'user_id' => $user->id
                    ]);
                    return false;
                }

                Mail::to($user->email)->queue(new DriverAssignment($booking, 'client'));

                self::logEmail([
                    'recipient_email' => $user->email,
                    'recipient_name' => $user->name,
                    'subject' => 'Driver Assigned to Your Booking',
                    'template' => 'driver_assignment_client',
                    'user_id' => $user->id,
                    'booking_id' => $booking->id,
                    'status' => 'queued'
                ]);
            } else {
                $driver = $booking->driver;
                if (!$driver || !$driver->email) {
                    Log::warning('Cannot send driver assignment email: No driver email', ['booking_id' => $booking->id]);
                    return false;
                }

                // Check driver email preferences
                if (!EmailPreference::canReceiveEmail($driver, 'ride_requests')) {
                    Log::info('Driver assignment email skipped due to driver preferences', [
                        'booking_id' => $booking->id,
                        'driver_id' => $driver->id
                    ]);
                    return false;
                }

                Mail::to($driver->email)->queue(new DriverAssignment($booking, 'driver'));

                self::logEmail([
                    'recipient_email' => $driver->email,
                    'recipient_name' => $driver->name,
                    'subject' => 'New Ride Assignment',
                    'template' => 'driver_assignment_driver',
                    'user_id' => $driver->id,
                    'booking_id' => $booking->id,
                    'status' => 'queued'
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send driver assignment email', [
                'booking_id' => $booking->id,
                'recipient' => $recipient,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send contact form submission email
     *
     * @param array $contactData
     * @return bool
     */
    public static function sendContactFormSubmission(array $contactData)
    {
        try {
            $adminEmail = SettingsService::getCompanyEmail();
            if (!$adminEmail) {
                Log::warning('Cannot send contact form: No admin email configured');
                return false;
            }

            Mail::to($adminEmail)->queue(new ContactFormSubmission($contactData));

            self::logEmail([
                'recipient_email' => $adminEmail,
                'recipient_name' => 'Admin',
                'subject' => 'New Contact Form Submission',
                'template' => 'contact_form',
                'status' => 'queued',
                'metadata' => json_encode($contactData)
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send contact form email', [
                'contact_data' => $contactData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }



    /**
     * Send bulk email to users
     *
     * @param array $userIds
     * @param string $subject
     * @param string $message
     * @param string $template
     * @return array
     */
    public static function sendBulkEmail(array $userIds, string $subject, string $message, string $template = 'general')
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        $users = User::whereIn('id', $userIds)->where('email', '!=', null)->get();

        foreach ($users as $user) {
            try {
                Mail::to($user->email)->queue(new \App\Mail\GeneralEmail($user, $subject, $message, $template));

                self::logEmail([
                    'recipient_email' => $user->email,
                    'recipient_name' => $user->name,
                    'subject' => $subject,
                    'template' => $template,
                    'user_id' => $user->id,
                    'status' => 'queued',
                    'metadata' => json_encode(['bulk_email' => true])
                ]);

                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Failed to send to {$user->email}: " . $e->getMessage();

                Log::error('Failed to send bulk email', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Test email configuration
     *
     * @param string $testEmail
     * @return bool
     */
    public static function testEmailConfiguration(string $testEmail)
    {
        try {
            Mail::to($testEmail)->send(new \App\Mail\TestEmail());

            self::logEmail([
                'recipient_email' => $testEmail,
                'recipient_name' => 'Test Recipient',
                'subject' => 'Email Configuration Test',
                'template' => 'test_email',
                'status' => 'sent'
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Email configuration test failed', [
                'test_email' => $testEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get email statistics
     *
     * @param array $filters
     * @return array
     */
    public static function getEmailStatistics(array $filters = [])
    {
        $query = EmailLog::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['template'])) {
            $query->where('template', $filters['template']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $total = $query->count();
        $sent = $query->where('status', 'sent')->count();
        $queued = $query->where('status', 'queued')->count();
        $failed = $query->where('status', 'failed')->count();

        $templateStats = EmailLog::selectRaw('template, COUNT(*) as count')
            ->groupBy('template')
            ->pluck('count', 'template')
            ->toArray();

        return [
            'total' => $total,
            'sent' => $sent,
            'queued' => $queued,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
            'template_stats' => $templateStats
        ];
    }

    /**
     * Log email activity
     *
     * @param array $data
     * @return void
     */
    private static function logEmail(array $data)
    {
        try {
            EmailLog::create($data);
        } catch (\Exception $e) {
            Log::error('Failed to log email', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
        }
    }
}
