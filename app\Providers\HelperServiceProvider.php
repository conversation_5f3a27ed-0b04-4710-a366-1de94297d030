<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the settings helper as a singleton
        $this->app->singleton('settings', function ($app) {
            return new \App\Helpers\SettingsHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Make the settings helper available in all views
        view()->composer('*', function ($view) {
            $view->with('settings', app('settings'));
        });
    }
}
