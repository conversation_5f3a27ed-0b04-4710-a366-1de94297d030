<?php $__env->startSection('title', '<PERSON><PERSON> Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Settings</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.emails.index')); ?>">Email Management</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">SMTP Configuration</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('admin.emails.settings.update')); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mail_driver" class="form-label">Mail Driver</label>
                                    <select name="mail_driver" id="mail_driver" class="form-select" required>
                                        <option value="smtp" <?php echo e(($emailSettings['mail_driver'] ?? 'smtp') === 'smtp' ? 'selected' : ''); ?>>SMTP</option>
                                        <option value="sendmail" <?php echo e(($emailSettings['mail_driver'] ?? '') === 'sendmail' ? 'selected' : ''); ?>>Sendmail</option>
                                        <option value="mailgun" <?php echo e(($emailSettings['mail_driver'] ?? '') === 'mailgun' ? 'selected' : ''); ?>>Mailgun</option>
                                        <option value="ses" <?php echo e(($emailSettings['mail_driver'] ?? '') === 'ses' ? 'selected' : ''); ?>>Amazon SES</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mail_encryption" class="form-label">Encryption</label>
                                    <select name="mail_encryption" id="mail_encryption" class="form-select">
                                        <option value="tls" <?php echo e(($emailSettings['mail_encryption'] ?? 'tls') === 'tls' ? 'selected' : ''); ?>>TLS</option>
                                        <option value="ssl" <?php echo e(($emailSettings['mail_encryption'] ?? '') === 'ssl' ? 'selected' : ''); ?>>SSL</option>
                                        <option value="none" <?php echo e(($emailSettings['mail_encryption'] ?? '') === 'none' ? 'selected' : ''); ?>>None</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="smtp-settings" style="<?php echo e(($emailSettings['mail_driver'] ?? 'smtp') === 'smtp' ? '' : 'display: none;'); ?>">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="mail_host" class="form-label">SMTP Host</label>
                                        <input type="text" name="mail_host" id="mail_host" class="form-control" 
                                               value="<?php echo e($emailSettings['mail_host'] ?? ''); ?>" 
                                               placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="mail_port" class="form-label">Port</label>
                                        <input type="number" name="mail_port" id="mail_port" class="form-control" 
                                               value="<?php echo e($emailSettings['mail_port'] ?? '587'); ?>" 
                                               placeholder="587">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mail_username" class="form-label">Username</label>
                                        <input type="text" name="mail_username" id="mail_username" class="form-control" 
                                               value="<?php echo e($emailSettings['mail_username'] ?? ''); ?>" 
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mail_password" class="form-label">Password</label>
                                        <input type="password" name="mail_password" id="mail_password" class="form-control" 
                                               value="<?php echo e($emailSettings['mail_password'] ?? ''); ?>" 
                                               placeholder="Your email password or app password">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mail_from_address" class="form-label">From Email Address</label>
                                    <input type="email" name="mail_from_address" id="mail_from_address" class="form-control" 
                                           value="<?php echo e($emailSettings['mail_from_address'] ?? ''); ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mail_from_name" class="form-label">From Name</label>
                                    <input type="text" name="mail_from_name" id="mail_from_name" class="form-control" 
                                           value="<?php echo e($emailSettings['mail_from_name'] ?? ''); ?>" 
                                           placeholder="Your Company Name" required>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to Email Management
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-content-save"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Test Email -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Test Email Configuration</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">Send a test email to verify your configuration is working correctly.</p>
                    
                    <form id="testEmailForm">
                        <div class="mb-3">
                            <label for="test_email" class="form-label">Test Email Address</label>
                            <input type="email" id="test_email" class="form-control" 
                                   placeholder="<EMAIL>" required>
                        </div>
                        <button type="submit" class="btn btn-outline-primary w-100" id="testEmailBtn">
                            <i class="mdi mdi-email-send"></i> Send Test Email
                        </button>
                    </form>

                    <div id="testEmailResult" class="mt-3" style="display: none;"></div>
                </div>
            </div>

            <!-- Email Configuration Tips -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Tips</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="mdi mdi-information"></i> Gmail Configuration</h6>
                        <ul class="mb-0">
                            <li>Host: smtp.gmail.com</li>
                            <li>Port: 587 (TLS) or 465 (SSL)</li>
                            <li>Use App Password instead of regular password</li>
                            <li>Enable 2-factor authentication</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="mdi mdi-alert"></i> Security Note</h6>
                        <p class="mb-0">Always use app-specific passwords for email services. Never use your main account password.</p>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="mdi mdi-check-circle"></i> Best Practices</h6>
                        <ul class="mb-0">
                            <li>Use a dedicated email for sending</li>
                            <li>Monitor email delivery rates</li>
                            <li>Set up SPF and DKIM records</li>
                            <li>Test regularly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle SMTP settings based on mail driver
    const mailDriverSelect = document.getElementById('mail_driver');
    const smtpSettings = document.querySelector('.smtp-settings');
    
    mailDriverSelect.addEventListener('change', function() {
        if (this.value === 'smtp') {
            smtpSettings.style.display = '';
        } else {
            smtpSettings.style.display = 'none';
        }
    });

    // Test email functionality
    const testEmailForm = document.getElementById('testEmailForm');
    const testEmailBtn = document.getElementById('testEmailBtn');
    const testEmailResult = document.getElementById('testEmailResult');

    testEmailForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const testEmail = document.getElementById('test_email').value;
        
        // Disable button and show loading
        testEmailBtn.disabled = true;
        testEmailBtn.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> Sending...';
        
        // Send test email
        fetch('<?php echo e(route("admin.emails.test")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                test_email: testEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            testEmailResult.style.display = 'block';
            
            if (data.success) {
                testEmailResult.innerHTML = `
                    <div class="alert alert-success">
                        <i class="mdi mdi-check-circle"></i> ${data.message}
                    </div>
                `;
            } else {
                testEmailResult.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="mdi mdi-alert-circle"></i> ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            testEmailResult.style.display = 'block';
            testEmailResult.innerHTML = `
                <div class="alert alert-danger">
                    <i class="mdi mdi-alert-circle"></i> An error occurred while sending the test email.
                </div>
            `;
        })
        .finally(() => {
            // Re-enable button
            testEmailBtn.disabled = false;
            testEmailBtn.innerHTML = '<i class="mdi mdi-email-send"></i> Send Test Email';
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/settings.blade.php ENDPATH**/ ?>