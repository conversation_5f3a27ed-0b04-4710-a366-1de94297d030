@extends('layouts.client')

@section('title', 'Notification Settings')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/notifications.css') }}">
@endsection

@section('content')
<div class="notification-container">
    <!-- Page Header -->
    <div class="page-header mb-4 p-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>Notification Settings</h2>
                    <p>Customize how and when you receive notifications from Ynr Cars</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{{ route('client.dashboard') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Notification Types -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4 mb-md-0">
            <div class="notification-type">
                <i class="fas fa-envelope notification-icon"></i>
                <h4>Email Notifications</h4>
                <p>Receive updates directly to your inbox</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark p-2">
                        <i class="fas fa-info-circle me-1"></i> Sent to {{ auth()->user()->email }}
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4 mb-md-0">
            <div class="notification-type">
                <i class="fas fa-sms notification-icon"></i>
                <h4>SMS Notifications</h4>
                <p>Get text messages for important updates</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark p-2">
                        <i class="fas fa-info-circle me-1"></i> Sent to {{ auth()->user()->phone ?? 'No phone number' }}
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="notification-type">
                <i class="fas fa-bell notification-icon"></i>
                <h4>Push Notifications</h4>
                <p>Receive alerts on your device</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark p-2">
                        <i class="fas fa-info-circle me-1"></i> Sent to your devices
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Preferences Form -->
    <form action="{{ route('client.notifications.update-settings') }}" method="POST">
        @csrf
        <div class="card notification-card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-sliders-h me-2"></i> Manage Your Notification Preferences</h4>
            </div>
            <div class="card-body p-0">
                <!-- Booking Updates Section -->
                <div class="notification-section">
                    <div class="notification-title">
                        <i class="fas fa-calendar-check me-2 text-primary"></i> Booking Updates
                    </div>
                    <div class="notification-description">
                        Notifications about your bookings, including confirmations, changes, and reminders.
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_booking_updates" name="email_booking_updates" {{ $settings->email_booking_updates ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_booking_updates">
                                    <i class="fas fa-envelope me-2 text-muted"></i> Email
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_booking_updates" name="sms_booking_updates" {{ $settings->sms_booking_updates ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="sms_booking_updates">
                                    <i class="fas fa-sms me-2 text-muted"></i> SMS
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="push_booking_updates" name="push_booking_updates" {{ $settings->push_booking_updates ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="push_booking_updates">
                                    <i class="fas fa-bell me-2 text-muted"></i> Push
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Promotions Section -->
                <div class="notification-section">
                    <div class="notification-title">
                        <i class="fas fa-tag me-2 text-success"></i> Promotions and Offers
                    </div>
                    <div class="notification-description">
                        Special deals, discounts, and promotional offers from Ynr Cars.
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_promotions" name="email_promotions" {{ $settings->email_promotions ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_promotions">
                                    <i class="fas fa-envelope me-2 text-muted"></i> Email
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_promotions" name="sms_promotions" {{ $settings->sms_promotions ?? false ? 'checked' : '' }}>
                                <label class="form-check-label" for="sms_promotions">
                                    <i class="fas fa-sms me-2 text-muted"></i> SMS
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="push_promotions" name="push_promotions" {{ $settings->push_promotions ?? false ? 'checked' : '' }}>
                                <label class="form-check-label" for="push_promotions">
                                    <i class="fas fa-bell me-2 text-muted"></i> Push
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Notifications Section -->
                <div class="notification-section">
                    <div class="notification-title">
                        <i class="fas fa-credit-card me-2 text-danger"></i> Payment Notifications
                    </div>
                    <div class="notification-description">
                        Updates about payments, receipts, and invoices.
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_payment" name="email_payment" {{ $settings->email_payment ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_payment">
                                    <i class="fas fa-envelope me-2 text-muted"></i> Email
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_payment" name="sms_payment" {{ $settings->sms_payment ?? false ? 'checked' : '' }}>
                                <label class="form-check-label" for="sms_payment">
                                    <i class="fas fa-sms me-2 text-muted"></i> SMS
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="push_payment" name="push_payment" {{ $settings->push_payment ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="push_payment">
                                    <i class="fas fa-bell me-2 text-muted"></i> Push
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Save Preferences
                </button>
            </div>
        </div>
    </form>

    <!-- About Notifications Section -->
    <div class="card notification-card mt-5">
        <div class="card-header">
            <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> About Notifications</h4>
        </div>
        <div class="card-body">
            <div class="about-notifications">
                <p>We use notifications to keep you informed about your bookings, payments, and special offers. Here's what each notification type means:</p>

                <ul>
                    <li><strong>Email Notifications:</strong> Sent to the email address associated with your account. These are great for detailed information and records.</li>
                    <li><strong>SMS Notifications:</strong> Text messages sent to your registered phone number. These are perfect for time-sensitive updates.</li>
                    <li><strong>Push Notifications:</strong> Alerts that appear on your device when using our mobile app. These provide immediate updates.</li>
                </ul>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i> <strong>Tip:</strong> You can change your notification preferences at any time. Please note that some service-related notifications may still be sent regardless of your preferences.
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to notification type cards
        const notificationTypes = document.querySelectorAll('.notification-type');
        notificationTypes.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate__animated', 'animate__fadeInUp');
            }, 100 * index);
        });

        // Add animation to form switches
        const switches = document.querySelectorAll('.form-check-input');
        switches.forEach((switchEl) => {
            switchEl.addEventListener('change', function() {
                const label = this.nextElementSibling;
                label.classList.add('animate__animated', 'animate__pulse');
                setTimeout(() => {
                    label.classList.remove('animate__animated', 'animate__pulse');
                }, 500);
            });
        });

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    });
</script>
@endsection
@endsection
