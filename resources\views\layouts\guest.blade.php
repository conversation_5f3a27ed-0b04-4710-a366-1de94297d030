<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="currency-symbol" content="{{ $currencySymbol ?? \App\Services\SettingsService::getCurrencySymbol() }}">
    <meta name="currency-code" content="{{ $currencyCode ?? \App\Services\SettingsService::getCurrencyCode() }}">
    <meta name="distance-unit" content="{{ \App\Services\SettingsService::getDistanceUnit() }}">

    <!-- Autocomplete Settings -->
    @php
        $autocompleteSettings = \App\Services\SettingsService::getAutocompleteSettings();
    @endphp
    <meta name="autocomplete-enabled" content="{{ $autocompleteSettings['enabled'] ? 'true' : 'false' }}">
    <meta name="autocomplete-restrict-country" content="{{ $autocompleteSettings['restrict_country'] ? 'true' : 'false' }}">
    <meta name="autocomplete-country" content="{{ $autocompleteSettings['country'] }}">
    <meta name="autocomplete-types" content="{{ $autocompleteSettings['types'] }}">
    <meta name="autocomplete-bias-radius" content="{{ $autocompleteSettings['bias_radius'] }}">
    <meta name="autocomplete-use-strict-bounds" content="{{ $autocompleteSettings['use_strict_bounds'] ? 'true' : 'false' }}">
    <meta name="autocomplete-fields" content="{{ $autocompleteSettings['fields'] }}">

    <title>@yield('title') - {{ $companyName ?? config('app.name', 'Ynr Cars') }}</title>

    <!-- Favicon -->
    @if(\App\Services\SettingsService::get('favicon'))
        <link rel="icon" href="{{ asset('storage/' . \App\Services\SettingsService::get('favicon')) }}" type="image/x-icon">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('css/pagination.css') }}">

    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            --bs-primary: {{ $primaryColor ?? '#ee393d' }};
            --bs-secondary: {{ $secondaryColor ?? '#343a40' }};
        }

        .btn-primary {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10) }};
            border-color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10) }};
        }

        .btn-outline-primary {
            color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: var(--bs-secondary);
        }

        .navbar-light {
            background-color: white;
        }

        .text-primary {
            color: var(--bs-primary) !important;
        }

        .bg-primary {
            background-color: var(--bs-primary) !important;
        }

        a {
            color: var(--bs-primary);
        }

        a:hover {
            color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -15) }};
        }

        /* Custom preloader spinner */
        .preloader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--bs-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>

    @yield('styles')
</head>
<body>
    <!-- Preloader -->
    <div class="preloader">
        <div class="preloader-content">
            <h2 style="color: {{ $primaryColor ?? '#ee393d' }}; font-weight: 700; margin-bottom: 20px;">{{ $companyName ?? 'YNR CARS' }}</h2>
            <div class="preloader-spinner"></div>
        </div>
    </div>

    <header>
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="{{ route('home') }}">
                    @php
                        $logoPath = \App\Services\SettingsService::get('logo');
                        $logoExists = $logoPath && file_exists(public_path('storage/' . $logoPath));
                    @endphp

                    @if($logoExists)
                        <img src="{{ asset('storage/' . $logoPath) }}" alt="{{ $companyName ?? config('app.name', 'Ynr Cars') }}" height="40">
                    @else
                        <span style="font-weight: bold; font-size: 24px; color: #ee393d;">YNR <span style="color: #343a40;">Cars</span></span>
                    @endif
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}" href="{{ route('services') }}">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fleet') ? 'active' : '' }}" href="{{ route('fleet') }}">Fleet</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">About Us</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">Contact</a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('terms-and-conditions') ? 'active' : '' }}" href="{{ route('terms-and-conditions') }}">T&C</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        @guest
                            <a href="{{ route('login') }}" class="btn btn-link text-black text-decoration-none">Login</a>
                            <a href="{{ route('register') }}" class="btn btn-primary ms-2">Register</a>
                        @else
                            <div class="dropdown">
                                <a class="btn btn-link text-black text-decoration-none dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ Auth::user()->name }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    @if(Auth::user()->role === 'admin')
                                        <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                    @elseif(Auth::user()->role === 'client')
                                        <li><a class="dropdown-item" href="{{ route('client.dashboard') }}">Dashboard</a></li>
                                    @elseif(Auth::user()->role === 'driver')
                                        <li><a class="dropdown-item" href="{{ route('driver.dashboard') }}">Dashboard</a></li>
                                    @else
                                        <li><a class="dropdown-item" href="{{ route('dashboard') }}">Dashboard</a></li>
                                    @endif
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">Logout</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        @endguest
                    </div>
                </div>
            </div>
        </nav>

        @hasSection('page-header')
            <div class="page-header">
                <div class="container">
                    <h1>@yield('page-header')</h1>
                    @hasSection('breadcrumbs')
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                @yield('breadcrumbs')
                            </ol>
                        </nav>
                    @endif
                </div>
            </div>
        @endif
    </header>

    <main>
        @yield('content')
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5>{{ config('app.name', 'Ynr Cars') }}</h5>
                    <p>Ynr Cars Taxi Services for all your needs. Experience comfort, reliability, and elegance.</p>
                    <div class="social-icons">
                        @if($socialMediaSettings['facebook_url'])
                            <a href="{{ $socialMediaSettings['facebook_url'] }}" target="_blank" rel="noopener"><i class="fab fa-facebook-f"></i></a>
                        @endif
                        @if($socialMediaSettings['twitter_url'])
                            <a href="{{ $socialMediaSettings['twitter_url'] }}" target="_blank" rel="noopener"><i class="fab fa-twitter"></i></a>
                        @endif
                        @if($socialMediaSettings['instagram_url'])
                            <a href="{{ $socialMediaSettings['instagram_url'] }}" target="_blank" rel="noopener"><i class="fab fa-instagram"></i></a>
                        @endif
                        @if($socialMediaSettings['linkedin_url'])
                            <a href="{{ $socialMediaSettings['linkedin_url'] }}" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
                        @endif
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="{{ route('about') }}">About Us</a></li>
                        <li><a href="{{ route('services') }}">Services</a></li>
                        <li><a href="{{ route('fleet') }}">Fleet</a></li>

                        <li><a href="{{ route('faq') }}">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Support</h5>
                    <ul>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                        <li><a href="{{ route('privacy-policy') }}">Privacy Policy</a></li>
                        <li><a href="{{ route('terms-and-conditions') }}">Terms and Conditions</a></li>
                        <li><a href="{{ route('faq') }}">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Contact Us</h5>
                    <p>
                        <i class="fas fa-phone me-2"></i> {{ $companyPhone }}<br>
                        <i class="fas fa-envelope me-2"></i> {{ $companyEmail }}<br>
                        <i class="fas fa-map-marker-alt me-2"></i> {{ $companyAddress }}
                    </p>
                </div>
            </div>
            <div class="text-center copyright">
                <p>&copy; {{ date('Y') }} {{ $companyName }}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="{{ asset('js/main.js') }}"></script>
    @yield('scripts')
</body>
</html>
