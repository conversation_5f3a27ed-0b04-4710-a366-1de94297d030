<?php

namespace App\Notifications;

use App\Models\Booking;
use App\Notifications\Channels\LogChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewRideAvailable extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The booking instance.
     *
     * @var \App\Models\Booking
     */
    protected $booking;

    /**
     * Create a new notification instance.
     *
     * @param \App\Models\Booking $booking
     * @return void
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [LogChannel::class, 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Ride Available - #' . $this->booking->booking_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('A new ride is available for you to accept.')
            ->line('Booking Details:')
            ->line('Pickup: ' . $this->booking->pickup_address)
            ->line('Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->line('Vehicle: ' . $this->booking->vehicle->name)
            ->line('Fare: ' . \App\Services\SettingsService::getCurrencySymbol() . number_format($this->booking->amount, 2))
            ->action('View Ride Details', url(route('driver.rides.show', $this->booking->id)))
            ->line('Thank you for being a part of our service!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'pickup_address' => $this->booking->pickup_address,
            'pickup_date' => $this->booking->pickup_date->format('M d, Y h:i A'),
            'vehicle' => $this->booking->vehicle->name,
            'amount' => $this->booking->amount,
            'message' => 'A new ride is available for you to accept.',
        ];
    }
}
