<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class StorageHelper
{
    /**
     * Get the full URL for a stored file
     *
     * @param string|null $path
     * @param string $disk
     * @param string|null $default
     * @return string
     */
    public static function url(?string $path, string $disk = 'public', ?string $default = null): string
    {
        if (empty($path)) {
            return $default ?: self::getDefaultImage();
        }

        try {
            // Check if file exists
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->url($path);
            }
            
            // If file doesn't exist, return default
            return $default ?: self::getDefaultImage();
            
        } catch (\Exception $e) {
            return $default ?: self::getDefaultImage();
        }
    }

    /**
     * Get profile photo URL with fallback
     *
     * @param string|null $path
     * @return string
     */
    public static function profilePhotoUrl(?string $path): string
    {
        return self::url($path, 'public', self::getDefaultProfileImage());
    }

    /**
     * Get vehicle image URL with fallback
     *
     * @param string|null $path
     * @return string
     */
    public static function vehicleImageUrl(?string $path): string
    {
        return self::url($path, 'public', self::getDefaultVehicleImage());
    }

    /**
     * Get document URL (for viewing/downloading)
     *
     * @param string|null $path
     * @return string|null
     */
    public static function documentUrl(?string $path): ?string
    {
        if (empty($path)) {
            return null;
        }

        try {
            if (Storage::disk('public')->exists($path)) {
                return Storage::disk('public')->url($path);
            }
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if a file exists in storage
     *
     * @param string|null $path
     * @param string $disk
     * @return bool
     */
    public static function exists(?string $path, string $disk = 'public'): bool
    {
        if (empty($path)) {
            return false;
        }

        try {
            return Storage::disk($disk)->exists($path);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get file size in human readable format
     *
     * @param string|null $path
     * @param string $disk
     * @return string
     */
    public static function fileSize(?string $path, string $disk = 'public'): string
    {
        if (empty($path) || !self::exists($path, $disk)) {
            return 'Unknown';
        }

        try {
            $bytes = Storage::disk($disk)->size($path);
            return self::formatBytes($bytes);
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get file extension
     *
     * @param string|null $path
     * @return string
     */
    public static function fileExtension(?string $path): string
    {
        if (empty($path)) {
            return '';
        }

        return strtolower(pathinfo($path, PATHINFO_EXTENSION));
    }

    /**
     * Check if file is an image
     *
     * @param string|null $path
     * @return bool
     */
    public static function isImage(?string $path): bool
    {
        $extension = self::fileExtension($path);
        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * Check if file is a PDF
     *
     * @param string|null $path
     * @return bool
     */
    public static function isPdf(?string $path): bool
    {
        return self::fileExtension($path) === 'pdf';
    }

    /**
     * Get file icon class based on extension
     *
     * @param string|null $path
     * @return string
     */
    public static function fileIcon(?string $path): string
    {
        $extension = self::fileExtension($path);
        
        $icons = [
            'pdf' => 'fas fa-file-pdf text-danger',
            'doc' => 'fas fa-file-word text-primary',
            'docx' => 'fas fa-file-word text-primary',
            'xls' => 'fas fa-file-excel text-success',
            'xlsx' => 'fas fa-file-excel text-success',
            'jpg' => 'fas fa-file-image text-info',
            'jpeg' => 'fas fa-file-image text-info',
            'png' => 'fas fa-file-image text-info',
            'gif' => 'fas fa-file-image text-info',
            'zip' => 'fas fa-file-archive text-warning',
            'rar' => 'fas fa-file-archive text-warning',
        ];

        return $icons[$extension] ?? 'fas fa-file text-secondary';
    }

    /**
     * Get default profile image
     *
     * @return string
     */
    public static function getDefaultProfileImage(): string
    {
        return asset('images/default-avatar.png');
    }

    /**
     * Get default vehicle image
     *
     * @return string
     */
    public static function getDefaultVehicleImage(): string
    {
        return asset('images/default-vehicle.png');
    }

    /**
     * Get default image
     *
     * @return string
     */
    public static function getDefaultImage(): string
    {
        return asset('images/default-image.png');
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    private static function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Generate a secure download URL for documents
     *
     * @param string $path
     * @param int $expiresInMinutes
     * @return string
     */
    public static function secureDownloadUrl(string $path, int $expiresInMinutes = 60): string
    {
        return URL::temporarySignedRoute(
            'secure.download',
            now()->addMinutes($expiresInMinutes),
            ['path' => encrypt($path)]
        );
    }

    /**
     * Get storage statistics
     *
     * @return array
     */
    public static function getStorageStats(): array
    {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'by_type' => []
        ];

        $directories = [
            'profile-photos' => 'Profile Photos',
            'driver-documents' => 'Driver Documents',
            'vehicles' => 'Vehicle Images',
            'blog-posts' => 'Blog Posts'
        ];

        foreach ($directories as $dir => $label) {
            try {
                $files = Storage::disk('public')->allFiles($dir);
                $size = 0;
                
                foreach ($files as $file) {
                    $size += Storage::disk('public')->size($file);
                }
                
                $stats['by_type'][$dir] = [
                    'label' => $label,
                    'count' => count($files),
                    'size' => $size,
                    'size_formatted' => self::formatBytes($size)
                ];
                
                $stats['total_files'] += count($files);
                $stats['total_size'] += $size;
                
            } catch (\Exception $e) {
                $stats['by_type'][$dir] = [
                    'label' => $label,
                    'count' => 0,
                    'size' => 0,
                    'size_formatted' => '0 B'
                ];
            }
        }

        $stats['total_size_formatted'] = self::formatBytes($stats['total_size']);

        return $stats;
    }

    /**
     * Clean up orphaned files (files not referenced in database)
     *
     * @param string $type
     * @return int
     */
    public static function cleanupOrphanedFiles(string $type): int
    {
        // This would need to be implemented based on your specific models
        // For now, return 0
        return 0;
    }
}
