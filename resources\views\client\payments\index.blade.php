@extends('layouts.client')

@section('title', 'My Payments')

@section('styles')
<style>
    .payment-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .payment-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .payment-card .card-body {
        padding: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto 15px;
    }

    .stat-title {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #343a40;
        margin-bottom: 0;
    }

    .payment-method-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        background-color: #f8f9fa;
        color: #343a40;
    }

    .payment-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-failed {
        background-color: #f8d7da;
        color: #721c24;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        margin-right: 5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
    }

    .action-btn-view {
        background-color: rgba(13, 110, 253, 0.1);
        color: #ee393d;
    }

    .action-btn-view:hover {
        background-color: #ee393d;
        color: #fff;
    }

    .action-btn-invoice {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .action-btn-invoice:hover {
        background-color: #6c757d;
        color: #fff;
    }

    .welcome-banner {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        color: white;
        padding: 30px;
        margin-bottom: 30px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(248, 193, 44, 0.05);
    }

    .table th {
        font-weight: 600;
        color: #343a40;
        border-top: none;
        border-bottom: 2px solid #ee393d;
    }

    .empty-state {
        text-align: center;
        padding: 50px 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 20px;
    }

    .empty-state h4 {
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 20px;
    }
</style>
@endsection

@section('content')
<div class="welcome-banner" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">My Payments</h2>
            <p class="mb-0">View and manage all your payment transactions and invoices</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ route('client.payments.history') }}" class="btn btn-light">
                <i class="fas fa-chart-line me-2"></i> View Payment Analytics
            </a>
        </div>
    </div>
</div>

@if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<!-- Payment Statistics -->
<div class="row mb-4">
    <div class="col-md-4 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-title">Total Payments</div>
            <div class="stat-value">{{ $stats['total_payments'] }}</div>
        </div>
    </div>
    <div class="col-md-4 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="200">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-title">Total Spent</div>
            <div class="stat-value">@currency(){{ number_format($stats['total_amount'], 2) }}</div>
        </div>
    </div>
    <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-title">Completed Payments</div>
            <div class="stat-value">{{ $stats['completed_payments'] }}</div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card payment-card" data-aos="fade-up" data-aos-delay="400">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i> Recent Payments</h5>
        <div>
            <a href="{{ route('client.payments.history') }}" class="btn btn-sm btn-light">
                <i class="fas fa-history me-1"></i> View All History
            </a>
        </div>
    </div>
    <div class="card-body">
        @if ($payments->isEmpty())
            <div class="empty-state">
                <i class="fas fa-receipt"></i>
                <h4>No payment records found</h4>
                <p>You haven't made any payments yet.</p>
                <a href="{{ route('booking.index') }}" class="btn btn-primary">
                    <i class="fas fa-car me-1"></i> Book a Ride
                </a>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Booking</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($payments as $payment)
                            <tr>
                                <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                <td>
                                    <a href="{{ route('client.bookings.show', $payment->booking_id) }}" class="text-decoration-none">
                                        <div class="d-flex align-items-center">
                                            @if($payment->booking->vehicle && $payment->booking->vehicle->image)
                                                <img src="{{ asset('storage/' . $payment->booking->vehicle->image) }}" class="me-2 rounded" width="40" height="30" alt="{{ $payment->booking->vehicle->name }}">
                                            @else
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center me-2 rounded" style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $payment->booking->booking_number }}</div>
                                                <small class="text-muted">{{ $payment->booking->vehicle ? $payment->booking->vehicle->name : 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </a>
                                </td>
                                <td class="fw-bold">@currency(){{ number_format($payment->amount, 2) }}</td>
                                <td>
                                    <span class="payment-method-badge">
                                        @if($payment->payment_method == 'paypal')
                                            <i class="fab fa-paypal me-1"></i> PayPal
                                        @elseif($payment->payment_method == 'credit_card')
                                            <i class="far fa-credit-card me-1"></i> Credit Card
                                        @elseif($payment->payment_method == 'cash')
                                            <i class="fas fa-money-bill-wave me-1"></i> Cash
                                        @else
                                            <i class="fas fa-money-check me-1"></i> {{ ucfirst($payment->payment_method) }}
                                        @endif
                                    </span>
                                </td>
                                <td>
                                    <span class="payment-status status-{{ strtolower($payment->status) }}">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('client.payments.show', $payment->id) }}" class="action-btn action-btn-view" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('client.payments.invoice', $payment->id) }}" class="action-btn action-btn-invoice" title="View Invoice">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $payments->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Payment Methods -->
<div class="card payment-card mt-4" data-aos="fade-up" data-aos-delay="500">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i> Payment Methods</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-4 mb-md-0">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fab fa-paypal fa-3x text-primary mb-3"></i>
                        <h5>PayPal</h5>
                        <p class="text-muted">Pay securely using your PayPal account</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4 mb-md-0">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="far fa-credit-card fa-3x text-success mb-3"></i>
                        <h5>Credit Card</h5>
                        <p class="text-muted">Pay using Visa, MasterCard, or American Express</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-3x text-warning mb-3"></i>
                        <h5>Cash</h5>
                        <p class="text-muted">Pay cash directly to the driver</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
