@extends('layouts.admin')

@section('title', 'Edit Airport')

@section('content')
<div class="content-wrapper">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2 text-warning"></i>Edit Airport: {{ $airport->name }}
            </h1>
            <div>
                <a href="{{ route('admin.airports.show', $airport) }}" class="btn btn-info me-2">
                    <i class="fas fa-eye me-1"></i> View
                </a>
                <a href="{{ route('admin.airports.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Airports
                </a>
            </div>
        </div>

        <!-- Airport Form -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plane me-2"></i>Airport Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.airports.update', $airport) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <!-- Basic Information -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Airport Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $airport->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="code" class="form-label">Airport Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                           id="code" name="code" value="{{ old('code', $airport->code) }}" 
                                           placeholder="e.g., LAX, JFK, LHR" maxlength="10" required>
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">IATA or ICAO airport code</small>
                                </div>
                            </div>

                            <!-- Location Information -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                           id="city" name="city" value="{{ old('city', $airport->city) }}" required>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                           id="country" name="country" value="{{ old('country', $airport->country) }}" required>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="country_code" class="form-label">Country Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('country_code') is-invalid @enderror" 
                                           id="country_code" name="country_code" value="{{ old('country_code', $airport->country_code) }}" 
                                           placeholder="e.g., US, GB, FR" maxlength="2" required>
                                    @error('country_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">ISO 3166-1 alpha-2 country code</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="timezone" class="form-label">Timezone</label>
                                    <input type="text" class="form-control @error('timezone') is-invalid @enderror" 
                                           id="timezone" name="timezone" value="{{ old('timezone', $airport->timezone) }}" 
                                           placeholder="e.g., America/New_York, Europe/London">
                                    @error('timezone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Coordinates -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="number" step="0.00000001" class="form-control @error('latitude') is-invalid @enderror" 
                                           id="latitude" name="latitude" value="{{ old('latitude', $airport->latitude) }}" 
                                           placeholder="e.g., 40.7128" min="-90" max="90">
                                    @error('latitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="number" step="0.00000001" class="form-control @error('longitude') is-invalid @enderror" 
                                           id="longitude" name="longitude" value="{{ old('longitude', $airport->longitude) }}" 
                                           placeholder="e.g., -74.0060" min="-180" max="180">
                                    @error('longitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="3" 
                                          placeholder="Full airport address">{{ old('address', $airport->address) }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <a href="{{ route('admin.airports.index') }}" class="btn btn-secondary me-2">Cancel</a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-1"></i> Update Airport
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Info Panel -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Current Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td><span class="badge bg-info">{{ $airport->code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Location:</strong></td>
                                <td>{{ $airport->city }}, {{ $airport->country }}</td>
                            </tr>
                            @if($airport->latitude && $airport->longitude)
                            <tr>
                                <td><strong>Coordinates:</strong></td>
                                <td>
                                    <small>
                                        {{ number_format($airport->latitude, 6) }},<br>
                                        {{ number_format($airport->longitude, 6) }}
                                    </small>
                                </td>
                            </tr>
                            @endif
                            @if($airport->timezone)
                            <tr>
                                <td><strong>Timezone:</strong></td>
                                <td><small>{{ $airport->timezone }}</small></td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td><small>{{ $airport->created_at->format('M d, Y') }}</small></td>
                            </tr>
                            <tr>
                                <td><strong>Updated:</strong></td>
                                <td><small>{{ $airport->updated_at->format('M d, Y') }}</small></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Delete Airport -->
                <div class="card border-danger mt-3">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-3">
                            Deleting this airport will permanently remove it from the system. This action cannot be undone.
                        </p>
                        <form action="{{ route('admin.airports.destroy', $airport) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this airport? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash me-1"></i> Delete Airport
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Auto-uppercase airport code
    document.getElementById('code').addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Auto-uppercase country code
    document.getElementById('country_code').addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
</script>
@endsection
