@extends('layouts.admin')

@section('title', 'Edit Driver')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Driver</h1>
        <div>
            <a href="{{ route('admin.drivers.index') }}" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Drivers
            </a>
            <a href="{{ route('admin.drivers.show', $driver->id) }}" class="btn btn-sm btn-info shadow-sm">
                <i class="fas fa-eye fa-sm text-white-50"></i> View Driver
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Driver Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.drivers.update', $driver->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $driver->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $driver->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password">
                            <small class="form-text text-muted">Leave blank to keep current password.</small>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password_confirmation">Confirm Password</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone">Phone Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $driver->phone) }}" required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="license_number">Driver License Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('license_number') is-invalid @enderror" id="license_number" name="license_number" value="{{ old('license_number', $driver->license_number) }}" required>
                            @error('license_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Address <span class="text-danger">*</span></label>
                    <textarea class="form-control @error('address') is-invalid @enderror" id="address" name="address" rows="3" required>{{ old('address', $driver->address) }}</textarea>
                    @error('address')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Vehicle Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_make">Make <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('vehicle_make') is-invalid @enderror" id="vehicle_make" name="vehicle_make" value="{{ old('vehicle_make', $driver->vehicle_make) }}" placeholder="e.g. Toyota" required>
                                    @error('vehicle_make')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_model">Model <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('vehicle_model') is-invalid @enderror" id="vehicle_model" name="vehicle_model" value="{{ old('vehicle_model', $driver->vehicle_model) }}" placeholder="e.g. Camry" required>
                                    @error('vehicle_model')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_color">Color <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('vehicle_color') is-invalid @enderror" id="vehicle_color" name="vehicle_color" value="{{ old('vehicle_color', $driver->vehicle_color) }}" placeholder="e.g. Black" required>
                                    @error('vehicle_color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_reg_number">Registration Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('vehicle_reg_number') is-invalid @enderror" id="vehicle_reg_number" name="vehicle_reg_number" value="{{ old('vehicle_reg_number', $driver->vehicle_reg_number) }}" placeholder="e.g. ABC123" required>
                                    @error('vehicle_reg_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="insurance_expiry">Insurance Expiry <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('insurance_expiry') is-invalid @enderror" id="insurance_expiry" name="insurance_expiry" value="{{ old('insurance_expiry', $driver->insurance_expiry ? $driver->insurance_expiry->format('Y-m-d') : '') }}" required>
                                    @error('insurance_expiry')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="mot_expiry">MOT Expiry <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('mot_expiry') is-invalid @enderror" id="mot_expiry" name="mot_expiry" value="{{ old('mot_expiry', $driver->mot_expiry ? $driver->mot_expiry->format('Y-m-d') : '') }}" required>
                                    @error('mot_expiry')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="vehicle_info">Additional Vehicle Information</label>
                            <textarea class="form-control @error('vehicle_info') is-invalid @enderror" id="vehicle_info" name="vehicle_info" rows="2" placeholder="Any additional information about the vehicle">{{ old('vehicle_info', $driver->vehicle_info) }}</textarea>
                            @error('vehicle_info')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-9">
                        <div class="form-group">
                            <label for="profile_photo">Profile Photo</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input @error('profile_photo') is-invalid @enderror" id="profile_photo" name="profile_photo" accept="image/*">
                                <label class="custom-file-label" for="profile_photo">Choose file</label>
                                @error('profile_photo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <small class="form-text text-muted">Upload a professional photo of the driver. Max size: 2MB.</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        @if($driver->profile_photo)
                            <div class="text-center">
                                <img src="{{ asset('storage/' . $driver->profile_photo) }}" alt="{{ $driver->name }}" class="img-thumbnail" style="max-height: 100px;">
                                <p class="small mt-1">Current Photo</p>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; color: white; font-size: 2rem;">
                                    {{ strtoupper(substr($driver->name, 0, 1)) }}
                                </div>
                                <p class="small mt-1">No Photo</p>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-switch">
                        <input type="hidden" name="is_active" value="0">
                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" {{ $driver->is_active ? 'checked' : '' }}>
                        <label class="custom-control-label" for="is_active">Active</label>
                    </div>
                    <small class="form-text text-muted">If checked, the driver will be active in the system.</small>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-switch">
                        <input type="hidden" name="is_available" value="0">
                        <input type="checkbox" class="custom-control-input" id="is_available" name="is_available" value="1" {{ $driver->is_available ? 'checked' : '' }}>
                        <label class="custom-control-label" for="is_available">Available</label>
                    </div>
                    <small class="form-text text-muted">If checked, the driver will be available for new bookings.</small>
                </div>

                <button type="submit" class="btn btn-primary">Update Driver</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Driver Documents</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Uploaded</th>
                            <th>Expiry Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($driver->driverDocuments as $document)
                        <tr>
                            <td>{{ $document->document_type }}</td>
                            <td>{{ $document->created_at->format('M d, Y') }}</td>
                            <td>
                                @if($document->expiry_date)
                                    {{ $document->expiry_date->format('M d, Y') }}
                                    @if($document->isExpired())
                                        <span class="badge badge-danger">Expired</span>
                                    @elseif($document->isExpiringSoon())
                                        <span class="badge badge-warning">Expiring Soon</span>
                                    @endif
                                @else
                                    N/A
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-{{ $document->is_verified ? 'success' : 'warning' }}">
                                    {{ $document->is_verified ? 'Verified' : 'Pending Verification' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form action="{{ route('admin.drivers.verify-document', $document->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-success" title="Verify Document">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <form action="{{ route('admin.drivers.delete-document', $document->id) }}" method="POST" class="d-inline delete-document-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete Document">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="text-center">No documents uploaded yet.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <h6 class="font-weight-bold">Upload New Document</h6>
                <form action="{{ route('admin.drivers.upload-document', $driver->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="document_type">Document Type</label>
                                <select class="form-control" id="document_type" name="document_type" required>
                                    <option value="Driver License">Driver License</option>
                                    <option value="Driver PCO License">Driver PCO License</option>
                                    <option value="Vehicle PCO License">Vehicle PCO License</option>
                                    <option value="Insurance">Insurance</option>
                                    <option value="MOT Certificate">MOT Certificate</option>
                                    <option value="V5C Logbook">V5C Logbook</option>
                                    <option value="Vehicle Photos">Vehicle Photos</option>
                                    <option value="Vehicle Registration">Vehicle Registration</option>
                                    <option value="Background Check">Background Check</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="expiry_date">Expiry Date</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="document">Document File</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="document" name="document" required>
                                    <label class="custom-file-label" for="document">Choose file</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload Document</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Show file name when selected
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').addClass("selected").html(fileName);
    });

    // Confirm document delete
    $('.delete-document-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);

        Swal.fire({
            title: 'Are you sure?',
            text: "This will delete the document. This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                form.off('submit').submit();
            }
        });
    });
</script>
@endsection
