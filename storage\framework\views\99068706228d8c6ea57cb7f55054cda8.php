<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?php echo e($payment->id); ?> - <?php echo e(config('app.name')); ?></title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f8f9fa;
        }

        .invoice-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .invoice-header {
            background-color: #343a40;
            color: #fff;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .invoice-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .invoice-company {
            text-align: right;
        }

        .invoice-company h2 {
            margin: 0;
            font-size: 20px;
            color: #ee393d;
        }

        .invoice-company p {
            margin: 5px 0 0;
            font-size: 14px;
            opacity: 0.8;
        }

        .invoice-info {
            padding: 30px;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #eee;
        }

        .invoice-info-item h3 {
            margin: 0 0 10px;
            font-size: 16px;
            color: #6c757d;
            font-weight: 600;
        }

        .invoice-info-item p {
            margin: 0;
            font-size: 15px;
            line-height: 1.5;
        }

        .invoice-items {
            padding: 30px;
        }

        .invoice-items h3 {
            margin: 0 0 20px;
            font-size: 18px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
        }

        .invoice-table th {
            background-color: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .invoice-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .invoice-table .text-right {
            text-align: right;
        }

        .invoice-totals {
            padding: 0 30px 30px;
        }

        .invoice-totals table {
            width: 300px;
            margin-left: auto;
        }

        .invoice-totals table td {
            padding: 8px 0;
        }

        .invoice-totals table td:first-child {
            font-weight: 600;
        }

        .invoice-totals table td:last-child {
            text-align: right;
        }

        .invoice-totals .grand-total {
            font-size: 18px;
            font-weight: 700;
            color: #343a40;
        }

        .invoice-footer {
            padding: 20px 30px;
            background-color: #f8f9fa;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #eee;
        }

        .invoice-footer p {
            margin: 5px 0;
        }

        .invoice-actions {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #343a40;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            margin: 0 5px;
        }

        .btn-primary {
            background-color: #ee393d;
            color: #343a40;
        }

        .payment-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }

        .status-completed {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #664d03;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #842029;
        }

        .status-refunded {
            background-color: #cff4fc;
            color: #055160;
        }

        @media print {
            body {
                background-color: #fff;
            }

            .invoice-container {
                box-shadow: none;
                margin: 0;
            }

            .invoice-actions {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div>
                <h1>INVOICE</h1>
                <p>Invoice #<?php echo e($payment->id); ?></p>
            </div>
            <div class="invoice-company">
                <h2><?php echo e(\App\Helpers\SettingsHelper::getCompanyName()); ?></h2>
                <p><?php echo e(\App\Helpers\SettingsHelper::getCompanyAddress()); ?></p>
                <p><?php echo e(\App\Helpers\SettingsHelper::getCompanyPhone()); ?></p>
                <p><?php echo e(\App\Helpers\SettingsHelper::getCompanyEmail()); ?></p>
            </div>
        </div>

        <div class="invoice-info">
            <div class="invoice-info-item">
                <h3>Bill To:</h3>
                <p><?php echo e($payment->booking->user->name); ?></p>
                <p><?php echo e($payment->booking->user->email); ?></p>
                <p><?php echo e($payment->booking->user->phone ?? 'N/A'); ?></p>
            </div>
            <div class="invoice-info-item">
                <h3>Invoice Details:</h3>
                <p><strong>Invoice Date:</strong> <?php echo e($payment->created_at ? $payment->created_at->format('F d, Y') : 'N/A'); ?></p>
                <p><strong>Due Date:</strong> <?php echo e($payment->created_at ? $payment->created_at->format('F d, Y') : 'N/A'); ?></p>
                <p>
                    <strong>Status:</strong>
                    <span class="payment-status status-<?php echo e($payment->status); ?>">
                        <?php echo e(ucfirst($payment->status)); ?>

                    </span>
                </p>
                <p><strong>Transaction ID:</strong> <?php echo e($payment->transaction_id); ?></p>
            </div>
        </div>

        <div class="invoice-items">
            <h3>Booking Details</h3>
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Booking #</th>
                        <th>Date</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <?php echo e($payment->booking->vehicle->name); ?> -
                            <?php echo e($payment->booking->pickup_address); ?> to
                            <?php echo e($payment->booking->dropoff_address); ?>

                        </td>
                        <td><?php echo e($payment->booking->booking_number); ?></td>
                        <td><?php echo e($payment->booking->pickup_date ? $payment->booking->pickup_date->format('M d, Y H:i') : 'N/A'); ?></td>
                        <td class="text-right"><?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="invoice-totals">
            <table>
                <tr>
                    <td>Subtotal:</td>
                    <td><?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?></td>
                </tr>
                <tr>
                    <td>Tax (0%):</td>
                    <td><?php echo e(\App\Helpers\SettingsHelper::formatPrice(0)); ?></td>
                </tr>
                <tr class="grand-total">
                    <td>Total:</td>
                    <td><?php echo e(\App\Helpers\SettingsHelper::formatPrice($payment->amount)); ?></td>
                </tr>
            </table>
        </div>

        <div class="invoice-footer">
            <p>Thank you for choosing <?php echo e(\App\Helpers\SettingsHelper::getCompanyName()); ?>!</p>
            <p>If you have any questions about this invoice, please contact us at <?php echo e(\App\Helpers\SettingsHelper::getCompanyEmail()); ?></p>
            <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(\App\Helpers\SettingsHelper::getCompanyName()); ?>. All rights reserved.</p>
        </div>
    </div>

    <div class="invoice-actions">
        <button class="btn btn-primary" onclick="window.print()">Print Invoice</button>
        <a href="<?php echo e(route('admin.payments.show', $payment->id)); ?>" class="btn">Back to Payment Details</a>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\payments\invoice.blade.php ENDPATH**/ ?>