<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;
use App\Services\AutocompleteService;
use Illuminate\Support\Facades\Cache;

class ConnectAutocompleteSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autocomplete:connect {--verify : Verify all connections} {--fix : Fix any inconsistencies}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Connect and synchronize all autocomplete settings across the application';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔗 Connecting all autocomplete settings across the application...');

        if ($this->option('verify')) {
            return $this->verifyConnections();
        }

        if ($this->option('fix')) {
            return $this->fixInconsistencies();
        }

        return $this->connectSettings();
    }

    /**
     * Connect all autocomplete settings
     */
    private function connectSettings()
    {
        $this->info('📋 Step 1: Ensuring all required settings exist...');
        $this->ensureRequiredSettings();

        $this->info('🔄 Step 2: Synchronizing settings across services...');
        $this->synchronizeSettings();

        $this->info('🧹 Step 3: Clearing caches...');
        $this->clearCaches();

        $this->info('✅ Step 4: Verifying connections...');
        $this->verifyConnections();

        $this->info('🎉 All autocomplete settings are now connected and synchronized!');
        return 0;
    }

    /**
     * Ensure all required settings exist
     */
    private function ensureRequiredSettings()
    {
        $requiredSettings = [
            'autocomplete_enabled' => ['value' => 'true', 'type' => 'boolean'],
            'autocomplete_restrict_country' => ['value' => 'false', 'type' => 'boolean'],
            'autocomplete_country' => ['value' => 'GB', 'type' => 'string'],
            'autocomplete_types' => ['value' => 'geocode', 'type' => 'select'],
            'autocomplete_bias_radius' => ['value' => '100', 'type' => 'number'],
            'autocomplete_use_strict_bounds' => ['value' => 'false', 'type' => 'boolean'],
            'autocomplete_fields' => ['value' => 'address_components,geometry,name,formatted_address,place_id', 'type' => 'string'],
            'autocomplete_session_token' => ['value' => 'true', 'type' => 'boolean'],
            'autocomplete_language' => ['value' => 'en', 'type' => 'string'],
            'autocomplete_region' => ['value' => 'GB', 'type' => 'string'],
        ];

        foreach ($requiredSettings as $key => $config) {
            $setting = Setting::where('key', $key)->first();
            
            if (!$setting) {
                Setting::create([
                    'key' => $key,
                    'value' => $config['value'],
                    'group' => 'autocomplete',
                    'type' => $config['type'],
                    'is_public' => true
                ]);
                $this->line("✓ Created missing setting: {$key}");
            } else {
                $this->line("✓ Setting exists: {$key}");
            }
        }
    }

    /**
     * Synchronize settings across services
     */
    private function synchronizeSettings()
    {
        // Get current settings from AutocompleteService
        $settings = AutocompleteService::getSettings(false);
        
        // Synchronize with Google Maps settings
        $this->synchronizeGoogleMapsSettings($settings);
        
        // Synchronize with country settings
        $this->synchronizeCountrySettings($settings);
        
        $this->line("✓ Settings synchronized across all services");
    }

    /**
     * Synchronize with Google Maps settings
     */
    private function synchronizeGoogleMapsSettings($autocompleteSettings)
    {
        $mappings = [
            'google_maps_restrict_country' => $autocompleteSettings['restrict_country'] ? 'true' : 'false',
            'google_maps_country_code' => $autocompleteSettings['country'],
            'google_maps_language' => $autocompleteSettings['language'],
        ];

        foreach ($mappings as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => 'google_maps',
                    'type' => 'string',
                    'is_public' => true
                ]
            );
        }
    }

    /**
     * Synchronize with country settings
     */
    private function synchronizeCountrySettings($autocompleteSettings)
    {
        // Ensure country_code is consistent
        Setting::updateOrCreate(
            ['key' => 'country_code'],
            [
                'value' => $autocompleteSettings['country'],
                'group' => 'general',
                'type' => 'string',
                'is_public' => true
            ]
        );
    }

    /**
     * Clear all relevant caches
     */
    private function clearCaches()
    {
        Cache::forget('autocomplete_settings');
        Cache::forget('settings_cache');
        
        // Clear Laravel caches
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('view:clear');
        
        $this->line("✓ All caches cleared");
    }

    /**
     * Verify all connections
     */
    private function verifyConnections()
    {
        $this->info('🔍 Verifying autocomplete connections...');

        $issues = [];

        // Check AutocompleteService
        try {
            $settings = AutocompleteService::getSettings();
            $this->line("✅ AutocompleteService: Working");
        } catch (\Exception $e) {
            $issues[] = "AutocompleteService: " . $e->getMessage();
        }

        // Check JavaScript config
        try {
            $jsConfig = AutocompleteService::getJavaScriptConfig();
            $this->line("✅ JavaScript Config: Working");
        } catch (\Exception $e) {
            $issues[] = "JavaScript Config: " . $e->getMessage();
        }

        // Check meta tags
        try {
            $metaTags = AutocompleteService::getMetaTags();
            $this->line("✅ Meta Tags: Working");
        } catch (\Exception $e) {
            $issues[] = "Meta Tags: " . $e->getMessage();
        }

        // Check database settings
        $requiredKeys = [
            'autocomplete_enabled',
            'autocomplete_restrict_country',
            'autocomplete_country',
            'autocomplete_types',
            'autocomplete_bias_radius',
            'autocomplete_use_strict_bounds',
            'autocomplete_fields'
        ];

        $missingSettings = [];
        foreach ($requiredKeys as $key) {
            if (!Setting::where('key', $key)->exists()) {
                $missingSettings[] = $key;
            }
        }

        if (empty($missingSettings)) {
            $this->line("✅ Database Settings: All present");
        } else {
            $issues[] = "Missing database settings: " . implode(', ', $missingSettings);
        }

        // Check file existence
        $requiredFiles = [
            'public/js/unified-autocomplete.js',
            'resources/views/components/autocomplete-meta.blade.php',
            'app/Services/AutocompleteService.php'
        ];

        foreach ($requiredFiles as $file) {
            if (file_exists(base_path($file))) {
                $this->line("✅ File exists: {$file}");
            } else {
                $issues[] = "Missing file: {$file}";
            }
        }

        // Display results
        if (empty($issues)) {
            $this->info("🎉 All autocomplete connections verified successfully!");
            $this->displayCurrentConfiguration();
            return 0;
        } else {
            $this->error("❌ Issues found:");
            foreach ($issues as $issue) {
                $this->line("  • {$issue}");
            }
            return 1;
        }
    }

    /**
     * Fix inconsistencies
     */
    private function fixInconsistencies()
    {
        $this->info('🔧 Fixing autocomplete inconsistencies...');

        // Run the connection process
        $this->connectSettings();

        // Additional fixes
        $this->fixLayoutFiles();
        $this->fixJavaScriptFiles();

        $this->info('✅ All inconsistencies fixed!');
        return 0;
    }

    /**
     * Fix layout files
     */
    private function fixLayoutFiles()
    {
        $layouts = [
            'resources/views/layouts/guest.blade.php',
            'resources/views/layouts/client.blade.php',
            'resources/views/layouts/admin.blade.php',
            'resources/views/layouts/driver.blade.php'
        ];

        foreach ($layouts as $layout) {
            if (file_exists(base_path($layout))) {
                $content = file_get_contents(base_path($layout));
                
                // Check if it includes the unified autocomplete component
                if (!str_contains($content, '@include(\'components.autocomplete-meta\')')) {
                    $this->warn("Layout {$layout} may need manual update to include unified autocomplete");
                } else {
                    $this->line("✓ Layout {$layout} includes unified autocomplete");
                }
            }
        }
    }

    /**
     * Fix JavaScript files
     */
    private function fixJavaScriptFiles()
    {
        $jsFiles = [
            'public/js/unified-autocomplete.js',
            'public/js/autocomplete-settings.js',
            'public/js/booking.js'
        ];

        foreach ($jsFiles as $file) {
            if (file_exists(base_path($file))) {
                $this->line("✓ JavaScript file exists: {$file}");
            } else {
                $this->warn("JavaScript file missing: {$file}");
            }
        }
    }

    /**
     * Display current configuration
     */
    private function displayCurrentConfiguration()
    {
        $this->info('📋 Current Autocomplete Configuration:');
        
        $settings = AutocompleteService::getSettings();
        
        $table = [];
        foreach ($settings as $key => $value) {
            $table[] = [
                'Setting' => $key,
                'Value' => is_bool($value) ? ($value ? 'true' : 'false') : $value,
                'Type' => gettype($value)
            ];
        }

        $this->table(['Setting', 'Value', 'Type'], $table);

        $this->info('🔗 Connected Components:');
        $this->line('• AutocompleteService - Centralized settings management');
        $this->line('• Unified JavaScript handler - public/js/unified-autocomplete.js');
        $this->line('• Meta tags component - resources/views/components/autocomplete-meta.blade.php');
        $this->line('• All layout files - guest, client, admin, driver');
        $this->line('• Database settings - synchronized and consistent');
    }
}
