<?php $__env->startSection('title', 'Add New Driver'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-plus text-primary mr-2"></i>Add New Driver
        </h1>
        <div>
            <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Drivers
            </a>
            <button type="button" class="btn btn-sm btn-info shadow-sm" data-toggle="modal" data-target="#helpModal">
                <i class="fas fa-question-circle fa-sm text-white-50"></i> Help
            </button>
        </div>
    </div>

    <!-- Progress Steps -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <div class="progress-steps">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-title">Personal Info</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-title">Vehicle Details</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-title">Documents</div>
                        </div>
                        <div class="step" data-step="4">
                            <div class="step-number">4</div>
                            <div class="step-title">Review</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Multi-step Form -->
    <form action="<?php echo e(route('admin.drivers.store')); ?>" method="POST" enctype="multipart/form-data" id="driverForm">
        <?php echo csrf_field(); ?>

        <!-- Step 1: Personal Information -->
        <div class="card shadow mb-4 form-step" id="step-1">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user mr-2"></i>Personal Information
                </h6>
                <span class="badge badge-primary">Step 1 of 4</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">
                                <i class="fas fa-user text-muted mr-1"></i>Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="name" name="name" value="<?php echo e(old('name')); ?>"
                                   placeholder="Enter driver's full name" required>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">
                                <i class="fas fa-envelope text-muted mr-1"></i>Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="email" name="email" value="<?php echo e(old('email')); ?>"
                                   placeholder="<EMAIL>" required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone">
                                <i class="fas fa-phone text-muted mr-1"></i>Phone Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="phone" name="phone" value="<?php echo e(old('phone')); ?>"
                                   placeholder="+****************" required>
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="license_number">
                                <i class="fas fa-id-card text-muted mr-1"></i>Driver License Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="license_number" name="license_number" value="<?php echo e(old('license_number')); ?>"
                                   placeholder="DL123456789" required>
                            <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password">
                                <i class="fas fa-lock text-muted mr-1"></i>Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="password" name="password" placeholder="Enter secure password" required>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">Minimum 8 characters with letters and numbers</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password_confirmation">
                                <i class="fas fa-lock text-muted mr-1"></i>Confirm Password <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="password_confirmation"
                                   name="password_confirmation" placeholder="Confirm password" required>
                            <div class="valid-feedback">Passwords match!</div>
                            <div class="invalid-feedback">Passwords do not match</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">
                        <i class="fas fa-map-marker-alt text-muted mr-1"></i>Address <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              id="address" name="address" rows="3"
                              placeholder="Enter complete address including city, state, and postal code" required><?php echo e(old('address')); ?></textarea>
                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <div class="valid-feedback">Looks good!</div>
                </div>

                <div class="form-group">
                    <label for="profile_photo">
                        <i class="fas fa-camera text-muted mr-1"></i>Profile Photo
                    </label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input <?php $__errorArgs = ['profile_photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="profile_photo" name="profile_photo" accept="image/*">
                        <label class="custom-file-label" for="profile_photo">Choose professional photo</label>
                        <?php $__errorArgs = ['profile_photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <small class="form-text text-muted">Upload a professional photo. Max size: 2MB. Formats: JPG, PNG</small>
                    <div id="profile_photo_preview" class="mt-2" style="display: none;">
                        <img id="preview_image" src="" alt="Preview" class="img-thumbnail" style="max-width: 150px;">
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                        Next: Vehicle Details <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Vehicle Details -->
        <div class="card shadow mb-4 form-step" id="step-2" style="display: none;">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-car mr-2"></i>Vehicle Details
                </h6>
                <span class="badge badge-primary">Step 2 of 4</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_make">
                                <i class="fas fa-industry text-muted mr-1"></i>Vehicle Make <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['vehicle_make'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="vehicle_make" name="vehicle_make" value="<?php echo e(old('vehicle_make')); ?>"
                                   placeholder="e.g. Toyota, BMW, Mercedes" required>
                            <?php $__errorArgs = ['vehicle_make'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_model">
                                <i class="fas fa-car text-muted mr-1"></i>Vehicle Model <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['vehicle_model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="vehicle_model" name="vehicle_model" value="<?php echo e(old('vehicle_model')); ?>"
                                   placeholder="e.g. Camry, X5, E-Class" required>
                            <?php $__errorArgs = ['vehicle_model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_color">
                                <i class="fas fa-palette text-muted mr-1"></i>Vehicle Color <span class="text-danger">*</span>
                            </label>
                            <select class="form-control <?php $__errorArgs = ['vehicle_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="vehicle_color" name="vehicle_color" required>
                                <option value="">Select Color</option>
                                <option value="Black" <?php echo e(old('vehicle_color') == 'Black' ? 'selected' : ''); ?>>Black</option>
                                <option value="White" <?php echo e(old('vehicle_color') == 'White' ? 'selected' : ''); ?>>White</option>
                                <option value="Silver" <?php echo e(old('vehicle_color') == 'Silver' ? 'selected' : ''); ?>>Silver</option>
                                <option value="Gray" <?php echo e(old('vehicle_color') == 'Gray' ? 'selected' : ''); ?>>Gray</option>
                                <option value="Blue" <?php echo e(old('vehicle_color') == 'Blue' ? 'selected' : ''); ?>>Blue</option>
                                <option value="Red" <?php echo e(old('vehicle_color') == 'Red' ? 'selected' : ''); ?>>Red</option>
                                <option value="Green" <?php echo e(old('vehicle_color') == 'Green' ? 'selected' : ''); ?>>Green</option>
                                <option value="Other" <?php echo e(old('vehicle_color') == 'Other' ? 'selected' : ''); ?>>Other</option>
                            </select>
                            <?php $__errorArgs = ['vehicle_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="vehicle_reg_number">
                                <i class="fas fa-hashtag text-muted mr-1"></i>Registration Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['vehicle_reg_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="vehicle_reg_number" name="vehicle_reg_number" value="<?php echo e(old('vehicle_reg_number')); ?>"
                                   placeholder="e.g. ABC123, XY12 ABC" style="text-transform: uppercase;" required>
                            <?php $__errorArgs = ['vehicle_reg_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="insurance_expiry">
                                <i class="fas fa-shield-alt text-muted mr-1"></i>Insurance Expiry <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control <?php $__errorArgs = ['insurance_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="insurance_expiry" name="insurance_expiry" value="<?php echo e(old('insurance_expiry')); ?>"
                                   min="<?php echo e(date('Y-m-d')); ?>" required>
                            <?php $__errorArgs = ['insurance_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                            <small class="form-text text-muted">Insurance must be valid for at least 30 days</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mot_expiry">
                                <i class="fas fa-tools text-muted mr-1"></i>MOT Expiry <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control <?php $__errorArgs = ['mot_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="mot_expiry" name="mot_expiry" value="<?php echo e(old('mot_expiry')); ?>"
                                   min="<?php echo e(date('Y-m-d')); ?>" required>
                            <?php $__errorArgs = ['mot_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="valid-feedback">Looks good!</div>
                            <small class="form-text text-muted">MOT certificate must be valid</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="vehicle_info">
                        <i class="fas fa-info-circle text-muted mr-1"></i>Additional Vehicle Information
                    </label>
                    <textarea class="form-control <?php $__errorArgs = ['vehicle_info'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              id="vehicle_info" name="vehicle_info" rows="3"
                              placeholder="Any additional information about the vehicle (year, features, condition, etc.)"><?php echo e(old('vehicle_info')); ?></textarea>
                    <?php $__errorArgs = ['vehicle_info'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="previousStep(1)">
                        <i class="fas fa-arrow-left mr-1"></i> Previous: Personal Info
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                        Next: Documents <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 3: Documents -->
        <div class="card shadow mb-4 form-step" id="step-3" style="display: none;">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-alt mr-2"></i>Documents Upload
                </h6>
                <span class="badge badge-primary">Step 3 of 4</span>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>Required Documents:</strong> Driver License, Driver PHD License, Vehicle PHD License, Insurance Certificate, MOT Certificate
                </div>

                <div id="documents-container">
                    <div class="document-upload-item border rounded p-3 mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Document Type <span class="text-danger">*</span></label>
                                    <select class="form-control document-type" name="document_types[]" required>
                                        <option value="">Select Document Type</option>
                                        <option value="Driver License">Driver License</option>
                                        <option value="Driver PHD License">Driver PHD License</option>
                                        <option value="Vehicle PHD License">Vehicle PHD License</option>
                                        <option value="Insurance">Insurance Certificate</option>
                                        <option value="MOT Certificate">MOT Certificate</option>
                                        <option value="V5C Logbook">V5C Logbook</option>
                                        <option value="Vehicle Photos">Vehicle Photos</option>
                                        <option value="Background Check">Background Check</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Document File <span class="text-danger">*</span></label>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input document-file" name="documents[]"
                                               accept=".pdf,.jpg,.jpeg,.png" required>
                                        <label class="custom-file-label">Choose file</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Expiry Date</label>
                                    <input type="date" class="form-control" name="expiry_dates[]" min="<?php echo e(date('Y-m-d')); ?>">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-danger btn-sm d-block remove-document" style="display: none;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea class="form-control" name="document_notes[]" rows="2"
                                      placeholder="Any additional notes about this document"></textarea>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-outline-primary btn-sm mb-3" id="add-document">
                    <i class="fas fa-plus mr-1"></i> Add Another Document
                </button>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="previousStep(2)">
                        <i class="fas fa-arrow-left mr-1"></i> Previous: Vehicle Details
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                        Next: Review <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 4: Review & Settings -->
        <div class="card shadow mb-4 form-step" id="step-4" style="display: none;">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-check-circle mr-2"></i>Review & Settings
                </h6>
                <span class="badge badge-primary">Step 4 of 4</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="font-weight-bold mb-3">Review Information</h6>
                        <div id="review-content">
                            <!-- Review content will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6 class="font-weight-bold mb-3">Driver Settings</h6>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" checked>
                                <label class="custom-control-label" for="is_active">
                                    <strong>Active Status</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Driver will be active in the system</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_available" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_available" name="is_available" value="1" checked>
                                <label class="custom-control-label" for="is_available">
                                    <strong>Available for Bookings</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Driver will be available for new bookings</small>
                        </div>

                        <div class="alert alert-success">
                            <i class="fas fa-info-circle mr-2"></i>
                            <small>The driver will receive login credentials via email after creation.</small>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="previousStep(3)">
                        <i class="fas fa-arrow-left mr-1"></i> Previous: Documents
                    </button>
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-user-plus mr-2"></i> Create Driver
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" role="dialog" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-question-circle mr-2"></i>Driver Creation Help
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user text-primary mr-2"></i>Personal Information</h6>
                            <ul class="list-unstyled">
                                <li><strong>Full Name:</strong> Driver's complete legal name</li>
                                <li><strong>Email:</strong> Must be unique, used for login</li>
                                <li><strong>Phone:</strong> Contact number for communication</li>
                                <li><strong>License Number:</strong> Driver's license number</li>
                                <li><strong>Address:</strong> Complete residential address</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-car text-primary mr-2"></i>Vehicle Information</h6>
                            <ul class="list-unstyled">
                                <li><strong>Make & Model:</strong> Vehicle manufacturer and model</li>
                                <li><strong>Color:</strong> Vehicle color for identification</li>
                                <li><strong>Registration:</strong> Vehicle registration number</li>
                                <li><strong>Insurance:</strong> Must be valid for at least 30 days</li>
                                <li><strong>MOT:</strong> Valid MOT certificate required</li>
                            </ul>
                        </div>
                    </div>
                    <hr>
                    <h6><i class="fas fa-file-alt text-primary mr-2"></i>Required Documents</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li>✓ Driver License</li>
                                <li>✓ Driver PHD License</li>
                                <li>✓ Vehicle PHD License</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li>✓ Insurance Certificate</li>
                                <li>✓ MOT Certificate</li>
                                <li>✓ V5C Logbook (optional)</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 60%;
        width: 80%;
        height: 2px;
        background-color: #e3e6f0;
        z-index: 1;
    }

    .step.active:not(:last-child)::after,
    .step.completed:not(:last-child)::after {
        background-color: #4e73df;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e3e6f0;
        color: #5a5c69;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
        position: relative;
        z-index: 2;
    }

    .step.active .step-number {
        background-color: #4e73df;
        color: white;
    }

    .step.completed .step-number {
        background-color: #1cc88a;
        color: white;
    }

    .step-title {
        font-size: 12px;
        font-weight: 600;
        color: #5a5c69;
        text-align: center;
    }

    .step.active .step-title {
        color: #4e73df;
    }

    .step.completed .step-title {
        color: #1cc88a;
    }

    .document-upload-item {
        background-color: #f8f9fc;
        transition: all 0.3s ease;
    }

    .document-upload-item:hover {
        background-color: #eaecf4;
    }

    .form-step {
        transition: all 0.3s ease;
    }

    .preview-card {
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    let currentStep = 1;
    const totalSteps = 4;

    // Initialize form
    initializeForm();

    // Form validation
    setupValidation();

    // File upload handlers
    setupFileUploads();

    // Document management
    setupDocumentManagement();

    function initializeForm() {
        // Show only first step
        $('.form-step').hide();
        $('#step-1').show();
        updateProgressSteps();
    }

    function setupValidation() {
        // Real-time validation
        $('input[required], select[required], textarea[required]').on('blur', function() {
            validateField($(this));
        });

        // Password confirmation
        $('#password_confirmation').on('input', function() {
            const password = $('#password').val();
            const confirmation = $(this).val();

            if (confirmation && password !== confirmation) {
                $(this).addClass('is-invalid').removeClass('is-valid');
                $(this).siblings('.invalid-feedback').text('Passwords do not match');
            } else if (confirmation && password === confirmation) {
                $(this).addClass('is-valid').removeClass('is-invalid');
            }
        });

        // Email validation
        $('#email').on('blur', function() {
            const email = $(this).val();
            if (email && !isValidEmail(email)) {
                $(this).addClass('is-invalid').removeClass('is-valid');
                $(this).siblings('.invalid-feedback').text('Please enter a valid email address');
            }
        });

        // Phone validation
        $('#phone').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            if (value.length >= 10) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                $(this).val(value);
            }
        });

        // Registration number uppercase
        $('#vehicle_reg_number').on('input', function() {
            $(this).val($(this).val().toUpperCase());
        });
    }

    function setupFileUploads() {
        // Profile photo preview
        $('#profile_photo').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#preview_image').attr('src', e.target.result);
                    $('#profile_photo_preview').show();
                };
                reader.readAsDataURL(file);

                // Update label
                $(this).next('.custom-file-label').text(file.name);
            }
        });

        // Document file uploads
        $(document).on('change', '.document-file', function() {
            const fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').text(fileName || 'Choose file');
        });

        // General file input handler
        $('.custom-file-input').on('change', function() {
            const fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').text(fileName || 'Choose file');
        });
    }

    function setupDocumentManagement() {
        // Add document button
        $('#add-document').on('click', function() {
            addDocumentUpload();
        });

        // Remove document button
        $(document).on('click', '.remove-document', function() {
            $(this).closest('.document-upload-item').remove();
            updateRemoveButtons();
        });

        // Update remove buttons visibility
        updateRemoveButtons();
    }

    function addDocumentUpload() {
        const template = `
            <div class="document-upload-item border rounded p-3 mb-3">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Document Type <span class="text-danger">*</span></label>
                            <select class="form-control document-type" name="document_types[]" required>
                                <option value="">Select Document Type</option>
                                <option value="Driver License">Driver License</option>
                                <option value="Driver PHD License">Driver PHD License</option>
                                <option value="Vehicle PHD License">Vehicle PHD License</option>
                                <option value="Insurance">Insurance Certificate</option>
                                <option value="MOT Certificate">MOT Certificate</option>
                                <option value="V5C Logbook">V5C Logbook</option>
                                <option value="Vehicle Photos">Vehicle Photos</option>
                                <option value="Background Check">Background Check</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Document File <span class="text-danger">*</span></label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input document-file" name="documents[]"
                                       accept=".pdf,.jpg,.jpeg,.png" required>
                                <label class="custom-file-label">Choose file</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Expiry Date</label>
                            <input type="date" class="form-control" name="expiry_dates[]" min="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm d-block remove-document">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Notes</label>
                    <textarea class="form-control" name="document_notes[]" rows="2"
                              placeholder="Any additional notes about this document"></textarea>
                </div>
            </div>
        `;

        $('#documents-container').append(template);
        updateRemoveButtons();
    }

    function updateRemoveButtons() {
        const items = $('.document-upload-item');
        if (items.length > 1) {
            $('.remove-document').show();
        } else {
            $('.remove-document').hide();
        }
    }

    function validateField(field) {
        const value = field.val().trim();
        const isRequired = field.prop('required');

        if (isRequired && !value) {
            field.addClass('is-invalid').removeClass('is-valid');
            return false;
        } else if (value) {
            field.addClass('is-valid').removeClass('is-invalid');
            return true;
        }

        return true;
    }

    function validateStep(step) {
        let isValid = true;
        const stepElement = $(`#step-${step}`);

        // Validate required fields in current step
        stepElement.find('input[required], select[required], textarea[required]').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });

        // Step-specific validations
        if (step === 1) {
            // Password confirmation
            const password = $('#password').val();
            const confirmation = $('#password_confirmation').val();
            if (password !== confirmation) {
                $('#password_confirmation').addClass('is-invalid');
                isValid = false;
            }

            // Email format
            const email = $('#email').val();
            if (email && !isValidEmail(email)) {
                $('#email').addClass('is-invalid');
                isValid = false;
            }
        }

        if (step === 2) {
            // Date validations
            const insuranceExpiry = new Date($('#insurance_expiry').val());
            const motExpiry = new Date($('#mot_expiry').val());
            const today = new Date();

            if (insuranceExpiry <= today) {
                $('#insurance_expiry').addClass('is-invalid');
                isValid = false;
            }

            if (motExpiry <= today) {
                $('#mot_expiry').addClass('is-invalid');
                isValid = false;
            }
        }

        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function updateProgressSteps() {
        $('.step').removeClass('active completed');

        for (let i = 1; i <= totalSteps; i++) {
            if (i < currentStep) {
                $(`.step[data-step="${i}"]`).addClass('completed');
            } else if (i === currentStep) {
                $(`.step[data-step="${i}"]`).addClass('active');
            }
        }
    }

    function updateReviewContent() {
        const reviewHtml = `
            <div class="preview-card">
                <h6 class="font-weight-bold text-primary mb-3">Personal Information</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> ${$('#name').val() || 'Not provided'}</p>
                        <p><strong>Email:</strong> ${$('#email').val() || 'Not provided'}</p>
                        <p><strong>Phone:</strong> ${$('#phone').val() || 'Not provided'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>License Number:</strong> ${$('#license_number').val() || 'Not provided'}</p>
                        <p><strong>Address:</strong> ${$('#address').val() || 'Not provided'}</p>
                    </div>
                </div>
            </div>

            <div class="preview-card">
                <h6 class="font-weight-bold text-primary mb-3">Vehicle Information</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Make:</strong> ${$('#vehicle_make').val() || 'Not provided'}</p>
                        <p><strong>Model:</strong> ${$('#vehicle_model').val() || 'Not provided'}</p>
                        <p><strong>Color:</strong> ${$('#vehicle_color').val() || 'Not provided'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Registration:</strong> ${$('#vehicle_reg_number').val() || 'Not provided'}</p>
                        <p><strong>Insurance Expiry:</strong> ${$('#insurance_expiry').val() || 'Not provided'}</p>
                        <p><strong>MOT Expiry:</strong> ${$('#mot_expiry').val() || 'Not provided'}</p>
                    </div>
                </div>
            </div>

            <div class="preview-card">
                <h6 class="font-weight-bold text-primary mb-3">Documents</h6>
                <div class="documents-summary">
                    ${getDocumentsSummary()}
                </div>
            </div>
        `;

        $('#review-content').html(reviewHtml);
    }

    function getDocumentsSummary() {
        let summary = '';
        $('.document-upload-item').each(function(index) {
            const type = $(this).find('.document-type').val();
            const file = $(this).find('.document-file')[0].files[0];
            const fileName = file ? file.name : 'No file selected';

            if (type) {
                summary += `<p><strong>${type}:</strong> ${fileName}</p>`;
            }
        });

        return summary || '<p class="text-muted">No documents uploaded</p>';
    }

    // Password toggle
    $('#togglePassword').on('click', function() {
        const passwordField = $('#password');
        const type = passwordField.attr('type') === 'password' ? 'text' : 'password';
        passwordField.attr('type', type);

        $(this).find('i').toggleClass('fa-eye fa-eye-slash');
    });

    // Form submission
    $('#driverForm').on('submit', function(e) {
        if (!validateStep(currentStep)) {
            e.preventDefault();
            showAlert('Please fix the validation errors before submitting.', 'danger');
            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating Driver...');
    });

    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        $('.container-fluid').prepend(alertHtml);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').alert('close');
        }, 5000);
    }

    // Global functions for navigation
    window.nextStep = function(step) {
        if (validateStep(currentStep)) {
            $('.form-step').hide();
            $(`#step-${step}`).show();
            currentStep = step;
            updateProgressSteps();

            if (step === 4) {
                updateReviewContent();
            }

            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 300);
        } else {
            showAlert('Please complete all required fields before proceeding.', 'warning');
        }
    };

    window.previousStep = function(step) {
        $('.form-step').hide();
        $(`#step-${step}`).show();
        currentStep = step;
        updateProgressSteps();

        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    };
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/drivers/create.blade.php ENDPATH**/ ?>