<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AutocompleteService
{
    /**
     * Cache key for autocomplete settings
     */
    const CACHE_KEY = 'autocomplete_settings';
    
    /**
     * Cache duration in minutes
     */
    const CACHE_DURATION = 60;

    /**
     * Default autocomplete settings
     */
    const DEFAULT_SETTINGS = [
        'enabled' => true,
        'restrict_country' => false,
        'country' => 'GB',
        'types' => 'geocode',
        'bias_radius' => 100,
        'use_strict_bounds' => false,
        'fields' => 'address_components,geometry,name,formatted_address,place_id',
        'session_token' => true,
        'language' => 'en',
        'region' => 'GB'
    ];

    /**
     * Get all autocomplete settings
     *
     * @param bool $useCache
     * @return array
     */
    public static function getSettings(bool $useCache = true): array
    {
        if (!$useCache) {
            return self::loadSettingsFromDatabase();
        }

        return Cache::remember(self::CACHE_KEY, self::CACHE_DURATION, function () {
            return self::loadSettingsFromDatabase();
        });
    }

    /**
     * Load settings from database
     *
     * @return array
     */
    private static function loadSettingsFromDatabase(): array
    {
        $settings = self::DEFAULT_SETTINGS;

        try {
            $dbSettings = Setting::whereIn('key', [
                'autocomplete_enabled',
                'autocomplete_restrict_country',
                'autocomplete_country',
                'autocomplete_types',
                'autocomplete_bias_radius',
                'autocomplete_use_strict_bounds',
                'autocomplete_fields',
                'autocomplete_session_token',
                'autocomplete_language',
                'autocomplete_region',
                'google_maps_restrict_country',
                'google_maps_country_code',
                'google_maps_language',
                'country_code'
            ])->pluck('value', 'key');

            // Map database settings to our structure
            $settings['enabled'] = ($dbSettings['autocomplete_enabled'] ?? 'true') === 'true';
            $settings['restrict_country'] = ($dbSettings['autocomplete_restrict_country'] ?? 'false') === 'true';
            $settings['country'] = $dbSettings['autocomplete_country'] ?? $dbSettings['google_maps_country_code'] ?? $dbSettings['country_code'] ?? 'GB';
            $settings['types'] = $dbSettings['autocomplete_types'] ?? 'geocode';
            $settings['bias_radius'] = (int) ($dbSettings['autocomplete_bias_radius'] ?? 100);
            $settings['use_strict_bounds'] = ($dbSettings['autocomplete_use_strict_bounds'] ?? 'false') === 'true';
            $settings['fields'] = $dbSettings['autocomplete_fields'] ?? 'address_components,geometry,name,formatted_address,place_id';
            $settings['session_token'] = ($dbSettings['autocomplete_session_token'] ?? 'true') === 'true';
            $settings['language'] = $dbSettings['autocomplete_language'] ?? $dbSettings['google_maps_language'] ?? 'en';
            $settings['region'] = $dbSettings['autocomplete_region'] ?? $settings['country'];

        } catch (\Exception $e) {
            Log::warning('Failed to load autocomplete settings from database', ['error' => $e->getMessage()]);
        }

        return $settings;
    }

    /**
     * Get JavaScript configuration object
     *
     * @return array
     */
    public static function getJavaScriptConfig(): array
    {
        $settings = self::getSettings();

        return [
            'enabled' => $settings['enabled'],
            'restrictCountry' => $settings['restrict_country'],
            'country' => $settings['country'],
            'types' => $settings['types'],
            'biasRadius' => $settings['bias_radius'],
            'useStrictBounds' => $settings['use_strict_bounds'],
            'fields' => $settings['fields'],
            'sessionToken' => $settings['session_token'],
            'language' => $settings['language'],
            'region' => $settings['region']
        ];
    }

    /**
     * Get Google Places API options
     *
     * @param array $overrides
     * @return array
     */
    public static function getPlacesApiOptions(array $overrides = []): array
    {
        $settings = self::getSettings();
        
        $options = [
            'fields' => explode(',', $settings['fields']),
            'types' => [$settings['types']]
        ];

        // Add country restriction if enabled
        if ($settings['restrict_country'] && !empty($settings['country'])) {
            $options['componentRestrictions'] = ['country' => $settings['country']];
        }

        // Add language if specified
        if (!empty($settings['language'])) {
            $options['language'] = $settings['language'];
        }

        // Add region if specified
        if (!empty($settings['region'])) {
            $options['region'] = $settings['region'];
        }

        // Merge with any overrides
        return array_merge($options, $overrides);
    }

    /**
     * Get bounds for autocomplete
     *
     * @param float|null $lat
     * @param float|null $lng
     * @return array|null
     */
    public static function getBounds(?float $lat = null, ?float $lng = null): ?array
    {
        $settings = self::getSettings();

        // If coordinates provided, create circular bounds
        if ($lat !== null && $lng !== null && $settings['bias_radius'] > 0) {
            $radius = max($settings['bias_radius'] * 1000, 50000); // Minimum 50km
            
            return [
                'center' => ['lat' => $lat, 'lng' => $lng],
                'radius' => $radius,
                'strict' => $settings['use_strict_bounds'] && $settings['bias_radius'] < 25
            ];
        }

        // Default bounds based on country
        return self::getCountryBounds($settings['country']);
    }

    /**
     * Get country-specific bounds
     *
     * @param string $country
     * @return array|null
     */
    public static function getCountryBounds(string $country): ?array
    {
        $bounds = [
            'GB' => [
                'southwest' => ['lat' => 49.9, 'lng' => -8.2],
                'northeast' => ['lat' => 60.9, 'lng' => 1.8]
            ],
            'US' => [
                'southwest' => ['lat' => 24.396308, 'lng' => -125.0],
                'northeast' => ['lat' => 49.384358, 'lng' => -66.93457]
            ],
            'CA' => [
                'southwest' => ['lat' => 41.6765559, 'lng' => -141.00187],
                'northeast' => ['lat' => 83.23324, 'lng' => -52.636291]
            ],
            'AU' => [
                'southwest' => ['lat' => -43.6345972634, 'lng' => 113.338953078],
                'northeast' => ['lat' => -10.6681857235, 'lng' => 153.569469029]
            ]
        ];

        return $bounds[$country] ?? null;
    }

    /**
     * Update autocomplete settings
     *
     * @param array $settings
     * @return bool
     */
    public static function updateSettings(array $settings): bool
    {
        try {
            $mapping = [
                'enabled' => 'autocomplete_enabled',
                'restrict_country' => 'autocomplete_restrict_country',
                'country' => 'autocomplete_country',
                'types' => 'autocomplete_types',
                'bias_radius' => 'autocomplete_bias_radius',
                'use_strict_bounds' => 'autocomplete_use_strict_bounds',
                'fields' => 'autocomplete_fields',
                'session_token' => 'autocomplete_session_token',
                'language' => 'autocomplete_language',
                'region' => 'autocomplete_region'
            ];

            foreach ($settings as $key => $value) {
                if (isset($mapping[$key])) {
                    $dbKey = $mapping[$key];
                    
                    // Convert boolean values to string
                    if (is_bool($value)) {
                        $value = $value ? 'true' : 'false';
                    }

                    Setting::updateOrCreate(
                        ['key' => $dbKey],
                        [
                            'value' => $value,
                            'group' => 'autocomplete',
                            'type' => is_numeric($value) ? 'number' : (in_array($value, ['true', 'false']) ? 'boolean' : 'string'),
                            'is_public' => true
                        ]
                    );
                }
            }

            // Clear cache
            self::clearCache();

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update autocomplete settings', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Clear autocomplete settings cache
     *
     * @return void
     */
    public static function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * Validate autocomplete settings
     *
     * @param array $settings
     * @return array
     */
    public static function validateSettings(array $settings): array
    {
        $errors = [];

        // Validate bias radius
        if (isset($settings['bias_radius'])) {
            $radius = (int) $settings['bias_radius'];
            if ($radius < 0 || $radius > 500) {
                $errors['bias_radius'] = 'Bias radius must be between 0 and 500 km';
            }
        }

        // Validate country code
        if (isset($settings['country'])) {
            if (!empty($settings['country']) && strlen($settings['country']) !== 2) {
                $errors['country'] = 'Country code must be 2 characters (ISO 3166-1 alpha-2)';
            }
        }

        // Validate types
        if (isset($settings['types'])) {
            $validTypes = ['geocode', 'address', 'establishment', '(cities)', '(regions)'];
            if (!in_array($settings['types'], $validTypes)) {
                $errors['types'] = 'Invalid place type specified';
            }
        }

        return $errors;
    }

    /**
     * Get meta tags for HTML head
     *
     * @return array
     */
    public static function getMetaTags(): array
    {
        $settings = self::getSettings();

        return [
            'autocomplete-enabled' => $settings['enabled'] ? 'true' : 'false',
            'autocomplete-restrict-country' => $settings['restrict_country'] ? 'true' : 'false',
            'autocomplete-country' => $settings['country'],
            'autocomplete-types' => $settings['types'],
            'autocomplete-bias-radius' => (string) $settings['bias_radius'],
            'autocomplete-use-strict-bounds' => $settings['use_strict_bounds'] ? 'true' : 'false',
            'autocomplete-fields' => $settings['fields'],
            'autocomplete-language' => $settings['language'],
            'autocomplete-region' => $settings['region']
        ];
    }
}
