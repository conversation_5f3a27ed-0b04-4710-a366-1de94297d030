@extends('layouts.driver')

@section('title', 'Edit Document')

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-edit me-2 text-primary"></i> Edit Document
        </h1>
        <div>
            <a href="{{ route('driver.documents.show', $document->id) }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-eye me-1"></i> View Document
            </a>
            <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Documents
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Edit Document Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('driver.documents.update', $document->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="document_type" class="form-label">Document Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('document_type') is-invalid @enderror" id="document_type" name="document_type" required>
                                    <option value="">Select Document Type</option>
                                    <option value="Driver License" {{ $document->type == 'Driver License' ? 'selected' : '' }}>Driver License</option>
                                    <option value="PHD License" {{ $document->type == 'PHD License' ? 'selected' : '' }}>PHD License</option>
                                    <option value="Vehicle PHD License" {{ $document->type == 'Vehicle PHD License' ? 'selected' : '' }}>Vehicle PHD License</option>
                                    <option value="Vehicle Insurance" {{ $document->type == 'Vehicle Insurance' ? 'selected' : '' }}>Vehicle Insurance</option>
                                    <option value="MOT Certificate" {{ $document->type == 'MOT Certificate' ? 'selected' : '' }}>MOT Certificate</option>
                                    <option value="V5C Logbook" {{ $document->type == 'V5C Logbook' ? 'selected' : '' }}>V5C Logbook</option>
                                    <option value="Other" {{ $document->type == 'Other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('document_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="document_number" class="form-label">Document Number</label>
                                <input type="text" class="form-control @error('document_number') is-invalid @enderror" id="document_number" name="document_number" value="{{ old('document_number', $document->document_number) }}">
                                @error('document_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">License number, certificate number, etc.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" id="expiry_date" name="expiry_date" value="{{ old('expiry_date', $document->expiry_date ? $document->expiry_date->format('Y-m-d') : '') }}">
                                @error('expiry_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <input type="text" class="form-control" value="{{ ucfirst($document->status) }}" disabled>
                                <div class="form-text">Status can only be changed by administrators</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes', $document->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Any additional information about this document</div>
                        </div>

                        <div class="mb-4">
                            <label for="document_file" class="form-label">Replace Document File</label>
                            <input type="file" class="form-control @error('document_file') is-invalid @enderror" id="document_file" name="document_file">
                            @error('document_file')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Leave empty to keep the current file. Uploading a new file will reset the verification status.</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Updating this document will reset its verification status. It will need to be verified again by an administrator.
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Document</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Current Document</h5>
                </div>
                <div class="card-body p-0">
                    <div class="document-preview-container">
                        @if(in_array(pathinfo($document->file_path, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif']))
                            <img src="{{ asset('storage/' . $document->file_path) }}" alt="{{ $document->type }}" class="img-fluid">
                        @elseif(pathinfo($document->file_path, PATHINFO_EXTENSION) == 'pdf')
                            <div class="p-4 text-center">
                                <i class="fas fa-file-pdf fa-5x text-danger mb-3"></i>
                                <p>PDF Document</p>
                                <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye me-1"></i> View PDF
                                </a>
                            </div>
                        @else
                            <div class="p-4 text-center">
                                <i class="fas fa-file fa-5x text-secondary mb-3"></i>
                                <p>Document File</p>
                                <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye me-1"></i> View File
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i> Uploaded: {{ $document->created_at->format('M d, Y') }}
                        </small>
                        <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank" download>
                            <i class="fas fa-download me-1"></i> Download
                        </a>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Document Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-3">
                            @if($document->status == 'verified')
                                <div class="rounded-circle bg-success p-2 me-3">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Verified</h6>
                                    <small class="text-muted">This document has been verified by an administrator</small>
                                </div>
                            @elseif($document->status == 'pending')
                                <div class="rounded-circle bg-warning p-2 me-3">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Pending Verification</h6>
                                    <small class="text-muted">This document is awaiting verification</small>
                                </div>
                            @elseif($document->status == 'rejected')
                                <div class="rounded-circle bg-danger p-2 me-3">
                                    <i class="fas fa-times text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Rejected</h6>
                                    <small class="text-muted">This document has been rejected</small>
                                </div>
                            @else
                                <div class="rounded-circle bg-secondary p-2 me-3">
                                    <i class="fas fa-question text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ ucfirst($document->status) }}</h6>
                                    <small class="text-muted">Current status of this document</small>
                                </div>
                            @endif
                        </div>

                        @if($document->status == 'rejected' && $document->rejection_reason)
                            <div class="alert alert-danger mb-0">
                                <strong>Rejection Reason:</strong><br>
                                {{ $document->rejection_reason }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
