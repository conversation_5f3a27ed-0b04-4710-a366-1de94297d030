<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display revenue reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function revenue(Request $request)
    {
        $period = $request->input('period', 'monthly');
        $year = $request->input('year', Carbon::now()->year);
        $month = $request->input('month', Carbon::now()->month);

        // Get revenue data based on period
        $revenueData = [];
        $labels = [];

        if ($period === 'daily') {
            // Daily revenue for the selected month
            $daysInMonth = Carbon::createFromDate($year, $month, 1)->daysInMonth;
            
            for ($day = 1; $day <= $daysInMonth; $day++) {
                $date = Carbon::createFromDate($year, $month, $day);
                $labels[] = $date->format('M d');
                
                $revenue = Booking::whereDate('created_at', $date->format('Y-m-d'))
                    ->where('status', 'completed')
                    ->sum('amount');
                
                $revenueData[] = $revenue;
            }
        } elseif ($period === 'monthly') {
            // Monthly revenue for the selected year
            for ($month = 1; $month <= 12; $month++) {
                $date = Carbon::createFromDate($year, $month, 1);
                $labels[] = $date->format('M');
                
                $revenue = Booking::whereYear('created_at', $year)
                    ->whereMonth('created_at', $month)
                    ->where('status', 'completed')
                    ->sum('amount');
                
                $revenueData[] = $revenue;
            }
        } elseif ($period === 'yearly') {
            // Yearly revenue for the last 5 years
            $currentYear = Carbon::now()->year;
            $startYear = $currentYear - 4;
            
            for ($year = $startYear; $year <= $currentYear; $year++) {
                $labels[] = $year;
                
                $revenue = Booking::whereYear('created_at', $year)
                    ->where('status', 'completed')
                    ->sum('amount');
                
                $revenueData[] = $revenue;
            }
        }

        // Get payment method distribution
        $paymentMethods = Payment::select('payment_method', DB::raw('count(*) as count'))
            ->groupBy('payment_method')
            ->get();

        // Get top 5 clients by revenue
        $topClients = Booking::select('user_id', DB::raw('sum(amount) as total_spent'))
            ->with('user')
            ->where('status', 'completed')
            ->groupBy('user_id')
            ->orderBy('total_spent', 'desc')
            ->take(5)
            ->get();

        return view('admin.reports.revenue', compact(
            'period',
            'year',
            'month',
            'labels',
            'revenueData',
            'paymentMethods',
            'topClients'
        ));
    }

    /**
     * Display booking reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function bookings(Request $request)
    {
        $period = $request->input('period', 'monthly');
        $year = $request->input('year', Carbon::now()->year);
        $month = $request->input('month', Carbon::now()->month);

        // Get booking data based on period
        $bookingData = [];
        $labels = [];

        if ($period === 'daily') {
            // Daily bookings for the selected month
            $daysInMonth = Carbon::createFromDate($year, $month, 1)->daysInMonth;
            
            for ($day = 1; $day <= $daysInMonth; $day++) {
                $date = Carbon::createFromDate($year, $month, $day);
                $labels[] = $date->format('M d');
                
                $bookings = Booking::whereDate('created_at', $date->format('Y-m-d'))->count();
                
                $bookingData[] = $bookings;
            }
        } elseif ($period === 'monthly') {
            // Monthly bookings for the selected year
            for ($month = 1; $month <= 12; $month++) {
                $date = Carbon::createFromDate($year, $month, 1);
                $labels[] = $date->format('M');
                
                $bookings = Booking::whereYear('created_at', $year)
                    ->whereMonth('created_at', $month)
                    ->count();
                
                $bookingData[] = $bookings;
            }
        } elseif ($period === 'yearly') {
            // Yearly bookings for the last 5 years
            $currentYear = Carbon::now()->year;
            $startYear = $currentYear - 4;
            
            for ($year = $startYear; $year <= $currentYear; $year++) {
                $labels[] = $year;
                
                $bookings = Booking::whereYear('created_at', $year)->count();
                
                $bookingData[] = $bookings;
            }
        }

        // Get booking status distribution
        $bookingStatuses = Booking::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        // Get popular pickup locations
        $popularPickups = Booking::select('pickup_address', DB::raw('count(*) as count'))
            ->groupBy('pickup_address')
            ->orderBy('count', 'desc')
            ->take(5)
            ->get();

        // Get popular dropoff locations
        $popularDropoffs = Booking::select('dropoff_address', DB::raw('count(*) as count'))
            ->groupBy('dropoff_address')
            ->orderBy('count', 'desc')
            ->take(5)
            ->get();

        return view('admin.reports.bookings', compact(
            'period',
            'year',
            'month',
            'labels',
            'bookingData',
            'bookingStatuses',
            'popularPickups',
            'popularDropoffs'
        ));
    }

    /**
     * Display driver reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function drivers(Request $request)
    {
        // Get top drivers by number of completed rides
        $topDriversByRides = User::where('role', 'driver')
            ->withCount(['driverRides' => function($query) {
                $query->where('status', 'completed');
            }])
            ->orderBy('driver_rides_count', 'desc')
            ->take(10)
            ->get();

        // Get top drivers by revenue generated
        $topDriversByRevenue = User::where('role', 'driver')
            ->withSum(['driverRides' => function($query) {
                $query->where('status', 'completed');
            }], 'amount')
            ->orderBy('driver_rides_sum_amount', 'desc')
            ->take(10)
            ->get();

        // Get driver availability statistics
        $availableDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->where('is_available', true)
            ->count();
        
        $unavailableDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->where('is_available', false)
            ->count();
        
        $inactiveDrivers = User::where('role', 'driver')
            ->where('is_active', false)
            ->count();

        return view('admin.reports.drivers', compact(
            'topDriversByRides',
            'topDriversByRevenue',
            'availableDrivers',
            'unavailableDrivers',
            'inactiveDrivers'
        ));
    }

    /**
     * Display vehicle reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function vehicles(Request $request)
    {
        // Get top vehicles by number of bookings
        $topVehiclesByBookings = Vehicle::withCount('bookings')
            ->orderBy('bookings_count', 'desc')
            ->take(10)
            ->get();

        // Get top vehicles by revenue generated
        $topVehiclesByRevenue = Vehicle::withSum(['bookings' => function($query) {
                $query->where('status', 'completed');
            }], 'amount')
            ->orderBy('bookings_sum_amount', 'desc')
            ->take(10)
            ->get();

        // Get vehicle type distribution
        $vehicleTypes = Vehicle::select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get();

        return view('admin.reports.vehicles', compact(
            'topVehiclesByBookings',
            'topVehiclesByRevenue',
            'vehicleTypes'
        ));
    }
}
