<?php

namespace App\Listeners;

use App\Events\PaymentCompleted;
use App\Services\EmailService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPaymentConfirmationEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentCompleted $event): void
    {
        try {
            EmailService::sendPaymentConfirmation($event->payment);
            
            Log::info('Payment confirmation email sent', [
                'payment_id' => $event->payment->id,
                'booking_id' => $event->payment->booking_id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation email', [
                'payment_id' => $event->payment->id,
                'booking_id' => $event->payment->booking_id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
