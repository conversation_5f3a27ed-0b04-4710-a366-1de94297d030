@extends('emails.layouts.master')

@section('title', 'Ride Cancellation Notice')

@section('content')
<h2>Ride Cancellation Notice</h2>

<p>Dear {{ $driver->name }},</p>

<p>We're writing to inform you that a ride assignment has been cancelled.</p>

<div class="booking-details">
    <h3>Cancelled Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value">{{ $user->name }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Original Pickup Date & Time:</span>
        <span class="detail-value">{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Earnings:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($booking->amount * 0.8, 2) }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Cancellation Time:</span>
        <span class="detail-value">{{ now()->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
</div>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #856404; margin-top: 0;">Important Information</h3>
    <ul style="color: #856404; margin-bottom: 0;">
        <li>This ride has been removed from your schedule</li>
        <li>You are now available for other ride assignments during this time slot</li>
        <li>No action is required from your side</li>
        <li>Your availability status remains unchanged</li>
    </ul>
</div>

<h3>What This Means for You:</h3>
<ul>
    <li><strong>Schedule Update:</strong> This time slot is now free in your schedule</li>
    <li><strong>Earnings:</strong> No earnings impact since the ride was cancelled before completion</li>
    <li><strong>Availability:</strong> You may receive new ride requests for this time period</li>
    <li><strong>Rating:</strong> This cancellation does not affect your driver rating</li>
</ul>

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('driver.rides.index') }}" class="btn">View Available Rides</a>
</div>

<h3>Stay Active!</h3>
<p>Don't let this cancellation discourage you. Keep your availability status active to receive more ride requests. 
Remember, cancellations are a normal part of the business and don't reflect on your service quality.</p>

<h3>Need Support?</h3>
<p>If you have any questions about this cancellation or need assistance, please contact our driver support team:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or use the driver app support feature</li>
</ul>

<p>Thank you for your understanding and continued partnership with {{ $companyName }}.</p>

<p>Safe driving,<br>
The {{ $companyName }} Team</p>
@endsection
