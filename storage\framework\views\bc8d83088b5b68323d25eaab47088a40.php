<?php $__env->startSection('title', 'Test Email - ' . ($template_name ?? 'Email Configuration Test')); ?>

<?php $__env->startSection('content'); ?>
<h2><?php echo e(isset($template_name) ? 'Template Test: ' . ucfirst(str_replace('_', ' ', $template_name)) : 'Email Configuration Test'); ?></h2>

<p>Hello <?php echo e($user->name ?? 'Test User'); ?>! This email confirms that your email configuration is working correctly.</p>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">✓ Email System is Working!</h3>
    <p style="color: #155724; margin-bottom: 0;">
        Your email configuration has been successfully tested. You can now send emails to your customers.
    </p>
</div>

<div class="booking-details">
    <h3>Test Details</h3>

    <div class="detail-row">
        <span class="detail-label">Test Time:</span>
        <span class="detail-value"><?php echo e(now()->format('M j, Y g:i A')); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Recipient:</span>
        <span class="detail-value"><?php echo e($user->email ?? '<EMAIL>'); ?></span>
    </div>

    <?php if(isset($template_name)): ?>
    <div class="detail-row">
        <span class="detail-label">Template:</span>
        <span class="detail-value"><?php echo e($template_name); ?></span>
    </div>
    <?php endif; ?>

    <div class="detail-row">
        <span class="detail-label">Application:</span>
        <span class="detail-value"><?php echo e($app_name ?? config('app.name')); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Status:</span>
        <span class="detail-value" style="color: #28a745;"><strong>✓ Success</strong></span>
    </div>
</div>

<?php if(isset($booking)): ?>
<div class="booking-details">
    <h3>🚗 Sample Booking Data</h3>

    <div class="detail-row">
        <span class="detail-label">Booking ID:</span>
        <span class="detail-value"><?php echo e($booking->id); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Location:</span>
        <span class="detail-value"><?php echo e($booking->pickup_location); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Drop-off Location:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_location); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_datetime); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Total Amount:</span>
        <span class="detail-value" style="color: #28a745; font-weight: bold;"><?php echo e($booking->total_amount); ?></span>
    </div>

    <?php if(isset($driver)): ?>
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value"><?php echo e($driver->name); ?> (<?php echo e($driver->phone); ?>)</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($driver->vehicle); ?></span>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<?php if(isset($payment)): ?>
<div class="booking-details">
    <h3>💳 Sample Payment Data</h3>

    <div class="detail-row">
        <span class="detail-label">Amount:</span>
        <span class="detail-value" style="color: #28a745; font-weight: bold;"><?php echo e($payment->amount); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value"><?php echo e($payment->method); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Transaction ID:</span>
        <span class="detail-value"><?php echo e($payment->transaction_id); ?></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Payment Date:</span>
        <span class="detail-value"><?php echo e($payment->date); ?></span>
    </div>
</div>
<?php endif; ?>

<?php if(isset($reset_url)): ?>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #856404; margin-top: 0;">🔐 Password Reset Test</h3>
    <p style="color: #856404;">This template would include a password reset link:</p>
    <p style="text-align: center;">
        <a href="<?php echo e($reset_url); ?>" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
    </p>
    <p style="color: #856404; font-size: 12px; margin-bottom: 0;"><em>Note: This is a sample link for testing purposes.</em></p>
</div>
<?php endif; ?>

<?php if(isset($verification_url)): ?>
<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #0c5460; margin-top: 0;">✉️ Email Verification Test</h3>
    <p style="color: #0c5460;">This template would include an email verification link:</p>
    <p style="text-align: center;">
        <a href="<?php echo e($verification_url); ?>" style="background-color: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
    </p>
    <p style="color: #0c5460; font-size: 12px; margin-bottom: 0;"><em>Note: This is a sample link for testing purposes.</em></p>
</div>
<?php endif; ?>

<h3>What This Means:</h3>
<ul>
    <li>✓ SMTP settings are configured correctly</li>
    <li>✓ Authentication is working</li>
    <li>✓ Email templates are loading properly</li>
    <li>✓ Your customers will receive booking confirmations</li>
    <li>✓ Payment confirmations will be sent automatically</li>
    <li>✓ Reminder emails will work as expected</li>
</ul>

<h3>Next Steps:</h3>
<ol>
    <li>Configure your email templates if needed</li>
    <li>Set up automated email schedules</li>
    <li>Test with a real booking to ensure everything works</li>
    <li>Monitor email delivery rates in your admin dashboard</li>
</ol>

<p>If you have any questions about email configuration or need assistance, please contact our support team.</p>

<p>Best regards,<br>
The <?php echo e($app_name ?? config('app.name')); ?> Technical Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\test.blade.php ENDPATH**/ ?>