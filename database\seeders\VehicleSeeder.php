<?php

namespace Database\Seeders;

use App\Models\Vehicle;
use Illuminate\Database\Seeder;

class VehicleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vehicles = [
            [
                'name' => 'Economy Sedan',
                'type' => 'economy',
                'model' => 'Toyota Corolla',
                'category' => 'sedan',
                'seats' => 4,
                'luggage_capacity' => 2,
                'transmission' => 'automatic',
                'price_per_km' => 1.50,
                'price_per_hour' => 25.00,
                'description' => 'Comfortable and fuel-efficient sedan perfect for city travel.',
                'is_active' => true,
            ],
            [
                'name' => 'Standard Sedan',
                'type' => 'sedan',
                'model' => 'Honda Accord',
                'category' => 'sedan',
                'seats' => 5,
                'luggage_capacity' => 3,
                'transmission' => 'automatic',
                'price_per_km' => 2.00,
                'price_per_hour' => 30.00,
                'description' => 'Spacious sedan with excellent comfort for longer journeys.',
                'is_active' => true,
            ],
            [
                'name' => 'Luxury Sedan',
                'type' => 'luxury',
                'model' => 'Mercedes-Benz E-Class',
                'category' => 'sedan',
                'seats' => 5,
                'luggage_capacity' => 4,
                'transmission' => 'automatic',
                'price_per_km' => 3.50,
                'price_per_hour' => 60.00,
                'description' => 'luxury sedan with top-tier comfort and amenities.',
                'is_active' => true,
            ],
            [
                'name' => 'Compact SUV',
                'type' => 'suv',
                'model' => 'Toyota RAV4',
                'category' => 'suv',
                'seats' => 5,
                'luggage_capacity' => 4,
                'transmission' => 'automatic',
                'price_per_km' => 2.50,
                'price_per_hour' => 35.00,
                'description' => 'Versatile SUV with ample space for passengers and luggage.',
                'is_active' => true,
            ],
            [
                'name' => 'Luxury SUV',
                'type' => 'luxury',
                'model' => 'BMW X5',
                'category' => 'suv',
                'seats' => 7,
                'luggage_capacity' => 5,
                'transmission' => 'automatic',
                'price_per_km' => 4.00,
                'price_per_hour' => 70.00,
                'description' => 'SUV offering exceptional comfort and space.',
                'is_active' => true,
            ],
            [
                'name' => 'Minivan',
                'type' => 'van',
                'model' => 'Honda Odyssey',
                'category' => 'van',
                'seats' => 8,
                'luggage_capacity' => 6,
                'transmission' => 'automatic',
                'price_per_km' => 3.00,
                'price_per_hour' => 45.00,
                'description' => 'Spacious minivan perfect for family travel or group outings.',
                'is_active' => true,
            ],
            [
                'name' => 'Stretch Limousine',
                'type' => 'limo',
                'model' => 'Lincoln Continental',
                'category' => 'limo',
                'seats' => 10,
                'luggage_capacity' => 5,
                'transmission' => 'automatic',
                'price_per_km' => 5.00,
                'price_per_hour' => 100.00,
                'description' => 'Luxurious stretch limousine for special occasions and VIP transport.',
                'is_active' => true,
            ],
        ];

        foreach ($vehicles as $vehicle) {
            Vehicle::updateOrCreate(
                [
                    'name' => $vehicle['name'],
                    'model' => $vehicle['model'],
                ],
                $vehicle
            );
        }
    }
}
