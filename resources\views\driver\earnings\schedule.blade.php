@extends('layouts.driver')

@section('title', 'Payment Schedule')

@section('styles')
<style>
    .schedule-container {
        padding: 20px;
    }

    .schedule-header {
        margin-bottom: 30px;
    }

    .schedule-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .schedule-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 20px;
    }

    .schedule-card .card-body {
        padding: 30px;
    }

    .next-payment-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 25px;
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
        text-align: center;
        border-left: 5px solid #ee393d;
    }

    .next-payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .next-payment-date {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: #343a40;
    }

    .next-payment-label {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .next-payment-amount {
        font-size: 2rem;
        font-weight: 700;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .payment-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .payment-table th,
    .payment-table td {
        padding: 15px;
        border-bottom: 1px solid #eee;
    }

    .payment-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #343a40;
    }

    .payment-table tr:last-child td {
        border-bottom: none;
    }

    .payment-table tr:hover td {
        background-color: #f8f9fa;
    }

    .payment-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-processing {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .payment-amount {
        font-weight: 700;
        color: #343a40;
    }

    .payment-date {
        color: #6c757d;
    }

    .payment-method {
        display: flex;
        align-items: center;
    }

    .payment-method i {
        margin-right: 5px;
        font-size: 1.2rem;
    }

    .payment-info-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .payment-info-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #343a40;
    }

    .payment-info-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }

    .payment-info-item:last-child {
        border-bottom: none;
    }

    .payment-info-label {
        color: #6c757d;
    }

    .payment-info-value {
        font-weight: 600;
        color: #343a40;
    }
</style>
@endsection

@section('content')
<div class="schedule-container">
    <div class="schedule-header">
        <h2><i class="fas fa-calendar-alt me-2"></i> Payment Schedule</h2>
        <p class="text-muted">View your upcoming payments and payment history</p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="next-payment-card">
                <h5 class="next-payment-label">Next Payment Date</h5>
                <div class="next-payment-date">{{ $nextPaymentDate->format('F d, Y') }}</div>
                <div class="next-payment-amount">
                    @php
                        $pendingAmount = $upcomingPayments->sum('amount');
                    @endphp
                    @currency(){{ number_format($pendingAmount, 2) }}
                </div>
                <p class="text-muted mb-0">Estimated amount based on completed rides</p>
            </div>

            <div class="schedule-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Payment Information</h5>
                </div>
                <div class="card-body">
                    <div class="payment-info-card">
                        <h6 class="payment-info-title">Payment Schedule</h6>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Payment Frequency</span>
                            <span class="payment-info-value">Monthly</span>
                        </div>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Payment Day</span>
                            <span class="payment-info-value">1st of each month</span>
                        </div>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Processing Time</span>
                            <span class="payment-info-value">1-3 business days</span>
                        </div>
                    </div>

                    <div class="payment-info-card">
                        <h6 class="payment-info-title">Payment Method</h6>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Method</span>
                            <span class="payment-info-value">Bank Transfer</span>
                        </div>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Account Name</span>
                            <span class="payment-info-value">{{ Auth::user()->name }}</span>
                        </div>
                        <div class="payment-info-item">
                            <span class="payment-info-label">Account Number</span>
                            <span class="payment-info-value">••••{{ substr(Auth::user()->bank_account_number ?? '1234', -4) }}</span>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ route('driver.profile.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i> Update Payment Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="schedule-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i> Upcoming Payments</h5>
                </div>
                <div class="card-body">
                    @if ($upcomingPayments->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times text-muted fa-3x mb-3"></i>
                            <h5 class="text-muted">No Upcoming Payments</h5>
                            <p class="text-muted">You don't have any pending payments at the moment.</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="payment-table">
                                <thead>
                                    <tr>
                                        <th>Booking #</th>
                                        <th>Date</th>
                                        <th>Client</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($upcomingPayments as $payment)
                                        <tr>
                                            <td>{{ $payment->booking_number }}</td>
                                            <td class="payment-date">{{ $payment->completed_at ? $payment->completed_at->format('M d, Y') : 'N/A' }}</td>
                                            <td>{{ $payment->user->name }}</td>
                                            <td class="payment-amount">@currency(){{ number_format($payment->amount, 2) }}</td>
                                            <td><span class="payment-status status-pending">Pending</span></td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>

            <div class="schedule-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i> Payment History</h5>
                </div>
                <div class="card-body">
                    @if ($paymentHistory->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-history text-muted fa-3x mb-3"></i>
                            <h5 class="text-muted">No Payment History</h5>
                            <p class="text-muted">Your payment history will appear here once payments are processed.</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="payment-table">
                                <thead>
                                    <tr>
                                        <th>Booking #</th>
                                        <th>Payment Date</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($paymentHistory as $payment)
                                        <tr>
                                            <td>{{ $payment->booking_number }}</td>
                                            <td class="payment-date">{{ $payment->payment_processed_at ? $payment->payment_processed_at->format('M d, Y') : 'N/A' }}</td>
                                            <td class="payment-amount">@currency(){{ number_format($payment->amount, 2) }}</td>
                                            <td class="payment-method">
                                                <i class="fas fa-university"></i> Bank Transfer
                                            </td>
                                            <td><span class="payment-status status-completed">Completed</span></td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
