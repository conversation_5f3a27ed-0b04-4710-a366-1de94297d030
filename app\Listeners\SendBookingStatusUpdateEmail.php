<?php

namespace App\Listeners;

use App\Events\BookingStatusChanged;
use App\Services\EmailService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendBookingStatusUpdateEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BookingStatusChanged $event): void
    {
        try {
            EmailService::sendBookingStatusUpdate(
                $event->booking, 
                $event->oldStatus, 
                $event->newStatus
            );
            
            Log::info('Booking status update email sent', [
                'booking_id' => $event->booking->id,
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send booking status update email', [
                'booking_id' => $event->booking->id,
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }
}
