<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;

class PaymentExportController extends Controller
{
    /**
     * Export payments data as CSV
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        // Get filter parameters
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $status = $request->input('status');
        $paymentMethod = $request->input('payment_method');
        $search = $request->input('search');
        
        // Build query
        $query = Payment::with(['booking', 'booking.user', 'booking.vehicle'])
            ->orderBy('created_at', 'desc');
        
        // Apply filters
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }
        
        if ($status) {
            $query->where('status', $status);
        }
        
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('booking', function($q) use ($search) {
                      $q->where('booking_number', 'like', "%{$search}%");
                  });
            });
        }
        
        // Get payments
        $payments = $query->get();
        
        // Create CSV
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="payments_export_' . date('Y-m-d') . '.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];
        
        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Transaction ID',
                'Booking Number',
                'Client Name',
                'Client Email',
                'Vehicle',
                'Amount',
                'Payment Method',
                'Status',
                'Date',
                'Pickup Date',
                'Pickup Location',
                'Dropoff Location'
            ]);
            
            // Add payment data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->transaction_id,
                    $payment->booking->booking_number,
                    $payment->booking->user->name,
                    $payment->booking->user->email,
                    $payment->booking->vehicle->name,
                    $payment->amount,
                    ucfirst(str_replace('_', ' ', $payment->payment_method)),
                    ucfirst($payment->status),
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->booking->pickup_date->format('Y-m-d H:i:s'),
                    $payment->booking->pickup_address,
                    $payment->booking->dropoff_address
                ]);
            }
            
            fclose($file);
        };
        
        return Response::stream($callback, 200, $headers);
    }
    
    /**
     * Export payment report data as CSV
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportReport(Request $request)
    {
        // Get date range
        $startDate = $request->input('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        
        // Convert to Carbon instances
        $startDateCarbon = Carbon::parse($startDate);
        $endDateCarbon = Carbon::parse($endDate);
        
        // Get daily payments
        $dailyPayments = Payment::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as total_amount')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        // Get payment methods
        $paymentMethods = Payment::selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('payment_method')
            ->orderBy('count', 'desc')
            ->get();
        
        // Get payment statuses
        $paymentStatuses = Payment::selectRaw('status, COUNT(*) as count, SUM(amount) as total_amount')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('status')
            ->orderBy('count', 'desc')
            ->get();
        
        // Get top clients
        $topClients = Payment::with(['booking.user'])
            ->selectRaw('booking_id, COUNT(*) as count, SUM(amount) as total_amount')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('booking_id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get();
        
        // Create CSV
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="payment_report_' . date('Y-m-d') . '.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];
        
        $callback = function() use ($dailyPayments, $paymentMethods, $paymentStatuses, $topClients, $startDate, $endDate) {
            $file = fopen('php://output', 'w');
            
            // Add report info
            fputcsv($file, ['Payment Report']);
            fputcsv($file, ['Date Range', $startDate . ' to ' . $endDate]);
            fputcsv($file, ['Generated On', Carbon::now()->format('Y-m-d H:i:s')]);
            fputcsv($file, []);
            
            // Daily Payments
            fputcsv($file, ['Daily Payments']);
            fputcsv($file, ['Date', 'Number of Payments', 'Total Amount']);
            
            foreach ($dailyPayments as $payment) {
                fputcsv($file, [
                    Carbon::parse($payment->date)->format('Y-m-d'),
                    $payment->count,
                    $payment->total_amount
                ]);
            }
            
            fputcsv($file, []);
            
            // Payment Methods
            fputcsv($file, ['Payment Methods']);
            fputcsv($file, ['Method', 'Number of Payments', 'Total Amount', 'Percentage']);
            
            $totalPayments = $paymentMethods->sum('count');
            
            foreach ($paymentMethods as $method) {
                $percentage = $totalPayments > 0 ? round(($method->count / $totalPayments) * 100, 2) : 0;
                
                fputcsv($file, [
                    ucfirst(str_replace('_', ' ', $method->payment_method)),
                    $method->count,
                    $method->total_amount,
                    $percentage . '%'
                ]);
            }
            
            fputcsv($file, []);
            
            // Payment Statuses
            fputcsv($file, ['Payment Statuses']);
            fputcsv($file, ['Status', 'Number of Payments', 'Total Amount', 'Percentage']);
            
            $totalStatusPayments = $paymentStatuses->sum('count');
            
            foreach ($paymentStatuses as $status) {
                $percentage = $totalStatusPayments > 0 ? round(($status->count / $totalStatusPayments) * 100, 2) : 0;
                
                fputcsv($file, [
                    ucfirst($status->status),
                    $status->count,
                    $status->total_amount,
                    $percentage . '%'
                ]);
            }
            
            fputcsv($file, []);
            
            // Top Clients
            fputcsv($file, ['Top Clients']);
            fputcsv($file, ['Client Name', 'Email', 'Number of Payments', 'Total Amount']);
            
            foreach ($topClients as $client) {
                fputcsv($file, [
                    $client->booking->user->name,
                    $client->booking->user->email,
                    $client->count,
                    $client->total_amount
                ]);
            }
            
            fclose($file);
        };
        
        return Response::stream($callback, 200, $headers);
    }
}
