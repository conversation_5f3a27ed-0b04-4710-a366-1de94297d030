<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_schedules', function (Blueprint $table) {
            // Check if the columns don't exist before adding them
            if (!Schema::hasColumn('driver_schedules', 'notes')) {
                $table->text('notes')->nullable()->after('is_available');
            }

            if (!Schema::hasColumn('driver_schedules', 'is_time_off')) {
                $table->boolean('is_time_off')->default(false)->after('notes');
            }

            if (!Schema::hasColumn('driver_schedules', 'time_off_reason')) {
                $table->string('time_off_reason')->nullable()->after('is_time_off');
            }

            if (!Schema::hasColumn('driver_schedules', 'status')) {
                $table->string('status')->nullable()->after('time_off_reason');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_schedules', function (Blueprint $table) {
            // Check if the columns exist before dropping them
            $columns = ['notes', 'is_time_off', 'time_off_reason', 'status'];

            foreach ($columns as $column) {
                if (Schema::hasColumn('driver_schedules', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
