<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Show the driver profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();
        return view('driver.profile.index', compact('user'));
    }

    /**
     * Show the form for editing the driver profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit()
    {
        $user = Auth::user();
        return view('driver.profile.edit', compact('user'));
    }

    /**
     * Update the driver profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'license_number' => 'required|string|max:50',
            'vehicle_info' => 'required|string|max:255',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->address = $request->address;
        $user->license_number = $request->license_number;
        $user->vehicle_info = $request->vehicle_info;

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->profile_photo) {
                Storage::disk('public')->delete($user->profile_photo);
            }

            $image = $request->file('profile_photo');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('profile-photos', $imageName, 'public');
            $user->profile_photo = $path;
        }

        $user->save();

        return redirect()->route('driver.profile.index')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Show the form for changing password.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showChangePasswordForm()
    {
        return view('driver.profile.change-password');
    }

    /**
     * Change the driver's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()
                ->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->route('driver.profile.index')
            ->with('success', 'Password changed successfully.');
    }

    /**
     * Show the form for updating availability status.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showAvailabilityForm()
    {
        $user = Auth::user();
        return view('driver.profile.availability', compact('user'));
    }

    /**
     * Update the driver's availability status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAvailability(Request $request)
    {
        $request->validate([
            'is_available' => 'required|boolean',
        ]);

        $user = Auth::user();
        $user->is_available = $request->is_available;
        $user->save();

        return redirect()->route('driver.profile.index')
            ->with('success', 'Availability status updated successfully.');
    }

    /**
     * Update the driver's avatar.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $user = Auth::user();

        // Delete old avatar if exists
        if ($user->profile_photo && Storage::exists('public/' . $user->profile_photo)) {
            Storage::delete('public/' . $user->profile_photo);
        }

        // Store new avatar
        $image = $request->file('avatar');
        $imageName = time() . '.' . $image->getClientOriginalExtension();
        $path = $image->storeAs('profile-photos', $imageName, 'public');
        $user->profile_photo = $path;
        $user->save();

        return redirect()->route('driver.profile.index')
            ->with('success', 'Profile photo updated successfully.');
    }

    /**
     * Show the driver's documents.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function documents()
    {
        $user = Auth::user();
        $documents = $user->documents()->orderBy('created_at', 'desc')->get();

        return view('driver.profile.documents', compact('documents'));
    }

    /**
     * Upload a document.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function uploadDocument(Request $request)
    {
        $request->validate([
            'document_type' => 'required|string|max:50',
            'document_file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'expiry_date' => 'nullable|date',
        ]);

        $user = Auth::user();

        // Store document
        $document = $request->file('document_file');
        $documentName = time() . '.' . $document->getClientOriginalExtension();
        $path = $document->storeAs('driver-documents', $documentName, 'public');

        // Create document record
        $user->documents()->create([
            'type' => $request->document_type,
            'file_path' => $path,
            'expiry_date' => $request->expiry_date,
        ]);

        return redirect()->route('driver.profile.documents')
            ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Delete a document.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteDocument($id)
    {
        $user = Auth::user();
        $document = $user->documents()->findOrFail($id);

        // Delete file
        if (Storage::exists('public/' . $document->file_path)) {
            Storage::delete('public/' . $document->file_path);
        }

        // Delete record
        $document->delete();

        return redirect()->route('driver.profile.documents')
            ->with('success', 'Document deleted successfully.');
    }
}
