<?php $__env->startSection('title', 'Email Templates'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Templates</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.emails.index')); ?>">Email Management</a></li>
                        <li class="breadcrumb-item active">Templates</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Categories -->
    <div class="row">
        <!-- Booking Templates -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Booking Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Confirmation</h6>
                                <p class="mb-1 text-muted">Sent when a booking is confirmed</p>
                                <small class="text-muted">Template: booking_confirmation</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_confirmation')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_confirmation')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                        
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Cancelled</h6>
                                <p class="mb-1 text-muted">Sent when a booking is cancelled</p>
                                <small class="text-muted">Template: booking_cancelled</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_cancelled')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_cancelled')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Booking Reminder</h6>
                                <p class="mb-1 text-muted">Sent as a reminder before pickup</p>
                                <small class="text-muted">Template: booking_reminder</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('booking_reminder')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('booking_reminder')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Driver Assigned</h6>
                                <p class="mb-1 text-muted">Sent when a driver is assigned to booking</p>
                                <small class="text-muted">Template: driver_assigned</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('driver_assigned')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('driver_assigned')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Templates -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">User Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Welcome Email</h6>
                                <p class="mb-1 text-muted">Sent to new users after registration</p>
                                <small class="text-muted">Template: user_welcome</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('user_welcome')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('user_welcome')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Password Reset</h6>
                                <p class="mb-1 text-muted">Sent when user requests password reset</p>
                                <small class="text-muted">Template: password_reset</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('password_reset')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('password_reset')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Email Verification</h6>
                                <p class="mb-1 text-muted">Sent to verify email address</p>
                                <small class="text-muted">Template: email_verification</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('email_verification')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('email_verification')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Account Activated</h6>
                                <p class="mb-1 text-muted">Sent when account is activated by admin</p>
                                <small class="text-muted">Template: account_activated</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('account_activated')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('account_activated')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Templates -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">System Templates</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Payment Confirmation</h6>
                                <p class="mb-1 text-muted">Sent when payment is processed</p>
                                <small class="text-muted">Template: payment_confirmation</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('payment_confirmation')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('payment_confirmation')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">System Notification</h6>
                                <p class="mb-1 text-muted">General system notifications</p>
                                <small class="text-muted">Template: system_notification</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="previewTemplate('system_notification')">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-secondary" onclick="editTemplate('system_notification')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Variables -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Available Variables</h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="variablesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#userVars">
                                    User Variables
                                </button>
                            </h2>
                            <div id="userVars" class="accordion-collapse collapse show" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code><?php echo e('{{$user->name); ?>' }}</code></div>
                                        <div class="col-6">User's full name</div>
                                        <div class="col-6"><code><?php echo e('{{$user->email); ?>' }}</code></div>
                                        <div class="col-6">User's email address</div>
                                        <div class="col-6"><code><?php echo e('{{$user->phone); ?>' }}</code></div>
                                        <div class="col-6">User's phone number</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#bookingVars">
                                    Booking Variables
                                </button>
                            </h2>
                            <div id="bookingVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code><?php echo e('{{$booking->id); ?>' }}</code></div>
                                        <div class="col-6">Booking ID</div>
                                        <div class="col-6"><code><?php echo e('{{$booking->pickup_location); ?>' }}</code></div>
                                        <div class="col-6">Pickup location</div>
                                        <div class="col-6"><code><?php echo e('{{$booking->dropoff_location); ?>' }}</code></div>
                                        <div class="col-6">Drop-off location</div>
                                        <div class="col-6"><code><?php echo e('{{$booking->pickup_datetime); ?>' }}</code></div>
                                        <div class="col-6">Pickup date and time</div>
                                        <div class="col-6"><code><?php echo e('{{$booking->total_amount); ?>' }}</code></div>
                                        <div class="col-6">Total booking amount</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#systemVars">
                                    System Variables
                                </button>
                            </h2>
                            <div id="systemVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-6"><code><?php echo e('{{config("app.name")); ?>' }}</code></div>
                                        <div class="col-6">Application name</div>
                                        <div class="col-6"><code><?php echo e('{{config("app.url")); ?>' }}</code></div>
                                        <div class="col-6">Application URL</div>
                                        <div class="col-6"><code><?php echo e('{{now()->format("Y")); ?>' }}</code></div>
                                        <div class="col-6">Current year</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="sendTestEmail()">Send Test Email</button>
            </div>
        </div>
    </div>
</div>

<script>
function previewTemplate(templateName) {
    const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    const content = document.getElementById('templatePreviewContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Simulate loading template preview
    setTimeout(() => {
        content.innerHTML = `
            <div class="border rounded p-3" style="background-color: #f8f9fa;">
                <h4>Preview: ${templateName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                <hr>
                <div class="email-preview">
                    <p><strong>Subject:</strong> Your ${templateName.includes('booking') ? 'Booking' : 'Account'} Update</p>
                    <div class="mt-3">
                        <p>Dear [User Name],</p>
                        <p>This is a preview of the ${templateName.replace('_', ' ')} email template.</p>
                        <p>Template variables will be replaced with actual values when sent.</p>
                        <p>Best regards,<br>YNR Cars Team</p>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

function editTemplate(templateName) {
    // Redirect to template editor (would be implemented)
    alert('Template editor would open here for: ' + templateName);
}

function sendTestEmail() {
    alert('Test email functionality would be implemented here');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/templates.blade.php ENDPATH**/ ?>