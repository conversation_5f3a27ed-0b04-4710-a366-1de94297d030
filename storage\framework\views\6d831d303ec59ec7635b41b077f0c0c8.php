<?php $__env->startSection('title', 'Reviews Management'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .review-card {
        transition: transform 0.3s, box-shadow 0.3s;
        margin-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
    }

    .review-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .review-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        padding: 15px 20px;
    }

    .review-body {
        padding: 20px;
    }

    .review-footer {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-top: 1px solid #eee;
    }

    .star-rating {
        color: #ffc107;
        font-size: 1.2rem;
    }

    .star-rating .far {
        color: #e0e0e0;
    }

    .rating-distribution {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .rating-bar-container {
        flex-grow: 1;
        background-color: #f0f0f0;
        height: 10px;
        border-radius: 5px;
        margin: 0 10px;
    }

    .rating-bar {
        height: 100%;
        border-radius: 5px;
        background-color: #ffc107;
    }

    .rating-count {
        min-width: 30px;
        text-align: right;
    }

    .filter-card {
        margin-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-star me-2"></i> Reviews Management</h2>
    </div>

    <div class="row">
        <!-- Review Statistics -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Review Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div class="text-center">
                            <h3 class="mb-0"><?php echo e($reviewStats['total']); ?></h3>
                            <p class="text-muted mb-0">Total Reviews</p>
                        </div>
                        <div class="text-center">
                            <h3 class="mb-0"><?php echo e(number_format($reviewStats['average_rating'], 1)); ?></h3>
                            <p class="text-muted mb-0">Average Rating</p>
                        </div>
                    </div>

                    <h6 class="mb-3">Rating Distribution</h6>
                    <?php for($i = 5; $i >= 1; $i--): ?>
                        <div class="rating-distribution">
                            <div class="rating-label"><?php echo e($i); ?> <i class="fas fa-star text-warning"></i></div>
                            <div class="rating-bar-container">
                                <div class="rating-bar" style="width: <?php echo e($reviewStats['total'] > 0 ? ($reviewStats['rating_distribution'][$i] / $reviewStats['total'] * 100) : 0); ?>%"></div>
                            </div>
                            <div class="rating-count"><?php echo e($reviewStats['rating_distribution'][$i]); ?></div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="col-md-8 mb-4">
            <div class="card filter-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i> Filter Reviews</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.reviews.index')); ?>" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="rating" class="form-label">Rating</label>
                            <select class="form-select" id="rating" name="rating">
                                <option value="">All Ratings</option>
                                <?php for($i = 5; $i >= 1; $i--): ?>
                                    <option value="<?php echo e($i); ?>" <?php echo e(request('rating') == $i ? 'selected' : ''); ?>><?php echo e($i); ?> Stars</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Search reviews..." value="<?php echo e(request('search')); ?>">
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i> Filter
                            </button>
                            <a href="<?php echo e(route('admin.reviews.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-redo me-2"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews List -->
    <div class="row">
        <div class="col-md-12">
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if($reviews->isEmpty()): ?>
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i> No reviews found matching your criteria.
                </div>
            <?php else: ?>
                <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card review-card">
                        <div class="review-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0">Review for Booking #<?php echo e($booking->booking_number); ?></h5>
                                <small><?php echo e($booking->reviewed_at->format('F d, Y h:i A')); ?></small>
                            </div>
                            <div class="star-rating">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <i class="<?php echo e($i <= $booking->rating ? 'fas' : 'far'); ?> fa-star"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="review-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="review-text"><?php echo e($booking->review); ?></p>
                                </div>
                                <div class="col-md-4">
                                    <div class="booking-details">
                                        <p><strong>Client:</strong> <?php echo e($booking->user->name); ?></p>
                                        <p><strong>Vehicle:</strong> <?php echo e($booking->vehicle->name); ?></p>
                                        <?php if($booking->driver): ?>
                                            <p><strong>Driver:</strong> <?php echo e($booking->driver->name); ?></p>
                                        <?php endif; ?>
                                        <p><strong>Booking Date:</strong> <?php echo e($booking->pickup_date->format('M d, Y')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="review-footer d-flex justify-content-end">
                            <a href="<?php echo e(route('admin.reviews.show', $booking->id)); ?>" class="btn btn-sm btn-primary me-2">
                                <i class="fas fa-eye me-1"></i> View
                            </a>
                            <a href="<?php echo e(route('admin.reviews.edit', $booking->id)); ?>" class="btn btn-sm btn-warning me-2">
                                <i class="fas fa-edit me-1"></i> Edit
                            </a>
                            <form action="<?php echo e(route('admin.reviews.destroy', $booking->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this review?');">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash me-1"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($reviews->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reviews\index.blade.php ENDPATH**/ ?>