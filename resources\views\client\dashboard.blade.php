@extends('layouts.client')

@section('title', 'Client Dashboard')

@section('styles')
<style>
    .dashboard-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        margin-bottom: 20px;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .dashboard-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .dashboard-icon {
        font-size: 2.5rem;
        color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto;
    }

    .booking-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #ffeeba;
        color: #856404;
    }

    .status-confirmed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-completed {
        background-color: #c3e6cb;
        color: #155724;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .welcome-banner {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .card-text {
        font-size: 2rem;
        font-weight: 700;
        color: #ee393d;
        margin: 10px 0;
    }

    .card-title {
        font-weight: 600;
        margin-bottom: 5px;
    }
</style>
@endsection

@section('content')
<div class="welcome-banner mb-4 p-4 bg-dark text-white rounded-3" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">Welcome, {{ Auth::user()->name }}</h2>
            <p class="mb-0">Manage your bookings, payments, and account settings from your personal dashboard.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ route('booking.index') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus-circle me-2"></i> Book a Ride
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check dashboard-icon mb-3"></i>
                <h5 class="card-title">Total Bookings</h5>
                <h3 class="card-text">{{ $totalBookings }}</h3>
                <div class="mt-3">
                    <a href="{{ route('client.bookings.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <i class="fas fa-car dashboard-icon mb-3"></i>
                <h5 class="card-title">Active Bookings</h5>
                <h3 class="card-text">{{ $activeBookings }}</h3>
                <div class="mt-3">
                    <a href="{{ route('client.bookings.index', ['status' => 'active']) }}" class="btn btn-sm btn-outline-primary">View Active</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign dashboard-icon mb-3"></i>
                <h5 class="card-title">Total Spent</h5>
                <h3 class="card-text">@currency(){{ number_format($totalSpent, 2) }}</h3>
                <div class="mt-3">
                    <a href="{{ route('client.payments.index') }}" class="btn btn-sm btn-outline-primary">View Payments</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6" data-aos="fade-up" data-aos-delay="400">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="{{ route('booking.index') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle mb-2 d-block" style="font-size: 24px;"></i>
                            New Booking
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ route('client.profile.index') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user mb-2 d-block" style="font-size: 24px;"></i>
                            My Profile
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ route('client.bookings.history') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-history mb-2 d-block" style="font-size: 24px;"></i>
                            Booking History
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ route('client.payments.history') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-credit-card mb-2 d-block" style="font-size: 24px;"></i>
                            Payment History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6" data-aos="fade-up" data-aos-delay="500">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Account Overview</h5>
                <a href="{{ route('client.profile.edit') }}" class="btn btn-sm btn-outline-primary">Edit Profile</a>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-4">
                    @if(Auth::user()->profile_photo)
                        <img src="{{ asset('storage/' . Auth::user()->profile_photo) }}" class="rounded-circle me-3" width="60" height="60" alt="Profile Photo">
                    @else
                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-user" style="font-size: 24px;"></i>
                        </div>
                    @endif
                    <div>
                        <h5 class="mb-1">{{ Auth::user()->name }}</h5>
                        <p class="text-muted mb-0">{{ Auth::user()->email }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="mb-0">{{ Auth::user()->phone ?? 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Member Since</label>
                            <p class="mb-0">{{ Auth::user()->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4" data-aos="fade-up" data-aos-delay="600">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Bookings</h5>
                <div>
                    <a href="{{ route('client.bookings.index') }}" class="btn btn-sm btn-outline-primary me-2">View All</a>
                    <a href="{{ route('booking.index') }}" class="btn btn-sm btn-primary">Book Now</a>
                </div>
            </div>
            <div class="card-body">
                @if ($recentBookings->isEmpty())
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h4>No bookings found</h4>
                        <p class="text-muted mb-4">You haven't made any bookings yet.</p>
                        <a href="{{ route('booking.index') }}" class="btn btn-primary">Book Your First Ride</a>
                    </div>
                @else
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Booking #</th>
                                    <th>Vehicle</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($recentBookings as $booking)
                                    <tr>
                                        <td>{{ $booking->booking_number }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($booking->vehicle->image)
                                                    <img src="{{ asset('storage/' . $booking->vehicle->image) }}" class="me-2 rounded" width="40" height="30" alt="{{ $booking->vehicle->name }}">
                                                @else
                                                    <div class="bg-secondary text-white d-flex align-items-center justify-content-center me-2 rounded" style="width: 40px; height: 30px;">
                                                        <i class="fas fa-car"></i>
                                                    </div>
                                                @endif
                                                {{ $booking->vehicle->name }}
                                            </div>
                                        </td>
                                        <td>{{ $booking->pickup_date->format('M d, Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $booking->status == 'completed' ? 'success' : ($booking->status == 'cancelled' ? 'danger' : 'info') }}">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </td>
                                        <td>@currency(){{ number_format($booking->amount, 2) }}</td>
                                        <td>
                                            <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                            @if(in_array($booking->status, ['pending', 'confirmed', 'assigned', 'in_progress']))
                                                <a href="{{ route('booking.track', $booking->id) }}" class="btn btn-sm btn-outline-success ms-1">
                                                    <i class="fas fa-map-marker-alt me-1"></i> Track
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
