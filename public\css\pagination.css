/* Custom Pagination Styling */
.pagination {
    margin-top: 20px;
    justify-content: center;
}

.pagination .page-item .page-link {
    color: #ee393d;
    border-radius: 5px;
    margin: 0 3px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
    background-color: #ee393d;
    border-color: #ee393d;
    color: #fff;
}

.pagination .page-item .page-link:hover {
    background-color: #f8d7d7;
    border-color: #ee393d;
    color: #ee393d;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .pagination .page-item .page-link {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}
