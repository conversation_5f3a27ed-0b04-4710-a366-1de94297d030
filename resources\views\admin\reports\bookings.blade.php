@extends('layouts.admin')

@section('title', 'Booking Reports')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calendar-alt me-2 text-info"></i> Booking Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-info" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.bookings') }}">
                <div class="row">
                    <div class="col-md-4">
                        <label for="period">Period</label>
                        <select name="period" id="period" class="form-control" onchange="this.form.submit()">
                            <option value="daily" {{ $period == 'daily' ? 'selected' : '' }}>Daily</option>
                            <option value="monthly" {{ $period == 'monthly' ? 'selected' : '' }}>Monthly</option>
                            <option value="yearly" {{ $period == 'yearly' ? 'selected' : '' }}>Yearly</option>
                        </select>
                    </div>
                    @if($period == 'daily' || $period == 'monthly')
                    <div class="col-md-4">
                        <label for="year">Year</label>
                        <select name="year" id="year" class="form-control" onchange="this.form.submit()">
                            @for($y = date('Y'); $y >= date('Y') - 5; $y--)
                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                    @endif
                    @if($period == 'daily')
                    <div class="col-md-4">
                        <label for="month">Month</label>
                        <select name="month" id="month" class="form-control" onchange="this.form.submit()">
                            @for($m = 1; $m <= 12; $m++)
                                <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>
                                    {{ date('F', mktime(0, 0, 0, $m, 1)) }}
                                </option>
                            @endfor
                        </select>
                    </div>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Booking Trend Chart -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Booking Trend</h6>
        </div>
        <div class="card-body">
            <canvas id="bookingChart" width="400" height="100"></canvas>
        </div>
    </div>

    <div class="row">
        <!-- Booking Status Distribution -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Booking Status Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Popular Locations -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Locations</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Top Pickup Locations</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    @foreach($popularPickups as $pickup)
                                    <tr>
                                        <td>{{ Str::limit($pickup->pickup_address, 30) }}</td>
                                        <td><span class="badge badge-primary">{{ $pickup->count }}</span></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div>
                        <h6>Top Dropoff Locations</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    @foreach($popularDropoffs as $dropoff)
                                    <tr>
                                        <td>{{ Str::limit($dropoff->dropoff_address, 30) }}</td>
                                        <td><span class="badge badge-success">{{ $dropoff->count }}</span></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Booking Trend Chart
const bookingCtx = document.getElementById('bookingChart').getContext('2d');
const bookingChart = new Chart(bookingCtx, {
    type: 'bar',
    data: {
        labels: @json($labels),
        datasets: [{
            label: 'Number of Bookings',
            data: @json($bookingData),
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: @json($bookingStatuses->pluck('status')->map(function($status) { return ucfirst(str_replace('_', ' ', $status)); })),
        datasets: [{
            data: @json($bookingStatuses->pluck('count')),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
@endpush
@endsection
