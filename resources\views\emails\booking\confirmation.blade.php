@extends('emails.layouts.master')

@section('title', 'Booking Confirmation')

@section('content')
<h2>Booking Confirmation</h2>

<p>Dear {{ $user->name }},</p>

<p>Thank you for choosing {{ $companyName }}! Your booking has been confirmed and we're excited to serve you.</p>

<div class="booking-details">
    <h3>Booking Details</h3>

    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value">{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>

    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif

    @if($booking->hasViaPoints())
    <div class="detail-row">
        <span class="detail-label">Via Points:</span>
        <span class="detail-value">{{ $booking->getViaPointsCount() }} intermediate stop{{ $booking->getViaPointsCount() > 1 ? 's' : '' }}</span>
    </div>
    @foreach($booking->getFormattedViaPoints() as $viaPoint)
    <div class="detail-row" style="margin-left: 20px;">
        <span class="detail-label">{{ $viaPoint['index'] }}.</span>
        <span class="detail-value">{{ $viaPoint['address'] }}@if(!empty($viaPoint['notes'])) - {{ $viaPoint['notes'] }}@endif</span>
    </div>
    @endforeach
    @endif

    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name ?? 'To be assigned' }}</span>
    </div>

    @if($driver)
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value">{{ $driver->name }}</span>
    </div>
    @endif

    <div class="detail-row">
        <span class="detail-label">Total Amount:</span>
        <span class="detail-value"><strong>{{ $currencySymbol }}{{ number_format($booking->amount, 2) }}</strong></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Payment Status:</span>
        <span class="detail-value">{{ ucfirst($booking->payment_status) }}</span>
    </div>
</div>

@if($booking->notes)
<div style="margin: 20px 0;">
    <strong>Special Instructions:</strong><br>
    {{ $booking->notes }}
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn">View Booking Details</a>
</div>

<h3>What's Next?</h3>
<ul>
    <li>You will receive a reminder email 24 hours before your pickup time</li>
    <li>Our driver will contact you 15-30 minutes before arrival</li>
    <li>Please be ready at your pickup location at the scheduled time</li>
    @if($booking->payment_status === 'pending')
    <li>Complete your payment to confirm your booking</li>
    @endif
</ul>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us at least 24 hours in advance:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or manage your booking online in your account</li>
</ul>

<p>Thank you for choosing {{ $companyName }}. We look forward to providing you with excellent service!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>
@endsection
