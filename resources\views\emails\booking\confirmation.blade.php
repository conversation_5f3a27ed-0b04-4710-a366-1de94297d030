@extends('emails.layouts.master')

@section('title', 'Booking Confirmation - ' . $companyName)

@section('content')
<div style="text-align: center; margin-bottom: 30px;">
    <h2 style="color: #ee393d; margin: 0 0 10px 0;">🎉 Booking Confirmed!</h2>
    <p style="font-size: 18px; color: #666; margin: 0;">Your ride has been successfully booked</p>
</div>

<div style="margin-bottom: 30px;">
    <p>Dear {{ $user->name ?? $booking->client_name ?? 'Valued Customer' }},</p>

    <p>Thank you for choosing {{ $companyName }}! Your booking has been confirmed and we're preparing to provide you with excellent service.</p>
</div>

<div class="booking-details">
    <h3>📋 Booking Details</h3>

    <div class="detail-row">
        <span class="detail-label">Booking Reference:</span>
        <span class="detail-value"><strong style="color: #ee393d; font-size: 16px;">#{{ $booking->booking_number ?? $booking->booking_reference ?? $booking->id }}</strong></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Service Type:</span>
        <span class="detail-value">
            @if(isset($booking->service_type))
                @if($booking->service_type === 'airport_transfer')
                    ✈️ Airport Transfer
                @elseif($booking->service_type === 'hourly')
                    ⏰ Hourly Service
                @elseif($booking->service_type === 'point_to_point')
                    📍 Point to Point
                @else
                    🚗 {{ ucfirst(str_replace('_', ' ', $booking->service_type)) }}
                @endif
            @else
                🚗 Private Hire
            @endif
        </span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value">
            <strong>{{ $booking->pickup_date->format('l, F j, Y') }}</strong><br>
            <span style="color: #ee393d; font-weight: bold;">{{ $booking->pickup_date->format('g:i A') }}</span>
        </span>
    </div>

    <div class="detail-row">
        <span class="detail-label">📍 Pickup Location:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>

    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">🎯 Destination:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif

    @if(isset($booking->passengers))
    <div class="detail-row">
        <span class="detail-label">👥 Passengers:</span>
        <span class="detail-value">{{ $booking->passengers }} {{ $booking->passengers == 1 ? 'person' : 'people' }}</span>
    </div>
    @endif

    @if(isset($booking->luggage))
    <div class="detail-row">
        <span class="detail-label">🧳 Luggage:</span>
        <span class="detail-value">{{ $booking->luggage }} {{ $booking->luggage == 1 ? 'piece' : 'pieces' }}</span>
    </div>
    @endif

    <div class="detail-row">
        <span class="detail-label">🚗 Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name ?? 'Professional vehicle (to be assigned)' }}</span>
    </div>

    <div class="detail-row" style="border-bottom: 2px solid #ee393d; padding-bottom: 15px;">
        <span class="detail-label">💰 Total Amount:</span>
        <span class="detail-value"><strong style="color: #ee393d; font-size: 18px;">{{ $currencySymbol ?? '£' }}{{ number_format($booking->amount ?? $booking->total_amount ?? 0, 2) }}</strong></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">💳 Payment Status:</span>
        <span class="detail-value">
            @if(($booking->payment_status ?? 'pending') === 'paid')
                <span style="color: #28a745; font-weight: bold;">✅ Paid</span>
            @elseif(($booking->payment_status ?? 'pending') === 'pending')
                <span style="color: #ffc107; font-weight: bold;">⏳ Pending</span>
            @else
                <span style="color: #dc3545; font-weight: bold;">❌ Unpaid</span>
            @endif
        </span>
    </div>
</div>

@if($driver ?? false)
<div style="background-color: #e8f5e8; border-left: 4px solid #28a745; padding: 20px; margin: 20px 0; border-radius: 5px;">
    <h4 style="color: #155724; margin-top: 0;">👨‍✈️ Your Assigned Driver</h4>
    <div style="display: flex; align-items: center; gap: 15px;">
        @if($driver->profile_photo ?? false)
        <img src="{{ asset('storage/' . $driver->profile_photo) }}" alt="{{ $driver->name }}" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;">
        @else
        <div style="width: 60px; height: 60px; border-radius: 50%; background-color: #28a745; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;">
            {{ substr($driver->name ?? 'D', 0, 1) }}
        </div>
        @endif
        <div>
            <p style="margin: 5px 0; font-size: 16px;"><strong>{{ $driver->name ?? 'Professional Driver' }}</strong></p>
            <p style="margin: 5px 0;"><strong>📞 Phone:</strong> <a href="tel:{{ $driver->phone ?? '' }}" style="color: #28a745;">{{ $driver->phone ?? 'Will be provided' }}</a></p>
            @if(isset($driver->vehicle))
            <p style="margin: 5px 0;"><strong>🚗 Vehicle:</strong> {{ $driver->vehicle->make ?? '' }} {{ $driver->vehicle->model ?? '' }} ({{ $driver->vehicle->color ?? '' }})</p>
            <p style="margin: 5px 0;"><strong>🔢 Registration:</strong> <strong>{{ $driver->vehicle->registration_number ?? 'TBA' }}</strong></p>
            @endif
        </div>
    </div>
</div>
@else
<div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin: 20px 0; border-radius: 5px;">
    <h4 style="color: #856404; margin-top: 0;">⏳ Driver Assignment</h4>
    <p style="margin: 0;">A professional driver will be assigned to your booking shortly. You'll receive another email with driver details once assigned.</p>
</div>
@endif

@if($booking->notes ?? false)
<div style="background-color: #f8d7da; border-left: 4px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 5px;">
    <h4 style="color: #721c24; margin-top: 0;">📝 Special Requirements</h4>
    <p style="margin: 0; color: #721c24;">{{ $booking->notes }}</p>
</div>
@endif

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn" style="background-color: #ee393d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin: 5px;">
        📋 View Booking Details
    </a>
    @if(($booking->payment_status ?? 'pending') !== 'paid')
    <a href="{{ route('booking.payment', $booking->id) }}" class="btn" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin: 5px;">
        💳 Complete Payment
    </a>
    @endif
</div>

<div style="background-color: #e7f3ff; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 5px;">
    <h4 style="color: #004085; margin-top: 0;">📞 What's Next?</h4>
    <ul style="margin: 0; padding-left: 20px; color: #004085;">
        <li><strong>Reminder email</strong> - You'll receive a reminder 24 hours before pickup</li>
        <li><strong>Driver contact</strong> - Your driver will contact you 15-30 minutes before arrival</li>
        <li><strong>Be ready</strong> - Please be at your pickup location at the scheduled time</li>
        @if(($booking->payment_status ?? 'pending') === 'pending')
        <li><strong>Payment</strong> - Complete your payment to secure your booking</li>
        @endif
        <li><strong>Track booking</strong> - Monitor your ride status in real-time</li>
    </ul>
</div>

<div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin: 20px 0; border-radius: 5px;">
    <h4 style="color: #856404; margin-top: 0;">📞 Need to Make Changes?</h4>
    <p style="margin-bottom: 10px; color: #856404;">If you need to modify or cancel your booking, please contact us at least 24 hours in advance:</p>
    <ul style="margin: 0; padding-left: 20px; color: #856404;">
        <li><strong>Phone:</strong> {{ $companyPhone ?? 'Contact us' }}</li>
        <li><strong>Email:</strong> {{ $companyEmail ?? '<EMAIL>' }}</li>
        <li><strong>Online:</strong> Manage your booking in your account dashboard</li>
    </ul>
</div>

<div style="margin-top: 30px;">
    <p>Thank you for choosing {{ $companyName }}. We look forward to providing you with excellent service!</p>

    <p style="margin-top: 20px;">
        Best regards,<br>
        <strong>The {{ $companyName }} Team</strong>
    </p>
</div>

<div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
    <p style="font-size: 14px; color: #666;">
        <strong>Need immediate assistance?</strong> Contact us at {{ $companyPhone ?? $companyEmail ?? 'our support line' }}
    </p>
</div>
@endsection
