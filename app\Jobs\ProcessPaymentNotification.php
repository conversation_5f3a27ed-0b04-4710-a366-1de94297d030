<?php

namespace App\Jobs;

use App\Models\Booking;
use App\Models\Payment;
use App\Notifications\BookingStatusChanged;
use App\Notifications\PaymentReceived;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessPaymentNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @param  \App\Models\Payment  $payment
     * @return void
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $booking = $this->payment->booking;
            
            if (!$booking) {
                Log::error('Payment notification job: Booking not found for payment ID ' . $this->payment->id);
                return;
            }

            // Update booking payment status
            if ($this->payment->status === 'completed') {
                $booking->update([
                    'payment_status' => 'completed',
                    'payment_processed_at' => now(),
                ]);

                // Add booking history
                $booking->addHistory('payment_completed', [
                    'payment_id' => $this->payment->id,
                    'transaction_id' => $this->payment->transaction_id,
                    'amount' => $this->payment->amount,
                    'payment_method' => $this->payment->payment_method,
                ]);

                // Send notifications
                $this->sendPaymentNotifications($booking);

                Log::info('Payment processed successfully for booking ' . $booking->booking_number);

            } elseif ($this->payment->status === 'failed') {
                $booking->addHistory('payment_failed', [
                    'payment_id' => $this->payment->id,
                    'transaction_id' => $this->payment->transaction_id,
                    'reason' => $this->payment->payment_details['failure_reason'] ?? 'Unknown error',
                ]);

                Log::warning('Payment failed for booking ' . $booking->booking_number);
            }

        } catch (\Exception $e) {
            Log::error('Error processing payment notification: ' . $e->getMessage(), [
                'payment_id' => $this->payment->id,
                'exception' => $e,
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Send payment-related notifications.
     *
     * @param  \App\Models\Booking  $booking
     * @return void
     */
    private function sendPaymentNotifications(Booking $booking)
    {
        try {
            // Notify the client about payment confirmation
            if ($booking->user) {
                $booking->user->notify(new PaymentReceived($this->payment));
            }

            // Notify admin about the payment
            $adminUsers = \App\Models\User::where('role', 'admin')->get();
            foreach ($adminUsers as $admin) {
                $admin->notify(new PaymentReceived($this->payment));
            }

            // If booking is now confirmed, notify about status change
            if ($booking->status === 'pending') {
                $booking->update(['status' => 'confirmed']);
                
                if ($booking->user) {
                    $booking->user->notify(new BookingStatusChanged($booking, 'pending'));
                }
            }

        } catch (\Exception $e) {
            Log::error('Error sending payment notifications: ' . $e->getMessage(), [
                'booking_id' => $booking->id,
                'payment_id' => $this->payment->id,
            ]);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('ProcessPaymentNotification job failed', [
            'payment_id' => $this->payment->id,
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
