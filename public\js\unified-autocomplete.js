/**
 * Unified Autocomplete Handler
 * Centralizes all autocomplete functionality across the application
 */

class UnifiedAutocomplete {
    constructor() {
        this.instances = new Map();
        this.settings = this.loadSettings();
        this.sessionToken = null;
        this.isInitialized = false;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.createInstance = this.createInstance.bind(this);
        this.handlePlaceChanged = this.handlePlaceChanged.bind(this);
    }

    /**
     * Load settings from various sources
     */
    loadSettings() {
        // Priority: window.autocompleteSettings > meta tags > defaults
        let settings = {
            enabled: true,
            restrictCountry: false,
            country: 'GB',
            types: 'geocode',
            biasRadius: 100,
            useStrictBounds: false,
            fields: 'address_components,geometry,name,formatted_address,place_id',
            sessionToken: true,
            language: 'en',
            region: 'GB'
        };

        // Load from window object if available
        if (window.autocompleteSettings) {
            Object.assign(settings, window.autocompleteSettings);
        } else {
            // Load from meta tags
            const metaSettings = this.loadFromMetaTags();
            Object.assign(settings, metaSettings);
        }

        return settings;
    }

    /**
     * Load settings from meta tags
     */
    loadFromMetaTags() {
        const getMetaContent = (name) => {
            const meta = document.querySelector(`meta[name="${name}"]`);
            return meta ? meta.content : null;
        };

        return {
            enabled: getMetaContent('autocomplete-enabled') === 'true',
            restrictCountry: getMetaContent('autocomplete-restrict-country') === 'true',
            country: getMetaContent('autocomplete-country') || 'GB',
            types: getMetaContent('autocomplete-types') || 'geocode',
            biasRadius: parseInt(getMetaContent('autocomplete-bias-radius')) || 100,
            useStrictBounds: getMetaContent('autocomplete-use-strict-bounds') === 'true',
            fields: getMetaContent('autocomplete-fields') || 'address_components,geometry,name,formatted_address,place_id',
            language: getMetaContent('autocomplete-language') || 'en',
            region: getMetaContent('autocomplete-region') || 'GB'
        };
    }

    /**
     * Initialize autocomplete
     */
    init() {
        if (!this.settings.enabled) {
            console.log('Autocomplete is disabled in settings');
            return;
        }

        if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
            console.error('Google Maps API not loaded. Autocomplete will not work.');
            this.showApiError();
            return;
        }

        // Create session token if enabled
        if (this.settings.sessionToken) {
            this.sessionToken = new google.maps.places.AutocompleteSessionToken();
        }

        // Find all address input fields
        this.findAndInitializeInputs();
        
        this.isInitialized = true;
        console.log('Unified Autocomplete initialized successfully');
    }

    /**
     * Find and initialize all address input fields
     */
    findAndInitializeInputs() {
        // Standard selectors for address inputs
        const selectors = [
            '.address-autocomplete',
            'input[id*="address"]',
            'input[id*="pickup"]',
            'input[id*="dropoff"]',
            'input[name*="address"]',
            'input[placeholder*="address" i]',
            'input[placeholder*="location" i]'
        ];

        const inputs = document.querySelectorAll(selectors.join(', '));
        
        inputs.forEach(input => {
            if (!this.instances.has(input.id || input.name)) {
                this.createInstance(input);
            }
        });

        console.log(`Initialized autocomplete for ${inputs.length} input fields`);
    }

    /**
     * Create autocomplete instance for an input
     */
    createInstance(input) {
        try {
            const options = this.buildOptions(input);
            const autocomplete = new google.maps.places.Autocomplete(input, options);

            // Set bounds
            this.setBounds(autocomplete, input);

            // Add event listeners
            autocomplete.addListener('place_changed', () => {
                this.handlePlaceChanged(autocomplete, input);
            });

            // Store instance
            const key = input.id || input.name || `input_${Date.now()}`;
            this.instances.set(key, {
                autocomplete,
                input,
                options
            });

            // Add CSS class for styling
            input.classList.add('autocomplete-initialized');

            console.log(`Autocomplete created for input: ${key}`);

        } catch (error) {
            console.error('Error creating autocomplete instance:', error);
        }
    }

    /**
     * Build options for autocomplete
     */
    buildOptions(input) {
        const options = {
            fields: this.settings.fields.split(',').map(f => f.trim()),
            types: [this.settings.types]
        };

        // Add country restriction if enabled
        if (this.settings.restrictCountry && this.settings.country) {
            options.componentRestrictions = { country: this.settings.country };
        }

        // Add session token if enabled
        if (this.sessionToken) {
            options.sessionToken = this.sessionToken;
        }

        // Check for input-specific overrides
        const inputCountry = input.dataset.country;
        const inputTypes = input.dataset.types;

        if (inputCountry) {
            options.componentRestrictions = { country: inputCountry };
        }

        if (inputTypes) {
            options.types = inputTypes.split(',').map(t => t.trim());
        }

        return options;
    }

    /**
     * Set bounds for autocomplete
     */
    setBounds(autocomplete, input) {
        // Check for input-specific coordinates
        const inputLat = parseFloat(input.dataset.lat);
        const inputLng = parseFloat(input.dataset.lng);

        if (!isNaN(inputLat) && !isNaN(inputLng)) {
            this.setCircularBounds(autocomplete, inputLat, inputLng);
            return;
        }

        // Try to get user's location
        if (navigator.geolocation && this.settings.biasRadius > 0) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.setCircularBounds(
                        autocomplete,
                        position.coords.latitude,
                        position.coords.longitude
                    );
                },
                () => {
                    this.setCountryBounds(autocomplete);
                }
            );
        } else {
            this.setCountryBounds(autocomplete);
        }
    }

    /**
     * Set circular bounds around a point
     */
    setCircularBounds(autocomplete, lat, lng) {
        const radius = Math.max(this.settings.biasRadius * 1000, 50000); // Minimum 50km
        
        const circle = new google.maps.Circle({
            center: { lat, lng },
            radius
        });

        const bounds = circle.getBounds();
        autocomplete.setBounds(bounds);

        // Only use strict bounds for small radius
        if (this.settings.useStrictBounds && this.settings.biasRadius < 25) {
            autocomplete.setOptions({ strictBounds: true });
        }
    }

    /**
     * Set country-specific bounds
     */
    setCountryBounds(autocomplete) {
        const countryBounds = {
            'GB': {
                southwest: { lat: 49.9, lng: -8.2 },
                northeast: { lat: 60.9, lng: 1.8 }
            },
            'US': {
                southwest: { lat: 24.396308, lng: -125.0 },
                northeast: { lat: 49.384358, lng: -66.93457 }
            }
        };

        const bounds = countryBounds[this.settings.country];
        if (bounds) {
            const googleBounds = new google.maps.LatLngBounds(
                new google.maps.LatLng(bounds.southwest.lat, bounds.southwest.lng),
                new google.maps.LatLng(bounds.northeast.lat, bounds.northeast.lng)
            );
            autocomplete.setBounds(googleBounds);
        }
    }

    /**
     * Handle place selection
     */
    handlePlaceChanged(autocomplete, input) {
        const place = autocomplete.getPlace();
        
        if (!place || !place.geometry) {
            console.warn('No valid place selected');
            return;
        }

        // Update input value
        input.value = place.formatted_address || place.name || '';

        // Store place data
        this.storePlaceData(input, place);

        // Trigger custom events
        this.triggerEvents(input, place);

        // Update related fields
        this.updateRelatedFields(input, place);
    }

    /**
     * Store place data in input attributes
     */
    storePlaceData(input, place) {
        if (place.geometry && place.geometry.location) {
            input.dataset.lat = place.geometry.location.lat();
            input.dataset.lng = place.geometry.location.lng();
        }

        if (place.place_id) {
            input.dataset.placeId = place.place_id;
        }

        if (place.formatted_address) {
            input.dataset.formattedAddress = place.formatted_address;
        }

        // Store address components
        if (place.address_components) {
            const components = {};
            place.address_components.forEach(component => {
                component.types.forEach(type => {
                    components[type] = component.long_name;
                });
            });
            input.dataset.addressComponents = JSON.stringify(components);
        }
    }

    /**
     * Trigger custom events
     */
    triggerEvents(input, place) {
        // Standard change event
        input.dispatchEvent(new Event('change', { bubbles: true }));

        // Custom autocomplete event
        input.dispatchEvent(new CustomEvent('autocomplete:place_changed', {
            detail: { place, input },
            bubbles: true
        }));

        // Legacy support
        if (window.updateMapAndFare && typeof window.updateMapAndFare === 'function') {
            setTimeout(window.updateMapAndFare, 500);
        }
    }

    /**
     * Update related hidden fields
     */
    updateRelatedFields(input, place) {
        const inputId = input.id;
        
        // Update latitude field
        const latField = document.getElementById(inputId.replace('_address', '_lat'));
        if (latField && place.geometry) {
            latField.value = place.geometry.location.lat();
        }

        // Update longitude field
        const lngField = document.getElementById(inputId.replace('_address', '_lng'));
        if (lngField && place.geometry) {
            lngField.value = place.geometry.location.lng();
        }

        // Update place ID field
        const placeIdField = document.getElementById(inputId.replace('_address', '_place_id'));
        if (placeIdField && place.place_id) {
            placeIdField.value = place.place_id;
        }
    }

    /**
     * Show API error message
     */
    showApiError() {
        const inputs = document.querySelectorAll('input[id*="address"], .address-autocomplete');
        inputs.forEach(input => {
            input.placeholder = 'Google Maps API key required for autocomplete';
            input.style.borderColor = '#dc3545';
            input.style.backgroundColor = '#fff5f5';
        });
    }

    /**
     * Add new input to autocomplete
     */
    addInput(input) {
        if (this.isInitialized && this.settings.enabled) {
            this.createInstance(input);
        }
    }

    /**
     * Remove input from autocomplete
     */
    removeInput(inputId) {
        if (this.instances.has(inputId)) {
            this.instances.delete(inputId);
        }
    }

    /**
     * Update settings and reinitialize
     */
    updateSettings(newSettings) {
        Object.assign(this.settings, newSettings);
        
        // Clear existing instances
        this.instances.clear();
        
        // Reinitialize
        this.init();
    }

    /**
     * Get instance by input ID
     */
    getInstance(inputId) {
        return this.instances.get(inputId);
    }

    /**
     * Get all instances
     */
    getAllInstances() {
        return Array.from(this.instances.values());
    }
}

// Create global instance
window.unifiedAutocomplete = new UnifiedAutocomplete();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.unifiedAutocomplete.init();
    });
} else {
    window.unifiedAutocomplete.init();
}

// Initialize when Google Maps API loads
window.addEventListener('google-maps-loaded', () => {
    window.unifiedAutocomplete.init();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAutocomplete;
}
