<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'model',
        'category',
        'seats',
        'luggage_capacity',
        'transmission',
        'image',
        'price_per_km',
        'price_per_hour',
        'base_fare',
        'tax_rate',
        'airport_surcharge',
        'booking_fee',
        'waiting_fee_per_minute',
        'cancellation_fee',
        'night_surcharge',
        'weekend_surcharge',
        'holiday_surcharge',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price_per_km' => 'decimal:2',
        'price_per_hour' => 'decimal:2',
        'base_fare' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'airport_surcharge' => 'decimal:2',
        'booking_fee' => 'decimal:2',
        'waiting_fee_per_minute' => 'decimal:2',
        'cancellation_fee' => 'decimal:2',
        'night_surcharge' => 'decimal:2',
        'weekend_surcharge' => 'decimal:2',
        'holiday_surcharge' => 'decimal:2',
        'seats' => 'integer',
        'luggage_capacity' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get bookings for this vehicle
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }
}
