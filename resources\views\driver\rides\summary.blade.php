@extends('layouts.driver')

@section('title', 'Ride Summary')

@section('styles')
<style>
    .summary-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .summary-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .summary-info {
        padding: 15px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        margin-bottom: 15px;
    }

    .summary-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .summary-value {
        color: #6c757d;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .map-container {
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        height: 100%;
        width: 2px;
        background-color: #e9ecef;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }

    .timeline-item:last-child {
        padding-bottom: 0;
    }

    .timeline-dot {
        position: absolute;
        left: -30px;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #ee393d;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 0.7rem;
    }

    .timeline-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
    }

    .timeline-time {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .timeline-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-details {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .fare-breakdown {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
    }

    .fare-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .fare-total {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
        font-weight: 600;
        font-size: 1.1rem;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Ride Summary</h2>
                <a href="{{ route('driver.rides.my-rides', ['status' => 'completed']) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Completed Rides
                </a>
            </div>

            <div class="card summary-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Booking #{{ $ride->booking_number }}</h5>
                    <span class="status-badge status-completed">Completed</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="summary-info">
                                <div class="summary-label">Client</div>
                                <div class="summary-value">Client #{{ substr($ride->user->id, 0, 5) }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Pickup</div>
                                <div class="summary-value">{{ $ride->pickup_address }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Dropoff</div>
                                <div class="summary-value">{{ $ride->dropoff_address }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Vehicle</div>
                                <div class="summary-value">{{ $ride->vehicle->name }} ({{ $ride->vehicle->model }})</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Booking Type</div>
                                <div class="summary-value">{{ ucfirst($ride->booking_type) }}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="summary-info">
                                <div class="summary-label">Pickup Date</div>
                                <div class="summary-value">{{ $ride->pickup_date->format('M d, Y h:i A') }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Started At</div>
                                <div class="summary-value">{{ $ride->started_at ? $ride->started_at->format('M d, Y h:i A') : 'N/A' }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Completed At</div>
                                <div class="summary-value">{{ $ride->completed_at ? $ride->completed_at->format('M d, Y h:i A') : 'N/A' }}</div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Duration</div>
                                <div class="summary-value">
                                    @if($duration)
                                        @if($duration < 60)
                                            {{ $duration }} minutes
                                        @else
                                            {{ floor($duration / 60) }} {{ floor($duration / 60) == 1 ? 'hour' : 'hours' }}
                                            {{ $duration % 60 > 0 ? ($duration % 60) . ' minutes' : '' }}
                                        @endif
                                    @else
                                        <span class="text-muted">Not available</span>
                                    @endif
                                </div>
                            </div>

                            <div class="summary-info">
                                <div class="summary-label">Distance</div>
                                <div class="summary-value">
                                    @if($ride->distance && $ride->distance > 0)
                                        {{ number_format($ride->distance, 1) }} km
                                    @else
                                        <span class="text-muted">Not available</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">Fare Breakdown</h5>
                            <div class="fare-breakdown">
                                @php
                                    // Calculate fare components based on the actual fare calculation logic
                                    $vehicleType = $ride->vehicle->type ?? 'sedan';
                                    $bookingType = $ride->booking_type ?? 'one_way';

                                    // Get vehicle multiplier
                                    $vehicleMultiplier = 1.0;
                                    switch (strtolower($vehicleType)) {
                                        case 'economy': $vehicleMultiplier = 0.8; break;
                                        case 'sedan': $vehicleMultiplier = 1.0; break;
                                        case 'suv': $vehicleMultiplier = 1.3; break;
                                        case 'luxury': $vehicleMultiplier = 1.8; break;
                                        case 'van': $vehicleMultiplier = 1.5; break;
                                        case 'limo': $vehicleMultiplier = 2.5; break;
                                    }

                                    // Base fare calculation
                                    $baseFare = 5.0 * $vehicleMultiplier;

                                    // Distance fare calculation
                                    $distanceInKm = $ride->distance_value ? ($ride->distance_value / 1000) : ($ride->distance ?? 0);
                                    $ratePerKm = $ride->vehicle->price_per_km ?? 2.5;
                                    $adjustedRatePerKm = $ratePerKm * $vehicleMultiplier;

                                    // Calculate distance fare based on booking type
                                    $distanceFare = 0;
                                    $airportSurcharge = 0;

                                    switch ($bookingType) {
                                        case 'return':
                                            $distanceFare = ($distanceInKm * $adjustedRatePerKm) * 2;
                                            break;
                                        case 'hourly':
                                            $hourlyRate = 30 * $vehicleMultiplier;
                                            $durationHours = $ride->duration_hours ?? 1;
                                            $distanceFare = $hourlyRate * $durationHours;
                                            break;
                                        case 'airport':
                                            $airportSurcharge = 15.00;
                                            $distanceFare = ($distanceInKm * $adjustedRatePerKm);
                                            break;
                                        default: // one_way
                                            $distanceFare = $distanceInKm * $adjustedRatePerKm;
                                            break;
                                    }

                                    // Booking fee
                                    $bookingFee = 2.50;

                                    // Calculate total fare components
                                    $calculatedTotalFare = $baseFare + $distanceFare + $airportSurcharge + $bookingFee;

                                    // Additional charges and discounts
                                    $additionalCharges = $ride->additional_charges ?? 0;
                                    $waitingCharge = $ride->waiting_charge ?? 0;
                                    $discount = $ride->discount ?? 0;

                                    // Final total with adjustments
                                    $finalTotal = $calculatedTotalFare + $additionalCharges + $waitingCharge - $discount;

                                    // If there's a significant difference between calculated fare and stored amount,
                                    // use the stored amount and adjust the distance fare to make it balance
                                    if (abs($finalTotal - $ride->amount) > 1) {
                                        $adjustment = $ride->amount - ($baseFare + $airportSurcharge + $bookingFee + $additionalCharges + $waitingCharge - $discount);
                                        $distanceFare = $adjustment;
                                    }
                                @endphp

                                <div class="fare-item">
                                    <div>Base Fare</div>
                                    <div>@currency(){{ number_format($baseFare, 2) }}</div>
                                </div>

                                <div class="fare-item">
                                    <div>Distance Charge ({{ number_format($distanceInKm, 1) }} km)</div>
                                    <div>@currency(){{ number_format($distanceFare, 2) }}</div>
                                </div>

                                @if($bookingType === 'airport' && $airportSurcharge > 0)
                                <div class="fare-item">
                                    <div>Airport Surcharge</div>
                                    <div>@currency(){{ number_format($airportSurcharge, 2) }}</div>
                                </div>
                                @endif

                                <div class="fare-item">
                                    <div>Booking Fee</div>
                                    <div>@currency(){{ number_format($bookingFee, 2) }}</div>
                                </div>

                                @if($ride->waiting_time > 0 && $waitingCharge > 0)
                                <div class="fare-item">
                                    <div>Waiting Time</div>
                                    <div>@currency(){{ number_format($waitingCharge, 2) }}</div>
                                </div>
                                @endif

                                @if($additionalCharges > 0)
                                <div class="fare-item">
                                    <div>Additional Charges</div>
                                    <div>@currency(){{ number_format($additionalCharges, 2) }}</div>
                                </div>
                                @endif

                                @if($discount > 0)
                                <div class="fare-item text-success">
                                    <div>Discount</div>
                                    <div>-@currency(){{ number_format($discount, 2) }}</div>
                                </div>
                                @endif

                                <div class="fare-total">
                                    <div>Total Fare</div>
                                    <div>@currency(){{ number_format($ride->amount, 2) }}</div>
                                </div>
                            </div>

                            <h5 class="mb-3 mt-4">Your Earnings</h5>
                            <div class="fare-breakdown">
                                <div class="fare-item">
                                    <div>Trip Fare</div>
                                    <div>@currency(){{ number_format($ride->amount, 2) }}</div>
                                </div>
                                <div class="fare-item text-danger">
                                    <div>Platform Fee ({{ config('app.platform_fee_percentage', 20) }}%)</div>
                                    <div>-@currency(){{ number_format($ride->amount * config('app.platform_fee_percentage', 20) / 100, 2) }}</div>
                                </div>
                                @if($ride->tip_amount > 0)
                                <div class="fare-item text-success">
                                    <div>Customer Tip</div>
                                    <div>@currency(){{ number_format($ride->tip_amount, 2) }}</div>
                                </div>
                                @endif
                                <div class="fare-total">
                                    <div>Net Earnings</div>
                                    <div>@currency(){{ number_format($ride->amount * (1 - config('app.platform_fee_percentage', 20) / 100) + ($ride->tip_amount ?? 0), 2) }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3">Route Map</h5>
                            <div class="map-container">
                                <iframe
                                    width="100%"
                                    height="100%"
                                    frameborder="0"
                                    style="border:0"
                                    src="https://www.google.com/maps/embed/v1/directions?key={{ env('GOOGLE_MAPS_API_KEY') }}&origin={{ urlencode($ride->pickup_address) }}&destination={{ urlencode($ride->dropoff_address) }}"
                                    allowfullscreen
                                ></iframe>
                            </div>

                            <h5 class="mb-3 mt-4">Payment Information</h5>
                            <div class="summary-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="summary-label">Payment Method</div>
                                        <div class="summary-value">
                                            @if($ride->payment && $ride->payment->payment_method)
                                                @if($ride->payment->payment_method == 'card')
                                                    <i class="fas fa-credit-card me-1"></i> Credit/Debit Card
                                                @elseif($ride->payment->payment_method == 'paypal')
                                                    <i class="fab fa-paypal me-1"></i> PayPal
                                                @elseif($ride->payment->payment_method == 'cash')
                                                    <i class="fas fa-money-bill-wave me-1"></i> Cash
                                                @else
                                                    {{ ucfirst($ride->payment->payment_method) }}
                                                @endif
                                            @else
                                                Not specified
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="summary-label">Payment Status</div>
                                        <div class="summary-value">
                                            @if($ride->payment && $ride->payment->status)
                                                @if($ride->payment->status == 'completed')
                                                    <span class="badge bg-success">Paid</span>
                                                @elseif($ride->payment->status == 'pending')
                                                    <span class="badge bg-warning">Pending</span>
                                                @elseif($ride->payment->status == 'failed')
                                                    <span class="badge bg-danger">Failed</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ ucfirst($ride->payment->status) }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">Unknown</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @if($ride->payment && $ride->payment->transaction_id)
                                <div class="mt-2">
                                    <div class="summary-label">Transaction ID</div>
                                    <div class="summary-value">{{ $ride->payment->transaction_id }}</div>
                                </div>
                                @endif
                                @if($ride->payment && $ride->payment->paid_at)
                                <div class="mt-2">
                                    <div class="summary-label">Payment Date</div>
                                    <div class="summary-value">{{ $ride->payment->paid_at->format('M d, Y h:i A') }}</div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="mb-3">Ride Timeline</h5>
                            <div class="timeline">
                                @foreach($ride->history()->orderBy('created_at', 'asc')->get() as $history)
                                    <div class="timeline-item">
                                        <div class="timeline-dot">
                                            <i class="fas fa-circle fa-xs"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-time">{{ $history->created_at->format('M d, Y h:i A') }}</div>
                                            <div class="timeline-title">
                                                @if($history->action == 'status_changed')
                                                    Status Changed: {{ ucfirst(str_replace('_', ' ', $history->status_after)) }}
                                                @elseif($history->action == 'driver_assigned')
                                                    Driver Assigned
                                                @elseif($history->action == 'driver_cancelled')
                                                    Driver Cancelled
                                                @elseif($history->action == 'note_added')
                                                    Note Added
                                                @else
                                                    {{ ucfirst(str_replace('_', ' ', $history->action)) }}
                                                @endif
                                            </div>
                                            <div class="timeline-details">
                                                @if($history->action == 'status_changed' && isset($history->details['reason']))
                                                    {{ $history->details['reason'] }}
                                                @elseif($history->action == 'driver_cancelled' && isset($history->details['reason']))
                                                    Reason: {{ $history->details['reason'] }}
                                                @elseif($history->action == 'note_added' && isset($history->details['note']))
                                                    {{ $history->details['note'] }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    @if($ride->notes)
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5 class="mb-3">Notes</h5>
                                <div class="summary-info">
                                    {{ $ride->notes }}
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">Customer Feedback</h5>
                            <div class="summary-info">
                                @if($ride->rating)
                                    <div class="mb-2">
                                        <div class="summary-label">Rating</div>
                                        <div class="summary-value">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $ride->rating)
                                                    <i class="fas fa-star text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-muted"></i>
                                                @endif
                                            @endfor
                                            <span class="ms-2">{{ $ride->rating }}/5</span>
                                        </div>
                                    </div>
                                    @if($ride->review)
                                        <div class="mt-3">
                                            <div class="summary-label">Review</div>
                                            <div class="summary-value">
                                                <i class="fas fa-quote-left text-muted me-1"></i>
                                                {{ $ride->review }}
                                                <i class="fas fa-quote-right text-muted ms-1"></i>
                                            </div>
                                        </div>
                                    @else
                                        <div class="text-muted">No written review provided.</div>
                                    @endif
                                @else
                                    <div class="text-center py-4">
                                        <i class="far fa-star fa-3x text-muted mb-3"></i>
                                        <p>No rating or feedback received yet.</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3">Additional Details</h5>
                            <div class="summary-info">
                                @if($ride->passengers)
                                    <div class="mb-2">
                                        <div class="summary-label">Passengers</div>
                                        <div class="summary-value">{{ $ride->passengers }}</div>
                                    </div>
                                @endif

                                @if($ride->luggage)
                                    <div class="mb-2">
                                        <div class="summary-label">Luggage</div>
                                        <div class="summary-value">{{ $ride->luggage }}</div>
                                    </div>
                                @endif

                                @if($ride->special_instructions)
                                    <div class="mb-2">
                                        <div class="summary-label">Special Instructions</div>
                                        <div class="summary-value">{{ $ride->special_instructions }}</div>
                                    </div>
                                @endif

                                @if($ride->flight_number)
                                    <div class="mb-2">
                                        <div class="summary-label">Flight Number</div>
                                        <div class="summary-value">{{ $ride->flight_number }}</div>
                                    </div>
                                @endif

                                @if($ride->booking_type == 'airport')
                                    <div class="mb-2">
                                        <div class="summary-label">Airport</div>
                                        <div class="summary-value">{{ $ride->airport_name }}</div>
                                    </div>

                                    <div class="mb-2">
                                        <div class="summary-label">Terminal</div>
                                        <div class="summary-value">{{ $ride->terminal ?? 'Not specified' }}</div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-center">
                                <a href="{{ route('driver.rides.download-receipt', $ride->id) }}" class="btn btn-primary mx-2">
                                    <i class="fas fa-download me-2"></i> Download Receipt
                                </a>
                                <button type="button" class="btn btn-outline-secondary mx-2" data-bs-toggle="modal" data-bs-target="#reportIssueModal">
                                    <i class="fas fa-flag me-2"></i> Report an Issue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Issue Modal -->
<div class="modal fade" id="reportIssueModal" tabindex="-1" aria-labelledby="reportIssueModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportIssueModalLabel">Report an Issue</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('driver.rides.report-issue', $ride->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="issue_type" class="form-label">Issue Type</label>
                        <select class="form-select" id="issue_type" name="issue_type" required>
                            <option value="">Select Issue Type</option>
                            <option value="payment">Payment Issue</option>
                            <option value="fare">Fare Calculation Issue</option>
                            <option value="customer">Customer Behavior Issue</option>
                            <option value="route">Route/Navigation Issue</option>
                            <option value="other">Other Issue</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="issue_description" class="form-label">Description</label>
                        <textarea class="form-control" id="issue_description" name="issue_description" rows="4" required placeholder="Please provide details about the issue..."></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="urgent" name="urgent">
                        <label class="form-check-label" for="urgent">
                            Mark as urgent
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Report</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here

        // Example: Print receipt
        document.getElementById('printReceipt')?.addEventListener('click', function() {
            window.print();
        });
    });
</script>
@endsection
