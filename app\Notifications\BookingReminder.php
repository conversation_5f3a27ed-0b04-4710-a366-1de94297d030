<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingR<PERSON>inder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;
    protected $recipientType;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Booking  $booking
     * @param  string  $recipientType  'client' or 'driver'
     * @return void
     */
    public function __construct(Booking $booking, $recipientType = 'client')
    {
        $this->booking = $booking;
        $this->recipientType = $recipientType;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($this->recipientType === 'driver') {
            return $this->driverReminderMail($notifiable);
        }

        return $this->clientReminderMail($notifiable);
    }

    /**
     * Get the client reminder mail.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    private function clientReminderMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Booking Reminder - Tomorrow')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a friendly reminder about your upcoming booking tomorrow.')
            ->line('Booking Number: ' . $this->booking->booking_number)
            ->line('Pickup Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->line('Pickup Address: ' . $this->booking->pickup_address)
            ->line('Dropoff Address: ' . $this->booking->dropoff_address)
            ->line('Vehicle: ' . $this->booking->vehicle->name)
            ->when($this->booking->driver, function ($mail) {
                return $mail->line('Driver: ' . $this->booking->driver->name)
                           ->line('Driver Phone: ' . $this->booking->driver->phone);
            })
            ->action('View Booking Details', url('/client/bookings/' . $this->booking->id))
            ->line('Please be ready at the pickup location 5 minutes before the scheduled time.')
            ->line('Thank you for choosing our service!');
    }

    /**
     * Get the driver reminder mail.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    private function driverReminderMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Ride Assignment Reminder - Tomorrow')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a reminder about your assigned ride tomorrow.')
            ->line('Booking Number: ' . $this->booking->booking_number)
            ->line('Pickup Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->line('Pickup Address: ' . $this->booking->pickup_address)
            ->line('Dropoff Address: ' . $this->booking->dropoff_address)
            ->line('Client: ' . $this->booking->user->name)
            ->line('Client Phone: ' . $this->booking->user->phone)
            ->line('Vehicle: ' . $this->booking->vehicle->name)
            ->line('Estimated Fare: ' . \App\Helpers\SettingsHelper::formatPrice($this->booking->amount))
            ->action('View Ride Details', url('/driver/bookings/' . $this->booking->id))
            ->line('Please arrive at the pickup location 5 minutes early.')
            ->line('Safe driving!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $message = $this->recipientType === 'driver' 
            ? 'Reminder: You have a ride tomorrow at ' . $this->booking->pickup_date->format('h:i A')
            : 'Reminder: Your booking is scheduled for tomorrow at ' . $this->booking->pickup_date->format('h:i A');

        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'pickup_date' => $this->booking->pickup_date->format('M d, Y h:i A'),
            'pickup_address' => $this->booking->pickup_address,
            'recipient_type' => $this->recipientType,
            'message' => $message,
            'type' => 'booking_reminder'
        ];
    }
}
