@extends('emails.layouts.master')

@section('title', 'Driver Assigned to Your Booking')

@section('content')
<h2>Driver Assigned to Your Booking</h2>

<p>Dear {{ $user->name }},</p>

<p>Great news! We've assigned a professional driver to your upcoming ride.</p>

<div class="booking-details">
    <h3>Your Ride Details</h3>

    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong>{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</strong></span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>

    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif

    @if($booking->hasViaPoints())
    <div class="detail-row">
        <span class="detail-label">Via Points:</span>
        <span class="detail-value">{{ $booking->getViaPointsCount() }} intermediate stop{{ $booking->getViaPointsCount() > 1 ? 's' : '' }}</span>
    </div>
    @foreach($booking->getFormattedViaPoints() as $viaPoint)
    <div class="detail-row" style="margin-left: 20px;">
        <span class="detail-label">{{ $viaPoint['index'] }}.</span>
        <span class="detail-value">{{ $viaPoint['address'] }}@if(!empty($viaPoint['notes'])) - {{ $viaPoint['notes'] }}@endif</span>
    </div>
    @endforeach
    @endif

    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name }}</span>
    </div>
</div>

<div class="booking-details">
    <h3>Your Driver Information</h3>

    <div class="detail-row">
        <span class="detail-label">Driver Name:</span>
        <span class="detail-value"><strong>{{ $driver->name }}</strong></span>
    </div>

    @if($driver->phone)
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value">{{ $driver->phone }}</span>
    </div>
    @endif

    @if($driver->license_number)
    <div class="detail-row">
        <span class="detail-label">License Number:</span>
        <span class="detail-value">{{ $driver->license_number }}</span>
    </div>
    @endif

    @if($vehicle->license_plate)
    <div class="detail-row">
        <span class="detail-label">Vehicle License Plate:</span>
        <span class="detail-value">{{ $vehicle->license_plate }}</span>
    </div>
    @endif

    @if($vehicle->color)
    <div class="detail-row">
        <span class="detail-label">Vehicle Color:</span>
        <span class="detail-value">{{ $vehicle->color }}</span>
    </div>
    @endif
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">✓ Your Ride is Confirmed!</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>Your driver has been notified and will contact you before pickup</li>
        <li>Please be ready at your pickup location 5 minutes early</li>
        <li>Your driver will arrive in a {{ $vehicle->color ?? '' }} {{ $vehicle->name }}</li>
        <li>Keep your booking confirmation number handy: <strong>#{{ $booking->booking_number }}</strong></li>
    </ul>
</div>

@if($booking->notes)
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <strong style="color: #856404;">Special Instructions:</strong><br>
    <span style="color: #856404;">{{ $booking->notes }}</span>
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn">Track Your Ride</a>
</div>

<h3>What to Expect:</h3>
<ul>
    <li><strong>15-30 minutes before pickup:</strong> Your driver will contact you</li>
    <li><strong>At pickup time:</strong> Look for the {{ $vehicle->color ?? '' }} {{ $vehicle->name }}</li>
    <li><strong>Identification:</strong> Your driver will confirm your name and destination</li>
    <li><strong>During the ride:</strong> Enjoy professional, safe transportation</li>
    <li><strong>After the ride:</strong> You'll receive a receipt and can rate your experience</li>
</ul>

<h3>Driver Contact Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Driver:</strong> {{ $driver->name }}</p>
    @if($driver->phone)
    <p><strong>Phone:</strong> <a href="tel:{{ $driver->phone }}">{{ $driver->phone }}</a></p>
    @endif
    <p><strong>Vehicle:</strong> {{ $vehicle->color ?? '' }} {{ $vehicle->name }}</p>
    @if($vehicle->license_plate)
    <p><strong>License Plate:</strong> {{ $vehicle->license_plate }}</p>
    @endif
</div>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us as soon as possible:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or manage your booking online in your account</li>
</ul>

<p>We're excited to provide you with excellent service!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>
@endsection
