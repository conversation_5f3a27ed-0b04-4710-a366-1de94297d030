<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BookingFlowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $client;
    protected $driver;
    protected $admin;
    protected $vehicle;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->client = User::factory()->client()->create();
        $this->driver = User::factory()->driver()->create();
        $this->admin = User::factory()->admin()->create();

        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create();
    }

    /** @test */
    public function client_can_create_booking()
    {
        $this->actingAs($this->client);

        $bookingData = [
            'booking_type' => 'one_way',
            'vehicle_id' => $this->vehicle->id,
            'pickup_address' => '123 Main St, City',
            'pickup_lat' => 40.7128,
            'pickup_lng' => -74.0060,
            'dropoff_address' => '456 Oak Ave, City',
            'dropoff_lat' => 40.7589,
            'dropoff_lng' => -73.9851,
            'pickup_date' => now()->addDay()->format('Y-m-d'),
            'pickup_time' => '10:00',
            'distance' => 5.2,
            'distance_value' => 5200,
            'duration_value' => 900,
            'amount' => 25.50,
            'notes' => 'Test booking',
        ];

        $response = $this->post(route('booking.store'), $bookingData);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'pickup_address' => '123 Main St, City',
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function admin_can_assign_driver_to_booking()
    {
        $this->actingAs($this->admin);

        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'status' => 'confirmed',
        ]);

        $response = $this->patch(route('admin.bookings.assign-driver', $booking), [
            'driver_id' => $this->driver->id,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'driver_id' => $this->driver->id,
            'status' => 'assigned',
        ]);
    }

    /** @test */
    public function driver_can_start_assigned_booking()
    {
        $this->actingAs($this->driver);

        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'status' => 'assigned',
        ]);

        $response = $this->patch(route('driver.bookings.start', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'in_progress',
        ]);
        $this->assertNotNull($booking->fresh()->started_at);
    }

    /** @test */
    public function driver_can_complete_booking()
    {
        $this->actingAs($this->driver);

        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'status' => 'in_progress',
            'started_at' => now()->subHour(),
        ]);

        $response = $this->patch(route('driver.bookings.complete', $booking));

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'completed',
        ]);
        $this->assertNotNull($booking->fresh()->completed_at);
    }

    /** @test */
    public function client_can_cancel_pending_booking()
    {
        $this->actingAs($this->client);

        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'status' => 'pending',
        ]);

        $response = $this->patch(route('client.bookings.cancel', $booking), [
            'cancellation_reason' => 'Change of plans',
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'cancelled',
            'cancellation_reason' => 'Change of plans',
        ]);
    }

    /** @test */
    public function client_cannot_cancel_in_progress_booking()
    {
        $this->actingAs($this->client);

        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'vehicle_id' => $this->vehicle->id,
            'status' => 'in_progress',
        ]);

        $response = $this->patch(route('client.bookings.cancel', $booking), [
            'cancellation_reason' => 'Change of plans',
        ]);

        $response->assertStatus(403);
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'in_progress',
        ]);
    }

    /** @test */
    public function booking_generates_unique_booking_number()
    {
        $booking1 = Booking::factory()->create();
        $booking2 = Booking::factory()->create();

        $this->assertNotEquals($booking1->booking_number, $booking2->booking_number);
        $this->assertStringStartsWith('YNR', $booking1->booking_number);
        $this->assertStringStartsWith('YNR', $booking2->booking_number);
    }

    /** @test */
    public function booking_calculates_formatted_amount()
    {
        $booking = Booking::factory()->create(['amount' => 25.50]);

        $this->assertEquals('$25.50', $booking->formatted_amount);
    }
}
