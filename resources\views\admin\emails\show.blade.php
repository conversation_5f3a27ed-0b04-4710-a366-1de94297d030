@extends('layouts.admin')

@section('title', 'Email Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Details</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.emails.index') }}">Email Management</a></li>
                        <li class="breadcrumb-item active">Email Details</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">Email Information</h4>
                        </div>
                        <div class="col-auto">
                            <span class="badge {{ $emailLog->status_badge_class }} fs-6">
                                {{ $emailLog->formatted_status }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Recipient Name</label>
                                <div class="fw-bold">{{ $emailLog->recipient_name ?: 'N/A' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Recipient Email</label>
                                <div class="fw-bold">{{ $emailLog->recipient_email }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label text-muted">Subject</label>
                                <div class="fw-bold">{{ $emailLog->subject }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Template</label>
                                <div>
                                    <span class="badge bg-info">
                                        {{ ucfirst(str_replace('_', ' ', $emailLog->template)) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Created At</label>
                                <div class="fw-bold">{{ $emailLog->created_at->format('M j, Y g:i A') }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Sent At</label>
                                <div class="fw-bold">
                                    @if($emailLog->sent_at)
                                        {{ $emailLog->sent_at->format('M j, Y g:i A') }}
                                    @else
                                        <span class="text-muted">Not sent</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Failed At</label>
                                <div class="fw-bold">
                                    @if($emailLog->failed_at)
                                        <span class="text-danger">{{ $emailLog->failed_at->format('M j, Y g:i A') }}</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($emailLog->error_message)
                    <div class="mb-3">
                        <label class="form-label text-muted">Error Message</label>
                        <div class="alert alert-danger">
                            {{ $emailLog->error_message }}
                        </div>
                    </div>
                    @endif

                    @if($emailLog->metadata)
                    <div class="mb-3">
                        <label class="form-label text-muted">Additional Information</label>
                        <div class="bg-light p-3 rounded">
                            <pre class="mb-0">{{ json_encode($emailLog->metadata, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    </div>
                    @endif

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.emails.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Email Management
                        </a>
                        
                        <div class="btn-group">
                            @if($emailLog->status === 'failed')
                                <form method="POST" action="{{ route('admin.emails.resend', $emailLog) }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-redo"></i> Resend Email
                                    </button>
                                </form>
                            @endif
                            
                            <form method="POST" action="{{ route('admin.emails.destroy', $emailLog) }}" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Are you sure you want to delete this email log?')">
                                    <i class="fas fa-trash"></i> Delete Log
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Related Information -->
            @if($emailLog->user)
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Related User</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">{{ $emailLog->user->name }}</h6>
                            <p class="text-muted mb-0">{{ $emailLog->user->email }}</p>
                            <small class="text-muted">{{ ucfirst($emailLog->user->role) }}</small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ route('admin.users.show', $emailLog->user) }}" class="btn btn-outline-primary btn-sm">
                            View User Profile
                        </a>
                    </div>
                </div>
            </div>
            @endif

            @if($emailLog->booking)
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Related Booking</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title">
                                    <i class="fas fa-calendar-check"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">#{{ $emailLog->booking->booking_number }}</h6>
                            <p class="text-muted mb-0">{{ $emailLog->booking->pickup_address }}</p>
                            <small class="text-muted">{{ $emailLog->booking->pickup_date->format('M j, Y') }}</small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ route('admin.bookings.show', $emailLog->booking) }}" class="btn btn-outline-success btn-sm">
                            View Booking
                        </a>
                    </div>
                </div>
            </div>
            @endif

            @if($emailLog->payment)
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Related Payment</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title">
                                    <i class="fas fa-money-bill-wave"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">${{ number_format($emailLog->payment->amount, 2) }}</h6>
                            <p class="text-muted mb-0">{{ $emailLog->payment->payment_method }}</p>
                            <small class="text-muted">{{ $emailLog->payment->created_at->format('M j, Y') }}</small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ route('admin.payments.show', $emailLog->payment) }}" class="btn btn-outline-warning btn-sm">
                            View Payment
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Email Statistics -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Statistics</h4>
                </div>
                <div class="card-body">
                    @php
                        $stats = \App\Models\EmailLog::getStatistics();
                    @endphp
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-muted">Success Rate</div>
                            <div class="fw-bold text-success">{{ $stats['success_rate'] }}%</div>
                        </div>
                        <div class="col-6">
                            <div class="text-muted">Total Emails</div>
                            <div class="fw-bold">{{ number_format($stats['total']) }}</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-muted">Sent</div>
                            <div class="fw-bold text-success">{{ number_format($stats['sent']) }}</div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted">Queued</div>
                            <div class="fw-bold text-warning">{{ number_format($stats['queued']) }}</div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted">Failed</div>
                            <div class="fw-bold text-danger">{{ number_format($stats['failed']) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
