<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DriverSchedule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'driver_id',
        'date',
        'start_time',
        'end_time',
        'is_available',
        'notes',
        'is_time_off',
        'time_off_reason',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'is_available' => 'boolean',
        'is_time_off' => 'boolean',
    ];

    /**
     * Get the driver that owns the schedule.
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    /**
     * Check if the schedule is for today.
     *
     * @return bool
     */
    public function isToday()
    {
        return $this->date->isToday();
    }

    /**
     * Check if the schedule is in the future.
     *
     * @return bool
     */
    public function isFuture()
    {
        return $this->date->isFuture();
    }

    /**
     * Check if the schedule is in the past.
     *
     * @return bool
     */
    public function isPast()
    {
        return $this->date->isPast();
    }

    /**
     * Get the formatted start time.
     *
     * @return string
     */
    public function getFormattedStartTimeAttribute()
    {
        return date('h:i A', strtotime($this->start_time));
    }

    /**
     * Get the formatted end time.
     *
     * @return string
     */
    public function getFormattedEndTimeAttribute()
    {
        return date('h:i A', strtotime($this->end_time));
    }

    /**
     * Get the formatted date.
     *
     * @return string
     */
    public function getFormattedDateAttribute()
    {
        return $this->date->format('M d, Y');
    }

    /**
     * Get the day of the week.
     *
     * @return string
     */
    public function getDayOfWeekAttribute()
    {
        return $this->date->format('l');
    }
}
