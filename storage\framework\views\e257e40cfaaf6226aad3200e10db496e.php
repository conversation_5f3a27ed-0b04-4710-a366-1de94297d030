<?php $__env->startSection('title', 'Send Bulk Email'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Send Bulk Email</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.emails.index')); ?>">Email Management</a></li>
                        <li class="breadcrumb-item active">Bulk Email</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Compose Bulk Email</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('admin.emails.bulk.send')); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Recipients -->
                        <div class="mb-4">
                            <label class="form-label">Recipients</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="recipient_all" value="all" checked>
                                        <label class="form-check-label" for="recipient_all">
                                            All Users
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="recipient_role" value="role">
                                        <label class="form-check-label" for="recipient_role">
                                            By Role
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="recipient_specific" value="specific">
                                        <label class="form-check-label" for="recipient_specific">
                                            Specific Users
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role Selection -->
                        <div class="mb-3" id="role_selection" style="display: none;">
                            <label for="user_role" class="form-label">Select Role</label>
                            <select name="user_role" id="user_role" class="form-select">
                                <option value="">Select a role</option>
                                <?php $__currentLoopData = $userRoles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role); ?>"><?php echo e(ucfirst($role)); ?>s</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Specific Users Selection -->
                        <div class="mb-3" id="specific_users" style="display: none;">
                            <label for="user_ids" class="form-label">Select Users</label>
                            <select name="user_ids[]" id="user_ids" class="form-select" multiple>
                                <!-- Users will be loaded via AJAX -->
                            </select>
                            <small class="text-muted">Hold Ctrl/Cmd to select multiple users</small>
                        </div>

                        <!-- Email Content -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" name="subject" id="subject" class="form-control" 
                                           placeholder="Enter email subject" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="template" class="form-label">Template</label>
                                    <select name="template" id="template" class="form-select" required>
                                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($template); ?>"><?php echo e(ucfirst($template)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea name="message" id="message" class="form-control" rows="8" 
                                      placeholder="Enter your email message..." required></textarea>
                        </div>

                        <!-- Preview -->
                        <div class="mb-4">
                            <button type="button" class="btn btn-outline-info" id="previewBtn">
                                <i class="mdi mdi-eye"></i> Preview Email
                            </button>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back
                            </a>
                            <button type="submit" class="btn btn-primary" id="sendBtn">
                                <i class="mdi mdi-email-send"></i> Send Bulk Email
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Email Statistics -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Recipient Count</h4>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h2 class="text-primary" id="recipientCount">0</h2>
                        <p class="text-muted">Recipients will receive this email</p>
                    </div>
                    
                    <div id="recipientBreakdown" style="display: none;">
                        <hr>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-muted">Clients</div>
                                <div class="fw-bold" id="clientCount">0</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">Drivers</div>
                                <div class="fw-bold" id="driverCount">0</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">Admins</div>
                                <div class="fw-bold" id="adminCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Tips -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Tips</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="mdi mdi-lightbulb"></i> Best Practices</h6>
                        <ul class="mb-0">
                            <li>Keep subject lines clear and concise</li>
                            <li>Personalize your message when possible</li>
                            <li>Include a clear call-to-action</li>
                            <li>Test with a small group first</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="mdi mdi-alert"></i> Important</h6>
                        <p class="mb-0">Bulk emails are queued and sent in the background. You can monitor the progress in the Email Management section.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recipientTypeRadios = document.querySelectorAll('input[name="recipient_type"]');
    const roleSelection = document.getElementById('role_selection');
    const specificUsers = document.getElementById('specific_users');
    const userRoleSelect = document.getElementById('user_role');
    const userIdsSelect = document.getElementById('user_ids');
    const recipientCount = document.getElementById('recipientCount');
    const recipientBreakdown = document.getElementById('recipientBreakdown');

    // Handle recipient type changes
    recipientTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            roleSelection.style.display = 'none';
            specificUsers.style.display = 'none';
            
            if (this.value === 'role') {
                roleSelection.style.display = 'block';
            } else if (this.value === 'specific') {
                specificUsers.style.display = 'block';
                loadUsers();
            }
            
            updateRecipientCount();
        });
    });

    // Handle role selection changes
    userRoleSelect.addEventListener('change', updateRecipientCount);

    // Load users for specific selection
    function loadUsers() {
        // This would typically load users via AJAX
        // For now, we'll add some placeholder options
        userIdsSelect.innerHTML = `
            <option value="1">John Doe (client)</option>
            <option value="2">Jane Smith (driver)</option>
            <option value="3">Admin User (admin)</option>
        `;
    }

    // Update recipient count
    function updateRecipientCount() {
        const selectedType = document.querySelector('input[name="recipient_type"]:checked').value;
        
        // This would typically make an AJAX call to get actual counts
        // For demo purposes, we'll use placeholder numbers
        let count = 0;
        
        switch(selectedType) {
            case 'all':
                count = 150; // Example total
                recipientBreakdown.style.display = 'block';
                document.getElementById('clientCount').textContent = '100';
                document.getElementById('driverCount').textContent = '45';
                document.getElementById('adminCount').textContent = '5';
                break;
            case 'role':
                const role = userRoleSelect.value;
                if (role === 'client') count = 100;
                else if (role === 'driver') count = 45;
                else if (role === 'admin') count = 5;
                recipientBreakdown.style.display = 'none';
                break;
            case 'specific':
                count = userIdsSelect.selectedOptions.length;
                recipientBreakdown.style.display = 'none';
                break;
        }
        
        recipientCount.textContent = count;
    }

    // Preview functionality
    document.getElementById('previewBtn').addEventListener('click', function() {
        const subject = document.getElementById('subject').value;
        const message = document.getElementById('message').value;
        const template = document.getElementById('template').value;
        
        if (!subject || !message) {
            alert('Please fill in the subject and message fields first.');
            return;
        }
        
        // Generate preview content
        const previewContent = `
            <div class="email-preview">
                <div class="border-bottom pb-3 mb-3">
                    <strong>Subject:</strong> ${subject}<br>
                    <strong>Template:</strong> ${template}
                </div>
                <div class="email-body">
                    ${message.replace(/\n/g, '<br>')}
                </div>
            </div>
        `;
        
        document.getElementById('previewContent').innerHTML = previewContent;
        new bootstrap.Modal(document.getElementById('previewModal')).show();
    });

    // Initialize
    updateRecipientCount();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/bulk.blade.php ENDPATH**/ ?>