<?php

namespace App\Services;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class PayPalCardService
{
    /**
     * PayPal client instance
     *
     * @var \Srmklive\PayPal\Services\PayPal
     */
    protected $paypalClient;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->paypalClient = new PayPalClient();

        // Get PayPal configuration from SettingsHelper
        $mode = SettingsHelper::getPaypalMode();
        $clientId = SettingsHelper::getPaypalClientId();
        $clientSecret = SettingsHelper::getPaypalSecret();

        // Set up configuration
        $config = [
            'mode' => $mode,
            $mode => [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'app_id' => 'APP-80W284485P519543T',
            ],
            'payment_action' => 'Sale',
            'currency' => 'USD',
            'notify_url' => '',
            'locale' => 'en_US',
            'validate_ssl' => true,
        ];

        // Log the configuration for debugging
        Log::info('PayPal Card configuration', [
            'mode' => $mode,
            'client_id' => $clientId ? 'Set' : 'Not set',
            'client_secret' => $clientSecret ? 'Set' : 'Not set',
        ]);

        $this->paypalClient->setApiCredentials($config);
        $this->paypalClient->getAccessToken();
    }

    /**
     * Create a payment order for card payment
     *
     * @param float $amount
     * @param string $currency
     * @param array $metadata
     * @return array|null
     */
    public function createPaymentOrder($amount, $currency = 'USD', $metadata = [])
    {
        try {
            $order = $this->paypalClient->createOrder([
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => number_format($amount, 2, '.', ''),
                        ],
                        'description' => $metadata['booking_number'] ?? 'Booking payment',
                        'custom_id' => $metadata['booking_id'] ?? '',
                    ],
                ],
                'application_context' => [
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW',
                    'return_url' => route('booking.payment.success', $metadata['booking_id'] ?? 0),
                    'cancel_url' => route('booking.payment.cancel', $metadata['booking_id'] ?? 0),
                ],
            ]);

            return $order;
        } catch (\Exception $e) {
            Log::error('PayPal create order exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Capture a payment order
     *
     * @param string $orderId
     * @return array|null
     */
    public function capturePaymentOrder($orderId)
    {
        try {
            $result = $this->paypalClient->capturePaymentOrder($orderId);

            return $result;
        } catch (\Exception $e) {
            Log::error('PayPal capture order exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get order details
     *
     * @param string $orderId
     * @return array|null
     */
    public function getOrderDetails($orderId)
    {
        try {
            $order = $this->paypalClient->showOrderDetails($orderId);

            return $order;
        } catch (\Exception $e) {
            Log::error('PayPal get order details exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
