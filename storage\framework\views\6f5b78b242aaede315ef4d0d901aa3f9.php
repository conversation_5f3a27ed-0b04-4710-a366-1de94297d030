<?php $__env->startSection('title', 'New Ride Assignment'); ?>

<?php $__env->startSection('content'); ?>
<h2>New Ride Assignment</h2>

<p>Dear <?php echo e($driver->name); ?>,</p>

<p>You have been assigned a new ride! Please review the details below and prepare for pickup.</p>

<div class="booking-details">
    <h3>Ride Assignment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value"><?php echo e($user->name); ?></span>
    </div>
    
    <?php if($user->phone): ?>
    <div class="detail-row">
        <span class="detail-label">Client Phone:</span>
        <span class="detail-value"><a href="tel:<?php echo e($user->phone); ?>"><?php echo e($user->phone); ?></a></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Earnings:</span>
        <span class="detail-value"><strong><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount * 0.8, 2)); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Trip Type:</span>
        <span class="detail-value"><?php echo e(ucfirst($booking->trip_type ?? 'one_way')); ?></span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">📋 Driver Checklist</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>✓ Review pickup and dropoff locations</li>
        <li>✓ Plan your route and check traffic conditions</li>
        <li>✓ Ensure your vehicle is clean and fueled</li>
        <li>✓ Contact client 15-30 minutes before pickup</li>
        <li>✓ Arrive 5 minutes early at pickup location</li>
        <li>✓ Confirm client identity before starting trip</li>
    </ul>
</div>

<?php if($booking->notes): ?>
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <h3 style="color: #856404; margin-top: 0;">📝 Special Instructions from Client</h3>
    <p style="color: #856404; margin-bottom: 0;"><?php echo e($booking->notes); ?></p>
</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('driver.rides.show', $booking->id)); ?>" class="btn">View Full Ride Details</a>
</div>

<h3>🚗 Pre-Trip Preparation:</h3>
<ul>
    <li><strong>Vehicle Check:</strong> Ensure your vehicle is clean, fueled, and in good condition</li>
    <li><strong>Route Planning:</strong> Review the pickup and dropoff locations, plan your route</li>
    <li><strong>Traffic Check:</strong> Check current traffic conditions and allow extra time if needed</li>
    <li><strong>Client Contact:</strong> Call the client 15-30 minutes before pickup time</li>
    <li><strong>Identification:</strong> Be prepared to confirm the client's name and destination</li>
</ul>

<h3>📱 During the Trip:</h3>
<ul>
    <li>Update trip status in your driver app</li>
    <li>Follow GPS navigation and traffic rules</li>
    <li>Provide professional and courteous service</li>
    <li>Assist with luggage if needed</li>
    <li>Complete the trip in your driver app upon arrival</li>
</ul>

<h3>💰 Earnings Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Trip Fare:</strong> <?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?></p>
    <p><strong>Your Earnings (80%):</strong> <?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount * 0.8, 2)); ?></p>
    <p><strong>Payment Method:</strong> <?php echo e($booking->payment_method ?? 'To be determined'); ?></p>
    <p class="mb-0"><strong>Payout:</strong> Included in your next weekly payout</p>
</div>

<h3>📞 Client Contact Information:</h3>
<div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Name:</strong> <?php echo e($user->name); ?></p>
    <?php if($user->phone): ?>
    <p><strong>Phone:</strong> <a href="tel:<?php echo e($user->phone); ?>"><?php echo e($user->phone); ?></a></p>
    <?php endif; ?>
    <p class="mb-0"><strong>Pickup:</strong> <?php echo e($booking->pickup_address); ?></p>
</div>

<h3>❓ Need Help?</h3>
<p>If you have any questions or encounter issues, contact our dispatch team immediately:</p>
<ul>
    <li><strong>Dispatch Phone:</strong> <?php echo e($companyPhone); ?></li>
    <li><strong>Email:</strong> <?php echo e($companyEmail); ?></li>
    <li><strong>Driver App:</strong> Use the support feature for immediate assistance</li>
</ul>

<p>Thank you for being a valued member of our driver team. Drive safely and provide excellent service!</p>

<p>Safe driving,<br>
The <?php echo e($companyName); ?> Dispatch Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\driver\assignment.blade.php ENDPATH**/ ?>