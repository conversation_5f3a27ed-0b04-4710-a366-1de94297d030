<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert default localization settings
        DB::table('settings')->updateOrInsert(
            ['key' => 'currency_code'],
            [
                'value' => 'USD',
                'group' => 'pricing',
                'type' => 'select',
                'options' => json_encode([
                    'USD' => 'US Dollar ($)',
                    'EUR' => 'Euro (€)',
                    'GBP' => 'British Pound (£)',
                    'CAD' => 'Canadian Dollar (C$)',
                    'AUD' => 'Australian Dollar (A$)',
                    'JPY' => 'Japanese Yen (¥)',
                    'INR' => 'Indian Rupee (₹)',
                    'CNY' => 'Chinese Yuan (¥)',
                    'AED' => 'UAE Dirham (د.إ)',
                    'SAR' => 'Saudi Riyal (﷼)'
                ]),
                'label' => 'Currency',
                'description' => 'Select the currency to use for all transactions',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        DB::table('settings')->updateOrInsert(
            ['key' => 'currency_symbol'],
            [
                'value' => '$',
                'group' => 'pricing',
                'type' => 'text',
                'label' => 'Currency Symbol',
                'description' => 'Symbol to display before prices',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        DB::table('settings')->updateOrInsert(
            ['key' => 'country_code'],
            [
                'value' => 'US',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'US' => 'United States',
                    'GB' => 'United Kingdom',
                    'CA' => 'Canada',
                    'AU' => 'Australia',
                    'DE' => 'Germany',
                    'FR' => 'France',
                    'IT' => 'Italy',
                    'ES' => 'Spain',
                    'IN' => 'India',
                    'AE' => 'United Arab Emirates',
                    'SA' => 'Saudi Arabia'
                ]),
                'label' => 'Country',
                'description' => 'Select the primary country for operations',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        DB::table('settings')->updateOrInsert(
            ['key' => 'distance_unit'],
            [
                'value' => 'miles',
                'group' => 'localization',
                'type' => 'select',
                'options' => json_encode([
                    'miles' => 'Miles',
                    'kilometers' => 'Kilometers'
                ]),
                'label' => 'Distance Unit',
                'description' => 'Unit to use for displaying distances',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to delete these settings in the down method
        // as they might have been customized by the user
    }
};
