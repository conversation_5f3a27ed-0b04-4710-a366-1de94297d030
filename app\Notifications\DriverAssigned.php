<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DriverAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The booking instance.
     *
     * @var \App\Models\Booking
     */
    protected $booking;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Booking  $booking
     * @return void
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = route('driver.rides.show', $this->booking->id);

        return (new MailMessage)
            ->subject('New Ride Assignment - Booking #' . $this->booking->booking_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have been assigned to a new ride.')
            ->line('Booking #: ' . $this->booking->booking_number)
            ->line('Client: ' . $this->booking->user->name)
            ->line('Pickup Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->line('Pickup Location: ' . $this->booking->pickup_address)
            ->line('Dropoff Location: ' . $this->booking->dropoff_address)
            ->line('Vehicle: ' . $this->booking->vehicle->name)
            ->action('View Ride Details', $url)
            ->line('Thank you for using our service!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'client_name' => $this->booking->user->name,
            'pickup_date' => $this->booking->pickup_date->format('M d, Y h:i A'),
            'pickup_address' => $this->booking->pickup_address,
            'dropoff_address' => $this->booking->dropoff_address,
            'vehicle' => $this->booking->vehicle->name,
            'message' => 'You have been assigned to a new ride.',
            'url' => route('driver.rides.show', $this->booking->id)
        ];
    }
}
