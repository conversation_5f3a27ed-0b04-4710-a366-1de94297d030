<?php $__env->startSection('title', 'Email Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Analytics</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.emails.index')); ?>">Email Management</a></li>
                        <li class="breadcrumb-item active">Analytics</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="<?php echo e(request('date_from', now()->subDays(30)->format('Y-m-d'))); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="<?php echo e(request('date_to', now()->format('Y-m-d'))); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="template_filter" class="form-label">Template</label>
                            <select name="template" id="template_filter" class="form-select">
                                <option value="">All Templates</option>
                                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($template); ?>" <?php echo e(request('template') === $template ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $template))); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Apply Filter</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title">
                                    <i class="fas fa-envelope"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Total Emails</h6>
                            <b class="text-primary"><?php echo e(number_format($analytics['total_emails'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title">
                                    <i class="fas fa-check-circle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Success Rate</h6>
                            <b class="text-success"><?php echo e(number_format($analytics['success_rate'], 1)); ?>%</b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title">
                                    <i class="fas fa-clock"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Avg. Send Time</h6>
                            <b class="text-warning"><?php echo e($analytics['avg_send_time']); ?>s</b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-danger">
                                <span class="avatar-title">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Failed Emails</h6>
                            <b class="text-danger"><?php echo e(number_format($analytics['failed_emails'])); ?></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Email Volume Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Volume Over Time</h4>
                </div>
                <div class="card-body">
                    <canvas id="emailVolumeChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Template Distribution -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Templates</h4>
                </div>
                <div class="card-body">
                    <canvas id="templateChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distribution and Top Recipients -->
    <div class="row">
        <!-- Status Distribution -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Status Distribution</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <h4 class="mt-2"><?php echo e(number_format($analytics['sent_emails'])); ?></h4>
                                <p class="text-muted">Sent</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-warning">
                                <i class="fas fa-clock fa-2x"></i>
                                <h4 class="mt-2"><?php echo e(number_format($analytics['queued_emails'])); ?></h4>
                                <p class="text-muted">Queued</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-danger">
                                <i class="fas fa-times-circle fa-2x"></i>
                                <h4 class="mt-2"><?php echo e(number_format($analytics['failed_emails'])); ?></h4>
                                <p class="text-muted">Failed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Recipients -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Top Email Recipients</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Count</th>
                                    <th>Success Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $analytics['top_recipients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recipient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo e($recipient['name'] ?: 'N/A'); ?></strong><br>
                                            <small class="text-muted"><?php echo e($recipient['email']); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo e($recipient['count']); ?></td>
                                    <td>
                                        <span class="badge <?php echo e($recipient['success_rate'] >= 90 ? 'bg-success' : ($recipient['success_rate'] >= 70 ? 'bg-warning' : 'bg-danger')); ?>">
                                            <?php echo e(number_format($recipient['success_rate'], 1)); ?>%
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Failed Emails -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Recent Failed Emails</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Template</th>
                                    <th>Failed At</th>
                                    <th>Error</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $analytics['recent_failed']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $email): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo e($email->recipient_name ?: 'N/A'); ?></strong><br>
                                            <small class="text-muted"><?php echo e($email->recipient_email); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo e(Str::limit($email->subject, 40)); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $email->template))); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($email->failed_at->format('M j, Y g:i A')); ?></td>
                                    <td>
                                        <small class="text-danger"><?php echo e(Str::limit($email->error_message, 50)); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.emails.show', $email)); ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form method="POST" action="<?php echo e(route('admin.emails.resend', $email)); ?>" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="btn btn-outline-warning">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-check-circle fa-2x d-block mb-2 text-success"></i>
                                            No recent failed emails
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Email Volume Chart
    const volumeCtx = document.getElementById('emailVolumeChart').getContext('2d');
    new Chart(volumeCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($analytics['volume_chart']['labels']); ?>,
            datasets: [{
                label: 'Emails Sent',
                data: <?php echo json_encode($analytics['volume_chart']['data']); ?>,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Template Distribution Chart
    const templateCtx = document.getElementById('templateChart').getContext('2d');
    new Chart(templateCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($analytics['template_chart']['labels']); ?>,
            datasets: [{
                data: <?php echo json_encode($analytics['template_chart']['data']); ?>,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/emails/analytics.blade.php ENDPATH**/ ?>