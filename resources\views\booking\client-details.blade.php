@extends('layouts.guest')

@section('title', 'Your Details')

@section('content')
<div class="guest-review-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="guest-review-card">
                    <div class="card-header">
                        <h3 class="mb-0">Your Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <h5 class="mb-4">Please provide your contact information</h5>

                                <form action="{{ route('booking.client-details.save') }}" method="POST" id="clientDetailsForm" class="guest-form">
                                    @csrf

                                    <div class="form-group mb-3">
                                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Please enter your full name as it appears on your ID.</div>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">We'll send your booking confirmation to this email.</div>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}" required>
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">The driver may contact you on this number.</div>
                                    </div>

                                    <div class="form-group mb-4">
                                        <label for="special_requests" class="form-label">Special Requests</label>
                                        <textarea class="form-control @error('special_requests') is-invalid @enderror" id="special_requests" name="special_requests" rows="3">{{ old('special_requests') }}</textarea>
                                        @error('special_requests')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Any special requirements or information for your driver.</div>
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input @error('terms') is-invalid @enderror" type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> <span class="text-danger">*</span>
                                        </label>
                                        @error('terms')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="booking-actions">
                                        <a href="{{ route('booking.guest.review') }}" class="btn btn-back">
                                            <i class="fas fa-arrow-left me-2"></i> Back
                                        </a>
                                        <button type="submit" class="btn btn-next">
                                            Continue <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="col-md-5">
                                <div class="booking-summary">
                                    <h4>Booking Summary</h4>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Booking Type:</div>
                                        <div class="booking-summary-value">
                                            @if(session('guest_booking.booking_type') == 'one_way')
                                                One Way
                                            @elseif(session('guest_booking.booking_type') == 'return')
                                                Return
                                            @elseif(session('guest_booking.booking_type') == 'airport')
                                                Airport Transfer
                                            @else
                                                Hourly ({{ session('guest_booking.duration_hours') }} hours)
                                            @endif
                                        </div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup:</div>
                                        <div class="booking-summary-value">{{ session('guest_booking.pickup_address') }}</div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Date & Time:</div>
                                        <div class="booking-summary-value">{{ \Carbon\Carbon::parse(session('guest_booking.pickup_date'))->format('M d, Y h:i A') }}</div>
                                    </div>

                                    @if(session('guest_booking.booking_type') != 'hourly')
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Dropoff:</div>
                                            <div class="booking-summary-value">{{ session('guest_booking.dropoff_address') }}</div>
                                        </div>
                                    @endif

                                    @if(session('guest_booking.booking_type') == 'return' && session('guest_booking.return_date'))
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Return Date:</div>
                                            <div class="booking-summary-value">{{ \Carbon\Carbon::parse(session('guest_booking.return_date'))->format('M d, Y h:i A') }}</div>
                                        </div>
                                    @endif

                                    <div class="booking-total">
                                        <div class="booking-total-label">Total:</div>
                                        <div class="booking-total-value">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format(session('guest_booking.amount'), 2) }}</div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> After providing your details, you'll be able to sign in or create an account to complete your booking.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Booking and Cancellation Policy</h6>
                <p>All bookings are subject to availability. Cancellations made less than 24 hours before the scheduled pickup time may incur a cancellation fee.</p>

                <h6>2. Payment Terms</h6>
                <p>Payment is required at the time of booking. We accept credit/debit cards and PayPal.</p>

                <h6>3. Privacy Policy</h6>
                <p>Your personal information will be used only for the purpose of providing our service and will be handled in accordance with our privacy policy.</p>

                <h6>4. Liability</h6>
                <p>We strive to provide reliable service, but cannot be held responsible for delays caused by traffic, weather, or other circumstances beyond our control.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('clientDetailsForm');

        if (form) {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...';
                }
            });
        }
    });
</script>
@endsection
