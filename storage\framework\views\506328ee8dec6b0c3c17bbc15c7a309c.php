<?php $__env->startSection('title', 'Booking Details'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/admin-booking-details.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="booking-details-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2 class="mb-1">
                Booking #<?php echo e($booking->booking_number); ?>

                <span class="booking-status status-<?php echo e(strtolower($booking->status)); ?> ms-2">
                    <?php echo e(ucfirst($booking->status)); ?>

                </span>
            </h2>
            <p class="text-muted mb-0">
                <i class="fas fa-calendar-alt me-1"></i> Created on <?php echo e($booking->created_at->format('F d, Y h:i A')); ?>

                <?php if($booking->driver): ?>
                <span class="mx-2">|</span> <i class="fas fa-user me-1"></i> Driver: <?php echo e($booking->driver->name); ?>

                <?php endif; ?>
            </p>
        </div>
        <div class="d-flex">
            <a href="<?php echo e(route('admin.bookings.index')); ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Bookings
            </a>
            <button class="btn btn-outline-primary me-2" onclick="window.print()">
                <i class="fas fa-print me-1"></i> Print
            </button>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="bookingActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog me-1"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="bookingActionsDropdown">
                    <?php if($booking->status == 'pending'): ?>
                        <li>
                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-status="confirmed">
                                <i class="fas fa-check-circle text-info me-2"></i> Confirm Booking
                            </button>
                        </li>
                    <?php endif; ?>

                    <?php if(in_array($booking->status, ['pending', 'confirmed'])): ?>
                        <li>
                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-status="completed">
                                <i class="fas fa-flag-checkered text-success me-2"></i> Mark as Completed
                            </button>
                        </li>
                        <li>
                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#cancelBookingModal">
                                <i class="fas fa-ban text-danger me-2"></i> Cancel Booking
                            </button>
                        </li>
                    <?php endif; ?>

                    <?php if(in_array($booking->status, ['pending', 'confirmed', 'assigned'])): ?>
                        <li>
                            <a href="<?php echo e(route('admin.bookings.available-drivers', $booking->id)); ?>" class="dropdown-item">
                                <i class="fas fa-user-plus text-primary me-2"></i> Manage Driver Assignment
                            </a>
                        </li>
                    <?php endif; ?>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a href="#" class="dropdown-item" onclick="window.print()">
                            <i class="fas fa-print me-2"></i> Print Booking Details
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i> <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Booking Status Progress Bar -->
<div class="booking-progress mb-4 no-print">
    <div class="progress" style="height: 5px;">
        <?php
            $progressPercentage = 0;
            switch($booking->status) {
                case 'pending':
                    $progressPercentage = 20;
                    break;
                case 'confirmed':
                    $progressPercentage = 40;
                    break;
                case 'assigned':
                    $progressPercentage = 60;
                    break;
                case 'in_progress':
                    $progressPercentage = 80;
                    break;
                case 'completed':
                    $progressPercentage = 100;
                    break;
                case 'cancelled':
                    $progressPercentage = 100;
                    break;
            }
        ?>
        <div class="progress-bar <?php echo e($booking->status == 'cancelled' ? 'bg-danger' : 'bg-primary'); ?>" role="progressbar" style="width: <?php echo e($progressPercentage); ?>%" aria-valuenow="<?php echo e($progressPercentage); ?>" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div class="d-flex justify-content-between mt-2">
        <div class="progress-step <?php echo e($progressPercentage >= 20 ? 'active' : ''); ?>">
            <div class="progress-marker"></div>
            <div class="progress-text">Pending</div>
        </div>
        <div class="progress-step <?php echo e($progressPercentage >= 40 ? 'active' : ''); ?>">
            <div class="progress-marker"></div>
            <div class="progress-text">Confirmed</div>
        </div>
        <div class="progress-step <?php echo e($progressPercentage >= 60 ? 'active' : ''); ?>">
            <div class="progress-marker"></div>
            <div class="progress-text">Assigned</div>
        </div>
        <div class="progress-step <?php echo e($progressPercentage >= 80 ? 'active' : ''); ?>">
            <div class="progress-marker"></div>
            <div class="progress-text">In Progress</div>
        </div>
        <div class="progress-step <?php echo e($progressPercentage >= 100 ? 'active' : ''); ?>">
            <div class="progress-marker"></div>
            <div class="progress-text"><?php echo e($booking->status == 'cancelled' ? 'Cancelled' : 'Completed'); ?></div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Content Area -->
    <div class="col-md-8">
        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs booking-tabs mb-3 no-print" id="bookingTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                    <i class="fas fa-info-circle me-2"></i> Booking Details
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="route-tab" data-bs-toggle="tab" data-bs-target="#route" type="button" role="tab" aria-controls="route" aria-selected="false">
                    <i class="fas fa-route me-2"></i> Route & Map
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab" aria-controls="payment" aria-selected="false">
                    <i class="fas fa-credit-card me-2"></i> Payment
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                    <i class="fas fa-history me-2"></i> History
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="bookingTabsContent">
            <!-- Details Tab -->
            <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                <div class="booking-details-card card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Information</h5>
                    </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="client-info mb-4">
                            <div class="booking-info-label">Client Information</div>
                            <div class="client-card p-3 bg-light rounded">
                                <?php if($booking->user): ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <?php if($booking->user->profile_photo): ?>
                                            <img src="<?php echo e(asset('storage/' . $booking->user->profile_photo)); ?>" alt="<?php echo e($booking->user->name); ?>" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; color: white; font-size: 1.2rem;">
                                                <?php echo e(strtoupper(substr($booking->user->name ?? 'U', 0, 1))); ?>

                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0 fw-bold"><?php echo e($booking->user->name ?? 'Unknown User'); ?></h6>
                                            <div class="text-muted small">Client ID: #<?php echo e($booking->user->id); ?></div>
                                        </div>
                                    </div>
                                    <div class="client-contact mt-2">
                                        <div><i class="fas fa-envelope me-2 text-primary"></i> <?php echo e($booking->user->email ?? 'No email provided'); ?></div>
                                        <div><i class="fas fa-phone me-2 text-primary"></i> <?php echo e($booking->user->phone ?? 'No phone number'); ?></div>
                                        <?php if($booking->user->created_at): ?>
                                            <div class="text-muted small mt-1">Member since: <?php echo e($booking->user->created_at->format('M Y')); ?></div>
                                        <?php endif; ?>
                                        <div class="mt-2">
                                            <a href="<?php echo e(route('admin.users.show', $booking->user->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-user me-1"></i> View Client Profile
                                            </a>
                                            <?php if($booking->user->email): ?>
                                                <a href="mailto:<?php echo e($booking->user->email); ?>" class="text-link ms-2">
                                                    <i class="fas fa-envelope me-1"></i> Email Client
                                                </a>
                                            <?php endif; ?>
                                            <?php if($booking->user->phone): ?>
                                                <a href="tel:<?php echo e($booking->user->phone); ?>" class="text-link ms-2">
                                                    <i class="fas fa-phone me-1"></i> Call Client
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-3">
                                        <div class="bg-secondary rounded-circle mb-2 mx-auto d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; color: white; font-size: 1.2rem;">
                                            <i class="fas fa-user-slash"></i>
                                        </div>
                                        <h6 class="mb-0 fw-bold text-muted">Guest Booking</h6>
                                        <div class="text-muted small">No registered user associated</div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="booking-info-label">Booking Details</div>
                        <div class="booking-info-value">
                            <div><strong>Booking Number:</strong> #<?php echo e($booking->booking_number); ?></div>
                            <div><strong>Booking Type:</strong> <?php echo e(ucfirst(str_replace('_', ' ', $booking->booking_type))); ?></div>
                            <?php if($booking->booking_type === 'airport_transfer'): ?>
                                <div><strong>Airport Direction:</strong>
                                    <span class="badge bg-info">
                                        <?php echo e($booking->airport_direction === 'to_airport' ? 'To Airport' : 'From Airport'); ?>

                                    </span>
                                </div>
                                <?php
                                    $airport = null;
                                    if($booking->airport_id) {
                                        $airport = \App\Models\Airport::find($booking->airport_id);
                                    }
                                ?>
                                <?php if($airport): ?>
                                    <div><strong>Airport:</strong> <?php echo e($airport->name); ?> (<?php echo e($airport->code); ?>) - <?php echo e($airport->city); ?></div>
                                <?php endif; ?>
                                <?php if(isset($booking->airport_surcharge) && $booking->airport_surcharge > 0): ?>
                                    <div><strong>Airport Surcharge:</strong> <?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($booking->airport_surcharge, 2)); ?></div>
                                <?php endif; ?>
                            <?php elseif($booking->booking_type === 'hourly'): ?>
                                <?php if($booking->duration_hours): ?>
                                    <div><strong>Duration:</strong> <?php echo e($booking->duration_hours); ?> hours</div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <div><strong>Created On:</strong> <?php echo e($booking->created_at->format('F d, Y h:i A')); ?></div>
                            <div><strong>Status:</strong> <span class="booking-status status-<?php echo e(strtolower($booking->status)); ?>"><?php echo e(ucfirst($booking->status)); ?></span></div>
                        </div>

                        <div class="booking-info-label">Special Instructions</div>
                        <div class="booking-info-value">
                            <div class="p-3 bg-light rounded">
                                <?php echo e($booking->notes ?? 'No special instructions provided.'); ?>

                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="booking-info-label">Trip Details</div>
                        <div class="booking-info-value">
                            <div class="trip-card p-3 bg-light rounded mb-3">
                                <div class="d-flex mb-3">
                                    <div class="trip-icon me-3">
                                        <div class="pickup-icon">
                                            <i class="fas fa-map-marker-alt text-success"></i>
                                        </div>
                                        <div class="trip-line"></div>
                                        <div class="dropoff-icon">
                                            <i class="fas fa-map-marker-alt text-danger"></i>
                                        </div>
                                    </div>
                                    <div class="trip-locations">
                                        <div class="pickup mb-3">
                                            <div class="small text-muted">PICKUP</div>
                                            <div class="fw-bold"><?php echo e($booking->pickup_address ?: 'Not specified'); ?></div>
                                            <div class="small"><?php echo e($booking->pickup_date ? $booking->pickup_date->format('F d, Y h:i A') : 'N/A'); ?></div>
                                            <?php if($booking->pickup_lat && $booking->pickup_lng): ?>
                                                <div class="small text-muted"><?php echo e(number_format($booking->pickup_lat, 6)); ?>, <?php echo e(number_format($booking->pickup_lng, 6)); ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($booking->booking_type !== 'hourly'): ?>
                                            <div class="dropoff">
                                                <div class="small text-muted">DROPOFF</div>
                                                <div class="fw-bold"><?php echo e($booking->dropoff_address ?: 'Not specified'); ?></div>
                                                <?php if($booking->booking_type == 'return' && $booking->return_date): ?>
                                                    <div class="small">Return: <?php echo e($booking->return_date->format('F d, Y h:i A')); ?></div>
                                                <?php endif; ?>
                                                <?php if($booking->dropoff_lat && $booking->dropoff_lng): ?>
                                                    <div class="small text-muted"><?php echo e(number_format($booking->dropoff_lat, 6)); ?>, <?php echo e(number_format($booking->dropoff_lng, 6)); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="dropoff">
                                                <div class="small text-muted">DURATION</div>
                                                <div class="fw-bold"><?php echo e($booking->duration_hours ?? 'N/A'); ?> hours</div>
                                                <div class="small">Hourly service</div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="trip-details d-flex justify-content-between border-top pt-2">
                                    <div class="trip-detail">
                                        <div class="small text-muted">DISTANCE</div>
                                        <?php
                                            // Helper function to calculate distance
                                            $calculateBookingDistance = function($booking) {
                                                $bookingDistance = $booking->distance ?? 0;

                                                // If distance is 0 or null, try to calculate from coordinates
                                                if ($bookingDistance <= 0 && $booking->pickup_lat && $booking->pickup_lng && $booking->dropoff_lat && $booking->dropoff_lng && $booking->booking_type !== 'hourly') {
                                                    // Calculate distance using Haversine formula
                                                    $earthRadius = 6371; // km
                                                    $latFrom = deg2rad($booking->pickup_lat);
                                                    $lonFrom = deg2rad($booking->pickup_lng);
                                                    $latTo = deg2rad($booking->dropoff_lat);
                                                    $lonTo = deg2rad($booking->dropoff_lng);

                                                    $latDelta = $latTo - $latFrom;
                                                    $lonDelta = $lonTo - $lonFrom;

                                                    $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) + cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
                                                    $bookingDistance = $angle * $earthRadius;
                                                    $bookingDistance = max($bookingDistance, 0.1); // Minimum 0.1 km
                                                }

                                                return $bookingDistance;
                                            };

                                            $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                                            $bookingDistance = $calculateBookingDistance($booking);
                                            $displayDistance = $bookingDistance;
                                            $unitLabel = 'km';

                                            if ($distanceUnit === 'miles') {
                                                $displayDistance = $bookingDistance * 0.621371; // Convert km to miles
                                                $unitLabel = 'mi';
                                            }

                                            // Show "N/A" for hourly bookings or when no distance available
                                            $distanceText = ($booking->booking_type === 'hourly' || $bookingDistance <= 0) ? 'N/A' : number_format((float)$displayDistance, 1) . ' ' . $unitLabel;
                                        ?>
                                        <div class="fw-bold"><?php echo e($distanceText); ?></div>
                                    </div>
                                    <div class="trip-detail">
                                        <div class="small text-muted">DURATION</div>
                                        <?php
                                            $durationText = 'N/A';
                                            if ($booking->duration_value && $booking->duration_value > 0) {
                                                $durationText = floor($booking->duration_value / 60) . ' min';
                                            } elseif ($booking->booking_type === 'hourly' && $booking->duration_hours) {
                                                $durationText = $booking->duration_hours . ' hours';
                                            } elseif ($bookingDistance > 0) {
                                                // Estimate duration based on distance (assuming 50 km/h average speed)
                                                $estimatedMinutes = ($bookingDistance / 50) * 60;
                                                $durationText = floor($estimatedMinutes) . ' min (est.)';
                                            }
                                        ?>
                                        <div class="fw-bold"><?php echo e($durationText); ?></div>
                                    </div>
                                    <div class="trip-detail">
                                        <div class="small text-muted">AMOUNT</div>
                                        <div class="fw-bold text-primary"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->amount, 2)); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="vehicle-driver-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="booking-info-label">Vehicle</div>
                                    <div class="booking-info-value">
                                        <div class="vehicle-card p-3 bg-light rounded text-center">
                                            <?php if($booking->vehicle->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $booking->vehicle->image)); ?>" alt="<?php echo e($booking->vehicle->name); ?>" class="img-fluid mb-2 rounded" style="max-height: 80px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="vehicle-placeholder mb-2 d-flex align-items-center justify-content-center bg-secondary rounded" style="height: 80px;">
                                                    <i class="fas fa-car fa-2x text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="fw-bold"><?php echo e($booking->vehicle->name); ?></div>
                                            <div class="small text-muted"><?php echo e(ucfirst($booking->vehicle->type)); ?></div>
                                            <div class="vehicle-features small mt-2">
                                                <span class="me-2"><i class="fas fa-user me-1"></i> <?php echo e($booking->vehicle->seats); ?></span>
                                                <span><i class="fas fa-suitcase me-1"></i> <?php echo e($booking->vehicle->luggage_capacity); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="booking-info-label">Driver</div>
                                    <div class="booking-info-value">
                                        <?php if($booking->driver): ?>
                                            <div class="driver-card p-3 bg-light rounded text-center">
                                                <?php if($booking->driver->profile_photo): ?>
                                                    <img src="<?php echo e(asset('storage/' . $booking->driver->profile_photo)); ?>" alt="<?php echo e($booking->driver->name); ?>" class="rounded-circle mb-2" style="width: 60px; height: 60px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded-circle mb-2 mx-auto d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; color: white; font-size: 1.5rem;">
                                                        <?php echo e(strtoupper(substr($booking->driver->name, 0, 1))); ?>

                                                    </div>
                                                <?php endif; ?>
                                                <div class="fw-bold"><?php echo e($booking->driver->name); ?></div>
                                                <div class="small text-muted"><?php echo e($booking->driver->phone); ?></div>
                                                <div class="mt-2">
                                                    <a href="<?php echo e(route('admin.drivers.show', $booking->driver->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i> View Driver
                                                    </a>
                                                    <?php if($booking->driver->phone): ?>
                                                        <a href="tel:<?php echo e($booking->driver->phone); ?>" class="icon-link ms-2">
                                                            <i class="fas fa-phone me-1"></i> Call
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="driver-card p-3 bg-light rounded text-center">
                                                <div class="bg-secondary rounded-circle mb-2 mx-auto d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; color: white; font-size: 1.5rem;">
                                                    <i class="fas fa-user-slash"></i>
                                                </div>
                                                <div class="fw-bold text-muted">No Driver Assigned</div>
                                                <div class="mt-2">
                                                    <a href="<?php echo e(route('admin.bookings.available-drivers', $booking->id)); ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-user-plus me-1"></i> Assign Driver
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if($booking->status == 'cancelled'): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <h5 class="alert-heading"><i class="fas fa-ban me-2"></i> Booking Cancelled</h5>
                            <hr>
                            <p><strong>Cancellation Reason:</strong> <?php echo e($booking->cancellation_reason); ?></p>
                            <p><strong>Cancelled By:</strong> <?php echo e(ucfirst($booking->cancelled_by ?? 'Unknown')); ?></p>
                            <p class="mb-0"><strong>Cancelled At:</strong> <?php echo e($booking->cancelled_at ? $booking->cancelled_at->format('F d, Y h:i A') : 'N/A'); ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

                <!-- Fare Breakdown Card -->
                <div class="booking-details-card card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i> Fare Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="fare-breakdown">
                            <?php if(isset($booking->fare_details) && is_array($booking->fare_details)): ?>
                                <?php $__currentLoopData = $booking->fare_details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($key != 'total' && !is_array($value)): ?>
                                        <div class="fare-item">
                                            <div class="fare-label"><?php echo e(ucwords(str_replace('_', ' ', $key))); ?></div>
                                            <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$value, 2)); ?></div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="fare-item">
                                    <div class="fare-label">Base Fare</div>
                                    <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)($booking->vehicle->base_fare ?? 0), 2)); ?></div>
                                </div>
                                <?php if($booking->booking_type !== 'hourly'): ?>
                                    <div class="fare-item">
                                        <?php
                                            $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                                            $bookingDistance = $calculateBookingDistance($booking);
                                            $displayDistance = $bookingDistance;
                                            $unitLabel = 'km';

                                            if ($distanceUnit === 'miles') {
                                                $displayDistance = $bookingDistance * 0.621371; // Convert km to miles
                                                $unitLabel = 'mi';
                                            }
                                        ?>
                                        <div class="fare-label">Distance Charge (<?php echo e($bookingDistance > 0 ? number_format((float)$displayDistance, 1) . ' ' . $unitLabel : 'N/A'); ?>)</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)($bookingDistance * $booking->vehicle->price_per_km), 2)); ?></div>
                                    </div>
                                <?php else: ?>
                                    <div class="fare-item">
                                        <div class="fare-label">Hourly Rate (<?php echo e($booking->duration_hours ?? 0); ?> hours)</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)($booking->duration_hours * (\App\Services\SettingsService::get('hourly_rate', 50))), 2)); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if($booking->booking_type === 'airport_transfer' && isset($booking->airport_surcharge) && $booking->airport_surcharge > 0): ?>
                                    <div class="fare-item">
                                        <div class="fare-label">Airport Surcharge</div>
                                        <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->airport_surcharge, 2)); ?></div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Extra Services -->
                            <?php
                                $extraServicesTotal = 0;
                                $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();
                            ?>

                            <?php if($booking->meet_and_greet): ?>
                                <?php $extraServicesTotal += $extraServicesSettings['meet_and_greet']['fee']; ?>
                                <div class="fare-item">
                                    <div class="fare-label"><i class="fas fa-handshake me-1 text-info"></i> Meet and Greet</div>
                                    <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($extraServicesSettings['meet_and_greet']['fee'], 2)); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if($booking->child_seat): ?>
                                <?php $extraServicesTotal += $extraServicesSettings['child_seat']['fee']; ?>
                                <div class="fare-item">
                                    <div class="fare-label"><i class="fas fa-baby me-1 text-warning"></i> Child Seat</div>
                                    <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($extraServicesSettings['child_seat']['fee'], 2)); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if($booking->wheelchair_accessible): ?>
                                <?php $extraServicesTotal += $extraServicesSettings['wheelchair_accessible']['fee']; ?>
                                <div class="fare-item">
                                    <div class="fare-label"><i class="fas fa-wheelchair me-1 text-success"></i> Wheelchair Accessible</div>
                                    <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2)); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if($booking->extra_luggage): ?>
                                <?php $extraServicesTotal += $extraServicesSettings['extra_luggage']['fee']; ?>
                                <div class="fare-item">
                                    <div class="fare-label"><i class="fas fa-suitcase me-1 text-secondary"></i> Extra Luggage</div>
                                    <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format($extraServicesSettings['extra_luggage']['fee'], 2)); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="fare-total">
                                <div class="fare-label">Total Amount</div>
                                <div class="fare-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->amount, 2)); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Route & Map Tab -->
            <div class="tab-pane fade" id="route" role="tabpanel" aria-labelledby="route-tab">
                <div class="booking-details-card card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-route me-2"></i> Route Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="map-container mb-4" id="booking-map">
                            <!-- Map will be loaded here -->
                            <div class="text-center py-5 bg-light rounded">
                                <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                                <h5>Loading Map...</h5>
                                <p class="text-muted">Please wait while we load the route map.</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="booking-info-label">Pickup Location</div>
                                <div class="booking-info-value">
                                    <div class="d-flex align-items-start">
                                        <div class="location-icon me-2">
                                            <i class="fas fa-map-marker-alt text-success"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo e($booking->pickup_address ?: 'Not specified'); ?></div>
                                            <div class="text-muted small"><?php echo e($booking->pickup_date ? $booking->pickup_date->format('F d, Y h:i A') : 'N/A'); ?></div>
                                            <?php if($booking->pickup_lat && $booking->pickup_lng): ?>
                                                <div class="text-muted small">Coordinates: <?php echo e(number_format($booking->pickup_lat, 6)); ?>, <?php echo e(number_format($booking->pickup_lng, 6)); ?></div>
                                            <?php endif; ?>
                                            <?php if(isset($booking->pickup_instructions)): ?>
                                                <div class="mt-2 p-2 bg-light rounded small">
                                                    <i class="fas fa-info-circle me-1"></i> <?php echo e($booking->pickup_instructions); ?>

                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <?php if($booking->booking_type !== 'hourly'): ?>
                                    <div class="booking-info-label">Dropoff Location</div>
                                    <div class="booking-info-value">
                                        <div class="d-flex align-items-start">
                                            <div class="location-icon me-2">
                                                <i class="fas fa-map-marker-alt text-danger"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($booking->dropoff_address ?: 'Not specified'); ?></div>
                                                <?php if($booking->booking_type == 'return' && $booking->return_date): ?>
                                                    <div class="text-muted small">Return: <?php echo e($booking->return_date ? $booking->return_date->format('F d, Y h:i A') : 'N/A'); ?></div>
                                                <?php endif; ?>
                                                <?php if($booking->dropoff_lat && $booking->dropoff_lng): ?>
                                                    <div class="text-muted small">Coordinates: <?php echo e(number_format($booking->dropoff_lat, 6)); ?>, <?php echo e(number_format($booking->dropoff_lng, 6)); ?></div>
                                                <?php endif; ?>
                                                <?php if(isset($booking->dropoff_instructions)): ?>
                                                    <div class="mt-2 p-2 bg-light rounded small">
                                                        <i class="fas fa-info-circle me-1"></i> <?php echo e($booking->dropoff_instructions); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="booking-info-label">Service Duration</div>
                                    <div class="booking-info-value">
                                        <div class="d-flex align-items-start">
                                            <div class="location-icon me-2">
                                                <i class="fas fa-clock text-warning"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($booking->duration_hours ?? 'N/A'); ?> hours</div>
                                                <div class="text-muted small">Hourly service at pickup location</div>
                                                <?php if(isset($booking->service_instructions)): ?>
                                                    <div class="mt-2 p-2 bg-light rounded small">
                                                        <i class="fas fa-info-circle me-1"></i> <?php echo e($booking->service_instructions); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="route-stat-card p-3 bg-light rounded text-center">
                                    <div class="route-stat-icon mb-2">
                                        <i class="fas fa-road fa-2x text-primary"></i>
                                    </div>
                                    <?php
                                        $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                                        $bookingDistance = $calculateBookingDistance($booking);
                                        $displayDistance = $bookingDistance;
                                        $unitLabel = 'km';

                                        if ($distanceUnit === 'miles') {
                                            $displayDistance = $bookingDistance * 0.621371; // Convert km to miles
                                            $unitLabel = 'mi';
                                        }

                                        // Show "N/A" for hourly bookings or when no distance available
                                        $distanceText = ($booking->booking_type === 'hourly' || $bookingDistance <= 0) ? 'N/A' : number_format((float)$displayDistance, 1) . ' ' . $unitLabel;
                                    ?>
                                    <div class="route-stat-value fw-bold"><?php echo e($distanceText); ?></div>
                                    <div class="route-stat-label text-muted small">Total Distance</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="route-stat-card p-3 bg-light rounded text-center">
                                    <div class="route-stat-icon mb-2">
                                        <i class="fas fa-clock fa-2x text-warning"></i>
                                    </div>
                                    <?php
                                        $routeDurationText = 'N/A';
                                        if ($booking->duration_value && $booking->duration_value > 0) {
                                            $routeDurationText = floor($booking->duration_value / 60) . ' min';
                                        } elseif ($booking->booking_type === 'hourly' && $booking->duration_hours) {
                                            $routeDurationText = $booking->duration_hours . ' hours';
                                        } elseif ($bookingDistance > 0) {
                                            // Estimate duration based on distance (assuming 50 km/h average speed)
                                            $estimatedMinutes = ($bookingDistance / 50) * 60;
                                            $routeDurationText = floor($estimatedMinutes) . ' min (est.)';
                                        }
                                    ?>
                                    <div class="route-stat-value fw-bold"><?php echo e($routeDurationText); ?></div>
                                    <div class="route-stat-label text-muted small">Estimated Duration</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="route-stat-card p-3 bg-light rounded text-center">
                                    <div class="route-stat-icon mb-2">
                                        <i class="fas fa-gas-pump fa-2x text-danger"></i>
                                    </div>
                                    <?php
                                        // Use the helper function for distance calculation
                                        $fuelDistance = $calculateBookingDistance($booking);

                                        // Calculate fuel consumption (7L per 100km = 0.07L per km)
                                        $fuelConsumption = $fuelDistance * 0.07;
                                        $fuelText = ($booking->booking_type === 'hourly' || $fuelDistance <= 0) ? 'N/A' : number_format($fuelConsumption, 1) . ' L';
                                    ?>
                                    <div class="route-stat-value fw-bold"><?php echo e($fuelText); ?></div>
                                    <div class="route-stat-label text-muted small">Est. Fuel Consumption</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Tab -->
            <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
                <div class="booking-details-card card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i> Payment Information</h5>
                    </div>
            <div class="card-body">
                <?php if($booking->payments->isEmpty()): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> No payment records found for this booking.
                    </div>
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-money-bill-wave fa-3x text-muted"></i>
                        </div>
                        <h5>No Payments Recorded</h5>
                        <p class="text-muted">This booking doesn't have any payment records yet.</p>
                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#markAsPaidModal">
                            <i class="fas fa-plus-circle me-1"></i> Record a Payment
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table bookings-table">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $booking->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold"><?php echo e($payment->transaction_id); ?></span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$payment->amount, 2)); ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <?php if($payment->payment_method == 'paypal'): ?>
                                                <i class="fab fa-paypal me-1 text-primary"></i>
                                            <?php elseif($payment->payment_method == 'credit_card'): ?>
                                                <i class="fas fa-credit-card me-1 text-primary"></i>
                                            <?php elseif($payment->payment_method == 'cash'): ?>
                                                <i class="fas fa-money-bill-wave me-1 text-primary"></i>
                                            <?php else: ?>
                                                <i class="fas fa-money-check me-1 text-primary"></i>
                                            <?php endif; ?>
                                            <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?>

                                        </div>
                                    </td>
                                    <td>
                                        <span class="payment-badge payment-<?php echo e($payment->status); ?>">
                                            <?php echo e(ucfirst($payment->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div><i class="fas fa-calendar-alt me-1 text-primary"></i> <?php echo e($payment->created_at ? $payment->created_at->format('M d, Y') : 'N/A'); ?></div>
                                        <div><i class="fas fa-clock me-1 text-primary"></i> <?php echo e($payment->created_at ? $payment->created_at->format('h:i A') : 'N/A'); ?></div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="<?php echo e(route('admin.payments.show', $payment->id)); ?>" class="btn btn-sm btn-outline-primary" title="View Payment Details">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                            <?php if($payment->status == 'completed'): ?>
                                                <a href="<?php echo e(route('admin.payments.invoice', $payment->id)); ?>" class="icon-link" title="View Invoice">
                                                    <i class="fas fa-file-invoice me-1"></i> Invoice
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-4">
                        <a href="<?php echo e(route('admin.payments.index')); ?>" class="text-link">
                            <i class="fas fa-external-link-alt me-1"></i> View All Payments
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
            </div>

            <!-- History Tab -->
            <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                <div class="booking-details-card card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i> Booking History</h5>
                    </div>
                    <div class="card-body">
                        <?php if($booking->history && $booking->history->count() > 0): ?>
                            <div class="timeline">
                                <?php $__currentLoopData = $booking->history; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $historyItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="timeline-item">
                                        <div class="timeline-icon active">
                                            <?php if($historyItem->action == 'booking_created'): ?>
                                                <i class="fas fa-plus"></i>
                                            <?php elseif($historyItem->action == 'status_changed' || $historyItem->action == 'status_change'): ?>
                                                <i class="fas fa-exchange-alt"></i>
                                            <?php elseif($historyItem->action == 'driver_assigned'): ?>
                                                <i class="fas fa-user-plus"></i>
                                            <?php elseif($historyItem->action == 'payment_completed'): ?>
                                                <i class="fas fa-credit-card"></i>
                                            <?php elseif($historyItem->action == 'payment_refunded'): ?>
                                                <i class="fas fa-undo-alt"></i>
                                            <?php elseif($historyItem->action == 'booking_cancelled' || $historyItem->action == 'cancelled'): ?>
                                                <i class="fas fa-ban"></i>
                                            <?php elseif($historyItem->action == 'booking_completed'): ?>
                                                <i class="fas fa-flag-checkered"></i>
                                            <?php else: ?>
                                                <i class="fas fa-info-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">
                                                <?php if($historyItem->action == 'booking_created'): ?>
                                                    Booking Created
                                                <?php elseif($historyItem->action == 'status_changed' || $historyItem->action == 'status_change'): ?>
                                                    <?php
                                                        $details = is_string($historyItem->details) ? json_decode($historyItem->details, true) : $historyItem->details;
                                                        $newStatus = $details['new_status'] ?? 'Unknown';
                                                    ?>
                                                    Status Changed to <?php echo e(ucfirst($newStatus)); ?>

                                                <?php elseif($historyItem->action == 'driver_assigned'): ?>
                                                    Driver Assigned
                                                <?php elseif($historyItem->action == 'payment_completed'): ?>
                                                    Payment Completed
                                                <?php elseif($historyItem->action == 'payment_refunded'): ?>
                                                    Payment Refunded
                                                <?php elseif($historyItem->action == 'booking_cancelled' || $historyItem->action == 'cancelled'): ?>
                                                    Booking Cancelled
                                                <?php elseif($historyItem->action == 'booking_completed'): ?>
                                                    Booking Completed
                                                <?php else: ?>
                                                    <?php echo e(ucwords(str_replace('_', ' ', $historyItem->action))); ?>

                                                <?php endif; ?>
                                            </div>
                                            <div class="timeline-date"><?php echo e($historyItem->created_at ? $historyItem->created_at->format('M d, Y h:i A') : 'N/A'); ?></div>
                                            <div class="timeline-description">
                                                <?php
                                                    $details = is_string($historyItem->details) ? json_decode($historyItem->details, true) : $historyItem->details;
                                                    $details = $details ?: [];
                                                ?>
                                                <?php if($historyItem->action == 'booking_created'): ?>
                                                    Booking #<?php echo e($booking->booking_number); ?> was created.
                                                <?php elseif($historyItem->action == 'status_changed' || $historyItem->action == 'status_change'): ?>
                                                    Status changed from <strong><?php echo e(ucfirst($details['old_status'] ?? 'Unknown')); ?></strong> to <strong><?php echo e(ucfirst($details['new_status'] ?? 'Unknown')); ?></strong>.
                                                    <?php if(isset($details['reason'])): ?>
                                                        <br>Reason: <?php echo e($details['reason']); ?>

                                                    <?php endif; ?>
                                                <?php elseif($historyItem->action == 'driver_assigned'): ?>
                                                    <?php if(isset($details['driver_name'])): ?>
                                                        Driver <strong><?php echo e($details['driver_name']); ?></strong> was assigned to this booking.
                                                    <?php else: ?>
                                                        A driver was assigned to this booking.
                                                    <?php endif; ?>
                                                <?php elseif($historyItem->action == 'payment_completed'): ?>
                                                    Payment of <strong><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->amount, 2)); ?></strong> was completed.
                                                <?php elseif($historyItem->action == 'payment_refunded'): ?>
                                                    <?php if(isset($details['amount'])): ?>
                                                        Payment refund of <strong><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$details['amount'], 2)); ?></strong> was processed.
                                                    <?php else: ?>
                                                        Payment was refunded.
                                                    <?php endif; ?>
                                                    <?php if(isset($details['reason'])): ?>
                                                        <br>Reason: <?php echo e($details['reason']); ?>

                                                    <?php endif; ?>
                                                <?php elseif($historyItem->action == 'booking_cancelled' || $historyItem->action == 'cancelled'): ?>
                                                    Booking was cancelled.
                                                    <?php if(isset($details['reason'])): ?>
                                                        <br>Reason: <?php echo e($details['reason']); ?>

                                                    <?php endif; ?>
                                                <?php elseif($historyItem->action == 'booking_completed'): ?>
                                                    The service was successfully completed.
                                                <?php else: ?>
                                                    <?php if(!empty($details)): ?>
                                                        <ul class="mb-0">
                                                            <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <?php if(!is_array($value)): ?>
                                                                    <li><?php echo e(ucwords(str_replace('_', ' ', $key))); ?>: <?php echo e($value); ?></li>
                                                                <?php endif; ?>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </ul>
                                                    <?php else: ?>
                                                        Action performed by <?php echo e($historyItem->user ? $historyItem->user->name : 'System'); ?>.
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="timeline-footer mt-2 text-muted small">
                                                By: <?php echo e($historyItem->user ? $historyItem->user->name : 'System'); ?>

                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No history records found for this booking.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Booking Summary Card -->
        <div class="booking-details-card card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Summary</h5>
            </div>
            <div class="card-body">
                <div class="booking-summary">
                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Status</div>
                        <div class="summary-value">
                            <span class="booking-status status-<?php echo e(strtolower($booking->status)); ?>">
                                <?php echo e(ucfirst($booking->status)); ?>

                            </span>
                        </div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Booking Number</div>
                        <div class="summary-value fw-bold"><?php echo e($booking->booking_number); ?></div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Booking Type</div>
                        <div class="summary-value"><?php echo e(ucfirst(str_replace('_', ' ', $booking->booking_type))); ?></div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Created On</div>
                        <div class="summary-value"><?php echo e($booking->created_at ? $booking->created_at->format('M d, Y') : 'N/A'); ?></div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Pickup Date</div>
                        <div class="summary-value"><?php echo e($booking->pickup_date ? $booking->pickup_date->format('M d, Y h:i A') : 'N/A'); ?></div>
                    </div>

                    <?php if($booking->booking_type == 'return' && $booking->return_date): ?>
                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Return Date</div>
                        <div class="summary-value"><?php echo e($booking->return_date ? $booking->return_date->format('M d, Y h:i A') : 'N/A'); ?></div>
                    </div>
                    <?php endif; ?>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Distance</div>
                        <div class="summary-value">
                            <?php
                                $summaryDistance = $calculateBookingDistance($booking);
                                $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                                $displayDistance = $summaryDistance;
                                $unitLabel = 'km';

                                if ($distanceUnit === 'miles') {
                                    $displayDistance = $summaryDistance * 0.621371; // Convert km to miles
                                    $unitLabel = 'mi';
                                }

                                $summaryDistanceText = ($booking->booking_type === 'hourly' || $summaryDistance <= 0) ? 'N/A' : number_format((float)$displayDistance, 1) . ' ' . $unitLabel;
                            ?>
                            <?php echo e($summaryDistanceText); ?>

                        </div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Amount</div>
                        <div class="summary-value fw-bold text-primary"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->amount, 2)); ?></div>
                    </div>

                    <div class="summary-item d-flex justify-content-between mb-3">
                        <div class="summary-label">Payment Status</div>
                        <div class="summary-value">
                            <?php if($booking->payments->isEmpty()): ?>
                                <span class="badge bg-warning">Pending</span>
                            <?php else: ?>
                                <?php
                                    $latestPayment = $booking->payments->sortByDesc('created_at')->first();
                                ?>
                                <span class="badge bg-<?php echo e($latestPayment->status == 'completed' ? 'success' : ($latestPayment->status == 'failed' ? 'danger' : 'warning')); ?>">
                                    <?php echo e(ucfirst($latestPayment->status)); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="booking-actions mt-4">
                    <div class="d-grid gap-2">
                        <?php if($booking->status == 'pending'): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-status="confirmed">
                                <i class="fas fa-check-circle me-2"></i> Confirm Booking
                            </button>
                        <?php endif; ?>

                        <?php if(in_array($booking->status, ['pending', 'confirmed'])): ?>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-status="completed">
                                <i class="fas fa-flag-checkered me-2"></i> Mark as Completed
                            </button>

                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelBookingModal">
                                <i class="fas fa-ban me-2"></i> Cancel Booking
                            </button>
                        <?php endif; ?>

                        <?php if(!$booking->driver && in_array($booking->status, ['pending', 'confirmed'])): ?>
                            <a href="<?php echo e(route('admin.bookings.available-drivers', $booking->id)); ?>" class="btn btn-info">
                                <i class="fas fa-user-plus me-2"></i> Assign Driver
                            </a>
                        <?php endif; ?>

                        <!-- Payment Actions -->
                        <div class="payment-actions mt-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#markAsPaidModal">
                                <i class="fas fa-money-bill-wave me-2"></i> Mark as Paid
                            </button>

                            <?php if($booking->payments->isNotEmpty() && $booking->payments->where('status', 'completed')->count() > 0): ?>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#issueRefundModal">
                                    <i class="fas fa-undo-alt me-2"></i> Issue Refund
                                </button>
                            <?php endif; ?>
                        </div>

                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i> Print Booking Details
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Timeline Card -->
        <div class="booking-details-card card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i> Booking Timeline</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-icon <?php echo e($booking->created_at ? 'active' : ''); ?>">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Booking Created</div>
                            <div class="timeline-date"><?php echo e($booking->created_at ? $booking->created_at->format('M d, Y h:i A') : 'N/A'); ?></div>
                            <div class="timeline-description">
                                Booking #<?php echo e($booking->booking_number); ?> was created by <?php echo e($booking->user->name); ?>.
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-icon <?php echo e($booking->status != 'pending' ? 'active' : ''); ?>">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Booking Confirmed</div>
                            <div class="timeline-date">
                                <?php if($booking->status != 'pending'): ?>
                                    <?php echo e($booking->updated_at ? $booking->updated_at->format('M d, Y h:i A') : 'N/A'); ?>

                                <?php else: ?>
                                    Pending
                                <?php endif; ?>
                            </div>
                            <div class="timeline-description">
                                <?php if($booking->status != 'pending'): ?>
                                    Booking was confirmed and is ready for service.
                                <?php else: ?>
                                    Waiting for confirmation.
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-icon <?php echo e($booking->status == 'completed' ? 'active' : ''); ?>">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Service Completed</div>
                            <div class="timeline-date">
                                <?php if($booking->status == 'completed'): ?>
                                    <?php echo e($booking->updated_at ? $booking->updated_at->format('M d, Y h:i A') : 'N/A'); ?>

                                <?php else: ?>
                                    Pending
                                <?php endif; ?>
                            </div>
                            <div class="timeline-description">
                                <?php if($booking->status == 'completed'): ?>
                                    The service was successfully completed.
                                <?php else: ?>
                                    Waiting for service completion.
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('admin.bookings.update-status', $booking->id)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Booking Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="pending" <?php echo e($booking->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="confirmed" <?php echo e($booking->status == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                            <option value="assigned" <?php echo e($booking->status == 'assigned' ? 'selected' : ''); ?>>Assigned</option>
                            <option value="in_progress" <?php echo e($booking->status == 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                            <option value="completed" <?php echo e($booking->status == 'completed' ? 'selected' : ''); ?>>Completed</option>
                            <option value="cancelled" <?php echo e($booking->status == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3" id="statusNoteContainer" style="display: none;">
                        <label for="status_note" class="form-label">Status Note</label>
                        <textarea class="form-control" id="status_note" name="status_note" rows="3" placeholder="Add a note about this status change (optional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Mark as Paid Modal -->
<div class="modal fade" id="markAsPaidModal" tabindex="-1" aria-labelledby="markAsPaidModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('admin.payments.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
                <div class="modal-header">
                    <h5 class="modal-title" id="markAsPaidModalLabel">Mark Booking as Paid</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> You are about to manually record a payment for this booking.
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?></span>
                            <input type="number" class="form-control" id="amount" name="amount" value="<?php echo e($booking->amount); ?>" step="0.01" min="0" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">Cash</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="paypal">PayPal</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="transaction_id" class="form-label">Transaction ID</label>
                        <input type="text" class="form-control" id="transaction_id" name="transaction_id" placeholder="Enter transaction ID (optional)">
                        <div class="form-text">Leave blank for cash payments or if no transaction ID is available.</div>
                    </div>

                    <div class="mb-3">
                        <label for="payment_note" class="form-label">Payment Note</label>
                        <textarea class="form-control" id="payment_note" name="payment_note" rows="3" placeholder="Add a note about this payment (optional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Record Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Issue Refund Modal -->
<div class="modal fade" id="issueRefundModal" tabindex="-1" aria-labelledby="issueRefundModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('admin.payments.process-refund')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="booking_id" value="<?php echo e($booking->id); ?>">
                <div class="modal-header">
                    <h5 class="modal-title" id="issueRefundModalLabel">Issue Refund</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> You are about to issue a refund for this booking.
                    </div>

                    <?php if($booking->payments->isEmpty()): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i> No payments found for this booking. Cannot issue a refund.
                        </div>
                    <?php else: ?>
                        <div class="mb-3">
                            <label for="payment_id" class="form-label">Payment to Refund</label>
                            <select class="form-select" id="payment_id" name="payment_id" required>
                                <?php $__currentLoopData = $booking->payments->where('status', 'completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($payment->id); ?>">
                                        <?php echo e($payment->created_at ? $payment->created_at->format('M d, Y') : 'N/A'); ?> -
                                        <?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$payment->amount, 2)); ?> -
                                        <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="refund_amount" class="form-label">Refund Amount</label>
                            <div class="input-group">
                                <span class="input-group-text"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?></span>
                                <input type="number" class="form-control" id="refund_amount" name="refund_amount" value="<?php echo e($booking->amount); ?>" step="0.01" min="0" required>
                            </div>
                            <div class="form-text">Enter the amount to refund. This can be partial or full.</div>
                        </div>

                        <div class="mb-3">
                            <label for="refund_reason" class="form-label">Refund Reason</label>
                            <textarea class="form-control" id="refund_reason" name="refund_reason" rows="3" required placeholder="Explain why this refund is being issued"></textarea>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <?php if(!$booking->payments->isEmpty()): ?>
                        <button type="submit" class="btn btn-danger">Issue Refund</button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign Driver Modal -->
<div class="modal fade" id="assignDriverModal" tabindex="-1" aria-labelledby="assignDriverModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('admin.bookings.assign-driver', $booking->id)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="assignDriverModalLabel">Assign Driver</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="driver_id" class="form-label">Select Driver</label>
                        <select class="form-select" id="driver_id" name="driver_id" required>
                            <option value="">-- Select a Driver --</option>
                            <?php $__currentLoopData = $availableDrivers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($driver->id); ?>" <?php echo e($booking->driver_id == $driver->id ? 'selected' : ''); ?>>
                                    <?php echo e($driver->name); ?> (<?php echo e($driver->email); ?>)
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Driver</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div class="modal fade" id="cancelBookingModal" tabindex="-1" aria-labelledby="cancelBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('admin.bookings.cancel', $booking->id)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelBookingModalLabel">Cancel Booking</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> Are you sure you want to cancel this booking? This action cannot be undone.
                    </div>
                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Cancellation Reason</label>
                        <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Cancel Booking</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php
    $googleMapsApiKey = \App\Services\SettingsService::get('google_maps_api_key', config('services.google_maps.key', ''));
?>

<?php if($googleMapsApiKey): ?>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places,geometry&callback=initMap" async defer onerror="handleGoogleMapsError()"></script>
<?php endif; ?>

<script>
    let map;
    let directionsService;
    let directionsRenderer;

    // Global error display function
    function showMapError(title, status, pickup = '', dropoff = '') {
        const mapContainer = document.getElementById('booking-map');
        if (!mapContainer) return;

        let actionButton = '';
        let helpText = '';

        if (status === 'REQUEST_DENIED' || status === 'NO_API_KEY' || status === 'SCRIPT_LOAD_ERROR') {
            helpText = `
                <div class="alert alert-warning mt-3">
                    <strong>Google Maps API Configuration Required:</strong><br>
                    • Check that your Google Maps API key is valid<br>
                    • Enable the Directions API in Google Cloud Console<br>
                    • Enable the Geocoding API in Google Cloud Console<br>
                    • Ensure billing is enabled for your Google Cloud project<br>
                    • Verify API key restrictions allow this domain<br>
                    • Check that the API key has sufficient quota
                </div>
            `;
            actionButton = `
                <a href="<?php echo e(route('admin.settings.index')); ?>" class="btn btn-sm btn-warning mt-2">
                    <i class="fas fa-cog me-1"></i> Configure API Settings
                </a>
            `;
        } else if (status !== 'API_LOAD_ERROR' && status !== 'MISSING_ADDRESSES') {
            actionButton = `
                <button class="btn btn-sm btn-primary mt-2" onclick="calculateAndDisplayRoute()">
                    <i class="fas fa-redo me-1"></i> Retry
                </button>
            `;
        }

        mapContainer.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>${title}</h5>
                <p class="text-muted">Error Code: ${status}</p>
                ${pickup ? `<p><strong>Pickup:</strong> ${pickup}</p>` : ''}
                ${dropoff ? `<p><strong>Dropoff:</strong> ${dropoff}</p>` : ''}
                ${helpText}
                ${actionButton}
            </div>
        `;
    }

    // Handle Google Maps API loading errors
    function handleGoogleMapsError() {
        console.error('Failed to load Google Maps API');
        showMapError('Google Maps API Failed to Load', 'SCRIPT_LOAD_ERROR');
    }

    // Fallback initialization if no API key is available
    <?php if(!$googleMapsApiKey): ?>
    document.addEventListener('DOMContentLoaded', function() {
        showMapError('Google Maps API Key Required', 'NO_API_KEY');
    });
    <?php endif; ?>

    function initMap() {
        // Check if Google Maps API is loaded
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            showMapError('Google Maps API Failed to Load', 'API_LOAD_ERROR');
            return;
        }

        // Check if map container exists
        const mapContainer = document.getElementById('booking-map');
        if (!mapContainer) {
            console.error('Map container not found');
            return;
        }

        try {
            // Initialize map
            map = new google.maps.Map(mapContainer, {
                zoom: 12,
                center: { lat: 51.5074, lng: -0.1278 }, // Default to London
                mapTypeControl: true,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
                styles: [
                    {
                        "featureType": "poi",
                        "stylers": [
                            { "visibility": "off" }
                        ]
                    }
                ]
            });
        } catch (error) {
            console.error('Error initializing map:', error);
            showMapError('Map Initialization Failed', error.message);
            return;
        }

        // Initialize directions service
        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            map: map,
            suppressMarkers: false,
            polylineOptions: {
                strokeColor: '#4e73df',
                strokeWeight: 5,
                strokeOpacity: 0.7
            }
        });

        // Get route
        calculateAndDisplayRoute();
    }

    // Global function to calculate and display route
    function calculateAndDisplayRoute() {
        // Check if required objects exist
        if (!directionsService || !directionsRenderer) {
            console.error('Directions service not initialized');
            showMapError('Map Services Not Available', 'SERVICE_NOT_INITIALIZED');
            return;
        }

        const pickupAddress = "<?php echo e(addslashes($booking->pickup_address)); ?>";
        const dropoffAddress = "<?php echo e(addslashes($booking->dropoff_address ?? '')); ?>";
        const bookingType = "<?php echo e($booking->booking_type); ?>";

        if (pickupAddress && (dropoffAddress || bookingType === 'hourly')) {
            if (bookingType === 'hourly') {
                // For hourly bookings, just show pickup location
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ address: pickupAddress }, function(results, status) {
                    if (status === 'OK') {
                        map.setCenter(results[0].geometry.location);
                        map.setZoom(15);

                        new google.maps.Marker({
                            map: map,
                            position: results[0].geometry.location,
                            title: 'Pickup Location',
                            icon: {
                                url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
                            }
                        });
                    } else {
                        showMapError('Could not geocode pickup address', status);
                    }
                });
            } else {
                // For other bookings, show route
                directionsService.route({
                    origin: pickupAddress,
                    destination: dropoffAddress,
                    travelMode: google.maps.TravelMode.DRIVING,
                    provideRouteAlternatives: false,
                    avoidTolls: false,
                    avoidHighways: false,
                    optimizeWaypoints: true
                }, function(response, status) {
                    if (status === 'OK') {
                        directionsRenderer.setDirections(response);

                        // Get route details
                        const route = response.routes[0];
                        const leg = route.legs[0];

                        // Update distance and duration if needed
                        console.log('Distance: ' + leg.distance.text);
                        console.log('Duration: ' + leg.duration.text);
                    } else {
                        console.error('Directions service failed:', status);
                        let errorMessage = 'Could not load route';

                        switch(status) {
                            case 'REQUEST_DENIED':
                                errorMessage = 'Google Maps API key is invalid or missing required permissions';
                                break;
                            case 'OVER_QUERY_LIMIT':
                                errorMessage = 'Google Maps API quota exceeded';
                                break;
                            case 'ZERO_RESULTS':
                                errorMessage = 'No route found between the specified locations';
                                break;
                            case 'UNKNOWN_ERROR':
                                errorMessage = 'Unknown error occurred. Please try again';
                                break;
                            case 'NOT_FOUND':
                                errorMessage = 'One or both locations could not be found';
                                break;
                            case 'INVALID_REQUEST':
                                errorMessage = 'Invalid route request';
                                break;
                            default:
                                errorMessage = `Route calculation failed: ${status}`;
                        }

                        showMapError(errorMessage, status, pickupAddress, dropoffAddress);
                    }
                });
            }
        } else {
            showMapError('Missing Address Information', 'MISSING_ADDRESSES');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching - reinitialize map when route tab is shown
        const routeTab = document.getElementById('route-tab');
        if (routeTab) {
            routeTab.addEventListener('shown.bs.tab', function (e) {
                setTimeout(function() {
                    if (map) {
                        google.maps.event.trigger(map, 'resize');
                    }
                }, 100);
            });
        }

        // Handle status dropdown in actions menu
        const statusButtons = document.querySelectorAll('[data-status]');
        const statusSelect = document.getElementById('status');
        const statusNoteContainer = document.getElementById('statusNoteContainer');

        // Show/hide status note field based on status
        if (statusSelect) {
            statusSelect.addEventListener('change', function() {
                const status = this.value;
                if (status === 'cancelled' || status === 'completed') {
                    statusNoteContainer.style.display = 'block';
                } else {
                    statusNoteContainer.style.display = 'none';
                }
            });
        }

        statusButtons.forEach(button => {
            button.addEventListener('click', function() {
                const status = this.getAttribute('data-status');
                statusSelect.value = status;

                // Trigger change event to show/hide note field
                const event = new Event('change');
                statusSelect.dispatchEvent(event);
            });
        });

        // Handle cancel booking form submission with SweetAlert2
        const cancelBookingForm = document.querySelector('#cancelBookingModal form');
        if (cancelBookingForm) {
            // Define the submit handler function
            function cancelSubmitHandler(e) {
                e.preventDefault();

                const reason = document.getElementById('cancellation_reason').value.trim();
                if (!reason) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please provide a cancellation reason',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You are about to cancel this booking. This action cannot be undone!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, cancel booking',
                    cancelButtonText: 'No, keep booking'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Remove the event listener and submit the form
                        cancelBookingForm.removeEventListener('submit', cancelSubmitHandler);
                        cancelBookingForm.submit();
                    }
                });
            }

            // Add the event listener
            cancelBookingForm.addEventListener('submit', cancelSubmitHandler);
        }

        // Handle update status form submission with SweetAlert2
        const updateStatusForm = document.querySelector('#updateStatusModal form');
        if (updateStatusForm) {
            // Define the submit handler function
            function updateStatusSubmitHandler(e) {
                e.preventDefault();

                const status = document.getElementById('status').value;
                let confirmMessage = 'You are about to update the booking status to ' + status + '.';
                let confirmIcon = 'info';

                if (status === 'cancelled') {
                    confirmMessage = 'You are about to cancel this booking. Consider using the Cancel Booking option instead to provide a reason.';
                    confirmIcon = 'warning';
                } else if (status === 'completed') {
                    confirmMessage = 'You are about to mark this booking as completed. Make sure all services have been delivered.';
                }

                Swal.fire({
                    title: 'Update Status?',
                    text: confirmMessage,
                    icon: confirmIcon,
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, update status'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Remove the event listener and submit the form
                        updateStatusForm.removeEventListener('submit', updateStatusSubmitHandler);
                        updateStatusForm.submit();
                    }
                });
            }

            // Add the event listener
            updateStatusForm.addEventListener('submit', updateStatusSubmitHandler);
        }

        // Handle mark as paid form submission
        const markAsPaidForm = document.querySelector('#markAsPaidModal form');
        if (markAsPaidForm) {
            // Define the submit handler function
            function submitHandler(e) {
                e.preventDefault();

                const amount = parseFloat(document.getElementById('amount').value);
                if (isNaN(amount) || amount <= 0) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please enter a valid amount greater than zero',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Record Payment?',
                    text: 'You are about to manually record a payment for this booking.',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, record payment'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Remove the event listener and submit the form
                        markAsPaidForm.removeEventListener('submit', submitHandler);
                        markAsPaidForm.submit();
                    }
                });
            }

            // Add the event listener
            markAsPaidForm.addEventListener('submit', submitHandler);
        }

        // Handle refund form submission
        const refundForm = document.querySelector('#issueRefundModal form');
        if (refundForm) {
            // Define the submit handler function
            function refundSubmitHandler(e) {
                e.preventDefault();

                const refundAmount = parseFloat(document.getElementById('refund_amount')?.value);
                const refundReason = document.getElementById('refund_reason')?.value.trim();

                if (isNaN(refundAmount) || refundAmount <= 0) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please enter a valid refund amount greater than zero',
                        icon: 'error'
                    });
                    return;
                }

                if (!refundReason) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please provide a reason for the refund',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Issue Refund?',
                    text: 'You are about to issue a refund for this booking. This action cannot be undone!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, issue refund',
                    cancelButtonText: 'No, cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Remove the event listener and submit the form
                        refundForm.removeEventListener('submit', refundSubmitHandler);
                        refundForm.submit();
                    }
                });
            }

            // Add the event listener
            refundForm.addEventListener('submit', refundSubmitHandler);
        }

        // Add animation classes to elements
        document.querySelectorAll('.booking-details-card').forEach(function(card, index) {
            card.classList.add('animate__animated', 'animate__fadeIn');
            card.style.animationDelay = (index * 0.1) + 's';
        });

        document.querySelectorAll('.timeline-item').forEach(function(item, index) {
            item.classList.add('animate__animated', 'animate__fadeInLeft');
            item.style.animationDelay = (index * 0.1) + 's';
        });

        document.querySelectorAll('.fare-item').forEach(function(item, index) {
            item.classList.add('animate__animated', 'animate__fadeInUp');
            item.style.animationDelay = (index * 0.05) + 's';
        });

        // Communication functions
        window.sendSMS = function(phoneNumber, messageType) {
            if (!phoneNumber) {
                Swal.fire({
                    title: 'Error',
                    text: 'No phone number available for this client.',
                    icon: 'error'
                });
                return;
            }

            let message = '';
            const bookingNumber = '<?php echo e($booking->booking_number); ?>';
            const pickupDate = '<?php echo e($booking->pickup_date ? $booking->pickup_date->format("M d, Y h:i A") : "N/A"); ?>';

            switch(messageType) {
                case 'booking_confirmation':
                    message = `Hi! Your booking #${bookingNumber} has been confirmed for ${pickupDate}. Thank you for choosing YNR Cars!`;
                    break;
                case 'booking_reminder':
                    message = `Reminder: Your booking #${bookingNumber} is scheduled for ${pickupDate}. Our driver will contact you shortly.`;
                    break;
                case 'driver_assigned':
                    message = `Your driver has been assigned for booking #${bookingNumber}. They will contact you before pickup.`;
                    break;
                default:
                    message = `Update regarding your booking #${bookingNumber}. Please contact us for more details.`;
            }

            // Open SMS app with pre-filled message
            const smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;
            window.open(smsUrl, '_blank');
        };

        window.sendEmail = function(emailAddress, messageType) {
            if (!emailAddress) {
                Swal.fire({
                    title: 'Error',
                    text: 'No email address available for this client.',
                    icon: 'error'
                });
                return;
            }

            let subject = '';
            let body = '';
            const bookingNumber = '<?php echo e($booking->booking_number); ?>';
            const pickupDate = '<?php echo e($booking->pickup_date ? $booking->pickup_date->format("M d, Y h:i A") : "N/A"); ?>';
            const clientName = '<?php echo e($booking->user->name ?? "Valued Customer"); ?>';

            switch(messageType) {
                case 'booking_confirmation':
                    subject = `Booking Confirmation - #${bookingNumber}`;
                    body = `Dear ${clientName},\n\nYour booking #${bookingNumber} has been confirmed for ${pickupDate}.\n\nPickup Address: <?php echo e($booking->pickup_address); ?>\nDropoff Address: <?php echo e($booking->dropoff_address ?? "N/A"); ?>\nAmount: <?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format((float)$booking->amount, 2)); ?>\n\nThank you for choosing YNR Cars!\n\nBest regards,\nYNR Cars Team`;
                    break;
                case 'booking_reminder':
                    subject = `Booking Reminder - #${bookingNumber}`;
                    body = `Dear ${clientName},\n\nThis is a reminder that your booking #${bookingNumber} is scheduled for ${pickupDate}.\n\nOur driver will contact you shortly before pickup.\n\nBest regards,\nYNR Cars Team`;
                    break;
                case 'driver_assigned':
                    subject = `Driver Assigned - #${bookingNumber}`;
                    body = `Dear ${clientName},\n\nA driver has been assigned to your booking #${bookingNumber}.\n\nThey will contact you before pickup time.\n\nBest regards,\nYNR Cars Team`;
                    break;
                default:
                    subject = `Booking Update - #${bookingNumber}`;
                    body = `Dear ${clientName},\n\nWe have an update regarding your booking #${bookingNumber}.\n\nPlease contact us for more details.\n\nBest regards,\nYNR Cars Team`;
            }

            // Open email client with pre-filled content
            const emailUrl = `mailto:${emailAddress}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            window.open(emailUrl, '_blank');
        };

        // Print functionality
        document.querySelectorAll('[onclick="window.print()"]').forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Show all tabs before printing
                document.querySelectorAll('.tab-pane').forEach(function(tab) {
                    tab.classList.add('print-show');
                });

                // Print after a short delay to ensure styles are applied
                setTimeout(function() {
                    window.print();

                    // Reset tabs after printing
                    setTimeout(function() {
                        document.querySelectorAll('.tab-pane').forEach(function(tab) {
                            tab.classList.remove('print-show');
                        });
                    }, 500);
                }, 300);
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/bookings/show.blade.php ENDPATH**/ ?>