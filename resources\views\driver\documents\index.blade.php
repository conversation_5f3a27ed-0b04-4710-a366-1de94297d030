@extends('layouts.driver')

@section('title', 'My Documents')

@section('styles')
<style>
    /* Document Styles */
    .document-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }
    
    .document-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }
    
    .document-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }
    
    .document-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }
    
    .document-card .card-body {
        padding: 25px;
    }
    
    .document-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(238, 57, 61, 0.1);
        color: #ee393d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 1.5rem;
    }
    
    .document-list-item {
        border-radius: 10px !important;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05) !important;
    }
    
    .document-list-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    
    .document-list-item h6 {
        font-weight: 600;
        color: #343a40;
    }
    
    .document-list-item small {
        font-size: 0.8rem;
    }
    
    .document-badge {
        font-size: 0.7rem;
        padding: 3px 8px;
        border-radius: 20px;
    }
    
    .document-alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    
    .document-alert i {
        font-size: 1.5rem;
        margin-right: 15px;
    }
    
    .document-alert-danger {
        background-color: rgba(248, 215, 218, 0.5);
        border-left: 4px solid #dc3545;
    }
    
    .document-alert-warning {
        background-color: rgba(255, 243, 205, 0.5);
        border-left: 4px solid #ffc107;
    }
    
    .document-alert-info {
        background-color: rgba(207, 244, 252, 0.5);
        border-left: 4px solid #0dcaf0;
    }
    
    .document-alert-success {
        background-color: rgba(209, 231, 221, 0.5);
        border-left: 4px solid #198754;
    }
    
    .document-upload-card {
        border: 2px dashed rgba(0,0,0,0.1);
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .document-upload-card:hover {
        border-color: #ee393d;
        background-color: rgba(238, 57, 61, 0.02);
    }
    
    .document-upload-icon {
        font-size: 3rem;
        color: #ee393d;
        margin-bottom: 15px;
    }
    
    .document-preview {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .document-preview-container {
        position: relative;
    }
    
    .document-preview-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        gap: 5px;
    }
    
    .document-preview-actions .btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        background-color: rgba(255, 255, 255, 0.8);
        border: none;
    }
    
    .document-preview-actions .btn:hover {
        background-color: #fff;
    }
    
    .document-status {
        position: absolute;
        bottom: 20px;
        left: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.7rem;
        font-weight: 600;
        background-color: rgba(255, 255, 255, 0.8);
    }
    
    .document-status.verified {
        color: #198754;
    }
    
    .document-status.pending {
        color: #ffc107;
    }
    
    .document-status.expired {
        color: #dc3545;
    }
    
    .document-status i {
        margin-right: 5px;
    }
    
    /* Responsive Styles */
    @media (max-width: 992px) {
        .document-card {
            margin-bottom: 20px;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt me-2 text-primary"></i> My Documents
        </h1>
        <a href="#uploadDocumentModal" data-bs-toggle="modal" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i> Upload New Document
        </a>
    </div>

    <!-- Document Status Summary -->
    <div class="row mb-4">
        <div class="col-md-12">
            @php
                $totalDocuments = $documents->count();
                $verifiedDocuments = $documents->where('is_verified', true)->count();
                $pendingDocuments = $documents->where('is_verified', false)->count();
                $expiredDocuments = $documents->filter(function($doc) {
                    return $doc->isExpired();
                })->count();
                $expiringDocuments = $documents->filter(function($doc) {
                    return !$doc->isExpired() && $doc->isExpiringSoon();
                })->count();
            @endphp

            @if($expiredDocuments > 0)
                <div class="document-alert document-alert-danger mb-3">
                    <i class="fas fa-exclamation-circle text-danger"></i>
                    <div>
                        <h6 class="mb-1 fw-bold">{{ $expiredDocuments }} Document(s) Expired</h6>
                        <p class="mb-0">Please update your expired documents as soon as possible to maintain your driver status.</p>
                    </div>
                </div>
            @elseif($expiringDocuments > 0)
                <div class="document-alert document-alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    <div>
                        <h6 class="mb-1 fw-bold">{{ $expiringDocuments }} Document(s) Expiring Soon</h6>
                        <p class="mb-0">Please update these documents before they expire to avoid any service interruptions.</p>
                    </div>
                </div>
            @elseif($pendingDocuments > 0)
                <div class="document-alert document-alert-info mb-3">
                    <i class="fas fa-info-circle text-info"></i>
                    <div>
                        <h6 class="mb-1 fw-bold">{{ $pendingDocuments }} Document(s) Pending Verification</h6>
                        <p class="mb-0">Your documents are being reviewed by our team. This usually takes 1-2 business days.</p>
                    </div>
                </div>
            @elseif($totalDocuments > 0)
                <div class="document-alert document-alert-success mb-3">
                    <i class="fas fa-check-circle text-success"></i>
                    <div>
                        <h6 class="mb-1 fw-bold">All Documents Verified</h6>
                        <p class="mb-0">All your documents are up to date and verified. You're good to go!</p>
                    </div>
                </div>
            @else
                <div class="document-alert document-alert-info mb-3">
                    <i class="fas fa-info-circle text-info"></i>
                    <div>
                        <h6 class="mb-1 fw-bold">No Documents Uploaded</h6>
                        <p class="mb-0">Please upload all required documents to start accepting rides.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Document Categories -->
    <div class="row">
        <!-- Driver License -->
        <div class="col-md-6 mb-4">
            <div class="document-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-id-card me-2"></i> Driver License</h5>
                    <a href="#uploadDocumentModal" data-bs-toggle="modal" data-document-type="Driver License" class="btn btn-sm btn-outline-primary upload-document-btn">
                        <i class="fas fa-upload me-1"></i> Upload
                    </a>
                </div>
                <div class="card-body">
                    @php
                        $driverLicenseDocs = $documents->filter(function($doc) {
                            return $doc->document_type == 'Driver License';
                        });
                    @endphp

                    @if($driverLicenseDocs->isEmpty())
                        <div class="text-center py-4">
                            <div class="document-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <h6 class="mt-3 mb-2">No Driver License Uploaded</h6>
                            <p class="text-muted mb-4">Please upload your driver license to continue</p>
                            <a href="#uploadDocumentModal" data-bs-toggle="modal" data-document-type="Driver License" class="btn btn-primary upload-document-btn">
                                <i class="fas fa-upload me-1"></i> Upload Driver License
                            </a>
                        </div>
                    @else
                        <div class="row">
                            @foreach($driverLicenseDocs as $document)
                                <div class="col-md-6 mb-3">
                                    <div class="document-preview-container">
                                        @if(in_array(pathinfo($document->file_path, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif']))
                                            <img src="{{ asset('storage/' . $document->file_path) }}" alt="Driver License" class="document-preview">
                                        @else
                                            <div class="document-preview d-flex align-items-center justify-content-center bg-light">
                                                <i class="fas fa-file-pdf fa-3x text-danger"></i>
                                            </div>
                                        @endif
                                        
                                        <div class="document-preview-actions">
                                            <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank" class="btn btn-light" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('driver.documents.destroy', $document->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-light" title="Delete" onclick="return confirm('Are you sure you want to delete this document?')">
                                                    <i class="fas fa-trash text-danger"></i>
                                                </button>
                                            </form>
                                        </div>
                                        
                                        @if($document->is_verified)
                                            <div class="document-status verified">
                                                <i class="fas fa-check-circle"></i> Verified
                                            </div>
                                        @elseif($document->isExpired())
                                            <div class="document-status expired">
                                                <i class="fas fa-exclamation-circle"></i> Expired
                                            </div>
                                        @else
                                            <div class="document-status pending">
                                                <i class="fas fa-clock"></i> Pending
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <div class="mt-2">
                                        <h6 class="mb-1">Driver License</h6>
                                        <div class="small text-muted mb-1">Uploaded: {{ $document->created_at->format('M d, Y') }}</div>
                                        @if($document->expiry_date)
                                            <div class="small {{ $document->isExpired() ? 'text-danger' : ($document->isExpiringSoon() ? 'text-warning' : 'text-success') }}">
                                                Expires: {{ $document->expiry_date->format('M d, Y') }}
                                                @if($document->isExpired())
                                                    (Expired)
                                                @elseif($document->isExpiringSoon())
                                                    (Expiring Soon)
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Document Modal -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadDocumentModalLabel">Upload Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('driver.documents.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="document_type" class="form-label">Document Type</label>
                        <select class="form-select" id="document_type" name="document_type" required>
                            <option value="">Select Document Type</option>
                            <option value="Driver License">Driver License</option>
                            <option value="Driver PCO License">Driver PCO License</option>
                            <option value="Vehicle PCO License">Vehicle PCO License</option>
                            <option value="Insurance">Insurance</option>
                            <option value="MOT Certificate">MOT Certificate</option>
                            <option value="V5C Logbook">V5C Logbook</option>
                            <option value="Vehicle Photos">Vehicle Photos</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="document_file" class="form-label">Document File</label>
                        <input type="file" class="form-control" id="document_file" name="document_file" required>
                        <div class="form-text">Accepted formats: PDF, JPG, PNG (Max: 5MB)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date (if applicable)</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Document</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Set document type in modal when clicking upload button
    document.querySelectorAll('.upload-document-btn').forEach(button => {
        button.addEventListener('click', function() {
            const documentType = this.getAttribute('data-document-type');
            if (documentType) {
                document.getElementById('document_type').value = documentType;
            }
        });
    });
</script>
@endsection
