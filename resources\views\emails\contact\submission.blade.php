@extends('emails.layouts.master')

@section('title', 'New Contact Form Submission')

@section('content')
<h2>New Contact Form Submission</h2>

<p>You have received a new contact form submission from your website.</p>

<div class="booking-details">
    <h3>Contact Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Name:</span>
        <span class="detail-value">{{ $contactData['name'] ?? 'Not provided' }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Email:</span>
        <span class="detail-value">{{ $contactData['email'] ?? 'Not provided' }}</span>
    </div>
    
    @if(isset($contactData['phone']))
    <div class="detail-row">
        <span class="detail-label">Phone:</span>
        <span class="detail-value">{{ $contactData['phone'] }}</span>
    </div>
    @endif
    
    @if(isset($contactData['subject']))
    <div class="detail-row">
        <span class="detail-label">Subject:</span>
        <span class="detail-value">{{ $contactData['subject'] }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Submitted:</span>
        <span class="detail-value">{{ now()->format('F j, Y \a\t g:i A') }}</span>
    </div>
</div>

@if(isset($contactData['message']))
<div style="margin: 20px 0;">
    <h3>Message:</h3>
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #ee393d;">
        {!! nl2br(e($contactData['message'])) !!}
    </div>
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="mailto:{{ $contactData['email'] ?? '' }}" class="btn">Reply to Customer</a>
</div>

<p><strong>Note:</strong> Please respond to this inquiry promptly to maintain excellent customer service.</p>

<p>Best regards,<br>
{{ $companyName }} Website</p>
@endsection
