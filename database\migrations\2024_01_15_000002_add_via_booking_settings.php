<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add via booking settings
        $settings = [
            [
                'key' => 'via_booking_enabled',
                'value' => 'true',
                'group' => 'booking',
                'type' => 'boolean',
                'is_public' => true
            ],
            [
                'key' => 'max_via_points',
                'value' => '5',
                'group' => 'booking',
                'type' => 'number',
                'is_public' => true
            ],
            [
                'key' => 'via_point_surcharge',
                'value' => '5.00',
                'group' => 'pricing',
                'type' => 'decimal',
                'is_public' => true
            ],
            [
                'key' => 'max_via_surcharge',
                'value' => '25.00',
                'group' => 'pricing',
                'type' => 'decimal',
                'is_public' => true
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $settingKeys = [
            'via_booking_enabled',
            'max_via_points',
            'via_point_surcharge',
            'max_via_surcharge'
        ];

        Setting::whereIn('key', $settingKeys)->delete();
    }
};
