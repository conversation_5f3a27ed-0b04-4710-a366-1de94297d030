<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Show the driver dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $driverId = Auth::id();

        // Get total trips
        $totalTrips = Booking::where('driver_id', $driverId)
            ->whereIn('status', ['assigned', 'in_progress', 'completed'])
            ->count();

        // Get upcoming trips
        $upcomingTrips = Booking::where('driver_id', $driverId)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->count();

        // Get completed rides count
        $completedRidesCount = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->count();

        // Get pending rides count
        $pendingRidesCount = Booking::where('driver_id', $driverId)
            ->where('status', 'assigned')
            ->count();

        // Get total distance (assuming we store distance in bookings)
        $totalDistance = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->sum('distance');

        // Get total earnings
        $totalEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->sum('amount');

        // Get current month earnings
        $currentMonthEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereMonth('updated_at', Carbon::now()->month)
            ->whereYear('updated_at', Carbon::now()->year)
            ->sum('amount');

        // Get current week earnings
        $currentWeekEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereBetween('updated_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->sum('amount');

        // Get monthly earnings for chart
        $monthlyEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereYear('updated_at', Carbon::now()->year)
            ->select(
                DB::raw('MONTH(updated_at) as month'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray();

        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlyEarnings[$i])) {
                $monthlyEarnings[$i] = 0;
            }
        }

        // Sort by month
        ksort($monthlyEarnings);

        // Get recent earnings
        $recentEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->orderBy('updated_at', 'desc')
            ->take(5)
            ->get();

        // Get upcoming rides
        $upcomingRides = Booking::where('driver_id', $driverId)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->with(['user', 'vehicle'])
            ->orderBy('pickup_date', 'asc')
            ->take(5)
            ->get();

        // Get available rides
        $availableRides = Booking::where('status', 'confirmed')
            ->whereNull('driver_id')
            ->with('vehicle')
            ->orderBy('pickup_date', 'asc')
            ->take(5)
            ->get();

        return view('driver.dashboard', compact(
            'totalTrips',
            'upcomingTrips',
            'completedRidesCount',
            'pendingRidesCount',
            'totalDistance',
            'totalEarnings',
            'currentMonthEarnings',
            'currentWeekEarnings',
            'monthlyEarnings',
            'recentEarnings',
            'upcomingRides',
            'availableRides'
        ));
    }
}
