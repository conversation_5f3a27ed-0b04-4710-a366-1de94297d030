<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Payment;
use App\Notifications\BookingStatusChanged;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:client']);
    }

    /**
     * Display a listing of the client's bookings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $status = $request->input('status');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        // Build query
        $query = Booking::where('user_id', Auth::id())
            ->with(['vehicle', 'payment', 'pickupAirport', 'dropoffAirport'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($dateFrom) {
            $query->whereDate('pickup_date', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('pickup_date', '<=', $dateTo);
        }

        // Get bookings with pagination
        $bookings = $query->paginate(10);

        return view('client.bookings.index', compact('bookings', 'status', 'dateFrom', 'dateTo'));
    }

    /**
     * Display the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $booking = Booking::where('user_id', Auth::id())
            ->with(['vehicle', 'payment', 'driver', 'pickupAirport', 'dropoffAirport', 'history' => function($query) {
                $query->orderBy('created_at', 'desc');
            }])
            ->findOrFail($id);

        return view('client.bookings.show', compact('booking'));
    }

    /**
     * Cancel the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        // Only allow cancellation of pending or confirmed bookings
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->back()->with('error', 'This booking cannot be cancelled.');
        }

        $oldStatus = $booking->status;
        $booking->status = 'cancelled';
        $booking->payment_status = 'cancelled';
        $booking->cancellation_reason = 'Cancelled by client';
        $booking->cancelled_at = now();
        $booking->save();

        // Add booking history
        $booking->addHistory('cancelled', [
            'reason' => 'Cancelled by client',
            'old_status' => $oldStatus,
        ]);

        // If there's a payment, mark it as refunded
        if ($booking->payment) {
            $booking->payment->status = 'refunded';
            $booking->payment->save();

            // Add payment history
            $booking->addHistory('payment_refunded', [
                'payment_id' => $booking->payment->id,
                'transaction_id' => $booking->payment->transaction_id,
                'amount' => $booking->payment->amount,
            ]);
        }

        return redirect()->route('client.bookings.index')
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Show the form for reviewing a completed booking.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showReviewForm($id)
    {
        $booking = Booking::where('user_id', Auth::id())
            ->where('status', 'completed')
            ->findOrFail($id);

        return view('client.bookings.review', compact('booking'));
    }

    /**
     * Store a review for a completed booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeReview(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())
            ->where('status', 'completed')
            ->findOrFail($id);

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|max:500',
        ]);

        $booking->rating = $request->rating;
        $booking->review = $request->comment;
        $booking->reviewed_at = now();
        $booking->save();

        // Add booking history
        $booking->addHistory('review_added', [
            'rating' => $booking->rating,
            'review' => $booking->review
        ]);

        return redirect()->route('client.bookings.show', $booking->id)
            ->with('success', 'Review submitted successfully.');
    }

    /**
     * Show the form for creating a new booking.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function create()
    {
        return view('client.bookings.create');
    }

    /**
     * Store a newly created booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'pickup_address' => 'required|string',
            'pickup_datetime' => 'required|string',
            'dropoff_address' => 'required_if:booking_type,one_way,return',
            'return_datetime' => 'required_if:booking_type,return|nullable|string',
            'duration_hours' => 'required_if:booking_type,hourly|nullable|integer|min:1',
            'vehicle_id' => 'required|exists:vehicles,id',
            'booking_type' => 'required|in:one_way,return,hourly',
            'amount' => 'required|numeric|min:1',
        ]);

        // Parse dates
        $pickupDate = Carbon::parse($request->input('pickup_datetime'));
        $returnDate = null;

        if ($request->input('booking_type') === 'return' && $request->input('return_datetime')) {
            $returnDate = Carbon::parse($request->input('return_datetime'));
        }

        // Get geocode data (if available)
        $pickupGeocode = null;
        $dropoffGeocode = null;

        // Create booking
        $booking = new Booking();
        $booking->user_id = Auth::id();
        $booking->vehicle_id = $request->input('vehicle_id');
        $booking->booking_number = Booking::generateBookingNumber();
        $booking->booking_type = $request->input('booking_type');
        $booking->pickup_address = $request->input('pickup_address');
        $booking->pickup_lat = $pickupGeocode ? $pickupGeocode['lat'] : null;
        $booking->pickup_lng = $pickupGeocode ? $pickupGeocode['lng'] : null;

        if ($request->input('booking_type') !== 'hourly') {
            $booking->dropoff_address = $request->input('dropoff_address');
            $booking->dropoff_lat = $dropoffGeocode ? $dropoffGeocode['lat'] : null;
            $booking->dropoff_lng = $dropoffGeocode ? $dropoffGeocode['lng'] : null;
        }

        $booking->pickup_date = $pickupDate;
        $booking->return_date = $returnDate;
        $booking->duration_hours = $request->input('duration_hours');
        $booking->amount = $request->input('amount');
        $booking->status = 'pending';
        $booking->payment_status = 'pending';
        $booking->notes = $request->input('notes');
        $booking->save();

        // Add booking history
        $booking->addHistory('booking_created', [
            'booking_type' => $booking->booking_type,
            'amount' => $booking->amount,
            'vehicle_id' => $booking->vehicle_id,
        ]);

        // Notify the client about the booking
        Auth::user()->notify(new BookingStatusChanged($booking));

        // Redirect to payment page
        return redirect()->route('booking.payment', $booking->id);
    }

    /**
     * Display a listing of the client's booking history.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function history()
    {
        $query = Booking::where('user_id', Auth::id());

        // Apply filters if provided
        if (request('date_from')) {
            $query->whereDate('created_at', '>=', request('date_from'));
        }

        if (request('date_to')) {
            $query->whereDate('created_at', '<=', request('date_to'));
        }

        if (request('vehicle_id')) {
            $query->where('vehicle_id', request('vehicle_id'));
        }

        if (request('status')) {
            $query->where('status', request('status'));
        } else {
            // Default to showing completed and cancelled bookings
            $query->whereIn('status', ['completed', 'cancelled']);
        }

        // Get bookings with related data
        $bookings = $query->with(['vehicle', 'payment'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate statistics
        $stats = [
            'total' => Booking::where('user_id', Auth::id())->count(),
            'completed' => Booking::where('user_id', Auth::id())->where('status', 'completed')->count(),
            'cancelled' => Booking::where('user_id', Auth::id())->where('status', 'cancelled')->count(),
            'total_amount' => Booking::where('user_id', Auth::id())->where('status', 'completed')->sum('amount')
        ];

        // Get all vehicles for filter dropdown
        $vehicles = \App\Models\Vehicle::where('is_active', true)->get();

        return view('client.bookings.history', compact('bookings', 'stats', 'vehicles'));
    }
}
