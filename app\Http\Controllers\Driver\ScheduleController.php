<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\DriverSchedule;
use App\Models\TimeOffRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ScheduleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Display the driver's schedule.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $driverId = Auth::id();
        
        // Get the driver's schedule for the current week
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();
        
        $schedule = DriverSchedule::where('driver_id', $driverId)
            ->whereBetween('date', [$startOfWeek, $endOfWeek])
            ->orderBy('date')
            ->get();
        
        // Get pending time off requests
        $timeOffRequests = TimeOffRequest::where('driver_id', $driverId)
            ->where('status', 'pending')
            ->orderBy('start_date')
            ->get();
        
        // Get upcoming rides for the week
        $upcomingRides = Auth::user()->driverRides()
            ->whereIn('status', ['assigned', 'in_progress'])
            ->whereBetween('pickup_date', [$startOfWeek, $endOfWeek])
            ->with('user', 'vehicle')
            ->orderBy('pickup_date')
            ->get();
        
        return view('driver.schedule.index', compact(
            'schedule',
            'timeOffRequests',
            'upcomingRides',
            'startOfWeek',
            'endOfWeek'
        ));
    }

    /**
     * Update the driver's schedule.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $driverId = Auth::id();
        
        $request->validate([
            'schedule' => 'required|array',
            'schedule.*.date' => 'required|date',
            'schedule.*.start_time' => 'nullable|date_format:H:i',
            'schedule.*.end_time' => 'nullable|date_format:H:i',
            'schedule.*.is_available' => 'boolean',
        ]);
        
        foreach ($request->schedule as $day) {
            DriverSchedule::updateOrCreate(
                [
                    'driver_id' => $driverId,
                    'date' => $day['date'],
                ],
                [
                    'start_time' => $day['is_available'] ? $day['start_time'] : null,
                    'end_time' => $day['is_available'] ? $day['end_time'] : null,
                    'is_available' => $day['is_available'],
                ]
            );
        }
        
        return redirect()->route('driver.schedule.index')
            ->with('success', 'Schedule updated successfully.');
    }

    /**
     * Request time off.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function requestTimeOff(Request $request)
    {
        $driverId = Auth::id();
        
        $request->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'required|string|max:255',
        ]);
        
        // Create time off request
        $timeOffRequest = new TimeOffRequest();
        $timeOffRequest->driver_id = $driverId;
        $timeOffRequest->start_date = $request->start_date;
        $timeOffRequest->end_date = $request->end_date;
        $timeOffRequest->reason = $request->reason;
        $timeOffRequest->status = 'pending';
        $timeOffRequest->save();
        
        return redirect()->route('driver.schedule.index')
            ->with('success', 'Time off request submitted successfully.');
    }
}
