<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of the payments.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's bookings
        $bookingIds = $user->bookings->pluck('id')->toArray();

        // Get payments for those bookings
        $payments = Payment::whereIn('booking_id', $bookingIds)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate payment statistics
        $stats = [
            'total_payments' => Payment::whereIn('booking_id', $bookingIds)->count(),
            'total_amount' => Payment::whereIn('booking_id', $bookingIds)->sum('amount'),
            'completed_payments' => Payment::whereIn('booking_id', $bookingIds)->where('status', 'completed')->count(),
        ];

        return view('client.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = Auth::user();
        $bookingIds = $user->bookings->pluck('id')->toArray();

        $payment = Payment::with(['booking', 'booking.user', 'booking.vehicle'])
            ->whereIn('booking_id', $bookingIds)
            ->findOrFail($id);

        return view('client.payments.show', compact('payment'));
    }

    /**
     * Display the invoice for the specified payment.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function invoice($id)
    {
        $user = Auth::user();
        $bookingIds = $user->bookings->pluck('id')->toArray();

        $payment = Payment::with(['booking', 'booking.user', 'booking.vehicle'])
            ->whereIn('booking_id', $bookingIds)
            ->findOrFail($id);

        return view('client.payments.invoice', compact('payment'));
    }

    /**
     * Download the invoice for the specified payment.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function downloadInvoice($id)
    {
        $user = Auth::user();
        $bookingIds = $user->bookings->pluck('id')->toArray();

        $payment = Payment::with(['booking', 'booking.user', 'booking.vehicle'])
            ->whereIn('booking_id', $bookingIds)
            ->findOrFail($id);

        // Generate PDF invoice
        $pdf = \PDF::loadView('client.payments.invoice-pdf', compact('payment'));

        return $pdf->download('invoice-' . $payment->id . '.pdf');
    }

    /**
     * Display payment history.
     *
     * @return \Illuminate\Http\Response
     */
    public function history()
    {
        $user = Auth::user();
        $bookingIds = $user->bookings->pluck('id')->toArray();

        // Get payments for those bookings
        $payments = Payment::whereIn('booking_id', $bookingIds)
            ->with(['booking', 'booking.vehicle'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate payment statistics by month
        $monthlyStats = Payment::whereIn('booking_id', $bookingIds)
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count, SUM(amount) as total')
            ->where('status', 'completed')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return view('client.payments.history', compact('payments', 'monthlyStats'));
    }
}
