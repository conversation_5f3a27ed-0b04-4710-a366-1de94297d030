@extends('layouts.client')

@section('title', 'My Profile')

@section('styles')
<style>
    .profile-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .profile-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        padding: 30px;
        border-radius: 10px 10px 0 0;
        position: relative;
        text-align: center;
    }

    .profile-avatar-container {
        position: relative;
        width: 120px;
        height: 120px;
        margin: 0 auto 20px;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid #fff;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }

    .profile-avatar-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: #adb5bd;
        border: 5px solid #fff;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }

    .profile-name {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .profile-email {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 20px;
    }

    .profile-stats {
        display: flex;
        justify-content: space-around;
        background-color: rgba(255, 255, 255, 0.1);
        padding: 15px 0;
        border-radius: 10px;
        margin-top: 20px;
    }

    .profile-stat {
        text-align: center;
    }

    .profile-stat-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: #ee393d;
    }

    .profile-stat-label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .profile-body {
        padding: 30px;
    }

    .profile-section {
        margin-bottom: 30px;
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .profile-section:last-child {
        margin-bottom: 0;
    }

    .profile-section-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
        color: #343a40;
    }

    .profile-detail {
        margin-bottom: 15px;
    }

    .profile-detail-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }

    .profile-detail-value {
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-radius: 5px;
    }

    .avatar-upload-btn {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #ee393d;
        color: #fff;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        border: 2px solid #fff;
    }

    .avatar-upload-btn:hover {
        background-color: #e0a800;
        transform: scale(1.1);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .action-button {
        flex: 1;
        padding: 12px;
        border-radius: 5px;
        text-align: center;
        transition: all 0.3s;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .action-button i {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .action-button.primary {
        background-color: #ee393d;
        color: #fff;
    }

    .action-button.secondary {
        background-color: #f8f9fa;
        color: #343a40;
    }

    .action-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .action-button.primary:hover {
        background-color: #e0a800;
    }

    .action-button.secondary:hover {
        background-color: #e9ecef;
    }
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>My Profile</h2>
</div>

@if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<div class="card profile-card" data-aos="fade-up">
    <div class="profile-header">
        <div class="profile-avatar-container">
            @if ($user->profile_photo)
                <img src="{{ asset('storage/' . $user->profile_photo) }}" class="profile-avatar" alt="Profile Photo">
            @else
                <div class="profile-avatar-placeholder">
                    <i class="fas fa-user"></i>
                </div>
            @endif

            <label for="avatar-upload" class="avatar-upload-btn" title="Change profile photo">
                <i class="fas fa-camera"></i>
            </label>

            <form id="avatar-form" action="{{ route('client.profile.update-avatar') }}" method="POST" enctype="multipart/form-data" class="d-none">
                @csrf
                <input type="file" id="avatar-upload" name="avatar" accept="image/*" onchange="document.getElementById('avatar-form').submit()">
            </form>
        </div>

        <h3 class="profile-name">{{ $user->name }}</h3>
        <p class="profile-email">{{ $user->email }}</p>

        <div class="action-buttons">
            <a href="{{ route('client.profile.edit') }}" class="action-button primary">
                <i class="fas fa-edit"></i>
                Edit Profile
            </a>
            <a href="{{ route('client.profile.change-password') }}" class="action-button secondary">
                <i class="fas fa-key"></i>
                Change Password
            </a>
        </div>

        <div class="profile-stats">
            <div class="profile-stat">
                <div class="profile-stat-value">{{ $totalBookings ?? 0 }}</div>
                <div class="profile-stat-label">Bookings</div>
            </div>
            <div class="profile-stat">
                <div class="profile-stat-value">@currency(){{ number_format($totalSpent ?? 0, 2) }}</div>
                <div class="profile-stat-label">Total Spent</div>
            </div>
            <div class="profile-stat">
                <div class="profile-stat-value">{{ $user->created_at->diffForHumans(null, true) }}</div>
                <div class="profile-stat-label">Member For</div>
            </div>
        </div>
    </div>

    <div class="profile-body">
        <div class="row">
            <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="profile-section">
                    <h4 class="profile-section-title">
                        <i class="fas fa-user-circle me-2"></i> Personal Information
                    </h4>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Full Name</div>
                        <div class="profile-detail-value">{{ $user->name }}</div>
                    </div>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Email Address</div>
                        <div class="profile-detail-value">{{ $user->email }}</div>
                    </div>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Phone Number</div>
                        <div class="profile-detail-value">{{ $user->phone ?? 'Not provided' }}</div>
                    </div>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Address</div>
                        <div class="profile-detail-value">{{ $user->address ?? 'Not provided' }}</div>
                    </div>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Date of Birth</div>
                        <div class="profile-detail-value">{{ $user->date_of_birth ? $user->date_of_birth->format('F d, Y') : 'Not provided' }}</div>
                    </div>
                </div>

                <div class="profile-section" data-aos="fade-up" data-aos-delay="200">
                    <h4 class="profile-section-title">
                        <i class="fas fa-shield-alt me-2"></i> Account Security
                    </h4>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Password</div>
                        <div class="profile-detail-value d-flex justify-content-between align-items-center">
                            <span>••••••••</span>
                            <a href="{{ route('client.profile.change-password') }}" class="btn btn-sm btn-outline-primary">Change</a>
                        </div>
                    </div>

                    <div class="profile-detail">
                        <div class="profile-detail-label">Two-Factor Authentication</div>
                        <div class="profile-detail-value d-flex justify-content-between align-items-center">
                            <span class="badge bg-danger">Not Enabled</span>
                            <button class="btn btn-sm btn-outline-primary" disabled>Enable</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="profile-section">
                    <h4 class="profile-section-title">
                        <i class="fas fa-map-marker-alt me-2"></i> Saved Addresses
                    </h4>

                    @if(isset($addresses) && count($addresses) > 0)
                        @foreach($addresses as $address)
                            <div class="profile-detail">
                                <div class="profile-detail-label d-flex justify-content-between">
                                    <span>
                                        {{ ucfirst($address->type) }} Address
                                        @if($address->is_default)
                                            <span class="badge bg-success ms-2">Default</span>
                                        @endif
                                    </span>
                                    <form action="{{ route('client.profile.delete-address', $address->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this address?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="profile-detail-value">{{ $address->address }}</div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <p class="mb-3">No addresses saved yet</p>
                            <a href="{{ route('client.profile.addresses') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Add Address
                            </a>
                        </div>
                    @endif
                </div>

                <div class="profile-section" data-aos="fade-up" data-aos-delay="400">
                    <h4 class="profile-section-title">
                        <i class="fas fa-cog me-2"></i> Account Settings
                    </h4>

                    <div class="action-buttons">
                        <a href="{{ route('client.notifications.settings') }}" class="action-button secondary">
                            <i class="fas fa-bell"></i>
                            Notification Settings
                        </a>
                        <a href="{{ route('client.bookings.history') }}" class="action-button secondary">
                            <i class="fas fa-history"></i>
                            Booking History
                        </a>
                    </div>

                    <div class="action-buttons mt-2">
                        <a href="{{ route('client.payments.history') }}" class="action-button secondary">
                            <i class="fas fa-credit-card"></i>
                            Payment History
                        </a>
                        <a href="{{ route('client.bookings.index') }}" class="action-button secondary">
                            <i class="fas fa-calendar-check"></i>
                            My Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Show file name when a file is selected
    document.getElementById('avatar-upload').addEventListener('change', function() {
        const fileName = this.files[0].name;
        console.log('Selected file: ' + fileName);
    });
</script>
@endsection
