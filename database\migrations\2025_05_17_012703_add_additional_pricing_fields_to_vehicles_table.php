<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->decimal('airport_surcharge', 10, 2)->default(15.00)->after('tax_rate');
            $table->decimal('booking_fee', 10, 2)->default(2.50)->after('airport_surcharge');
            $table->decimal('waiting_fee_per_minute', 10, 2)->default(0.50)->after('booking_fee');
            $table->decimal('cancellation_fee', 10, 2)->default(10.00)->after('waiting_fee_per_minute');
            $table->decimal('night_surcharge', 10, 2)->default(5.00)->after('cancellation_fee');
            $table->decimal('weekend_surcharge', 10, 2)->default(5.00)->after('night_surcharge');
            $table->decimal('holiday_surcharge', 10, 2)->default(10.00)->after('weekend_surcharge');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn([
                'airport_surcharge',
                'booking_fee',
                'waiting_fee_per_minute',
                'cancellation_fee',
                'night_surcharge',
                'weekend_surcharge',
                'holiday_surcharge'
            ]);
        });
    }
};
