#!/bin/bash

# YNR Cars Email Management System Setup Script
# This script sets up the complete email management system

echo "🚗📧 YNR Cars Email Management System Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    print_error "This script must be run from the root of a Laravel project"
    exit 1
fi

print_info "Starting email system setup..."

# Step 1: Run migrations
print_info "Running database migrations..."
if php artisan migrate --force; then
    print_status "Database migrations completed"
else
    print_error "Failed to run migrations"
    exit 1
fi

# Step 2: Clear caches
print_info "Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
print_status "Caches cleared"

# Step 3: Create queue tables if not exists
print_info "Setting up queue tables..."
php artisan queue:table 2>/dev/null || true
php artisan migrate --force
print_status "Queue tables ready"

# Step 4: Create storage links
print_info "Creating storage links..."
php artisan storage:link
print_status "Storage links created"

# Step 5: Set up email preferences for existing users
print_info "Setting up email preferences for existing users..."
php artisan tinker --execute="
\$users = App\Models\User::all();
foreach (\$users as \$user) {
    App\Models\EmailPreference::setPreference(\$user->id, 'booking_confirmations', true);
    App\Models\EmailPreference::setPreference(\$user->id, 'payment_confirmations', true);
    App\Models\EmailPreference::setPreference(\$user->id, 'cancellation_notices', true);
    if (\$user->role === 'driver') {
        App\Models\EmailPreference::setPreference(\$user->id, 'ride_requests', true);
    }
}
echo 'Email preferences set for ' . \$users->count() . ' users';
"
print_status "Email preferences configured for existing users"

# Step 6: Optimize application
print_info "Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
print_status "Application optimized"

# Step 7: Check email configuration
print_info "Checking email configuration..."
MAIL_DRIVER=$(php artisan tinker --execute="echo config('mail.default');")
if [ "$MAIL_DRIVER" = "log" ]; then
    print_warning "Email driver is set to 'log'. Configure SMTP settings in admin panel."
else
    print_status "Email driver configured: $MAIL_DRIVER"
fi

echo ""
echo "🎉 Email Management System Setup Complete!"
echo "=========================================="
echo ""
print_info "Next Steps:"
echo "1. 📧 Configure SMTP settings in Admin Panel → Email Management → Settings"
echo "2. 🧪 Test email configuration using the test email feature"
echo "3. 🔄 Start queue workers: php artisan queue:work"
echo "4. ⏰ Set up cron job for scheduled commands (optional)"
echo "5. 📊 Monitor email activity in Admin Panel → Email Management"
echo ""
print_info "Available Console Commands:"
echo "• php artisan email:send-booking-reminders    - Send booking reminders"
echo "• php artisan email:cleanup-logs              - Clean up old email logs"
echo "• php artisan email:process-queue             - Process email queue"
echo ""
print_info "Admin Panel Access:"
echo "• Email Management: /admin/emails"
echo "• Email Settings: /admin/emails/settings"
echo "• Bulk Email: /admin/emails/bulk"
echo ""
print_status "Setup completed successfully! 🚀"

# Optional: Start queue worker in background (commented out for safety)
# print_info "Starting queue worker in background..."
# nohup php artisan queue:work --daemon > storage/logs/queue.log 2>&1 &
# print_status "Queue worker started"

echo ""
print_warning "Remember to:"
echo "• Configure your SMTP settings"
echo "• Test email functionality"
echo "• Set up queue workers for production"
echo "• Schedule automated commands if needed"
echo ""
echo "📚 Documentation available in:"
echo "• EMAIL_SYSTEM_DOCUMENTATION.md"
echo "• SETUP_EMAIL_SYSTEM.md"
