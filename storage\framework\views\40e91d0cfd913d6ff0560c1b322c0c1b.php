<?php $__env->startSection('title', 'Airport Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plane me-2 text-primary"></i>Airport Management
            </h1>
            <a href="<?php echo e(route('admin.airports.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Add Airport
            </a>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.airports.index')); ?>" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" 
                               placeholder="Search by name, code, city, or country...">
                    </div>
                    <div class="col-md-3">
                        <label for="country" class="form-label">Country</label>
                        <select class="form-select" id="country" name="country">
                            <option value="">All Countries</option>
                            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($country->country_code); ?>" 
                                        <?php echo e(request('country') == $country->country_code ? 'selected' : ''); ?>>
                                    <?php echo e($country->country); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="<?php echo e(route('admin.airports.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Airports Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Airports 
                    <span class="badge bg-primary ms-2"><?php echo e($airports->total()); ?></span>
                </h5>
            </div>
            <div class="card-body">
                <?php if($airports->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>City</th>
                                    <th>Country</th>
                                    <th>Coordinates</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $airports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $airport): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($airport->code); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo e($airport->name); ?></strong>
                                        </td>
                                        <td><?php echo e($airport->city); ?></td>
                                        <td>
                                            <span class="flag-icon flag-icon-<?php echo e(strtolower($airport->country_code)); ?>"></span>
                                            <?php echo e($airport->country); ?>

                                        </td>
                                        <td>
                                            <?php if($airport->latitude && $airport->longitude): ?>
                                                <small class="text-muted">
                                                    <?php echo e(number_format($airport->latitude, 4)); ?>, 
                                                    <?php echo e(number_format($airport->longitude, 4)); ?>

                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.airports.show', $airport)); ?>" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.airports.edit', $airport)); ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.airports.destroy', $airport)); ?>" 
                                                      method="POST" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this airport?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            Showing <?php echo e($airports->firstItem()); ?> to <?php echo e($airports->lastItem()); ?> 
                            of <?php echo e($airports->total()); ?> results
                        </div>
                        <?php echo e($airports->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-plane fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No airports found</h5>
                        <p class="text-muted">Start by adding your first airport to the system.</p>
                        <a href="<?php echo e(route('admin.airports.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add First Airport
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Auto-submit form on country change
    document.getElementById('country').addEventListener('change', function() {
        this.form.submit();
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\airports\index.blade.php ENDPATH**/ ?>