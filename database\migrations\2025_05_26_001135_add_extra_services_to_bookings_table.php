<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add extra services columns
            // First add meet_and_greet if it doesn't exist
            if (!Schema::hasColumn('bookings', 'meet_and_greet')) {
                $table->boolean('meet_and_greet')->default(false)->after('reminder_sent_at');
            }

            // Then add other extra service columns
            if (!Schema::hasColumn('bookings', 'child_seat')) {
                $table->boolean('child_seat')->default(false)->after('meet_and_greet');
            }
            if (!Schema::hasColumn('bookings', 'wheelchair_accessible')) {
                $table->boolean('wheelchair_accessible')->default(false)->after('child_seat');
            }
            if (!Schema::hasColumn('bookings', 'extra_luggage')) {
                $table->boolean('extra_luggage')->default(false)->after('wheelchair_accessible');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Remove extra services columns
            $columnsToDrop = [];

            if (Schema::hasColumn('bookings', 'extra_luggage')) {
                $columnsToDrop[] = 'extra_luggage';
            }
            if (Schema::hasColumn('bookings', 'wheelchair_accessible')) {
                $columnsToDrop[] = 'wheelchair_accessible';
            }
            if (Schema::hasColumn('bookings', 'child_seat')) {
                $columnsToDrop[] = 'child_seat';
            }
            if (Schema::hasColumn('bookings', 'meet_and_greet')) {
                $columnsToDrop[] = 'meet_and_greet';
            }

            if (!empty($columnsToDrop)) {
                $table->dropColumn($columnsToDrop);
            }
        });
    }
};
