<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add extra services columns
            $table->boolean('child_seat')->default(false)->after('meet_and_greet');
            $table->boolean('wheelchair_accessible')->default(false)->after('child_seat');
            $table->boolean('extra_luggage')->default(false)->after('wheelchair_accessible');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Remove extra services columns
            $table->dropColumn(['child_seat', 'wheelchair_accessible', 'extra_luggage']);
        });
    }
};
