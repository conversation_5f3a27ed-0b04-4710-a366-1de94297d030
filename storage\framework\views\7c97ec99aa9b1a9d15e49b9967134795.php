<?php $__env->startSection('title', 'Edit Review'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .edit-review-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .edit-review-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        padding: 20px;
    }

    .edit-review-body {
        padding: 30px;
    }

    .edit-review-footer {
        background-color: #f8f9fa;
        padding: 20px;
        border-top: 1px solid #eee;
    }

    .booking-info-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .booking-info-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .booking-info-body {
        padding: 20px;
    }

    .booking-detail {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .booking-detail:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .booking-detail-label {
        font-weight: 600;
        color: #495057;
    }

    .star-rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        cursor: pointer;
        width: 40px;
        height: 40px;
        margin-right: 5px;
        position: relative;
        font-size: 30px;
    }

    .star-rating label:before {
        content: '\f005';
        font-family: 'Font Awesome 5 Free';
        font-weight: 400;
        position: absolute;
        top: 0;
        left: 0;
        color: #e0e0e0;
    }

    .star-rating input:checked ~ label:before,
    .star-rating label:hover:before,
    .star-rating label:hover ~ label:before {
        content: '\f005';
        font-weight: 900;
        color: #ffc107;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-edit me-2"></i> Edit Review</h2>
        <div>
            <a href="<?php echo e(route('admin.reviews.show', $booking->id)); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Review
            </a>
        </div>
    </div>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Edit Review Form -->
        <div class="col-md-8">
            <div class="edit-review-card">
                <div class="edit-review-header">
                    <h4 class="mb-2">Edit Review for Booking #<?php echo e($booking->booking_number); ?></h4>
                    <p class="mb-0">By <?php echo e($booking->user->name); ?> on <?php echo e($booking->reviewed_at->format('F d, Y h:i A')); ?></p>
                </div>
                <div class="edit-review-body">
                    <form action="<?php echo e(route('admin.reviews.update', $booking->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="mb-4">
                            <label class="form-label">Rating</label>
                            <div class="star-rating mb-3">
                                <input type="radio" id="star5" name="rating" value="5" <?php echo e($booking->rating == 5 ? 'checked' : ''); ?>>
                                <label for="star5" title="5 stars"></label>
                                
                                <input type="radio" id="star4" name="rating" value="4" <?php echo e($booking->rating == 4 ? 'checked' : ''); ?>>
                                <label for="star4" title="4 stars"></label>
                                
                                <input type="radio" id="star3" name="rating" value="3" <?php echo e($booking->rating == 3 ? 'checked' : ''); ?>>
                                <label for="star3" title="3 stars"></label>
                                
                                <input type="radio" id="star2" name="rating" value="2" <?php echo e($booking->rating == 2 ? 'checked' : ''); ?>>
                                <label for="star2" title="2 stars"></label>
                                
                                <input type="radio" id="star1" name="rating" value="1" <?php echo e($booking->rating == 1 ? 'checked' : ''); ?>>
                                <label for="star1" title="1 star"></label>
                            </div>
                            <div class="rating-value" id="rating-text">
                                <?php echo e($booking->rating); ?> Star<?php echo e($booking->rating != 1 ? 's' : ''); ?> - 
                                <?php if($booking->rating == 5): ?>
                                    Excellent
                                <?php elseif($booking->rating == 4): ?>
                                    Very Good
                                <?php elseif($booking->rating == 3): ?>
                                    Good
                                <?php elseif($booking->rating == 2): ?>
                                    Fair
                                <?php else: ?>
                                    Poor
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="review" class="form-label">Review Content</label>
                            <textarea class="form-control" id="review" name="review" rows="6" required><?php echo e(old('review', $booking->review)); ?></textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Update Review
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Booking Information -->
        <div class="col-md-4">
            <div class="booking-info-card">
                <div class="booking-info-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Information</h5>
                </div>
                <div class="booking-info-body">
                    <div class="booking-detail">
                        <div class="booking-detail-label">Client</div>
                        <div><?php echo e($booking->user->name); ?></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Vehicle</div>
                        <div><?php echo e($booking->vehicle->name); ?> (<?php echo e($booking->vehicle->type); ?>)</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Booking Type</div>
                        <div><?php echo e(ucfirst(str_replace('_', ' ', $booking->booking_type))); ?></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Pickup Date & Time</div>
                        <div><?php echo e($booking->pickup_date->format('M d, Y h:i A')); ?></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Status</div>
                        <div><span class="badge bg-<?php echo e($booking->status === 'completed' ? 'success' : 'primary'); ?>"><?php echo e(ucfirst($booking->status)); ?></span></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Amount</div>
                        <div><?php echo e($currencySymbol ?? '£'); ?><?php echo e(number_format($booking->amount, 2)); ?></div>
                    </div>

                    <?php if($booking->driver): ?>
                    <div class="booking-detail">
                        <div class="booking-detail-label">Driver</div>
                        <div><?php echo e($booking->driver->name); ?></div>
                    </div>
                    <?php endif; ?>

                    <div class="mt-4">
                        <a href="<?php echo e(route('admin.bookings.show', $booking->id)); ?>" class="btn btn-info w-100">
                            <i class="fas fa-calendar-check me-2"></i> View Full Booking Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update rating text when a star is selected
        const ratingInputs = document.querySelectorAll('.star-rating input');
        const ratingText = document.getElementById('rating-text');
        const ratingDescriptions = [
            '',
            '1 Star - Poor',
            '2 Stars - Fair',
            '3 Stars - Good',
            '4 Stars - Very Good',
            '5 Stars - Excellent'
        ];

        ratingInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = parseInt(this.value);
                ratingText.textContent = ratingDescriptions[value];
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reviews\edit.blade.php ENDPATH**/ ?>