<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'phone' => $this->phone,
            'address' => $this->address,
            'profile_photo' => $this->profile_photo ? asset('storage/' . $this->profile_photo) : null,
            'is_active' => $this->is_active,
            'is_available' => $this->when($this->role === 'driver', $this->is_available),
            'driver_info' => $this->when($this->role === 'driver', [
                'license_number' => $this->license_number,
                'vehicle_info' => $this->vehicle_info,
                'vehicle_make' => $this->vehicle_make,
                'vehicle_model' => $this->vehicle_model,
                'vehicle_color' => $this->vehicle_color,
                'vehicle_reg_number' => $this->vehicle_reg_number,
                'insurance_expiry' => $this->insurance_expiry?->toDateString(),
                'mot_expiry' => $this->mot_expiry?->toDateString(),
            ]),
            'email_verified_at' => $this->email_verified_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
