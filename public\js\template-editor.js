// Template Editor JavaScript
let currentTemplate = '';

// Template editor functions
function showTemplateEditor(templateName) {
    currentTemplate = templateName;
    const modal = new bootstrap.Modal(document.getElementById('templateEditorModal'));
    const templateNameSpan = document.getElementById('editorTemplateName');
    const subjectInput = document.getElementById('templateSubject');
    const contentTextarea = document.getElementById('templateContent');
    const alert = document.getElementById('templateEditorAlert');
    
    // Set template name
    templateNameSpan.textContent = templateName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    // Clear previous content
    subjectInput.value = '';
    contentTextarea.value = '';
    alert.classList.add('d-none');
    
    // Load template content
    loadTemplateContent(templateName);
    
    modal.show();
}

function loadTemplateContent(templateName) {
    const subjectInput = document.getElementById('templateSubject');
    const contentTextarea = document.getElementById('templateContent');
    
    // Show loading
    subjectInput.value = 'Loading...';
    contentTextarea.value = 'Loading template content...';
    
    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    // Fetch template content
    fetch(`/admin/emails/templates/load/${templateName}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            subjectInput.value = data.subject || getDefaultSubject(templateName);
            contentTextarea.value = data.content || getDefaultContent(templateName);
            updatePreview();
        } else {
            // Use default content if template doesn't exist
            subjectInput.value = getDefaultSubject(templateName);
            contentTextarea.value = getDefaultContent(templateName);
            updatePreview();
        }
    })
    .catch(error => {
        console.error('Error loading template:', error);
        // Use default content on error
        subjectInput.value = getDefaultSubject(templateName);
        contentTextarea.value = getDefaultContent(templateName);
        updatePreview();
    });
}

function getDefaultSubject(templateName) {
    const subjects = {
        'booking_confirmation': 'Booking Confirmation - {{ $booking->id }}',
        'booking_cancelled': 'Booking Cancelled - {{ $booking->id }}',
        'booking_reminder': 'Booking Reminder - {{ $booking->id }}',
        'driver_assigned': 'Driver Assigned - {{ $booking->id }}',
        'payment_confirmation': 'Payment Confirmation - {{ $payment->transaction_id }}',
        'user_welcome': 'Welcome to {{ config("app.name") }}',
        'password_reset': 'Reset Your Password',
        'email_verification': 'Verify Your Email Address'
    };
    return subjects[templateName] || 'Email from {{ config("app.name") }}';
}

function getDefaultContent(templateName) {
    return `<h2>{{ config('app.name') }}</h2>

<p>Dear {{ $user->name }},</p>

<p>This is a ${templateName.replace('_', ' ')} email.</p>

<p>Best regards,<br>
{{ config('app.name') }} Team</p>

<hr>
<p><small>&copy; {{ now()->year }} {{ config('app.name') }}. All rights reserved.</small></p>`;
}

function insertVariable(variable) {
    const contentTextarea = document.getElementById('templateContent');
    const cursorPos = contentTextarea.selectionStart;
    const textBefore = contentTextarea.value.substring(0, cursorPos);
    const textAfter = contentTextarea.value.substring(contentTextarea.selectionEnd);
    
    contentTextarea.value = textBefore + '{{ ' + variable + ' }}' + textAfter;
    contentTextarea.focus();
    contentTextarea.setSelectionRange(cursorPos + variable.length + 6, cursorPos + variable.length + 6);
    
    updatePreview();
}

function updatePreview() {
    const content = document.getElementById('templateContent').value;
    const preview = document.getElementById('templatePreview');
    
    // Simple preview - replace variables with sample data
    let previewContent = content
        .replace(/\{\{\s*\$user->name\s*\}\}/g, 'John Doe')
        .replace(/\{\{\s*\$user->email\s*\}\}/g, '<EMAIL>')
        .replace(/\{\{\s*\$user->phone\s*\}\}/g, '+****************')
        .replace(/\{\{\s*\$booking->id\s*\}\}/g, 'BK-1234')
        .replace(/\{\{\s*\$booking->pickup_location\s*\}\}/g, '123 Main Street')
        .replace(/\{\{\s*\$booking->dropoff_location\s*\}\}/g, '456 Oak Avenue')
        .replace(/\{\{\s*\$booking->total_amount\s*\}\}/g, '$85.00')
        .replace(/\{\{\s*\$payment->transaction_id\s*\}\}/g, 'TXN-789012')
        .replace(/\{\{\s*\$payment->amount\s*\}\}/g, '$85.00')
        .replace(/\{\{\s*config\("app\.name"\)\s*\}\}/g, 'YNR Cars')
        .replace(/\{\{\s*now\(\)->year\s*\}\}/g, new Date().getFullYear());
    
    preview.innerHTML = previewContent || '<p class="text-muted">Preview will appear here as you type...</p>';
}

function previewTemplate() {
    const subject = document.getElementById('templateSubject').value;
    const content = document.getElementById('templateContent').value;
    
    // Hide editor modal temporarily
    const editorModal = bootstrap.Modal.getInstance(document.getElementById('templateEditorModal'));
    editorModal.hide();
    
    // Show preview in the main preview modal
    const previewModal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    const previewContent = document.getElementById('templatePreviewContent');
    
    previewContent.innerHTML = `
        <div class="border rounded p-3" style="background-color: #f8f9fa;">
            <h4>Subject: ${subject}</h4>
            <hr>
            <div class="email-preview">
                ${content.replace(/\{\{\s*\$user->name\s*\}\}/g, 'John Doe')
                        .replace(/\{\{\s*\$user->email\s*\}\}/g, '<EMAIL>')
                        .replace(/\{\{\s*\$booking->id\s*\}\}/g, 'BK-1234')
                        .replace(/\{\{\s*\$booking->pickup_location\s*\}\}/g, '123 Main Street')
                        .replace(/\{\{\s*\$booking->total_amount\s*\}\}/g, '$85.00')
                        .replace(/\{\{\s*config\("app\.name"\)\s*\}\}/g, 'YNR Cars')
                        .replace(/\{\{\s*now\(\)->year\s*\}\}/g, new Date().getFullYear())}
            </div>
        </div>
    `;
    
    previewModal.show();
    
    // Re-show editor modal when preview is closed
    document.getElementById('templatePreviewModal').addEventListener('hidden.bs.modal', function() {
        editorModal.show();
    }, { once: true });
}

function saveTemplate() {
    const templateName = currentTemplate;
    const subject = document.getElementById('templateSubject').value;
    const content = document.getElementById('templateContent').value;
    const saveBtn = document.getElementById('saveTemplateBtn');
    const spinner = document.getElementById('templateSaveSpinner');
    const alert = document.getElementById('templateEditorAlert');
    
    if (!subject.trim() || !content.trim()) {
        showTemplateAlert('Please fill in both subject and content.', 'danger');
        return;
    }
    
    // Show loading state
    saveBtn.disabled = true;
    spinner.classList.remove('d-none');
    alert.classList.add('d-none');
    
    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    // Prepare form data
    const formData = new FormData();
    formData.append('template', templateName);
    formData.append('subject', subject);
    formData.append('content', content);
    formData.append('_token', csrfToken);
    
    // Save template
    fetch('/admin/emails/templates/save', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showTemplateAlert('Template saved successfully!', 'success');
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('templateEditorModal'));
                modal.hide();
            }, 2000);
        } else {
            showTemplateAlert(data.message || 'Failed to save template.', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showTemplateAlert('An error occurred while saving the template.', 'danger');
    })
    .finally(() => {
        // Hide loading state
        saveBtn.disabled = false;
        spinner.classList.add('d-none');
    });
}

function showTemplateAlert(message, type) {
    const alert = document.getElementById('templateEditorAlert');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.classList.remove('d-none');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const templateEditorForm = document.getElementById('templateEditorForm');
    if (templateEditorForm) {
        templateEditorForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveTemplate();
        });
    }
    
    // Add live preview update
    const contentTextarea = document.getElementById('templateContent');
    if (contentTextarea) {
        contentTextarea.addEventListener('input', updatePreview);
    }
});
