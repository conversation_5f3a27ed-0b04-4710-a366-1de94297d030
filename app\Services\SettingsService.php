<?php

namespace App\Services;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class SettingsService
{
    /**
     * Apply all settings to the application
     *
     * @return void
     */
    public static function applySettings()
    {
        try {
            // Apply email settings
            self::applyEmailSettings();

            // Apply appearance settings
            self::applyAppearanceSettings();

            // Apply booking settings
            self::applyBookingSettings();

            // Apply driver settings
            self::applyDriverSettings();

            // Apply localization settings
            self::applyLocalizationSettings();

            // Apply autocomplete settings
            self::applyAutocompleteSettings();
        } catch (\Exception $e) {
            Log::error('Error applying settings: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
        }
    }

    /**
     * Apply email settings to the application
     *
     * @return void
     */
    private static function applyEmailSettings()
    {
        $mailDriver = SettingsHelper::get('mail_driver');
        if ($mailDriver) {
            Config::set('mail.default', $mailDriver);
        }

        $mailHost = SettingsHelper::get('mail_host');
        if ($mailHost) {
            Config::set('mail.mailers.smtp.host', $mailHost);
        }

        $mailPort = SettingsHelper::get('mail_port');
        if ($mailPort) {
            Config::set('mail.mailers.smtp.port', $mailPort);
        }

        $mailUsername = SettingsHelper::get('mail_username');
        if ($mailUsername) {
            Config::set('mail.mailers.smtp.username', $mailUsername);
        }

        $mailPassword = SettingsHelper::get('mail_password');
        if ($mailPassword) {
            Config::set('mail.mailers.smtp.password', $mailPassword);
        }

        $mailEncryption = SettingsHelper::get('mail_encryption');
        if ($mailEncryption && $mailEncryption !== 'none') {
            Config::set('mail.mailers.smtp.encryption', $mailEncryption);
        } else {
            Config::set('mail.mailers.smtp.encryption', null);
        }

        $mailFromAddress = SettingsHelper::get('mail_from_address');
        if ($mailFromAddress) {
            Config::set('mail.from.address', $mailFromAddress);
        }

        $mailFromName = SettingsHelper::get('mail_from_name');
        if ($mailFromName) {
            Config::set('mail.from.name', $mailFromName);
        }
    }

    /**
     * Apply appearance settings to the application
     *
     * @return void
     */
    private static function applyAppearanceSettings()
    {
        // These will be used in views, no need to set config
        $primaryColor = SettingsHelper::get('primary_color', '#ee393d');
        $secondaryColor = SettingsHelper::get('secondary_color', '#343a40');
        $logo = SettingsHelper::get('logo', 'logo.png');
        $favicon = SettingsHelper::get('favicon', 'favicon.ico');

        // Set default locale
        $defaultLanguage = SettingsHelper::get('default_language', 'en');
        if ($defaultLanguage) {
            Config::set('app.locale', $defaultLanguage);
        }
    }

    /**
     * Apply booking settings to the application
     *
     * @return void
     */
    private static function applyBookingSettings()
    {
        // These will be used in controllers and models
        $advanceBookingTime = SettingsHelper::get('advance_booking_time', 30);
        $minimumHourlyDuration = SettingsHelper::get('minimum_hourly_duration', 2);
        $cancellationTimeWindow = SettingsHelper::get('cancellation_time_window', 24);
        $cancellationFeePercentage = SettingsHelper::get('cancellation_fee_percentage', 20);
        $allowGuestBookings = SettingsHelper::get('allow_guest_bookings', 'true');

        // Set as config values for easy access
        Config::set('booking.advance_booking_time', $advanceBookingTime);
        Config::set('booking.minimum_hourly_duration', $minimumHourlyDuration);
        Config::set('booking.cancellation_time_window', $cancellationTimeWindow);
        Config::set('booking.cancellation_fee_percentage', $cancellationFeePercentage);
        Config::set('booking.allow_guest_bookings', $allowGuestBookings === 'true');
    }

    /**
     * Apply driver settings to the application
     *
     * @return void
     */
    private static function applyDriverSettings()
    {
        // These will be used in controllers and models
        $driverCommissionPercentage = SettingsHelper::get('driver_commission_percentage', 80);
        $minimumPayoutAmount = SettingsHelper::get('minimum_payout_amount', 50);
        $payoutSchedule = SettingsHelper::get('payout_schedule', 'weekly');
        $driverVerificationRequirements = SettingsHelper::get('driver_verification_requirements');
        $maximumActiveHours = SettingsHelper::get('maximum_active_hours', 12);

        // Set as config values for easy access
        Config::set('driver.commission_percentage', $driverCommissionPercentage);
        Config::set('driver.minimum_payout_amount', $minimumPayoutAmount);
        Config::set('driver.payout_schedule', $payoutSchedule);
        Config::set('driver.maximum_active_hours', $maximumActiveHours);

        // Parse JSON for verification requirements if it exists
        if ($driverVerificationRequirements) {
            try {
                $requirements = json_decode($driverVerificationRequirements, true);
                Config::set('driver.verification_requirements', $requirements);
            } catch (\Exception $e) {
                Log::error('Error parsing driver verification requirements: ' . $e->getMessage());
                Config::set('driver.verification_requirements', [
                    'license' => true,
                    'insurance' => true,
                    'background_check' => true,
                    'vehicle_inspection' => true
                ]);
            }
        }
    }

    /**
     * Get a specific setting with a default value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        return Cache::remember('settings.' . $key, 3600, function() use ($key, $default) {
            return SettingsHelper::get($key, $default);
        });
    }

    /**
     * Check if guest bookings are allowed
     *
     * @return bool
     */
    public static function allowGuestBookings()
    {
        return self::get('allow_guest_bookings', 'true') === 'true';
    }

    /**
     * Get the maximum date for advance bookings
     *
     * @return \Carbon\Carbon
     */
    public static function getMaxBookingDate()
    {
        $advanceDays = (int) self::get('advance_booking_time', 30);
        return \Carbon\Carbon::now()->addDays($advanceDays);
    }

    /**
     * Get the minimum hourly duration for bookings
     *
     * @return int
     */
    public static function getMinimumHourlyDuration()
    {
        return (int) self::get('minimum_hourly_duration', 2);
    }

    /**
     * Calculate cancellation fee for a booking
     *
     * @param float $bookingAmount
     * @return float
     */
    public static function calculateCancellationFee($bookingAmount)
    {
        $percentage = (float) self::get('cancellation_fee_percentage', 20);
        return round(($percentage / 100) * $bookingAmount, 2);
    }

    /**
     * Check if a booking can be cancelled for free
     *
     * @param \Carbon\Carbon $pickupDate
     * @return bool
     */
    public static function canCancelForFree($pickupDate)
    {
        $hoursWindow = (int) self::get('cancellation_time_window', 24);
        $now = \Carbon\Carbon::now();
        $hoursDifference = $now->diffInHours($pickupDate, false);

        return $hoursDifference >= $hoursWindow;
    }

    /**
     * Apply localization settings to the application
     *
     * @return void
     */
    private static function applyLocalizationSettings()
    {
        // Get country and currency settings
        $countryCode = SettingsHelper::get('country_code', 'US');
        $currencyCode = SettingsHelper::get('currency_code', 'USD');
        $currencySymbol = SettingsHelper::get('currency_symbol', '$');
        $distanceUnit = SettingsHelper::get('distance_unit', 'miles');

        // Set as config values for easy access
        Config::set('app.country_code', $countryCode);
        Config::set('app.currency_code', $currencyCode);
        Config::set('app.currency_symbol', $currencySymbol);
        Config::set('app.distance_unit', $distanceUnit);
    }

    /**
     * Get the country code
     *
     * @return string
     */
    public static function getCountryCode()
    {
        return self::get('country_code', 'US');
    }

    /**
     * Get the distance unit (miles or kilometers)
     *
     * @return string
     */
    public static function getDistanceUnit()
    {
        return self::get('distance_unit', 'miles');
    }

    /**
     * Apply autocomplete settings to the application
     *
     * @return void
     */
    private static function applyAutocompleteSettings()
    {
        $googleMapsApiKey = SettingsHelper::get('google_maps_api_key');
        if ($googleMapsApiKey) {
            Config::set('services.google_maps.key', $googleMapsApiKey);
        }

        $autocompleteCountries = SettingsHelper::get('autocomplete_countries');
        if ($autocompleteCountries) {
            Config::set('services.google_maps.autocomplete_countries', $autocompleteCountries);
        }
    }

    /**
     * Format a price with the currency symbol
     *
     * @param float $amount
     * @return string
     */
    public static function formatPrice($amount)
    {
        return SettingsHelper::formatPrice($amount);
    }

    /**
     * Get the currency symbol
     *
     * @return string
     */
    public static function getCurrencySymbol()
    {
        return SettingsHelper::getCurrencySymbol();
    }

    /**
     * Get the currency code
     *
     * @return string
     */
    public static function getCurrencyCode()
    {
        return SettingsHelper::getCurrencyCode();
    }

    /**
     * Get Google Maps API key
     *
     * @return string|null
     */
    public static function getGoogleMapsApiKey()
    {
        return SettingsHelper::getGoogleMapsApiKey();
    }

    /**
     * Get PayPal client ID
     *
     * @return string|null
     */
    public static function getPaypalClientId()
    {
        return SettingsHelper::getPaypalClientId();
    }

    /**
     * Get company name
     *
     * @return string
     */
    public static function getCompanyName()
    {
        return SettingsHelper::getCompanyName();
    }

    /**
     * Get company email
     *
     * @return string
     */
    public static function getCompanyEmail()
    {
        return SettingsHelper::getCompanyEmail();
    }

    /**
     * Get company phone
     *
     * @return string
     */
    public static function getCompanyPhone()
    {
        return SettingsHelper::getCompanyPhone();
    }

    /**
     * Get company address
     *
     * @return string
     */
    public static function getCompanyAddress()
    {
        return SettingsHelper::getCompanyAddress();
    }

    /**
     * Get company description
     *
     * @return string
     */
    public static function getCompanyDescription()
    {
        return SettingsHelper::getCompanyDescription();
    }

    /**
     * Get primary color
     *
     * @return string
     */
    public static function getPrimaryColor()
    {
        return self::get('primary_color', '#ee393d');
    }

    /**
     * Get secondary color
     *
     * @return string
     */
    public static function getSecondaryColor()
    {
        return self::get('secondary_color', '#343a40');
    }

    /**
     * Get autocomplete settings
     *
     * @return array
     */
    public static function getAutocompleteSettings()
    {
        return [
            'enabled' => self::get('autocomplete_enabled', 'true') === 'true',
            'restrict_country' => self::get('autocomplete_restrict_country', 'false') === 'true',
            'country' => self::get('autocomplete_country', ''),
            'types' => self::get('autocomplete_types', 'geocode'), // Changed to geocode for better postal code support
            'bias_radius' => (int) self::get('autocomplete_bias_radius', 50),
            'use_strict_bounds' => self::get('autocomplete_use_strict_bounds', 'false') === 'true',
            'fields' => self::get('autocomplete_fields', 'formatted_address'),
        ];
    }

    /**
     * Get email settings
     *
     * @return array
     */
    public static function getEmailSettings()
    {
        return [
            'mail_driver' => self::get('mail_driver', 'smtp'),
            'mail_host' => self::get('mail_host', ''),
            'mail_port' => self::get('mail_port', '587'),
            'mail_username' => self::get('mail_username', ''),
            'mail_password' => self::get('mail_password', ''),
            'mail_encryption' => self::get('mail_encryption', 'tls'),
            'mail_from_address' => self::get('mail_from_address', ''),
            'mail_from_name' => self::get('mail_from_name', ''),
        ];
    }

    /**
     * Get social media settings
     *
     * @return array
     */
    public static function getSocialMediaSettings()
    {
        return [
            'facebook_url' => self::get('facebook_url', ''),
            'twitter_url' => self::get('twitter_url', ''),
            'instagram_url' => self::get('instagram_url', ''),
            'linkedin_url' => self::get('linkedin_url', ''),
        ];
    }

    /**
     * Get booking settings
     *
     * @return array
     */
    public static function getBookingSettings()
    {
        return [
            'advance_booking_time' => (int) self::get('advance_booking_time', 30),
            'minimum_hourly_duration' => (int) self::get('minimum_hourly_duration', 2),
            'cancellation_time_window' => (int) self::get('cancellation_time_window', 24),
            'cancellation_fee_percentage' => (int) self::get('cancellation_fee_percentage', 20),
            'allow_guest_bookings' => self::get('allow_guest_bookings', 'true') === 'true',
        ];
    }

    /**
     * Get extra services settings
     *
     * @return array
     */
    public static function getExtraServicesSettings()
    {
        return [
            'meet_and_greet' => [
                'enabled' => self::get('meet_and_greet_enabled', 'true') === 'true',
                'fee' => (float) self::get('meet_and_greet_fee', 10.00),
                'label' => 'Meet & Greet Service',
                'description' => 'Driver will meet you with a name sign'
            ],
            'child_seat' => [
                'enabled' => self::get('child_seat_enabled', 'true') === 'true',
                'fee' => (float) self::get('child_seat_fee', 15.00),
                'label' => 'Child Seat',
                'description' => 'Child safety seat provided'
            ],
            'wheelchair_accessible' => [
                'enabled' => self::get('wheelchair_enabled', 'true') === 'true',
                'fee' => (float) self::get('wheelchair_fee', 0.00),
                'label' => 'Wheelchair Accessible',
                'description' => 'Vehicle suitable for wheelchair access'
            ],
            'extra_luggage' => [
                'enabled' => self::get('extra_luggage_enabled', 'true') === 'true',
                'fee' => (float) self::get('extra_luggage_fee', 5.00),
                'label' => 'Extra Luggage Space',
                'description' => 'Additional luggage capacity required'
            ]
        ];
    }

    /**
     * Check if a specific extra service is enabled
     *
     * @param string $service
     * @return bool
     */
    public static function isExtraServiceEnabled($service)
    {
        $services = self::getExtraServicesSettings();
        return isset($services[$service]) && $services[$service]['enabled'];
    }

    /**
     * Get the fee for a specific extra service
     *
     * @param string $service
     * @return float
     */
    public static function getExtraServiceFee($service)
    {
        $services = self::getExtraServicesSettings();
        return isset($services[$service]) ? $services[$service]['fee'] : 0.00;
    }

    /**
     * Get driver settings
     *
     * @return array
     */
    public static function getDriverSettings()
    {
        return [
            'driver_commission_percentage' => (int) self::get('driver_commission_percentage', 80),
            'minimum_payout_amount' => (float) self::get('minimum_payout_amount', 50),
            'payout_schedule' => self::get('payout_schedule', 'weekly'),
            'maximum_active_hours' => (int) self::get('maximum_active_hours', 12),
            'driver_verification_requirements' => json_decode(self::get('driver_verification_requirements', '{}'), true),
        ];
    }



    /**
     * Get appearance settings
     *
     * @return array
     */
    public static function getAppearanceSettings()
    {
        return [
            'primary_color' => self::get('primary_color', '#ee393d'),
            'secondary_color' => self::get('secondary_color', '#343a40'),
            'logo' => self::get('logo', ''),
            'favicon' => self::get('favicon', ''),
            'default_language' => self::get('default_language', 'en'),
        ];
    }
}
