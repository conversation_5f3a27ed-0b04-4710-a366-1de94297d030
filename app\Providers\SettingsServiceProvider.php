<?php

namespace App\Providers;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load settings if the settings table exists
        if (Schema::hasTable('settings')) {
            // Share settings with all views
            View::composer('*', function ($view) {
                $view->with('appSettings', SettingsHelper::getAllSettings());
            });
        }
    }
}
