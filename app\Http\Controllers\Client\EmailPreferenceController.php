<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\EmailPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmailPreferenceController extends Controller
{
    /**
     * Display email preferences
     */
    public function index()
    {
        $user = Auth::user();
        $preferences = EmailPreference::getUserPreferences($user->id);
        $rolePreferences = EmailPreference::getPreferencesForRole($user->role);

        return view('client.email-preferences.index', compact('preferences', 'rolePreferences'));
    }

    /**
     * Update email preferences
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $rolePreferences = EmailPreference::getPreferencesForRole($user->role);

        $rules = [];
        foreach ($rolePreferences as $type => $label) {
            $rules["preferences.{$type}.enabled"] = 'boolean';
            $rules["preferences.{$type}.frequency"] = 'in:immediate,daily,weekly';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $preferences = $request->input('preferences', []);

        foreach ($rolePreferences as $type => $label) {
            $enabled = isset($preferences[$type]['enabled']) && $preferences[$type]['enabled'] == '1';
            $frequency = $preferences[$type]['frequency'] ?? 'immediate';

            EmailPreference::setPreference(
                $user->id,
                $type,
                $enabled,
                ['frequency' => $frequency]
            );
        }

        return redirect()->back()
            ->with('success', 'Email preferences updated successfully!');
    }

    /**
     * Reset preferences to default
     */
    public function reset()
    {
        $user = Auth::user();
        
        // Delete all existing preferences for the user
        EmailPreference::where('user_id', $user->id)->delete();

        return redirect()->back()
            ->with('success', 'Email preferences reset to default settings!');
    }
}
