<?php

namespace Database\Seeders;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Database\Seeder;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all bookings that are confirmed, in_progress, or completed
        $bookings = Booking::whereIn('status', ['confirmed', 'in_progress', 'completed'])->get();
        
        foreach ($bookings as $booking) {
            // Create payment record
            $payment = Payment::updateOrCreate(
                ['booking_id' => $booking->id],
                [
                    'user_id' => $booking->user_id,
                    'transaction_id' => 'TXN' . time() . rand(1000, 9999),
                    'payment_method' => 'paypal',
                    'amount' => $booking->amount,
                    'status' => 'completed',
                    'payment_details' => json_encode([
                        'payer' => [
                            'name' => [
                                'given_name' => $booking->user->name,
                                'surname' => '',
                            ],
                            'email_address' => $booking->user->email,
                        ],
                        'purchase_units' => [
                            [
                                'amount' => [
                                    'value' => $booking->amount,
                                    'currency_code' => 'USD',
                                ],
                            ],
                        ],
                        'status' => 'COMPLETED',
                    ]),
                ]
            );
            
            // Add payment history to booking
            $booking->addHistory('payment_completed', [
                'payment_id' => $payment->id,
                'transaction_id' => $payment->transaction_id,
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method,
            ], $booking->user_id);
        }
    }
}
