<?php $__env->startSection('title', 'Earnings Reports'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .reports-container {
        padding: 20px;
    }

    .reports-header {
        margin-bottom: 30px;
    }

    .reports-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .reports-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 20px;
    }

    .reports-card .card-body {
        padding: 30px;
    }

    .report-type-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 25px;
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .report-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .report-type-icon {
        font-size: 2.5rem;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .report-type-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .report-type-desc {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .quarterly-report {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .quarterly-report-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #343a40;
    }

    .quarter-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.3s;
    }

    .quarter-card:hover {
        transform: translateY(-5px);
    }

    .quarter-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #343a40;
    }

    .quarter-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: #ee393d;
        margin-bottom: 10px;
    }

    .quarter-months {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 30px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="reports-container">
    <div class="reports-header">
        <h2><i class="fas fa-file-invoice-dollar me-2"></i> Earnings Reports</h2>
        <p class="text-muted">Download and view your earnings reports for tax and financial planning purposes</p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="reports-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-download me-2"></i> Available Reports</h5>
                </div>
                <div class="card-body">
                    <div class="report-type-card">
                        <div class="report-type-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h5 class="report-type-title">Annual Tax Report</h5>
                        <p class="report-type-desc">Download your annual earnings report for tax filing purposes</p>
                        
                        <form action="<?php echo e(route('driver.earnings.download-statement')); ?>" method="POST" class="mb-3">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="tax-year" class="form-label">Select Year</label>
                                <select class="form-select" id="tax-year" name="year">
                                    <?php $__currentLoopData = $availableYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <input type="hidden" name="month" value="1">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-download me-2"></i> Download Annual Report
                            </button>
                        </form>
                    </div>

                    <div class="report-type-card">
                        <div class="report-type-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h5 class="report-type-title">Quarterly Reports</h5>
                        <p class="report-type-desc">Download quarterly earnings reports for financial planning</p>
                        
                        <form action="<?php echo e(route('driver.earnings.download-statement')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="quarter" class="form-label">Quarter</label>
                                    <select class="form-select" id="quarter" name="quarter">
                                        <option value="1">Q1 (Jan-Mar)</option>
                                        <option value="2">Q2 (Apr-Jun)</option>
                                        <option value="3">Q3 (Jul-Sep)</option>
                                        <option value="4">Q4 (Oct-Dec)</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="quarter-year" class="form-label">Year</label>
                                    <select class="form-select" id="quarter-year" name="year">
                                        <?php $__currentLoopData = $availableYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" name="month" value="1">
                            <input type="hidden" name="report_type" value="quarterly">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-download me-2"></i> Download Quarterly Report
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="reports-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i> <?php echo e($currentYear); ?> Quarterly Earnings</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="quarterlyChart"></canvas>
                    </div>

                    <div class="quarterly-report">
                        <h5 class="quarterly-report-title">Quarterly Breakdown</h5>
                        <div class="row">
                            <?php for($quarter = 1; $quarter <= 4; $quarter++): ?>
                                <div class="col-md-6 col-lg-3">
                                    <div class="quarter-card">
                                        <h6 class="quarter-title">Q<?php echo e($quarter); ?></h6>
                                        <div class="quarter-amount"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($quarterlyEarnings[$quarter], 2)); ?></div>
                                        <div class="quarter-months">
                                            <?php if($quarter == 1): ?>
                                                Jan - Mar
                                            <?php elseif($quarter == 2): ?>
                                                Apr - Jun
                                            <?php elseif($quarter == 3): ?>
                                                Jul - Sep
                                            <?php else: ?>
                                                Oct - Dec
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="<?php echo e(route('driver.earnings.statement')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-file-alt me-2"></i> View Monthly Statements
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quarterly earnings chart
        const quarterlyData = [
            <?php echo e($quarterlyEarnings[1]); ?>,
            <?php echo e($quarterlyEarnings[2]); ?>,
            <?php echo e($quarterlyEarnings[3]); ?>,
            <?php echo e($quarterlyEarnings[4]); ?>

        ];

        const ctx = document.getElementById('quarterlyChart').getContext('2d');
        const quarterlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Q1 (Jan-Mar)', 'Q2 (Apr-Jun)', 'Q3 (Jul-Sep)', 'Q4 (Oct-Dec)'],
                datasets: [{
                    label: 'Quarterly Earnings (<?php echo e($currentYear); ?>)',
                    data: quarterlyData,
                    backgroundColor: [
                        'rgba(238, 57, 61, 0.7)',
                        'rgba(238, 57, 61, 0.7)',
                        'rgba(238, 57, 61, 0.7)',
                        'rgba(238, 57, 61, 0.7)'
                    ],
                    borderColor: [
                        '#ee393d',
                        '#ee393d',
                        '#ee393d',
                        '#ee393d'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?php echo e($currencySymbol ?? '£'); ?>' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Earnings: <?php echo e($currencySymbol ?? '£'); ?>' + context.raw.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.driver', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\driver\earnings\reports.blade.php ENDPATH**/ ?>