<?php $__env->startSection('title', 'Notifications'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }

    .notification-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }

    .notification-card:hover {
        transform: translateY(-5px);
    }

    .notification-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .notification-card .card-body {
        padding: 20px;
    }

    .notification-card.unread {
        border-left: 4px solid #ee393d;
    }

    .notification-time {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .notification-actions {
        margin-top: 10px;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .notification-icon i {
        font-size: 1.2rem;
        color: #ee393d;
    }

    .notification-content {
        flex: 1;
    }

    .notification-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .notification-message {
        color: #6c757d;
    }

    .mark-all-read {
        margin-bottom: 20px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Notifications</h2>
                <?php if($unreadCount > 0): ?>
                    <form action="<?php echo e(route('notifications.mark-all-read')); ?>" method="POST" class="mark-all-read">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-check-double me-2"></i> Mark All as Read
                        </button>
                    </form>
                <?php endif; ?>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if($notifications->isEmpty()): ?>
                <div class="alert alert-info">
                    You don't have any notifications yet.
                </div>
            <?php else: ?>
                <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card notification-card <?php echo e($notification->read_at ? '' : 'unread'); ?>">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="notification-icon">
                                    <?php if(isset($notification->data['new_status']) && $notification->data['new_status'] === 'confirmed'): ?>
                                        <i class="fas fa-check-circle"></i>
                                    <?php elseif(isset($notification->data['new_status']) && $notification->data['new_status'] === 'assigned'): ?>
                                        <i class="fas fa-user"></i>
                                    <?php elseif(isset($notification->data['new_status']) && $notification->data['new_status'] === 'in_progress'): ?>
                                        <i class="fas fa-car"></i>
                                    <?php elseif(isset($notification->data['new_status']) && $notification->data['new_status'] === 'completed'): ?>
                                        <i class="fas fa-flag-checkered"></i>
                                    <?php elseif(isset($notification->data['new_status']) && $notification->data['new_status'] === 'cancelled'): ?>
                                        <i class="fas fa-times-circle"></i>
                                    <?php else: ?>
                                        <i class="fas fa-bell"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">
                                        <?php if(isset($notification->data['booking_number'])): ?>
                                            Booking #<?php echo e($notification->data['booking_number']); ?>

                                        <?php else: ?>
                                            Notification
                                        <?php endif; ?>
                                    </div>
                                    <div class="notification-message">
                                        <?php echo e($notification->data['message'] ?? 'You have a new notification.'); ?>

                                    </div>
                                    <div class="notification-time">
                                        <?php echo e($notification->created_at->diffForHumans()); ?>

                                    </div>
                                    <div class="notification-actions">
                                        <?php if(isset($notification->data['booking_id'])): ?>
                                            <?php if(Auth::user()->hasRole('client')): ?>
                                                <a href="<?php echo e(route('client.bookings.show', $notification->data['booking_id'])); ?>" class="btn btn-sm btn-primary">
                                                    View Booking
                                                </a>
                                            <?php elseif(Auth::user()->hasRole('driver')): ?>
                                                <a href="<?php echo e(route('driver.rides.show', $notification->data['booking_id'])); ?>" class="btn btn-sm btn-primary">
                                                    View Ride
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php if(!$notification->read_at): ?>
                                            <form action="<?php echo e(route('notifications.mark-as-read', $notification->id)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="btn btn-sm btn-outline-secondary">
                                                    Mark as Read
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <div class="d-flex justify-content-center">
                    <?php echo e($notifications->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make(Auth::user()->hasRole('admin') ? 'layouts.admin' : (Auth::user()->hasRole('driver') ? 'layouts.driver' : 'layouts.client'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\shared\notifications.blade.php ENDPATH**/ ?>