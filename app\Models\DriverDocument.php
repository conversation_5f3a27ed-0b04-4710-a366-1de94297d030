<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DriverDocument extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'driver_id',
        'document_type',
        'file_path',
        'expiry_date',
        'is_verified',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expiry_date' => 'date',
        'is_verified' => 'boolean',
    ];

    /**
     * Get the driver that owns the document.
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    /**
     * Check if the document is expired.
     *
     * @return bool
     */
    public function isExpired()
    {
        if (!$this->expiry_date) {
            return false;
        }

        return $this->expiry_date->isPast();
    }

    /**
     * Check if document is expiring soon (within 30 days)
     *
     * @return bool
     */
    public function isExpiringSoon()
    {
        if (!$this->expiry_date) {
            return false;
        }

        return !$this->isExpired() && $this->expiry_date->diffInDays(now()) <= 30;
    }

    /**
     * Get document category
     *
     * @return string
     */
    public function getCategory()
    {
        $driverLicenseTypes = ['Driver License'];
        $driverPcoLicenseTypes = ['Driver PCO License'];
        $vehiclePcoLicenseTypes = ['Vehicle PCO License'];
        $insuranceTypes = ['Insurance'];
        $motTypes = ['MOT Certificate'];
        $v5cTypes = ['V5C Logbook'];
        $vehiclePhotoTypes = ['Vehicle Photos'];

        if (in_array($this->document_type, $driverLicenseTypes)) {
            return 'driver_license';
        } elseif (in_array($this->document_type, $driverPcoLicenseTypes)) {
            return 'driver_pco_license';
        } elseif (in_array($this->document_type, $vehiclePcoLicenseTypes)) {
            return 'vehicle_pco_license';
        } elseif (in_array($this->document_type, $insuranceTypes)) {
            return 'insurance';
        } elseif (in_array($this->document_type, $motTypes)) {
            return 'mot';
        } elseif (in_array($this->document_type, $v5cTypes)) {
            return 'v5c';
        } elseif (in_array($this->document_type, $vehiclePhotoTypes)) {
            return 'vehicle_photos';
        } else {
            return 'other';
        }
    }


}
