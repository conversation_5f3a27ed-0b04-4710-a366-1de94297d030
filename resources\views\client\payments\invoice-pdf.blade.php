<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $payment->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 20px;
        }

        .invoice-logo {
            max-height: 60px;
        }

        .invoice-company {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .invoice-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }

        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .invoice-details-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .invoice-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: left;
            padding: 10px;
            border-bottom: 2px solid #ddd;
        }

        .invoice-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

        .invoice-table .text-right {
            text-align: right;
        }

        .invoice-total {
            font-size: 18px;
            font-weight: bold;
        }

        .invoice-notes {
            font-size: 12px;
            color: #666;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .invoice-footer {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .payment-method {
            display: inline-block;
            padding: 5px 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            font-size: 12px;
            margin-top: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div>
                <h1 class="invoice-company">Ynr Cars</h1>
                <div>123 Main Street</div>
                <div>New York, NY 10001</div>
                <div>United States</div>
                <div><EMAIL></div>
            </div>
            <div>
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-subtitle">Invoice #{{ $payment->id }}</div>
                <div>Date: {{ $payment->created_at->format('F d, Y') }}</div>
                <div>Status: {{ ucfirst($payment->status) }}</div>
            </div>
        </div>

        <div class="invoice-details">
            <div>
                <div class="invoice-details-title">Bill To:</div>
                <div>{{ $payment->booking->user->name }}</div>
                <div>{{ $payment->booking->user->email }}</div>
                <div>{{ $payment->booking->user->phone ?? 'N/A' }}</div>
                <div>{{ $payment->booking->user->address ?? 'N/A' }}</div>
            </div>
            <div>
                <div class="invoice-details-title">Payment Details:</div>
                <div>Payment Method: {{ ucfirst($payment->payment_method) }}</div>
                <div>Transaction ID: {{ $payment->transaction_id ?? 'N/A' }}</div>
                <div>Payment Date: {{ $payment->updated_at->format('F d, Y') }}</div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Booking Details</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <strong>{{ $payment->booking->vehicle->name }}</strong><br>
                        {{ $payment->booking->booking_type }} Booking<br>
                        Booking #{{ $payment->booking->booking_number }}
                    </td>
                    <td>
                        Pickup: {{ $payment->booking->pickup_date->format('M d, Y h:i A') }}<br>
                        @if($payment->booking->return_date)
                            Return: {{ $payment->booking->return_date->format('M d, Y h:i A') }}<br>
                        @endif
                        Duration: {{ $payment->booking->duration_hours }} hours<br>
                        @if($payment->booking->distance)
                            Distance: {{ $payment->booking->distance }} km
                        @endif
                    </td>
                    <td class="text-right">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($payment->amount, 2) }}</td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="2" class="text-right">Subtotal:</td>
                    <td class="text-right">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($payment->amount * 0.9, 2) }}</td>
                </tr>
                <tr>
                    <td colspan="2" class="text-right">Tax (10%):</td>
                    <td class="text-right">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($payment->amount * 0.1, 2) }}</td>
                </tr>
                <tr>
                    <td colspan="2" class="text-right invoice-total">Total:</td>
                    <td class="text-right invoice-total">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($payment->amount, 2) }}</td>
                </tr>
            </tfoot>
        </table>

        <div class="invoice-notes">
            <strong>Notes:</strong>
            <p>Thank you for choosing Ynr Cars. This invoice serves as your official receipt for the services provided. Please retain this document for your records.</p>
        </div>

        <div class="invoice-footer">
            <p>If you have any questions about this invoice, please contact our customer <NAME_EMAIL></p>
        </div>
    </div>
</body>
</html>
