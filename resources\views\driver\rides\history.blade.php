@extends('layouts.driver')

@section('title', 'Ride History')

@section('styles')
<style>
    .ride-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }

    .ride-card:hover {
        transform: translateY(-5px);
    }

    .ride-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .client-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .client-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 15px;
    }

    .ride-details {
        margin-bottom: 15px;
    }

    .ride-detail-item {
        margin-bottom: 10px;
    }

    .ride-detail-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .ride-detail-value {
        color: #6c757d;
    }

    .ride-actions {
        display: flex;
        justify-content: flex-end;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Ride History</h2>
        <div>
            <a href="{{ route('driver.rides.my-rides') }}" class="btn btn-primary">
                <i class="fas fa-car me-2"></i> Current Rides
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Completed Rides</h5>
        </div>
        <div class="card-body">
            @if($completedRides->isEmpty())
                <div class="alert alert-info">
                    No completed rides found.
                </div>
            @else
                <div class="row">
                    @foreach($completedRides as $ride)
                        <div class="col-md-6">
                            <div class="card ride-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Booking #{{ $ride->booking_number }}</h5>
                                    <span class="status-badge status-completed">
                                        Completed
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="client-info">
                                        @if($ride->user->profile_photo)
                                            <img src="{{ asset('storage/' . $ride->user->profile_photo) }}" class="client-img" alt="Client">
                                        @else
                                            <img src="https://via.placeholder.com/50x50?text=Client" class="client-img" alt="Client">
                                        @endif
                                        <div>
                                            <h6 class="mb-0">Client #{{ substr($ride->user->id, 0, 5) }}</h6>
                                            <small class="text-muted">Booking #{{ $ride->booking_number }}</small>
                                        </div>
                                    </div>

                                    <div class="ride-details">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Pickup</div>
                                                    <div class="ride-detail-value">{{ $ride->pickup_address }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Dropoff</div>
                                                    <div class="ride-detail-value">{{ $ride->dropoff_address }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Date</div>
                                                    <div class="ride-detail-value">{{ $ride->pickup_date->format('M d, Y h:i A') }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Completed</div>
                                                    <div class="ride-detail-value">{{ $ride->completed_at ? $ride->completed_at->format('M d, Y h:i A') : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Vehicle</div>
                                                    <div class="ride-detail-value">{{ $ride->vehicle->name }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="ride-detail-item">
                                                    <div class="ride-detail-label">Amount</div>
                                                    <div class="ride-detail-value">@currency(){{ number_format($ride->amount, 2) }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        @if($ride->distance)
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="ride-detail-item">
                                                        <div class="ride-detail-label">Distance</div>
                                                        <div class="ride-detail-value">{{ number_format($ride->distance, 1) }} km</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="ride-detail-item">
                                                        <div class="ride-detail-label">Duration</div>
                                                        <div class="ride-detail-value">
                                                            @if($ride->started_at && $ride->completed_at)
                                                                {{ $ride->started_at->diffInMinutes($ride->completed_at) }} minutes
                                                            @else
                                                                N/A
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="ride-actions">
                                        <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="d-flex justify-content-center mt-4">
                    {{ $completedRides->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
