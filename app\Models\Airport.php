<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Airport extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'city',
        'country',
        'country_code',
        'latitude',
        'longitude',
        'timezone',
        'address',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get bookings where this airport is the pickup location
     */
    public function pickupBookings()
    {
        return $this->hasMany(Booking::class, 'pickup_airport_id');
    }

    /**
     * Get bookings where this airport is the dropoff location
     */
    public function dropoffBookings()
    {
        return $this->hasMany(Booking::class, 'dropoff_airport_id');
    }

    /**
     * Get all bookings related to this airport (pickup or dropoff)
     */
    public function allBookings()
    {
        return Booking::where('pickup_airport_id', $this->id)
            ->orWhere('dropoff_airport_id', $this->id);
    }

    /**
     * Get the total number of bookings for this airport
     */
    public function getTotalBookingsAttribute()
    {
        return $this->allBookings()->count();
    }

    /**
     * Get the total number of bookings this month for this airport
     */
    public function getThisMonthBookingsAttribute()
    {
        return $this->allBookings()
            ->whereMonth('pickup_date', now()->month)
            ->whereYear('pickup_date', now()->year)
            ->count();
    }
}
