<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class ViaBookingService
{
    /**
     * Maximum number of via points allowed
     */
    const MAX_VIA_POINTS = 5;

    /**
     * Default stop duration in minutes (removed from UI but kept for calculations)
     */
    const DEFAULT_STOP_DURATION = 0;

    /**
     * Calculate fare with via points
     *
     * @param array $routeData
     * @param int $vehicleId
     * @param array $viaPoints
     * @return array
     */
    public static function calculateFareWithViaPoints($routeData, $vehicleId, $viaPoints = [])
    {
        try {
            // Get base fare calculation
            $baseFare = FareCalculationService::calculateFare($routeData, $vehicleId);

            if (empty($viaPoints)) {
                return $baseFare;
            }

            // Calculate via points surcharge
            $viaSurcharge = self::calculateViaSurcharge($viaPoints, $vehicleId);

            // Calculate additional distance and time for via points
            $viaCalculations = self::calculateViaPointsRoute($routeData, $viaPoints);

            // Update fare with via points
            $totalFare = $baseFare['total_fare'] + $viaSurcharge;

            return [
                'base_fare' => $baseFare['base_fare'],
                'distance_fare' => $baseFare['distance_fare'],
                'time_fare' => $baseFare['time_fare'],
                'via_surcharge' => $viaSurcharge,
                'total_fare' => $totalFare,
                'distance' => $viaCalculations['total_distance'],
                'duration' => $viaCalculations['total_duration'],
                'via_count' => count($viaPoints),
                'via_details' => $viaCalculations['via_details'],
                'breakdown' => array_merge($baseFare['breakdown'] ?? [], [
                    'via_points' => [
                        'count' => count($viaPoints),
                        'surcharge' => $viaSurcharge,
                        'details' => $viaCalculations['via_details']
                    ]
                ])
            ];

        } catch (\Exception $e) {
            Log::error('Via booking fare calculation failed', [
                'error' => $e->getMessage(),
                'route_data' => $routeData,
                'via_points' => $viaPoints
            ]);

            // Return base fare if via calculation fails
            return FareCalculationService::calculateFare($routeData, $vehicleId);
        }
    }

    /**
     * Calculate surcharge for via points
     *
     * @param array $viaPoints
     * @param int $vehicleId
     * @return float
     */
    public static function calculateViaSurcharge($viaPoints, $vehicleId)
    {
        $viaCount = count($viaPoints);

        if ($viaCount === 0) {
            return 0;
        }

        // Get via point surcharge settings
        $surchargePerPoint = (float) Setting::where('key', 'via_point_surcharge')->value('value') ?? 5.00;
        $maxSurcharge = (float) Setting::where('key', 'max_via_surcharge')->value('value') ?? 25.00;

        // Calculate base surcharge
        $baseSurcharge = $viaCount * $surchargePerPoint;

        // Apply maximum limit
        $surcharge = min($baseSurcharge, $maxSurcharge);

        // Apply vehicle-specific multiplier if needed
        $vehicleMultiplier = self::getVehicleViaSurchargeMultiplier($vehicleId);

        return round($surcharge * $vehicleMultiplier, 2);
    }

    /**
     * Get vehicle-specific via surcharge multiplier
     *
     * @param int $vehicleId
     * @return float
     */
    private static function getVehicleViaSurchargeMultiplier($vehicleId)
    {
        // This could be expanded to have vehicle-specific multipliers
        // For now, return 1.0 (no multiplier)
        return 1.0;
    }

    /**
     * Calculate route with via points
     *
     * @param array $routeData
     * @param array $viaPoints
     * @return array
     */
    public static function calculateViaPointsRoute($routeData, $viaPoints)
    {
        $pickup = [
            'lat' => $routeData['pickup_lat'],
            'lng' => $routeData['pickup_lng']
        ];

        $dropoff = [
            'lat' => $routeData['dropoff_lat'],
            'lng' => $routeData['dropoff_lng']
        ];

        // Build waypoints array
        $waypoints = [];
        foreach ($viaPoints as $point) {
            $waypoints[] = [
                'lat' => $point['lat'],
                'lng' => $point['lng'],
                'address' => $point['address']
            ];
        }

        // Calculate route with Google Maps API
        $routeCalculation = self::calculateRouteWithWaypoints($pickup, $dropoff, $waypoints);

        return [
            'total_distance' => $routeCalculation['distance'],
            'total_duration' => $routeCalculation['duration'],
            'via_details' => $routeCalculation['via_details']
        ];
    }

    /**
     * Calculate route with waypoints using Google Maps API
     *
     * @param array $origin
     * @param array $destination
     * @param array $waypoints
     * @return array
     */
    private static function calculateRouteWithWaypoints($origin, $destination, $waypoints)
    {
        try {
            $apiKey = Setting::where('key', 'google_maps_api_key')->value('value');

            if (!$apiKey) {
                throw new \Exception('Google Maps API key not configured');
            }

            // Build waypoints string for API
            $waypointsStr = '';
            if (!empty($waypoints)) {
                $waypointCoords = array_map(function($wp) {
                    return $wp['lat'] . ',' . $wp['lng'];
                }, $waypoints);
                $waypointsStr = '&waypoints=' . implode('|', $waypointCoords);
            }

            // Build API URL
            $url = "https://maps.googleapis.com/maps/api/directions/json?" .
                   "origin={$origin['lat']},{$origin['lng']}" .
                   "&destination={$destination['lat']},{$destination['lng']}" .
                   $waypointsStr .
                   "&key={$apiKey}";

            // Make API request
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if ($data['status'] !== 'OK') {
                throw new \Exception('Google Maps API error: ' . $data['status']);
            }

            $route = $data['routes'][0];
            $legs = $route['legs'];

            $totalDistance = 0;
            $totalDuration = 0;
            $viaDetails = [];

            foreach ($legs as $index => $leg) {
                $totalDistance += $leg['distance']['value']; // in meters
                $totalDuration += $leg['duration']['value']; // in seconds

                if ($index < count($waypoints)) {
                    $viaDetails[] = [
                        'index' => $index + 1,
                        'address' => $waypoints[$index]['address'],
                        'distance_to_here' => $leg['distance']['value'],
                        'duration_to_here' => $leg['duration']['value']
                    ];
                }
            }

            return [
                'distance' => round($totalDistance / 1000, 2), // convert to km
                'duration' => round($totalDuration / 60), // convert to minutes
                'via_details' => $viaDetails
            ];

        } catch (\Exception $e) {
            Log::error('Via points route calculation failed', [
                'error' => $e->getMessage(),
                'origin' => $origin,
                'destination' => $destination,
                'waypoints' => $waypoints
            ]);

            // Fallback: estimate based on direct distance
            return self::estimateViaPointsRoute($origin, $destination, $waypoints);
        }
    }

    /**
     * Estimate route with via points (fallback method)
     *
     * @param array $origin
     * @param array $destination
     * @param array $waypoints
     * @return array
     */
    private static function estimateViaPointsRoute($origin, $destination, $waypoints)
    {
        // Simple estimation: calculate distance between consecutive points
        $points = array_merge([$origin], $waypoints, [$destination]);

        $totalDistance = 0;
        $totalDuration = 0;
        $viaDetails = [];

        for ($i = 0; $i < count($points) - 1; $i++) {
            $distance = self::calculateHaversineDistance(
                $points[$i]['lat'], $points[$i]['lng'],
                $points[$i + 1]['lat'], $points[$i + 1]['lng']
            );

            $totalDistance += $distance;

            // Estimate duration (assuming 30 km/h average speed in city)
            $duration = ($distance / 30) * 60; // minutes
            $totalDuration += $duration;

            if ($i < count($waypoints)) {
                $viaDetails[] = [
                    'index' => $i + 1,
                    'address' => $waypoints[$i]['address'],
                    'distance_to_here' => $distance,
                    'duration_to_here' => $duration
                ];
            }
        }

        return [
            'distance' => round($totalDistance, 2),
            'duration' => round($totalDuration),
            'via_details' => $viaDetails
        ];
    }

    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers
     */
    private static function calculateHaversineDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Validate via points data
     *
     * @param array $viaPoints
     * @return array
     */
    public static function validateViaPoints($viaPoints)
    {
        $errors = [];

        if (count($viaPoints) > self::MAX_VIA_POINTS) {
            $errors[] = "Maximum " . self::MAX_VIA_POINTS . " via points allowed";
        }

        foreach ($viaPoints as $index => $point) {
            $pointNumber = $index + 1;

            if (empty($point['address'])) {
                $errors[] = "Via point {$pointNumber}: Address is required";
            }

            if (!isset($point['lat']) || !isset($point['lng'])) {
                $errors[] = "Via point {$pointNumber}: Coordinates are required";
            }

            // Stop duration validation removed - no longer used in UI
        }

        return $errors;
    }

    /**
     * Format via points for storage
     *
     * @param array $viaPoints
     * @return array
     */
    public static function formatViaPointsForStorage($viaPoints)
    {
        return array_map(function($point) {
            return [
                'address' => $point['address'] ?? '',
                'lat' => (float) ($point['lat'] ?? 0),
                'lng' => (float) ($point['lng'] ?? 0),
                'notes' => $point['notes'] ?? ''
            ];
        }, $viaPoints);
    }
}
