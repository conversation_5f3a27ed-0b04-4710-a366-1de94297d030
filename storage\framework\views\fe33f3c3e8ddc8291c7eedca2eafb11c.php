<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="currency-symbol" content="<?php echo e($currencySymbol ?? \App\Services\SettingsService::getCurrencySymbol()); ?>">
    <meta name="currency-code" content="<?php echo e($currencyCode ?? \App\Services\SettingsService::getCurrencyCode()); ?>">
    <meta name="distance-unit" content="<?php echo e(\App\Services\SettingsService::getDistanceUnit()); ?>">

    <!-- Autocomplete Settings -->
    <?php
        $autocompleteSettings = \App\Services\SettingsService::getAutocompleteSettings();
    ?>
    <meta name="autocomplete-enabled" content="<?php echo e($autocompleteSettings['enabled'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-restrict-country" content="<?php echo e($autocompleteSettings['restrict_country'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-country" content="<?php echo e($autocompleteSettings['country']); ?>">
    <meta name="autocomplete-types" content="<?php echo e($autocompleteSettings['types']); ?>">
    <meta name="autocomplete-bias-radius" content="<?php echo e($autocompleteSettings['bias_radius']); ?>">
    <meta name="autocomplete-use-strict-bounds" content="<?php echo e($autocompleteSettings['use_strict_bounds'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-fields" content="<?php echo e($autocompleteSettings['fields']); ?>">

    <title><?php echo $__env->yieldContent('title'); ?> - <?php echo e($companyName ?? config('app.name', 'Ynr Cars')); ?></title>

    <!-- Favicon -->
    <?php if(\App\Services\SettingsService::get('favicon')): ?>
        <link rel="icon" href="<?php echo e(asset('storage/' . \App\Services\SettingsService::get('favicon'))); ?>" type="image/x-icon">
    <?php endif; ?>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/pagination.css')); ?>">

    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            --bs-primary: <?php echo e($primaryColor ?? '#ee393d'); ?>;
            --bs-secondary: <?php echo e($secondaryColor ?? '#343a40'); ?>;
        }

        .btn-primary {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: <?php echo e($colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10)); ?>;
            border-color: <?php echo e($colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10)); ?>;
        }

        .btn-outline-primary {
            color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: var(--bs-secondary);
        }

        .navbar-light {
            background-color: white;
        }

        .text-primary {
            color: var(--bs-primary) !important;
        }

        .bg-primary {
            background-color: var(--bs-primary) !important;
        }

        a {
            color: var(--bs-primary);
        }

        a:hover {
            color: <?php echo e($colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -15)); ?>;
        }

        /* Custom preloader spinner */
        .preloader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--bs-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>

    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader">
        <div class="preloader-content">
            <h2 style="color: <?php echo e($primaryColor ?? '#ee393d'); ?>; font-weight: 700; margin-bottom: 20px;"><?php echo e($companyName ?? 'YNR CARS'); ?></h2>
            <div class="preloader-spinner"></div>
        </div>
    </div>

    <header>
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                    <?php
                        $logoPath = \App\Services\SettingsService::get('logo');
                        $logoExists = $logoPath && file_exists(public_path('storage/' . $logoPath));
                    ?>

                    <?php if($logoExists): ?>
                        <img src="<?php echo e(asset('storage/' . $logoPath)); ?>" alt="<?php echo e($companyName ?? config('app.name', 'Ynr Cars')); ?>" height="40">
                    <?php else: ?>
                        <span style="font-weight: bold; font-size: 24px; color: #ee393d;">YNR <span style="color: #343a40;">Cars</span></span>
                    <?php endif; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>" href="<?php echo e(route('home')); ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('services') ? 'active' : ''); ?>" href="<?php echo e(route('services')); ?>">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('fleet') ? 'active' : ''); ?>" href="<?php echo e(route('fleet')); ?>">Fleet</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>" href="<?php echo e(route('about')); ?>">About Us</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>" href="<?php echo e(route('contact')); ?>">Contact</a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('terms-and-conditions') ? 'active' : ''); ?>" href="<?php echo e(route('terms-and-conditions')); ?>">T&C</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <?php if(auth()->guard()->guest()): ?>
                            <a href="<?php echo e(route('login')); ?>" class="btn btn-link text-black text-decoration-none">Login</a>
                            <a href="<?php echo e(route('register')); ?>" class="btn btn-primary ms-2">Register</a>
                        <?php else: ?>
                            <div class="dropdown">
                                <a class="btn btn-link text-black text-decoration-none dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <?php echo e(Auth::user()->name); ?>

                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <?php if(Auth::user()->role === 'admin'): ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                                    <?php elseif(Auth::user()->role === 'client'): ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('client.dashboard')); ?>">Dashboard</a></li>
                                    <?php elseif(Auth::user()->role === 'driver'): ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('driver.dashboard')); ?>">Dashboard</a></li>
                                    <?php else: ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                                    <?php endif; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="dropdown-item">Logout</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>

        <?php if (! empty(trim($__env->yieldContent('page-header')))): ?>
            <div class="page-header">
                <div class="container">
                    <h1><?php echo $__env->yieldContent('page-header'); ?></h1>
                    <?php if (! empty(trim($__env->yieldContent('breadcrumbs')))): ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <?php echo $__env->yieldContent('breadcrumbs'); ?>
                            </ol>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </header>

    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5><?php echo e(config('app.name', 'Ynr Cars')); ?></h5>
                    <p>Ynr Cars Taxi Services for all your needs. Experience comfort, reliability, and elegance.</p>
                    <div class="social-icons">
                        <?php if($socialMediaSettings['facebook_url']): ?>
                            <a href="<?php echo e($socialMediaSettings['facebook_url']); ?>" target="_blank" rel="noopener"><i class="fab fa-facebook-f"></i></a>
                        <?php endif; ?>
                        <?php if($socialMediaSettings['twitter_url']): ?>
                            <a href="<?php echo e($socialMediaSettings['twitter_url']); ?>" target="_blank" rel="noopener"><i class="fab fa-twitter"></i></a>
                        <?php endif; ?>
                        <?php if($socialMediaSettings['instagram_url']): ?>
                            <a href="<?php echo e($socialMediaSettings['instagram_url']); ?>" target="_blank" rel="noopener"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                        <?php if($socialMediaSettings['linkedin_url']): ?>
                            <a href="<?php echo e($socialMediaSettings['linkedin_url']); ?>" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="<?php echo e(route('about')); ?>">About Us</a></li>
                        <li><a href="<?php echo e(route('services')); ?>">Services</a></li>
                        <li><a href="<?php echo e(route('fleet')); ?>">Fleet</a></li>

                        <li><a href="<?php echo e(route('faq')); ?>">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Support</h5>
                    <ul>
                        <li><a href="<?php echo e(route('contact')); ?>">Contact</a></li>
                        <li><a href="<?php echo e(route('privacy-policy')); ?>">Privacy Policy</a></li>
                        <li><a href="<?php echo e(route('terms-and-conditions')); ?>">Terms and Conditions</a></li>
                        <li><a href="<?php echo e(route('faq')); ?>">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Contact Us</h5>
                    <p>
                        <i class="fas fa-phone me-2"></i> <?php echo e($companyPhone); ?><br>
                        <i class="fas fa-envelope me-2"></i> <?php echo e($companyEmail); ?><br>
                        <i class="fas fa-map-marker-alt me-2"></i> <?php echo e($companyAddress); ?>

                    </p>
                </div>
            </div>
            <div class="text-center copyright">
                <p>&copy; <?php echo e(date('Y')); ?> <?php echo e($companyName); ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="<?php echo e(asset('js/main.js')); ?>"></script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/layouts/guest.blade.php ENDPATH**/ ?>