<?php $__env->startSection('title', 'Password Reset Request'); ?>

<?php $__env->startSection('content'); ?>
<h2>Password Reset Request</h2>

<p>Dear <?php echo e($user->name ?? 'User'); ?>,</p>

<p>We received a request to reset the password for your <?php echo e($companyName); ?> account.</p>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: #856404; margin-top: 0;">🔐 Password Reset Required</h3>
    <p style="color: #856404; margin-bottom: 0;">
        Click the button below to reset your password. This link will expire in 60 minutes.
    </p>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e($resetUrl); ?>" class="btn" style="background-color: #dc3545; font-size: 18px; padding: 15px 30px;">
        Reset My Password
    </a>
</div>

<h3>🔒 Security Information:</h3>
<div class="booking-details">
    <div class="detail-row">
        <span class="detail-label">Request Time:</span>
        <span class="detail-value"><?php echo e(now()->format('F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">IP Address:</span>
        <span class="detail-value"><?php echo e($ipAddress ?? 'Not available'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Browser:</span>
        <span class="detail-value"><?php echo e($userAgent ?? 'Not available'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Link Expires:</span>
        <span class="detail-value"><?php echo e(now()->addMinutes(60)->format('F j, Y \a\t g:i A')); ?></span>
    </div>
</div>

<h3>⚠️ Important Security Notes:</h3>
<ul>
    <li><strong>Link Expiration:</strong> This reset link will expire in 60 minutes for security</li>
    <li><strong>One-Time Use:</strong> The link can only be used once</li>
    <li><strong>Secure Connection:</strong> Always ensure you're on our official website</li>
    <li><strong>Strong Password:</strong> Choose a strong, unique password</li>
</ul>

<div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h4 style="color: #721c24; margin-top: 0;">🚨 Didn't Request This?</h4>
    <p style="color: #721c24; margin-bottom: 0;">
        If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
        However, if you're concerned about your account security, please contact us immediately.
    </p>
</div>

<h3>🛡️ Password Security Tips:</h3>
<ul>
    <li>Use at least 8 characters with a mix of letters, numbers, and symbols</li>
    <li>Avoid using personal information like birthdays or names</li>
    <li>Don't reuse passwords from other accounts</li>
    <li>Consider using a password manager</li>
    <li>Enable two-factor authentication if available</li>
</ul>

<h3>🔗 Alternative Reset Method:</h3>
<p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; word-break: break-all;">
    <code><?php echo e($resetUrl); ?></code>
</div>

<h3>📱 Mobile App Users:</h3>
<p>If you're using our mobile app, you can also reset your password directly through the app:</p>
<ol>
    <li>Open the <?php echo e($companyName); ?> mobile app</li>
    <li>Tap "Forgot Password" on the login screen</li>
    <li>Enter your email address</li>
    <li>Follow the instructions sent to your email</li>
</ol>

<h3>📞 Need Help?</h3>
<p>If you're having trouble resetting your password or have security concerns:</p>
<ul>
    <li><strong>Customer Support:</strong> <?php echo e($companyPhone); ?></li>
    <li><strong>Email Support:</strong> <?php echo e($companyEmail); ?></li>
    <li><strong>Live Chat:</strong> Available on our website</li>
    <li><strong>Help Center:</strong> Visit our FAQ section</li>
</ul>

<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h4 style="color: #0c5460; margin-top: 0;">💡 Account Security Reminder</h4>
    <ul style="color: #0c5460; margin-bottom: 0;">
        <li><?php echo e($companyName); ?> will never ask for your password via email or phone</li>
        <li>Always log in through our official website or mobile app</li>
        <li>Be cautious of phishing emails that look like they're from us</li>
        <li>Report any suspicious activity to our security team</li>
    </ul>
</div>

<h3>🔄 After Resetting Your Password:</h3>
<ul>
    <li>You'll be automatically logged out of all devices</li>
    <li>You'll need to log in again with your new password</li>
    <li>Consider updating your password in any saved browsers</li>
    <li>Update your mobile app login if you use auto-login</li>
</ul>

<p>Thank you for keeping your <?php echo e($companyName); ?> account secure!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Security Team</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 12px; color: #6c757d; text-align: center;">
    This password reset email was sent to <?php echo e($user->email ?? 'your email address'); ?> because a password reset was requested for your <?php echo e($companyName); ?> account.
    If you didn't request this, please contact our support team immediately.
</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\auth\password-reset.blade.php ENDPATH**/ ?>