

<?php $__env->startSection('title', 'Edit Vehicle'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }

    .form-card {
        background-color: black !important;
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .form-card:hover {
        box-shadow: 0 8px 35px rgba(0,0,0,0.15);
    }

    .form-card .card-header {
        background-color: black !important;
        color: #fff;
        border-radius: 15px 15px 0 0;
        padding: 25px;
        border: none;
    }

    .form-card .card-body {
        padding: 35px;
    }

    .section-card {
        border: 1px solid #e9ecef;
        border-radius: 12px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
    }

    .section-card:hover {
        border-color: #007bff;
        box-shadow: 0 3px 15px rgba(0,123,255,0.1);
    }

    .section-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        border-radius: 12px 12px 0 0;
        padding: 20px 25px;
    }

    .section-card .card-body {
        padding: 25px;
    }

    .preview-image {
        max-width: 100%;
        max-height: 250px;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .preview-image:hover {
        transform: scale(1.02);
        box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    }

    .image-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .image-upload-area:hover {
        border-color: #007bff;
        background: #e3f2fd;
    }

    .image-upload-area.dragover {
        border-color: #28a745;
        background: #d4edda;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .pricing-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }

    .pricing-item:hover {
        background: #e3f2fd;
        transform: translateY(-2px);
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.3);
    }

    .btn-secondary {
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
    }

    .alert {
        border-radius: 10px;
        border: none;
        padding: 15px 20px;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .text-muted {
        font-size: 0.875rem;
    }

    .vehicle-stats {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #007bff;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }

    @media (max-width: 768px) {
        .content-wrapper {
            padding: 15px;
        }

        .form-card .card-body,
        .section-card .card-body {
            padding: 20px;
        }

        .pricing-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Edit Vehicle</h2>
                    <p class="text-muted mb-0">Update vehicle information and pricing details</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('admin.vehicles.show', $vehicle->id)); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i> View Details
                    </a>
                    <a href="<?php echo e(route('admin.vehicles.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Vehicles
                    </a>
                </div>
            </div>

            <!-- Vehicle Statistics -->
            <div class="vehicle-stats">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo e($vehicle->bookings()->count()); ?></div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo e($vehicle->bookings()->where('status', 'completed')->count()); ?></div>
                            <div class="stat-label">Completed Trips</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($vehicle->bookings()->where('status', 'completed')->sum('amount'), 2)); ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value">
                                <span class="badge <?php echo e($vehicle->is_active ? 'bg-success' : 'bg-danger'); ?>">
                                    <?php echo e($vehicle->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                            <div class="stat-label">Current Status</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card form-card">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fas fa-car me-2"></i>Vehicle Information</h4>
                </div>
                <div class="card-body">
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger">
                            <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('admin.vehicles.update', $vehicle->id)); ?>" method="POST" enctype="multipart/form-data" id="vehicleForm">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Basic Information Section -->
                        <div class="section-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="name" class="form-label">Vehicle Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name', $vehicle->name)); ?>" required placeholder="e.g., Mercedes E-Class">
                                        <small class="text-muted">Enter the full vehicle name/model</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="type" class="form-label">Vehicle Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="">Select Type</option>
                                            <option value="Saloon" <?php echo e(old('type', $vehicle->type) == 'Saloon' ? 'selected' : ''); ?>>Saloon</option>
                                            <option value="Executive" <?php echo e(old('type', $vehicle->type) == 'Executive' ? 'selected' : ''); ?>>Executive</option>
                                            <option value="Estate" <?php echo e(old('type', $vehicle->type) == 'Estate' ? 'selected' : ''); ?>>Estate</option>
                                            <option value="People Carrier" <?php echo e(old('type', $vehicle->type) == 'People Carrier' ? 'selected' : ''); ?>>People Carrier</option>
                                            <option value="Executive People Carrier" <?php echo e(old('type', $vehicle->type) == 'Executive People Carrier' ? 'selected' : ''); ?>>Executive People Carrier</option>
                                            <option value="8 Seater Minibus" <?php echo e(old('type', $vehicle->type) == '8 Seater Minibus' ? 'selected' : ''); ?>>8 Seater Minibus</option>
                                            <option value="Sedan" <?php echo e(old('type', $vehicle->type) == 'Sedan' ? 'selected' : ''); ?>>Sedan</option>
                                            <option value="SUV" <?php echo e(old('type', $vehicle->type) == 'SUV' ? 'selected' : ''); ?>>SUV</option>
                                            <option value="Limousine" <?php echo e(old('type', $vehicle->type) == 'Limousine' ? 'selected' : ''); ?>>Limousine</option>
                                            <option value="Van" <?php echo e(old('type', $vehicle->type) == 'Van' ? 'selected' : ''); ?>>Van</option>
                                            <option value="Bus" <?php echo e(old('type', $vehicle->type) == 'Bus' ? 'selected' : ''); ?>>Bus</option>
                                        </select>
                                        <small class="text-muted">Choose the vehicle category</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="model" class="form-label">Model <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="model" name="model" value="<?php echo e(old('model', $vehicle->model)); ?>" required placeholder="e.g., E-Class, V-Class">
                                        <small class="text-muted">Specific model name</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="">Select Category</option>
                                            <option value="Economy" <?php echo e(old('category', $vehicle->category) == 'Economy' ? 'selected' : ''); ?>>Economy</option>
                                            <option value="Business" <?php echo e(old('category', $vehicle->category) == 'Business' ? 'selected' : ''); ?>>Business</option>
                                            <option value="Luxury" <?php echo e(old('category', $vehicle->category) == 'Luxury' ? 'selected' : ''); ?>>Luxury</option>
                                            <option value="Premium" <?php echo e(old('category', $vehicle->category) == 'Premium' ? 'selected' : ''); ?>>Premium</option>
                                            <option value="Executive" <?php echo e(old('category', $vehicle->category) == 'Executive' ? 'selected' : ''); ?>>Executive</option>
                                        </select>
                                        <small class="text-muted">Service level category</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="seats" class="form-label">Number of Seats <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="seats" name="seats" value="<?php echo e(old('seats', $vehicle->seats)); ?>" min="1" max="20" required>
                                        <small class="text-muted">Maximum passenger capacity</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="luggage_capacity" class="form-label">Luggage Capacity <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="luggage_capacity" name="luggage_capacity" value="<?php echo e(old('luggage_capacity', $vehicle->luggage_capacity)); ?>" min="0" max="20" required>
                                        <small class="text-muted">Number of suitcases</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="transmission" class="form-label">Transmission <span class="text-danger">*</span></label>
                                        <select class="form-select" id="transmission" name="transmission" required>
                                            <option value="">Select Transmission</option>
                                            <option value="Automatic" <?php echo e(old('transmission', $vehicle->transmission) == 'Automatic' ? 'selected' : ''); ?>>Automatic</option>
                                            <option value="Manual" <?php echo e(old('transmission', $vehicle->transmission) == 'Manual' ? 'selected' : ''); ?>>Manual</option>
                                        </select>
                                        <small class="text-muted">Transmission type</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter detailed description of the vehicle, features, amenities, etc."><?php echo e(old('description', $vehicle->description)); ?></textarea>
                                    <small class="text-muted">Detailed vehicle description for customers</small>
                                </div>
                            </div>
                        </div>



                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Pricing Information</h5>
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            Currency: <?php echo e(\App\Services\SettingsService::getCurrencyCode()); ?> (<?php echo \App\Services\SettingsService::getCurrencySymbol(); ?>) |
                                            Distance: <?php echo e(\App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Miles' : 'Kilometers'); ?>

                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="base_fare" class="form-label">Base Fare</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="base_fare" name="base_fare" value="<?php echo e(old('base_fare', $vehicle->base_fare ?? 5.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Minimum fare for any ride with this vehicle</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="price_per_km" class="form-label">Price Per <?php echo e(\App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Mile' : 'KM'); ?></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="price_per_km" name="price_per_km" value="<?php echo e(old('price_per_km', $vehicle->price_per_km)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Rate charged per <?php echo e(\App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'mile' : 'kilometer'); ?></small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="price_per_hour" class="form-label">Price Per Hour</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="price_per_hour" name="price_per_hour" value="<?php echo e(old('price_per_hour', $vehicle->price_per_hour)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Rate charged per hour for hourly bookings</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="booking_fee" class="form-label">Booking Fee</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="booking_fee" name="booking_fee" value="<?php echo e(old('booking_fee', $vehicle->booking_fee ?? 2.50)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Fixed fee added to all bookings</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="<?php echo e(old('tax_rate', $vehicle->tax_rate ?? 0.00)); ?>" min="0" max="100" step="0.01" required>
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <small class="text-muted">Tax percentage applied to rides</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="airport_surcharge" class="form-label">Airport Surcharge</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="airport_surcharge" name="airport_surcharge" value="<?php echo e(old('airport_surcharge', $vehicle->airport_surcharge ?? 15.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Additional fee for airport transfers</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="waiting_fee_per_minute" class="form-label">Waiting Fee Per Minute</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="waiting_fee_per_minute" name="waiting_fee_per_minute" value="<?php echo e(old('waiting_fee_per_minute', $vehicle->waiting_fee_per_minute ?? 0.50)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Fee charged per minute of waiting time</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="cancellation_fee" class="form-label">Cancellation Fee</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="cancellation_fee" name="cancellation_fee" value="<?php echo e(old('cancellation_fee', $vehicle->cancellation_fee ?? 10.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Fee charged for cancellations</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="night_surcharge" class="form-label">Night Surcharge</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="night_surcharge" name="night_surcharge" value="<?php echo e(old('night_surcharge', $vehicle->night_surcharge ?? 5.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Additional fee for night rides</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="weekend_surcharge" class="form-label">Weekend Surcharge</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="weekend_surcharge" name="weekend_surcharge" value="<?php echo e(old('weekend_surcharge', $vehicle->weekend_surcharge ?? 5.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Additional fee for weekend rides</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="holiday_surcharge" class="form-label">Holiday Surcharge</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?></span>
                                            <input type="number" class="form-control" id="holiday_surcharge" name="holiday_surcharge" value="<?php echo e(old('holiday_surcharge', $vehicle->holiday_surcharge ?? 10.00)); ?>" min="0" step="0.01" required>
                                        </div>
                                        <small class="text-muted">Additional fee for holiday rides</small>
                                    </div>
                                </div>

                                <!-- Price Preview -->
                                <div class="alert alert-info mt-3">
                                    <h6 class="alert-heading"><i class="fas fa-calculator me-2"></i>Price Preview</h6>
                                    <div id="pricePreview">
                                        <em>Enter pricing values above to see example calculation</em>
                                    </div>
                                    <small class="text-muted mt-2 d-block">
                                        <i class="fas fa-info-circle me-1"></i>
                                        This preview shows an example fare calculation for a 10 <?php echo e(\App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'mile' : 'kilometer'); ?> trip using current currency settings.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Image Section -->
                        <div class="section-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-image me-2"></i>Vehicle Image</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="image" class="form-label">Upload New Image</label>
                                        <div class="image-upload-area" id="imageUploadArea">
                                            <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)" style="display: none;">
                                            <div class="upload-content" onclick="document.getElementById('image').click()">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="mb-2"><strong>Click to upload</strong> or drag and drop</p>
                                                <p class="text-muted mb-0">PNG, JPG, GIF up to 2MB</p>
                                            </div>
                                        </div>
                                        <small class="text-muted">Recommended size: 800x600 pixels</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Current Image</label>
                                        <div class="current-image-container">
                                            <?php if($vehicle->image): ?>
                                                <img id="preview" class="preview-image" src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" alt="<?php echo e($vehicle->name); ?>">
                                                <div class="mt-2">
                                                    <small class="text-muted">Current image: <?php echo e(basename($vehicle->image)); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <img id="preview" class="preview-image" src="https://via.placeholder.com/300x200?text=No+Image" alt="No Image">
                                                <div class="mt-2">
                                                    <small class="text-muted">No image uploaded</small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Settings Section -->
                        <div class="section-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Status & Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="is_active" value="0">
                                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', $vehicle->is_active) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                <strong>Active Status</strong>
                                                <br><small class="text-muted">Enable this vehicle for bookings</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Note:</strong> Inactive vehicles will not appear in booking forms but existing bookings will remain unaffected.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="section-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Ready to update?</h6>
                                        <small class="text-muted">Make sure all information is correct before saving</small>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo e(route('admin.vehicles.index')); ?>" class="btn btn-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                        <button type="button" class="btn btn-outline-primary" onclick="validateForm()">
                                            <i class="fas fa-check-circle me-2"></i>Validate
                                        </button>
                                        <button type="submit" class="btn btn-primary" id="submitBtn">
                                            <i class="fas fa-save me-2"></i>Update Vehicle
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Get currency symbol from meta tag or settings
    const currencySymbol = document.querySelector('meta[name="currency-symbol"]')?.getAttribute('content') || '<?php echo \App\Services\SettingsService::getCurrencySymbol(); ?>';
    const currencyCode = document.querySelector('meta[name="currency-code"]')?.getAttribute('content') || '<?php echo e(\App\Services\SettingsService::getCurrencyCode()); ?>';
    const distanceUnit = document.querySelector('meta[name="distance-unit"]')?.getAttribute('content') || '<?php echo e(\App\Services\SettingsService::getDistanceUnit()); ?>';

    // Currency helper functions
    function formatPrice(amount) {
        return currencySymbol + parseFloat(amount).toFixed(2);
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currencyCode,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    // Distance unit helper
    function getDistanceUnitLabel() {
        return distanceUnit === 'miles' ? 'Mile' : 'KM';
    }

    // Log currency settings for debugging
    console.log('Vehicle Edit Form - Currency Settings:', {
        symbol: currencySymbol,
        code: currencyCode,
        distanceUnit: distanceUnit
    });

    // Enhanced image preview functionality
    function previewImage(input) {
        const preview = document.getElementById('preview');
        const uploadArea = document.getElementById('imageUploadArea');

        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Validate file size (2MB limit)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                input.value = '';
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPEG, PNG, GIF)');
                input.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';

                // Update upload area to show success
                uploadArea.style.borderColor = '#28a745';
                uploadArea.style.backgroundColor = '#d4edda';

                // Show file info
                const uploadContent = uploadArea.querySelector('.upload-content');
                uploadContent.innerHTML = `
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="mb-2"><strong>Image selected:</strong> ${file.name}</p>
                    <p class="text-muted mb-0">Click to change image</p>
                `;
            }
            reader.readAsDataURL(file);
        }
    }

    // Drag and drop functionality
    document.addEventListener('DOMContentLoaded', function() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('image');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        uploadArea.addEventListener('drop', handleDrop, false);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            uploadArea.classList.add('dragover');
        }

        function unhighlight(e) {
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                previewImage(fileInput);
            }
        }
    });

    // Form validation
    function validateForm() {
        const form = document.getElementById('vehicleForm');
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        let firstInvalidField = null;

        // Reset previous validation styles
        requiredFields.forEach(field => {
            field.classList.remove('is-invalid');
        });

        // Validate each required field
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
                isValid = false;
            }
        });

        // Custom validations
        const seats = document.getElementById('seats');
        const luggage = document.getElementById('luggage_capacity');
        const taxRate = document.getElementById('tax_rate');

        if (seats.value && (parseInt(seats.value) < 1 || parseInt(seats.value) > 20)) {
            seats.classList.add('is-invalid');
            isValid = false;
            if (!firstInvalidField) firstInvalidField = seats;
        }

        if (luggage.value && (parseInt(luggage.value) < 0 || parseInt(luggage.value) > 20)) {
            luggage.classList.add('is-invalid');
            isValid = false;
            if (!firstInvalidField) firstInvalidField = luggage;
        }

        if (taxRate.value && (parseFloat(taxRate.value) < 0 || parseFloat(taxRate.value) > 100)) {
            taxRate.classList.add('is-invalid');
            isValid = false;
            if (!firstInvalidField) firstInvalidField = taxRate;
        }

        if (isValid) {
            // Show success message with currency info
            showNotification(`Form validation passed! All fields are valid. Currency: ${currencyCode} (${currencySymbol})`, 'success');
        } else {
            // Show error message and focus first invalid field
            showNotification('Please fix the highlighted errors before submitting.', 'error');
            if (firstInvalidField) {
                firstInvalidField.focus();
                firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        return isValid;
    }

    // Show notification
    function showNotification(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="${icon} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(notification);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Form submission with loading state
    document.getElementById('vehicleForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        submitBtn.disabled = true;

        // Re-enable button after 10 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Vehicle';
            submitBtn.disabled = false;
        }, 10000);
    });

    // Price preview functionality
    function updatePricePreview() {
        const baseFare = parseFloat(document.getElementById('base_fare').value) || 0;
        const pricePerKm = parseFloat(document.getElementById('price_per_km').value) || 0;
        const pricePerHour = parseFloat(document.getElementById('price_per_hour').value) || 0;
        const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

        // Example calculation for 10km trip
        const exampleDistance = 10;
        const exampleFare = baseFare + (pricePerKm * exampleDistance);
        const exampleTax = (exampleFare * taxRate) / 100;
        const exampleTotal = exampleFare + exampleTax;

        // Update preview if element exists
        const previewElement = document.getElementById('pricePreview');
        if (previewElement) {
            previewElement.innerHTML = `
                <strong>Example (${exampleDistance} ${getDistanceUnitLabel().toLowerCase()}s):</strong><br>
                Base: ${formatPrice(baseFare)} + Distance: ${formatPrice(pricePerKm * exampleDistance)} + Tax: ${formatPrice(exampleTax)} = <strong>${formatPrice(exampleTotal)}</strong>
            `;
        }
    }

    // Auto-save draft functionality (optional)
    let autoSaveTimer;
    const formInputs = document.querySelectorAll('#vehicleForm input, #vehicleForm select, #vehicleForm textarea');

    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(() => {
                saveDraft();
            }, 2000); // Save draft after 2 seconds of inactivity

            // Update price preview for pricing fields
            if (['base_fare', 'price_per_km', 'price_per_hour', 'tax_rate'].includes(this.id)) {
                updatePricePreview();
            }
        });
    });

    function saveDraft() {
        const formData = new FormData(document.getElementById('vehicleForm'));
        const draftData = {};

        for (let [key, value] of formData.entries()) {
            if (key !== 'image' && key !== '_token' && key !== '_method') {
                draftData[key] = value;
            }
        }

        localStorage.setItem('vehicle_edit_draft_<?php echo e($vehicle->id); ?>', JSON.stringify(draftData));
        console.log('Draft saved automatically');
    }

    // Load draft on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize price preview
        updatePricePreview();

        const draftData = localStorage.getItem('vehicle_edit_draft_<?php echo e($vehicle->id); ?>');
        if (draftData) {
            try {
                const data = JSON.parse(draftData);
                let hasChanges = false;

                Object.keys(data).forEach(key => {
                    const field = document.querySelector(`[name="${key}"]`);
                    if (field && field.value !== data[key]) {
                        hasChanges = true;
                    }
                });

                if (hasChanges) {
                    if (confirm('A draft of your changes was found. Would you like to restore it?')) {
                        Object.keys(data).forEach(key => {
                            const field = document.querySelector(`[name="${key}"]`);
                            if (field) {
                                if (field.type === 'checkbox') {
                                    field.checked = data[key] === 'on';
                                } else {
                                    field.value = data[key];
                                }
                            }
                        });
                        showNotification('Draft restored successfully!', 'success');
                    }
                }
            } catch (e) {
                console.error('Error loading draft:', e);
            }
        }
    });

    // Clear draft on successful submission
    window.addEventListener('beforeunload', function() {
        // Only clear if form was actually submitted
        if (document.getElementById('submitBtn').disabled) {
            localStorage.removeItem('vehicle_edit_draft_<?php echo e($vehicle->id); ?>');
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/vehicles/edit.blade.php ENDPATH**/ ?>