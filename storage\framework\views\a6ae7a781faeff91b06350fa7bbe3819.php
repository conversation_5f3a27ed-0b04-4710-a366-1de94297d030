<?php $__env->startSection('title', 'Payment Reports'); ?>

<?php $__env->startSection('payment-logs-styles'); ?>
<style>
    .report-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .report-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .report-card .card-body {
        padding: 30px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }

    .filter-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .filter-form .form-control {
        border-radius: 5px;
    }

    .filter-form .btn {
        border-radius: 5px;
    }

    .top-clients-table {
        margin-bottom: 0;
    }

    .top-clients-table th,
    .top-clients-table td {
        padding: 12px 15px;
        vertical-align: middle;
    }

    .top-clients-table tbody tr {
        transition: background-color 0.3s;
    }

    .top-clients-table tbody tr:hover {
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-title', 'Payment Reports'); ?>

<?php $__env->startSection('payment-logs-actions'); ?>
<div class="btn-group">
    <a href="<?php echo e(route('admin.payments.export-report')); ?>" class="btn btn-outline-primary">
        <i class="fas fa-file-export me-1"></i> Export Report
    </a>
    <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Payments
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-content'); ?>
<!-- Filter Form -->
<div class="filter-form">
    <form action="<?php echo e(route('admin.payments.report')); ?>" method="GET">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate); ?>">
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <div class="mb-3 w-100">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i> Apply Filter
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <!-- Daily Payments Chart -->
    <div class="col-md-8">
        <div class="card report-card">
            <div class="card-header">
                <h4 class="mb-0">Daily Payments</h4>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="dailyPaymentsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Chart -->
    <div class="col-md-4">
        <div class="card report-card">
            <div class="card-header">
                <h4 class="mb-0">Payment Methods</h4>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Payment Status Chart -->
    <div class="col-md-4">
        <div class="card report-card">
            <div class="card-header">
                <h4 class="mb-0">Payment Status</h4>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="paymentStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Clients -->
    <div class="col-md-8">
        <div class="card report-card">
            <div class="card-header">
                <h4 class="mb-0">Top Clients</h4>
            </div>
            <div class="card-body">
                <?php if(count($topClients) > 0): ?>
                    <div class="table-responsive">
                        <table class="table top-clients-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Email</th>
                                    <th>Total Payments</th>
                                    <th>Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topClients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($client->booking->user->name); ?></td>
                                        <td><?php echo e($client->booking->user->email); ?></td>
                                        <td><?php echo e($client->count); ?></td>
                                        <td><?php echo e(\App\Helpers\SettingsHelper::formatPrice($client->total_amount)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <p class="text-muted mb-0">No client data available for the selected period.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Daily Payments Chart
        const dailyPaymentsCtx = document.getElementById('dailyPaymentsChart').getContext('2d');
        const dailyPaymentsChart = new Chart(dailyPaymentsCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php $__currentLoopData = $dailyPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        '<?php echo e(\Carbon\Carbon::parse($payment->date)->format("M d")); ?>',
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ],
                datasets: [{
                    label: 'Number of Payments',
                    data: [
                        <?php $__currentLoopData = $dailyPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($payment->count); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    yAxisID: 'y'
                }, {
                    label: 'Total Amount',
                    data: [
                        <?php $__currentLoopData = $dailyPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($payment->total_amount); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Number of Payments'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Total Amount (<?php echo e(\App\Helpers\SettingsHelper::getCurrencySymbol()); ?>)'
                        },
                        grid: {
                            drawOnChartArea: false
                        },
                        ticks: {
                            callback: function(value) {
                                return '<?php echo e(\App\Helpers\SettingsHelper::getCurrencySymbol()); ?>' + value;
                            }
                        }
                    }
                }
            }
        });

        // Payment Methods Chart
        const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
        const paymentMethodsChart = new Chart(paymentMethodsCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        '<?php echo e(ucfirst(str_replace("_", " ", $method->payment_method))); ?>',
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ],
                datasets: [{
                    data: [
                        <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($method->count); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 159, 64, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Payment Status Chart
        const paymentStatusCtx = document.getElementById('paymentStatusChart').getContext('2d');
        const paymentStatusChart = new Chart(paymentStatusCtx, {
            type: 'pie',
            data: {
                labels: [
                    <?php $__currentLoopData = $paymentStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        '<?php echo e(ucfirst($status->status)); ?>',
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ],
                datasets: [{
                    data: [
                        <?php $__currentLoopData = $paymentStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($status->count); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',  // completed - green
                        'rgba(255, 193, 7, 0.8)',  // pending - yellow
                        'rgba(220, 53, 69, 0.8)',  // failed - red
                        'rgba(23, 162, 184, 0.8)'  // refunded - blue
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.payment-logs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\payments\report.blade.php ENDPATH**/ ?>