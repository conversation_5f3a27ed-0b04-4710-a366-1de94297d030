<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Show the admin dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get counts for dashboard
        $userCount = User::count();
        $vehicleCount = Vehicle::count();
        $bookingCount = Booking::count();
        $totalRevenue = Booking::where('status', 'completed')->sum('amount');

        // Get recent bookings with more details
        $recentBookings = Booking::with(['user', 'vehicle'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent users with more details
        $recentUsers = User::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent reviews
        $recentReviews = Booking::with(['user', 'vehicle'])
            ->whereNotNull('rating')
            ->whereNotNull('review')
            ->orderBy('reviewed_at', 'desc')
            ->take(5)
            ->get();

        // Get booking statistics by status
        $bookingStats = [
            'pending' => Booking::where('status', 'pending')->count(),
            'confirmed' => Booking::where('status', 'confirmed')->count(),
            'completed' => Booking::where('status', 'completed')->count(),
            'cancelled' => Booking::where('status', 'cancelled')->count(),
        ];

        // Get user statistics by role
        $userStats = [
            'admin' => User::where('role', 'admin')->count(),
            'client' => User::where('role', 'client')->count(),
            'driver' => User::where('role', 'driver')->count(),
        ];

        // Get revenue statistics
        $currentMonthRevenue = Booking::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        $previousMonthRevenue = Booking::where('status', 'completed')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('amount');

        $revenueGrowth = $previousMonthRevenue > 0
            ? (($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100
            : 100;

        // Get popular vehicles
        $popularVehicles = Vehicle::withCount(['bookings' => function($query) {
                $query->where('status', '!=', 'cancelled');
            }])
            ->orderBy('bookings_count', 'desc')
            ->take(5)
            ->get();

        // Get drivers with expiring documents
        $thirtyDaysFromNow = now()->addDays(30);
        $driversWithExpiringDocuments = User::where('role', 'driver')
            ->where(function($query) use ($thirtyDaysFromNow) {
                $query->whereDate('insurance_expiry', '>=', now())
                      ->whereDate('insurance_expiry', '<=', $thirtyDaysFromNow)
                      ->orWhere(function($q) use ($thirtyDaysFromNow) {
                          $q->whereDate('mot_expiry', '>=', now())
                             ->whereDate('mot_expiry', '<=', $thirtyDaysFromNow);
                      });
            })
            ->with('driverDocuments')
            ->take(5)
            ->get();

        // Get drivers with expired documents
        $driversWithExpiredDocuments = User::where('role', 'driver')
            ->where(function($query) {
                $query->whereDate('insurance_expiry', '<', now())
                      ->orWhereDate('mot_expiry', '<', now());
            })
            ->with('driverDocuments')
            ->take(5)
            ->get();

        return view('admin.dashboard', compact(
            'userCount',
            'vehicleCount',
            'bookingCount',
            'totalRevenue',
            'recentBookings',
            'recentUsers',
            'recentReviews',
            'bookingStats',
            'userStats',
            'currentMonthRevenue',
            'previousMonthRevenue',
            'revenueGrowth',
            'popularVehicles',
            'driversWithExpiringDocuments',
            'driversWithExpiredDocuments'
        ));
    }
}
