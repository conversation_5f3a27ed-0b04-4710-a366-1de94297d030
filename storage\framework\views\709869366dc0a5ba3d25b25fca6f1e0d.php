<?php $__env->startSection('title', 'Booking Cancellation'); ?>

<?php $__env->startSection('content'); ?>
<h2>Booking Cancellation</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>We're writing to confirm that your booking has been cancelled as requested.</p>

<div class="booking-details">
    <h3>Cancelled Booking Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Original Pickup Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name ?? 'N/A'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Original Amount:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Cancellation Date:</span>
        <span class="detail-value"><?php echo e(now()->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
</div>

<?php
    $canCancelForFree = \App\Services\SettingsService::canCancelForFree($booking->pickup_date);
    $cancellationFee = $canCancelForFree ? 0 : \App\Services\SettingsService::calculateCancellationFee($booking->amount);
?>

<div style="background-color: <?php echo e($canCancelForFree ? '#d4edda' : '#fff3cd'); ?>; border: 1px solid <?php echo e($canCancelForFree ? '#c3e6cb' : '#ffeaa7'); ?>; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: <?php echo e($canCancelForFree ? '#155724' : '#856404'); ?>; margin-top: 0;">Refund Information</h3>
    <?php if($canCancelForFree): ?>
        <p style="color: #155724; margin-bottom: 0;">
            <strong>Good news!</strong> Your booking was cancelled within our free cancellation window. 
            You will receive a full refund of <?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?>.
        </p>
    <?php else: ?>
        <p style="color: #856404; margin-bottom: 0;">
            Your booking was cancelled within <?php echo e(\App\Services\SettingsService::get('cancellation_time_window', 24)); ?> hours of pickup. 
            A cancellation fee of <?php echo e($currencySymbol); ?><?php echo e(number_format($cancellationFee, 2)); ?> applies. 
            You will receive a refund of <?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount - $cancellationFee, 2)); ?>.
        </p>
    <?php endif; ?>
</div>

<h3>What Happens Next?</h3>
<ul>
    <li>Your refund will be processed within 3-5 business days</li>
    <li>The refund will be credited to your original payment method</li>
    <li>You will receive a separate email confirmation once the refund is processed</li>
    <li>Your booking slot has been released and is now available for other customers</li>
</ul>

<h3>Need Another Ride?</h3>
<p>We're sorry this booking didn't work out. If you need to book another ride, we're here to help:</p>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('booking.index')); ?>" class="btn">Book a New Ride</a>
</div>

<h3>Questions?</h3>
<p>If you have any questions about your cancellation or refund, please contact us:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or visit your account dashboard online</li>
</ul>

<p>We appreciate your understanding and look forward to serving you in the future.</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\cancellation-client.blade.php ENDPATH**/ ?>