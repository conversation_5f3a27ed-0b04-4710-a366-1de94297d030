<?php

namespace Tests\Unit;

use App\Helpers\SettingsHelper;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SettingsHelperTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_setting_value()
    {
        Setting::create([
            'key' => 'test_setting',
            'value' => 'test_value',
            'type' => 'text',
        ]);

        $value = SettingsHelper::get('test_setting');
        $this->assertEquals('test_value', $value);
    }

    /** @test */
    public function it_returns_default_value_for_missing_setting()
    {
        $value = SettingsHelper::get('missing_setting', 'default_value');
        $this->assertEquals('default_value', $value);
    }

    /** @test */
    public function it_can_set_setting_value()
    {
        SettingsHelper::set('new_setting', 'new_value');

        $this->assertDatabaseHas('settings', [
            'key' => 'new_setting',
            'value' => 'new_value',
        ]);
    }

    /** @test */
    public function it_can_update_existing_setting()
    {
        Setting::create([
            'key' => 'existing_setting',
            'value' => 'old_value',
            'type' => 'text',
        ]);

        SettingsHelper::set('existing_setting', 'new_value');

        $this->assertDatabaseHas('settings', [
            'key' => 'existing_setting',
            'value' => 'new_value',
        ]);

        $this->assertDatabaseMissing('settings', [
            'key' => 'existing_setting',
            'value' => 'old_value',
        ]);
    }

    /** @test */
    public function it_formats_price_correctly()
    {
        Setting::create([
            'key' => 'currency_symbol',
            'value' => '$',
            'type' => 'text',
        ]);

        $formatted = SettingsHelper::formatPrice(25.50);
        $this->assertEquals('$25.50', $formatted);
    }

    /** @test */
    public function it_gets_currency_symbol()
    {
        Setting::create([
            'key' => 'currency_symbol',
            'value' => '€',
            'type' => 'text',
        ]);

        $symbol = SettingsHelper::getCurrencySymbol();
        $this->assertEquals('€', $symbol);
    }

    /** @test */
    public function it_falls_back_to_default_currency_symbol()
    {
        $symbol = SettingsHelper::getCurrencySymbol();
        $this->assertEquals('$', $symbol);
    }

    /** @test */
    public function it_gets_currency_code()
    {
        Setting::create([
            'key' => 'currency_code',
            'value' => 'EUR',
            'type' => 'text',
        ]);

        $code = SettingsHelper::getCurrencyCode();
        $this->assertEquals('EUR', $code);
    }

    /** @test */
    public function it_gets_google_maps_api_key()
    {
        Setting::create([
            'key' => 'google_maps_api_key',
            'value' => 'test_api_key',
            'type' => 'text',
        ]);

        $apiKey = SettingsHelper::getGoogleMapsApiKey();
        $this->assertEquals('test_api_key', $apiKey);
    }

    /** @test */
    public function it_gets_paypal_client_id()
    {
        Setting::create([
            'key' => 'paypal_mode',
            'value' => 'sandbox',
            'type' => 'text',
        ]);

        Setting::create([
            'key' => 'paypal_client_id',
            'value' => 'sandbox_client_id',
            'type' => 'text',
        ]);

        $clientId = SettingsHelper::getPaypalClientId();
        $this->assertEquals('sandbox_client_id', $clientId);
    }

    /** @test */
    public function it_gets_company_name()
    {
        Setting::create([
            'key' => 'company_name',
            'value' => 'Test Company',
            'type' => 'text',
        ]);

        $name = SettingsHelper::getCompanyName();
        $this->assertEquals('Test Company', $name);
    }

    /** @test */
    public function it_gets_company_email()
    {
        Setting::create([
            'key' => 'company_email',
            'value' => '<EMAIL>',
            'type' => 'text',
        ]);

        $email = SettingsHelper::getCompanyEmail();
        $this->assertEquals('<EMAIL>', $email);
    }

    /** @test */
    public function it_gets_company_phone()
    {
        Setting::create([
            'key' => 'company_phone',
            'value' => '+1234567890',
            'type' => 'text',
        ]);

        $phone = SettingsHelper::getCompanyPhone();
        $this->assertEquals('+1234567890', $phone);
    }

    /** @test */
    public function it_gets_company_address()
    {
        Setting::create([
            'key' => 'company_address',
            'value' => '123 Main St, City',
            'type' => 'text',
        ]);

        $address = SettingsHelper::getCompanyAddress();
        $this->assertEquals('123 Main St, City', $address);
    }
}
