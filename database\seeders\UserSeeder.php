<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'phone' => '************',
                'address' => '123 Admin St, New York, NY 10001',
                'is_active' => true,
            ]
        );

        // Create Client Users
        $clients = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'client',
                'phone' => '************',
                'address' => '456 Client Ave, New York, NY 10002',
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'client',
                'phone' => '************',
                'address' => '789 Client Blvd, New York, NY 10003',
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'client',
                'phone' => '************',
                'address' => '101 Client St, New York, NY 10004',
                'is_active' => true,
            ],
        ];

        foreach ($clients as $client) {
            User::updateOrCreate(
                ['email' => $client['email']],
                $client
            );
        }

        // Create Driver Users
        $drivers = [
            [
                'name' => 'Michael Driver',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'driver',
                'phone' => '************',
                'address' => '222 Driver St, New York, NY 10005',
                'license_number' => '**********',
                'is_active' => true,
            ],
            [
                'name' => 'Sarah Driver',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'driver',
                'phone' => '************',
                'address' => '333 Driver Ave, New York, NY 10006',
                'license_number' => '**********',
                'is_active' => true,
            ],
            [
                'name' => 'David Driver',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'driver',
                'phone' => '************',
                'address' => '444 Driver Blvd, New York, NY 10007',
                'license_number' => '**********',
                'is_active' => true,
            ],
        ];

        foreach ($drivers as $driver) {
            User::updateOrCreate(
                ['email' => $driver['email']],
                $driver
            );
        }
    }
}
