<?php

namespace Database\Seeders;

use App\Models\Booking;
use Illuminate\Database\Seeder;

class UpdateBookingPaymentStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all bookings
        $bookings = Booking::all();
        
        foreach ($bookings as $booking) {
            // Set payment_status based on status
            if ($booking->status === 'completed') {
                $booking->payment_status = 'completed';
            } elseif ($booking->status === 'cancelled') {
                $booking->payment_status = 'cancelled';
            } else {
                $booking->payment_status = 'pending';
            }
            
            $booking->save();
        }
        
        $this->command->info('Updated payment_status for ' . $bookings->count() . ' bookings.');
    }
}
