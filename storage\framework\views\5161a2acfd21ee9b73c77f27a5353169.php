<?php $__env->startSection('title', 'Frequently Asked Questions | Ynr Cars'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1 data-aos="fade-up">Frequently Asked Questions</h1>
        <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">FAQ</li>
            </ol>
        </nav>
    </div>
</div>

<!-- FAQ Introduction Section -->
<section class="content-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center" data-aos="fade-up">
                <h2 class="section-title">Have Questions? We Have Answers!</h2>
                <p class="lead mb-5">We've compiled a list of the most frequently asked questions to help you find the information you need. If you can't find the answer to your question, please don't hesitate to <a href="<?php echo e(route('contact')); ?>">contact us</a>.</p>

                <!-- Search Box -->
                <div class="search-box mb-5" data-aos="fade-up" data-aos-delay="100">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search for answers..." id="faqSearch">
                        <button class="btn btn-primary" type="button">Search</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Categories Section -->
<section class="content-section bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="row">
                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                        <div class="card h-100 border-0 shadow text-center">
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <i class="fas fa-car fa-3x text-primary"></i>
                                </div>
                                <h4>Booking & Reservations</h4>
                                <p>Questions about how to book, modify, or cancel your reservation.</p>
                                <a href="#booking-reservations" class="btn btn-outline-primary mt-3">View Questions</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="card h-100 border-0 shadow text-center">
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <i class="fas fa-credit-card fa-3x text-primary"></i>
                                </div>
                                <h4>Payments & Pricing</h4>
                                <p>Questions about payment methods, pricing, and additional charges.</p>
                                <a href="#payments-pricing" class="btn btn-outline-primary mt-3">View Questions</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                        <div class="card h-100 border-0 shadow text-center">
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <i class="fas fa-user-tie fa-3x text-primary"></i>
                                </div>
                                <h4>Services & Policies</h4>
                                <p>Questions about our services, policies, and special requirements.</p>
                                <a href="#services-policies" class="btn btn-outline-primary mt-3">View Questions</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Booking & Reservations FAQ Section -->
<section id="booking-reservations" class="content-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <h3 class="mb-4" data-aos="fade-up">Booking & Reservations</h3>
                <div class="accordion" id="bookingAccordion" data-aos="fade-up" data-aos-delay="100">
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                How far in advance should I book a car service?
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#bookingAccordion">
                            <div class="accordion-body">
                                We recommend booking your car service at least 24-48 hours in advance to ensure availability, especially during peak times. However, we understand that last-minute needs arise, and we will do our best to accommodate your request. For special events, corporate functions, or during holiday seasons, we suggest booking several weeks in advance.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                Can I book a car service for someone else?
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#bookingAccordion">
                            <div class="accordion-body">
                                Yes, you can book a car service for someone else. During the booking process, you'll have the option to provide the passenger's information. Please ensure that you provide accurate contact details for the passenger so that our chauffeur can communicate with them if necessary.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                What information do I need to provide when booking?
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#bookingAccordion">
                            <div class="accordion-body">
                                When booking, you'll need to provide: pickup date and time, pickup and drop-off locations, number of passengers, amount of luggage, preferred vehicle type (if any), and your contact information. For airport pickups, we also require your flight information to track your arrival time.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Payments & Pricing FAQ Section -->
<section id="payments-pricing" class="content-section bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <h3 class="mb-4" data-aos="fade-up">Payments & Pricing</h3>
                <div class="accordion" id="paymentsAccordion" data-aos="fade-up" data-aos-delay="100">
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="true" aria-controls="collapseFour">
                                How is the fare calculated?
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse show" aria-labelledby="headingFour" data-bs-parent="#paymentsAccordion">
                            <div class="accordion-body">
                                Our fares are calculated based on several factors including distance, travel time, vehicle type, and service type (one-way, round-trip, hourly). For point-to-point transfers, we primarily use distance-based pricing. For hourly bookings, rates are based on the vehicle type and the number of hours reserved.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingFive">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                What payment methods do you accept?
                            </button>
                        </h2>
                        <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#paymentsAccordion">
                            <div class="accordion-body">
                                We accept all major credit cards (Visa, MasterCard, American Express, Discover), PayPal, and bank transfers for corporate accounts. Payment is typically processed at the time of booking, though corporate clients may have different payment terms based on their agreement.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingSix">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSix" aria-expanded="false" aria-controls="collapseSix">
                                Are there any additional fees I should be aware of?
                            </button>
                        </h2>
                        <div id="collapseSix" class="accordion-collapse collapse" aria-labelledby="headingSix" data-bs-parent="#paymentsAccordion">
                            <div class="accordion-body">
                                The quote you receive includes most standard costs, but there may be additional charges for: waiting time beyond the included grace period, additional stops not included in the original booking, tolls and parking fees, after-hours service (late night/early morning), and special requests requiring additional resources.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services & Policies FAQ Section -->
<section id="services-policies" class="content-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <h3 class="mb-4" data-aos="fade-up">Services & Policies</h3>
                <div class="accordion" id="servicesAccordion" data-aos="fade-up" data-aos-delay="100">
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingSeven">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSeven" aria-expanded="true" aria-controls="collapseSeven">
                                What is your cancellation policy?
                            </button>
                        </h2>
                        <div id="collapseSeven" class="accordion-collapse collapse show" aria-labelledby="headingSeven" data-bs-parent="#servicesAccordion">
                            <div class="accordion-body">
                                Cancellations made at least 24 hours before the scheduled service time receive a full refund. Cancellations made between 12-24 hours before service incur a 50% fee. Cancellations made less than 12 hours before service or no-shows are charged the full amount. Special events and large group bookings may have different cancellation policies.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingEight">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEight" aria-expanded="false" aria-controls="collapseEight">
                                What happens if my flight is delayed?
                            </button>
                        </h2>
                        <div id="collapseEight" class="accordion-collapse collapse" aria-labelledby="headingEight" data-bs-parent="#servicesAccordion">
                            <div class="accordion-body">
                                We monitor all flight arrivals in real-time. If your flight is delayed, your chauffeur will adjust their schedule accordingly to be there when you arrive. There is no additional charge for flight delays, as we understand this is beyond your control. This is why we require your flight information when booking airport transfers.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h2 class="accordion-header" id="headingNine">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseNine" aria-expanded="false" aria-controls="collapseNine">
                                Are your vehicles equipped with child seats?
                            </button>
                        </h2>
                        <div id="collapseNine" class="accordion-collapse collapse" aria-labelledby="headingNine" data-bs-parent="#servicesAccordion">
                            <div class="accordion-body">
                                Yes, we can provide child seats upon request. Please specify the type of seat needed (infant, toddler, booster) and the number of seats required when making your booking. There may be a small additional fee for this service. For safety reasons, we require that all children travel in appropriate car seats as required by law.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <h2 class="cta-title" data-aos="fade-up">Still Have Questions?</h2>
        <p class="cta-subtitle" data-aos="fade-up" data-aos-delay="100">Our customer service team is available 24/7 to assist you with any inquiries or concerns. Feel free to contact us, and we'll be happy to help.</p>
        <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg cta-btn" data-aos="fade-up" data-aos-delay="200">Contact Us</a>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('faqSearch');
        const accordionItems = document.querySelectorAll('.accordion-item');
        const faqSections = document.querySelectorAll('#booking-reservations, #payments-pricing, #services-policies');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();
            let hasResults = false;

            // First hide all sections
            faqSections.forEach(section => {
                section.style.display = 'none';
            });

            // Show all items initially
            accordionItems.forEach(item => {
                item.style.display = 'block';
            });

            // Filter items based on search term
            accordionItems.forEach(item => {
                const question = item.querySelector('.accordion-button').textContent.toLowerCase();
                const answer = item.querySelector('.accordion-body').textContent.toLowerCase();

                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                    // Show the parent section
                    const parentSection = item.closest('section');
                    if (parentSection) {
                        parentSection.style.display = 'block';
                    }
                    hasResults = true;
                } else {
                    item.style.display = 'none';
                }
            });

            // If search is empty, show all sections
            if (searchTerm === '') {
                faqSections.forEach(section => {
                    section.style.display = 'block';
                });
            }

            // If no results, show a message
            if (!hasResults && searchTerm !== '') {
                // Show all sections
                faqSections.forEach(section => {
                    section.style.display = 'block';
                });

                // Show all items
                accordionItems.forEach(item => {
                    item.style.display = 'none';
                });

                // Add a "no results" message if it doesn't exist
                if (!document.getElementById('noResultsMessage')) {
                    const message = document.createElement('div');
                    message.id = 'noResultsMessage';
                    message.className = 'alert alert-info text-center my-4';
                    message.innerHTML = 'No results found. Please try a different search term or <a href="<?php echo e(route("contact")); ?>">contact us</a> for assistance.';
                    document.querySelector('#booking-reservations .accordion').after(message);
                }
            } else {
                // Remove the "no results" message if it exists
                const noResultsMessage = document.getElementById('noResultsMessage');
                if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/faq.blade.php ENDPATH**/ ?>