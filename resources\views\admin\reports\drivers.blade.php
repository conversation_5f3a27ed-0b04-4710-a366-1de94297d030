@extends('layouts.admin')

@section('title', 'Driver Reports')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users me-2 text-warning"></i> Driver Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-warning" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Driver Statistics -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Available Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $availableDrivers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unavailable Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $unavailableDrivers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive Drivers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $inactiveDrivers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Drivers by Rides -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Drivers by Completed Rides</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Driver</th>
                                    <th>Completed Rides</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topDriversByRides as $driver)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($driver->profile_photo)
                                                <img src="{{ asset('storage/' . $driver->profile_photo) }}" 
                                                     class="rounded-circle me-2" width="32" height="32">
                                            @else
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-weight-bold">{{ $driver->name }}</div>
                                                <small class="text-muted">{{ $driver->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $driver->driver_rides_count }}</span>
                                    </td>
                                    <td>
                                        @if($driver->is_active && $driver->is_available)
                                            <span class="badge badge-success">Available</span>
                                        @elseif($driver->is_active)
                                            <span class="badge badge-warning">Busy</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Drivers by Revenue -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Drivers by Revenue Generated</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Driver</th>
                                    <th>Total Revenue</th>
                                    <th>Rating</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topDriversByRevenue as $driver)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($driver->profile_photo)
                                                <img src="{{ asset('storage/' . $driver->profile_photo) }}" 
                                                     class="rounded-circle me-2" width="32" height="32">
                                            @else
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-weight-bold">{{ $driver->name }}</div>
                                                <small class="text-muted">{{ $driver->phone }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-success font-weight-bold">
                                            ${{ number_format($driver->driver_rides_sum_amount ?? 0, 2) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $avgRating = $driver->driverRides()->whereNotNull('rating')->avg('rating');
                                        @endphp
                                        @if($avgRating)
                                            <div class="d-flex align-items-center">
                                                <span class="me-1">{{ number_format($avgRating, 1) }}</span>
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= $avgRating)
                                                        <i class="fas fa-star text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                            </div>
                                        @else
                                            <span class="text-muted">No ratings</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
@endpush
@endsection
