<?php

namespace App\Mail;

use App\Services\SettingsService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TestEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $companyName = SettingsService::getCompanyName() ?: config('app.name', 'YNR Cars');
        $companyEmail = SettingsService::getCompanyEmail() ?: config('mail.from.address', '<EMAIL>');

        return new Envelope(
            subject: 'Email Configuration Test - ' . $companyName,
            from: [
                'address' => $companyEmail,
                'name' => $companyName,
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.test',
            with: [
                'companyName' => SettingsService::getCompanyName() ?: config('app.name', 'YNR Cars'),
                'companyPhone' => SettingsService::getCompanyPhone() ?: '+****************',
                'companyEmail' => SettingsService::getCompanyEmail() ?: config('mail.from.address', '<EMAIL>'),
                'testTime' => now()->format('Y-m-d H:i:s'),
                'app_name' => SettingsService::getCompanyName() ?: config('app.name', 'YNR Cars'),
                'app_url' => config('app.url'),
                'current_year' => now()->year,
                'user' => (object) [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
