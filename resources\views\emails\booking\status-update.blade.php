@extends('emails.layouts.master')

@section('title', 'Booking Status Update')

@section('content')
<h2>Booking Status Update</h2>

<p>Dear {{ $user->name }},</p>

<p>We're writing to update you on the status of your booking.</p>

<div class="booking-details">
    <h3>Booking Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value">{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
</div>

<div class="booking-details">
    <h3>Status Change</h3>
    
    <div class="detail-row">
        <span class="detail-label">Previous Status:</span>
        <span class="detail-value">{{ ucfirst(str_replace('_', ' ', $oldStatus)) }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Current Status:</span>
        <span class="detail-value"><strong>{{ ucfirst(str_replace('_', ' ', $newStatus)) }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Updated At:</span>
        <span class="detail-value">{{ now()->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
</div>

@php
    $statusInfo = [
        'pending' => [
            'color' => '#ffc107',
            'bg' => '#fff3cd',
            'border' => '#ffeaa7',
            'icon' => '⏳',
            'title' => 'Booking Pending',
            'description' => 'Your booking is being processed. We will confirm it shortly and assign a driver.'
        ],
        'confirmed' => [
            'color' => '#28a745',
            'bg' => '#d4edda',
            'border' => '#c3e6cb',
            'icon' => '✅',
            'title' => 'Booking Confirmed',
            'description' => 'Great! Your booking has been confirmed. We will assign a driver and notify you soon.'
        ],
        'assigned' => [
            'color' => '#17a2b8',
            'bg' => '#d1ecf1',
            'border' => '#bee5eb',
            'icon' => '🚗',
            'title' => 'Driver Assigned',
            'description' => 'A driver has been assigned to your booking. They will contact you before pickup.'
        ],
        'in_progress' => [
            'color' => '#007bff',
            'bg' => '#d1ecf1',
            'border' => '#bee5eb',
            'icon' => '🛣️',
            'title' => 'Trip in Progress',
            'description' => 'Your ride is currently in progress. Enjoy your journey!'
        ],
        'completed' => [
            'color' => '#28a745',
            'bg' => '#d4edda',
            'border' => '#c3e6cb',
            'icon' => '🏁',
            'title' => 'Trip Completed',
            'description' => 'Your trip has been completed successfully. Thank you for choosing our service!'
        ],
        'cancelled' => [
            'color' => '#dc3545',
            'bg' => '#f8d7da',
            'border' => '#f5c6cb',
            'icon' => '❌',
            'title' => 'Booking Cancelled',
            'description' => 'Your booking has been cancelled. If you need assistance, please contact us.'
        ]
    ];
    
    $currentStatusInfo = $statusInfo[$newStatus] ?? $statusInfo['pending'];
@endphp

<div style="background-color: {{ $currentStatusInfo['bg'] }}; border: 1px solid {{ $currentStatusInfo['border'] }}; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: {{ $currentStatusInfo['color'] }}; margin-top: 0;">
        {{ $currentStatusInfo['icon'] }} {{ $currentStatusInfo['title'] }}
    </h3>
    <p style="color: {{ $currentStatusInfo['color'] }}; margin-bottom: 0; font-size: 16px;">
        {{ $currentStatusInfo['description'] }}
    </p>
</div>

@if($newStatus === 'confirmed')
<h3>What's Next?</h3>
<ul>
    <li>We will assign a professional driver to your booking</li>
    <li>You'll receive another email once a driver is assigned</li>
    <li>Your driver will contact you 15-30 minutes before pickup</li>
    <li>Please be ready at your pickup location at the scheduled time</li>
</ul>
@elseif($newStatus === 'assigned')
<h3>What's Next?</h3>
<ul>
    <li>Your assigned driver will contact you before pickup</li>
    <li>Please be ready 5 minutes before your scheduled pickup time</li>
    <li>Look for your driver's vehicle at the pickup location</li>
    <li>Have your booking confirmation number ready</li>
</ul>
@elseif($newStatus === 'in_progress')
<h3>During Your Trip:</h3>
<ul>
    <li>Enjoy your comfortable ride with our professional driver</li>
    <li>Feel free to communicate any special requests to your driver</li>
    <li>Your safety and comfort are our top priorities</li>
    <li>You'll receive a completion notification when you arrive</li>
</ul>
@elseif($newStatus === 'completed')
<h3>Thank You!</h3>
<ul>
    <li>We hope you enjoyed your ride with {{ $companyName }}</li>
    <li>You'll receive a receipt and invoice shortly</li>
    <li>Please consider rating your experience in your account</li>
    <li>We'd love to serve you again in the future</li>
</ul>

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.review', $booking->id) }}" class="btn">Rate Your Experience</a>
</div>
@elseif($newStatus === 'cancelled')
<h3>Cancellation Information:</h3>
<ul>
    <li>Your booking has been cancelled as requested</li>
    <li>Refund processing information will be sent separately</li>
    <li>You can book a new ride anytime through our platform</li>
    <li>Contact us if you have any questions about the cancellation</li>
</ul>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn">View Booking Details</a>
</div>

<h3>Need Assistance?</h3>
<p>If you have any questions about this status update or your booking, please don't hesitate to contact us:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or visit your account dashboard online</li>
</ul>

<p>Thank you for choosing {{ $companyName }}!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>
@endsection
