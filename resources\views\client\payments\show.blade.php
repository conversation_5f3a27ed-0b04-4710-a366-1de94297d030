@extends('layouts.client')

@section('title', 'Payment Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        Payment Details
        <span class="booking-status status-{{ strtolower($payment->status) }} ms-2">
            {{ ucfirst($payment->status) }}
        </span>
    </h2>
    <div>
        <a href="{{ route('client.payments.invoice', $payment->id) }}" class="btn btn-primary">
            <i class="fas fa-file-invoice me-1"></i> View Invoice
        </a>
        <a href="{{ route('client.payments.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Payments
        </a>
    </div>
</div>

@if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<div class="row">
    <!-- Payment Information -->
    <div class="col-md-7">
        <div class="card dashboard-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Payment Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Transaction ID</p>
                        <p class="fw-bold">{{ $payment->transaction_id }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Amount</p>
                        <p class="fw-bold">{{ \App\Helpers\SettingsHelper::formatPrice($payment->amount) }}</p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Payment Method</p>
                        <p class="fw-bold">
                            @if($payment->payment_method == 'paypal')
                                <i class="fab fa-paypal me-1"></i> PayPal
                            @elseif($payment->payment_method == 'credit_card')
                                <i class="far fa-credit-card me-1"></i> Credit Card
                            @elseif($payment->payment_method == 'cash')
                                <i class="fas fa-money-bill-wave me-1"></i> Cash
                            @else
                                <i class="fas fa-money-check me-1"></i> {{ ucfirst($payment->payment_method) }}
                            @endif
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Payment Date</p>
                        <p class="fw-bold">{{ $payment->created_at->format('F d, Y h:i A') }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Status</p>
                        <p class="fw-bold">
                            <span class="booking-status status-{{ strtolower($payment->status) }}">
                                {{ ucfirst($payment->status) }}
                            </span>
                        </p>
                    </div>
                    @if($payment->paid_at)
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Paid At</p>
                        <p class="fw-bold">{{ $payment->paid_at->format('F d, Y h:i A') }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Booking Information -->
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">Booking Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Booking Number</p>
                        <p class="fw-bold">{{ $payment->booking->booking_number }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Vehicle</p>
                        <p class="fw-bold">{{ $payment->booking->vehicle->name }}</p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Pickup Date</p>
                        <p class="fw-bold">{{ $payment->booking->pickup_date->format('F d, Y h:i A') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Booking Status</p>
                        <p class="fw-bold">
                            <span class="badge bg-{{ $payment->booking->status == 'completed' ? 'success' : ($payment->booking->status == 'cancelled' ? 'danger' : 'info') }}">
                                {{ ucfirst($payment->booking->status) }}
                            </span>
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Pickup Location</p>
                        <p class="fw-bold">{{ $payment->booking->pickup_address }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Dropoff Location</p>
                        <p class="fw-bold">{{ $payment->booking->dropoff_address }}</p>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ route('client.bookings.show', $payment->booking->id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-1"></i> View Booking Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Receipt and Actions -->
    <div class="col-md-5">
        <div class="card dashboard-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Payment Receipt</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        @if($payment->status == 'completed')
                            <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
                            <h4>Payment Successful</h4>
                        @elseif($payment->status == 'pending')
                            <i class="fas fa-clock text-warning fa-5x mb-3"></i>
                            <h4>Payment Pending</h4>
                        @elseif($payment->status == 'failed')
                            <i class="fas fa-times-circle text-danger fa-5x mb-3"></i>
                            <h4>Payment Failed</h4>
                        @elseif($payment->status == 'refunded')
                            <i class="fas fa-undo text-info fa-5x mb-3"></i>
                            <h4>Payment Refunded</h4>
                        @endif
                    </div>
                    
                    <div class="border-top border-bottom py-3 mb-3">
                        <h3 class="mb-0">{{ \App\Helpers\SettingsHelper::formatPrice($payment->amount) }}</h3>
                        <p class="text-muted">{{ $payment->created_at->format('F d, Y') }}</p>
                    </div>
                    
                    <p class="mb-4">
                        Thank you for your payment. This page serves as your official receipt.
                    </p>
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('client.payments.invoice', $payment->id) }}" class="btn btn-primary">
                            <i class="fas fa-file-invoice me-1"></i> View Invoice
                        </a>
                        <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Receipt
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">Need Help?</h5>
            </div>
            <div class="card-body">
                <p>If you have any questions about this payment or need assistance, please contact our support team.</p>
                <div class="d-grid gap-2">
                    <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
