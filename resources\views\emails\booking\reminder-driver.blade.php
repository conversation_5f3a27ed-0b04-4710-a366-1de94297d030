@extends('emails.layouts.master')

@section('title', 'Ride Reminder')

@section('content')
<h2>Upcoming Ride Reminder</h2>

<p>Dear {{ $driver->name }},</p>

<p>This is a reminder about your upcoming ride assignment with {{ $companyName }}.</p>

<div class="booking-details">
    <h3>Ride Assignment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong>{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value">{{ $user->name }}</span>
    </div>
    
    @if($user->phone)
    <div class="detail-row">
        <span class="detail-label">Client Phone:</span>
        <span class="detail-value">{{ $user->phone }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Fare:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($booking->amount, 2) }}</span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">Driver Instructions:</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>Contact the client 15-30 minutes before pickup time</li>
        <li>Arrive at the pickup location 5 minutes early</li>
        <li>Confirm the client's identity before starting the ride</li>
        <li>Update the ride status in your driver app</li>
        <li>Follow all safety protocols and company guidelines</li>
    </ul>
</div>

@if($booking->notes)
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <strong style="color: #856404;">Special Instructions from Client:</strong><br>
    <span style="color: #856404;">{{ $booking->notes }}</span>
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('driver.rides.show', $booking->id) }}" class="btn">View Ride Details</a>
</div>

<h3>Need Assistance?</h3>
<p>If you have any questions or need to report an issue, please contact dispatch:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or use the driver app support feature</li>
</ul>

<p>Thank you for being a valued member of our driver team!</p>

<p>Safe driving,<br>
The {{ $companyName }} Team</p>
@endsection
