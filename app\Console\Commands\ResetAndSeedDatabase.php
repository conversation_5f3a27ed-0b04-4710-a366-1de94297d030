<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ResetAndSeedDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:reset-and-seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the database and run all seeders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->confirm('This will reset your database. All data will be lost. Do you wish to continue?')) {
            $this->info('Resetting database...');
            
            // Migrate fresh
            $this->info('Running migrations...');
            Artisan::call('migrate:fresh');
            $this->info('Migrations completed successfully.');
            
            // Run seeders
            $this->info('Running seeders...');
            Artisan::call('db:seed');
            $this->info('Seeders completed successfully.');
            
            $this->info('Database has been reset and seeded successfully!');
        } else {
            $this->info('Operation cancelled.');
        }
    }
}
