<?php $__env->startSection('title', 'Booking Reports'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calendar-alt me-2 text-info"></i> Booking Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-info" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.reports.bookings')); ?>">
                <div class="row">
                    <div class="col-md-4">
                        <label for="period">Period</label>
                        <select name="period" id="period" class="form-control" onchange="this.form.submit()">
                            <option value="daily" <?php echo e($period == 'daily' ? 'selected' : ''); ?>>Daily</option>
                            <option value="monthly" <?php echo e($period == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                            <option value="yearly" <?php echo e($period == 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                        </select>
                    </div>
                    <?php if($period == 'daily' || $period == 'monthly'): ?>
                    <div class="col-md-4">
                        <label for="year">Year</label>
                        <select name="year" id="year" class="form-control" onchange="this.form.submit()">
                            <?php for($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?php echo e($y); ?>" <?php echo e($year == $y ? 'selected' : ''); ?>><?php echo e($y); ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    <?php if($period == 'daily'): ?>
                    <div class="col-md-4">
                        <label for="month">Month</label>
                        <select name="month" id="month" class="form-control" onchange="this.form.submit()">
                            <?php for($m = 1; $m <= 12; $m++): ?>
                                <option value="<?php echo e($m); ?>" <?php echo e($month == $m ? 'selected' : ''); ?>>
                                    <?php echo e(date('F', mktime(0, 0, 0, $m, 1))); ?>

                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Booking Trend Chart -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Booking Trend</h6>
        </div>
        <div class="card-body">
            <canvas id="bookingChart" width="400" height="100"></canvas>
        </div>
    </div>

    <div class="row">
        <!-- Booking Status Distribution -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Booking Status Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Popular Locations -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Locations</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Top Pickup Locations</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <?php $__currentLoopData = $popularPickups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pickup): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e(Str::limit($pickup->pickup_address, 30)); ?></td>
                                        <td><span class="badge badge-primary"><?php echo e($pickup->count); ?></span></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div>
                        <h6>Top Dropoff Locations</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <?php $__currentLoopData = $popularDropoffs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dropoff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e(Str::limit($dropoff->dropoff_address, 30)); ?></td>
                                        <td><span class="badge badge-success"><?php echo e($dropoff->count); ?></span></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Booking Trend Chart
const bookingCtx = document.getElementById('bookingChart').getContext('2d');
const bookingChart = new Chart(bookingCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($labels, 15, 512) ?>,
        datasets: [{
            label: 'Number of Bookings',
            data: <?php echo json_encode($bookingData, 15, 512) ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: <?php echo json_encode($bookingStatuses->pluck('status')->map(function($status) { return ucfirst(str_replace('_', ' ', $status)); })) ?>,
        datasets: [{
            data: <?php echo json_encode($bookingStatuses->pluck('count'), 15, 512) ?>,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reports\bookings.blade.php ENDPATH**/ ?>