<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', $companyName ?? 'YNR Cars'); ?></title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Remove blue links for iOS */
        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        /* Main styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f4f4f4;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background-color: #ee393d;
            padding: 20px;
            text-align: center;
        }

        .email-header h1 {
            color: #ffffff;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        .email-body {
            padding: 30px;
            line-height: 1.6;
            color: #333333;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #ee393d;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }

        .btn:hover {
            background-color: #d32f2f;
        }

        .booking-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #ee393d;
        }

        .booking-details h3 {
            margin-top: 0;
            color: #ee393d;
        }

        .detail-row {
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .detail-label {
            font-weight: bold;
            color: #555;
        }

        .detail-value {
            color: #333;
        }

        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #ee393d;
            text-decoration: none;
        }

        .contact-info {
            font-size: 14px;
            color: #666;
            margin: 10px 0;
        }

        /* Responsive */
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }
            
            .email-body {
                padding: 20px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1><?php echo e($companyName ?? 'YNR Cars'); ?></h1>
        </div>

        <!-- Body -->
        <div class="email-body">
            <?php echo $__env->yieldContent('content'); ?>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <div class="contact-info">
                <strong><?php echo e($companyName ?? 'YNR Cars'); ?></strong><br>
                <?php if(isset($companyPhone)): ?>
                    Phone: <?php echo e($companyPhone); ?><br>
                <?php endif; ?>
                <?php if(isset($companyEmail)): ?>
                    Email: <?php echo e($companyEmail); ?><br>
                <?php endif; ?>
                <?php if(isset($companyAddress)): ?>
                    Address: <?php echo e($companyAddress); ?><br>
                <?php endif; ?>
            </div>

            <?php if(isset($socialMediaSettings)): ?>
            <div class="social-links">
                <?php if($socialMediaSettings['facebook_url']): ?>
                    <a href="<?php echo e($socialMediaSettings['facebook_url']); ?>">Facebook</a>
                <?php endif; ?>
                <?php if($socialMediaSettings['twitter_url']): ?>
                    <a href="<?php echo e($socialMediaSettings['twitter_url']); ?>">Twitter</a>
                <?php endif; ?>
                <?php if($socialMediaSettings['instagram_url']): ?>
                    <a href="<?php echo e($socialMediaSettings['instagram_url']); ?>">Instagram</a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <p style="font-size: 12px; color: #999; margin-top: 20px;">
                © <?php echo e(date('Y')); ?> <?php echo e($companyName ?? 'YNR Cars'); ?>. All rights reserved.<br>
                This email was sent to you because you have an account with us or have requested this information.
            </p>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\layouts\master.blade.php ENDPATH**/ ?>