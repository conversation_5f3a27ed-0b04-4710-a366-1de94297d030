@extends('layouts.guest')

@section('title', 'Create Account to Complete Your Booking')

@section('styles')
<style>
    .auth-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .auth-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    .booking-summary {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .summary-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .summary-label {
        font-weight: 600;
        color: #495057;
    }

    .summary-value {
        color: #6c757d;
    }

    .summary-total {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 2px solid #e9ecef;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 5px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #343a40;
        font-weight: 600;
        padding: 12px 20px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e0a800;
        border-color: #e0a800;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-primary {
        border-color: #ee393d;
        color: #ee393d;
        font-weight: 600;
        padding: 12px 20px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-outline-primary:hover {
        background-color: #ee393d;
        color: #343a40;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .auth-divider {
        display: flex;
        align-items: center;
        margin: 30px 0;
    }

    .auth-divider::before,
    .auth-divider::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #e9ecef;
    }

    .auth-divider-text {
        padding: 0 15px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .password-requirements {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-top: 10px;
        font-size: 0.9rem;
    }

    .password-requirements ul {
        margin-bottom: 0;
        padding-left: 20px;
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="auth-card">
                <div class="card-header">
                    <h4 class="mb-0">Create Account to Complete Your Booking</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-4">Create Account</h5>

                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('booking.guest.process-register') }}">
                                @csrf

                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input id="name" type="text" class="form-control @error('name') is-invalid @enderror" name="name" value="{{ old('name', session('client_details.name', '')) }}" required autocomplete="name" autofocus>
                                    @error('name')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email', session('client_details.email', '')) }}" required autocomplete="email">
                                    @error('email')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input id="phone" type="text" class="form-control @error('phone') is-invalid @enderror" name="phone" value="{{ old('phone', session('client_details.phone', '')) }}" required autocomplete="tel">
                                    @error('phone')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="new-password">
                                    @error('password')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror

                                    <div class="password-requirements">
                                        <p class="mb-1">Password must:</p>
                                        <ul>
                                            <li>Be at least 8 characters long</li>
                                            <li>Include at least one uppercase letter</li>
                                            <li>Include at least one number</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password-confirm" class="form-label">Confirm Password</label>
                                    <input id="password-confirm" type="password" class="form-control" name="password_confirmation" required autocomplete="new-password">
                                </div>

                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" name="terms" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="{{ route('terms-and-conditions') }}" target="_blank">Terms and Conditions</a>
                                    </label>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" id="registerBtn" class="btn btn-primary">
                                        <span class="button-text"><i class="fas fa-user-plus me-2"></i> Create Account</span>
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                    </button>
                                </div>
                            </form>

                            <div class="auth-divider">
                                <span class="auth-divider-text">OR</span>
                            </div>

                            <div class="text-center">
                                <p class="mb-3">Already have an account?</p>
                                <a href="{{ route('booking.guest.login') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i> Sign In
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-4">Booking Summary</h5>

                            <div class="booking-summary">
                                <div class="summary-item">
                                    <div class="summary-label">Booking Type:</div>
                                    <div class="summary-value">
                                        @if(session('guest_booking')['booking_type'] == 'one_way')
                                            One Way
                                        @elseif(session('guest_booking')['booking_type'] == 'return')
                                            Return
                                        @else
                                            Hourly ({{ session('guest_booking')['duration_hours'] }} hours)
                                        @endif
                                    </div>
                                </div>

                                <div class="summary-item">
                                    <div class="summary-label">Pickup:</div>
                                    <div class="summary-value">{{ session('guest_booking')['pickup_address'] }}</div>
                                </div>

                                @if(session('guest_booking')['booking_type'] != 'hourly')
                                    <div class="summary-item">
                                        <div class="summary-label">Dropoff:</div>
                                        <div class="summary-value">{{ session('guest_booking')['dropoff_address'] }}</div>
                                    </div>
                                @endif

                                <div class="summary-item">
                                    <div class="summary-label">Date:</div>
                                    <div class="summary-value">{{ \Carbon\Carbon::parse(session('guest_booking')['pickup_date'])->format('M d, Y') }}</div>
                                </div>

                                <div class="summary-item">
                                    <div class="summary-label">Time:</div>
                                    <div class="summary-value">{{ \Carbon\Carbon::parse(session('guest_booking')['pickup_date'])->format('h:i A') }}</div>
                                </div>

                                <div class="summary-total">
                                    <div>Total:</div>
                                    <div>@currency(){{ number_format(session('guest_booking')['amount'], 2) }}</div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> Create an account to complete your booking and proceed to payment.
                            </div>

                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> By creating an account, you'll be able to:
                                <ul class="mb-0 mt-2">
                                    <li>Track your booking status</li>
                                    <li>View your booking history</li>
                                    <li>Receive email notifications</li>
                                    <li>Book faster next time</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle registration form submission
        const registerForm = document.querySelector('form[action="{{ route("booking.guest.process-register") }}"]');
        const registerBtn = document.getElementById('registerBtn');

        if (registerForm && registerBtn) {
            registerForm.addEventListener('submit', function(e) {
                // Validate password
                const password = document.getElementById('password').value;
                const passwordConfirm = document.getElementById('password-confirm').value;

                // Basic password validation
                if (password.length < 8) {
                    e.preventDefault();
                    alert('Password must be at least 8 characters long.');
                    return;
                }

                if (password !== passwordConfirm) {
                    e.preventDefault();
                    alert('Passwords do not match.');
                    return;
                }

                // Show loading indicator
                const spinner = registerBtn.querySelector('.spinner-border');
                const buttonText = registerBtn.querySelector('.button-text');

                if (spinner && buttonText) {
                    spinner.classList.remove('d-none');
                    registerBtn.disabled = true;
                }

                // Form will submit normally
            });
        }
    });
</script>
@endsection