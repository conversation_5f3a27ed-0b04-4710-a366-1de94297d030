<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\User;
use App\Notifications\BookingReminder;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendBookingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder notifications for upcoming bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Sending booking reminders...');

        // Get bookings that are scheduled for tomorrow and haven't been reminded yet
        $upcomingBookings = Booking::with(['user', 'driver', 'vehicle'])
            ->whereIn('status', ['confirmed', 'assigned'])
            ->whereDate('pickup_date', Carbon::tomorrow())
            ->whereNull('reminder_sent_at')
            ->get();

        $remindersSent = 0;

        foreach ($upcomingBookings as $booking) {
            try {
                // Send reminder to client
                if ($booking->user) {
                    $booking->user->notify(new BookingReminder($booking, 'client'));
                }

                // Send reminder to driver if assigned
                if ($booking->driver) {
                    $booking->driver->notify(new BookingReminder($booking, 'driver'));
                }

                // Mark as reminded
                $booking->update(['reminder_sent_at' => now()]);
                
                $remindersSent++;
                $this->info("Reminder sent for booking #{$booking->booking_number}");

            } catch (\Exception $e) {
                $this->error("Failed to send reminder for booking #{$booking->booking_number}: " . $e->getMessage());
            }
        }

        $this->info("Sent {$remindersSent} booking reminders.");

        return Command::SUCCESS;
    }
}
