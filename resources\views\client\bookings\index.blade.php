@extends('layouts.client')

@section('title', 'My Bookings')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }

    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: calc(100vh - 76px);
        padding-top: 20px;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 10px 20px;
        margin-bottom: 5px;
        border-radius: 5px;
    }

    .sidebar .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
        color: #fff;
        background-color: #ee393d;
    }

    .sidebar .nav-link i {
        margin-right: 10px;
    }

    .booking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }

    .booking-card:hover {
        transform: translateY(-5px);
    }

    .booking-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .booking-card .card-body {
        padding: 20px;
    }

    .booking-card .card-footer {
        background-color: #fff;
        border-top: 1px solid #f1f1f1;
        padding: 15px 20px;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-confirmed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-assigned {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-in-progress {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #c3e6cb;
        color: #155724;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .booking-detail {
        margin-bottom: 10px;
    }

    .booking-detail-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .booking-detail-value {
        color: #6c757d;
    }

    .vehicle-img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 5px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>My Bookings</h2>
                <a href="{{ route('booking.index') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Book a Ride
                </a>
            </div>

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if ($bookings->isEmpty())
                <div class="alert alert-info">
                    You don't have any bookings yet. <a href="{{ route('booking.index') }}">Book a ride now</a>.
                </div>
            @else
                <div class="row">
                    @foreach ($bookings as $booking)
                        <div class="col-md-6">
                            <div class="card booking-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Booking #{{ $booking->booking_number }}</h5>
                                    <span class="status-badge status-{{ strtolower(str_replace('_', '-', $booking->status)) }}">
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            @if ($booking->vehicle->image)
                                                <img src="{{ asset('storage/' . $booking->vehicle->image) }}" class="vehicle-img" alt="{{ $booking->vehicle->name }}">
                                            @else
                                                <img src="https://via.placeholder.com/150x120?text=Vehicle" class="vehicle-img" alt="{{ $booking->vehicle->name }}">
                                            @endif
                                        </div>
                                        <div class="col-md-8">
                                            <div class="booking-detail">
                                                <div class="booking-detail-label">Vehicle</div>
                                                <div class="booking-detail-value">{{ $booking->vehicle->name }}</div>
                                            </div>
                                            <div class="booking-detail">
                                                <div class="booking-detail-label">Date & Time</div>
                                                <div class="booking-detail-value">{{ $booking->pickup_date->format('M d, Y h:i A') }}</div>
                                            </div>
                                            @if($booking->booking_type === 'airport_transfer')
                                                <div class="booking-detail">
                                                    <div class="booking-detail-label">Transfer Type</div>
                                                    <div class="booking-detail-value">{{ ucfirst(str_replace('_', ' ', $booking->airport_direction)) }}</div>
                                                </div>
                                                @if($booking->pickupAirport)
                                                    <div class="booking-detail">
                                                        <div class="booking-detail-label">From Airport</div>
                                                        <div class="booking-detail-value">{{ $booking->pickupAirport->code }}</div>
                                                    </div>
                                                @endif
                                                @if($booking->dropoffAirport)
                                                    <div class="booking-detail">
                                                        <div class="booking-detail-label">To Airport</div>
                                                        <div class="booking-detail-value">{{ $booking->dropoffAirport->code }}</div>
                                                    </div>
                                                @endif
                                            @else
                                                <div class="booking-detail">
                                                    <div class="booking-detail-label">Pickup</div>
                                                    <div class="booking-detail-value">{{ Str::limit($booking->pickup_address, 30) }}</div>
                                                </div>
                                                @if ($booking->booking_type !== 'hourly')
                                                    <div class="booking-detail">
                                                        <div class="booking-detail-label">Dropoff</div>
                                                        <div class="booking-detail-value">{{ Str::limit($booking->dropoff_address, 30) }}</div>
                                                    </div>
                                                @endif
                                                @if($booking->hasViaPoints())
                                                    <div class="booking-detail">
                                                        <div class="booking-detail-label">Via Points</div>
                                                        <div class="booking-detail-value">{{ $booking->getViaPointsCount() }} stop{{ $booking->getViaPointsCount() > 1 ? 's' : '' }}</div>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Total:</strong> @currency(){{ number_format($booking->amount, 2) }}
                                    </div>
                                    <div>
                                        <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-sm btn-primary">View Details</a>

                                        @if (in_array($booking->status, ['pending', 'confirmed']))
                                            <form action="{{ route('client.bookings.cancel', $booking->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to cancel this booking?');">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-danger">Cancel</button>
                                            </form>
                                        @endif

                                        @if ($booking->status === 'completed' && !$booking->review)
                                            <a href="{{ route('client.bookings.review', $booking->id) }}" class="btn btn-sm btn-outline-primary">Leave Review</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
