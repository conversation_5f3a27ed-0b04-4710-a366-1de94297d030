@extends('layouts.admin')

@section('title', 'Airport Details')

@section('content')
<div class="content-wrapper">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plane me-2 text-info"></i>{{ $airport->name }}
                <span class="badge bg-info ms-2">{{ $airport->code }}</span>
            </h1>
            <div>
                <a href="{{ route('admin.airports.edit', $airport) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i> Edit
                </a>
                <a href="{{ route('admin.airports.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Airports
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Airport Information -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Airport Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold" width="120">Name:</td>
                                        <td>{{ $airport->name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Code:</td>
                                        <td><span class="badge bg-primary">{{ $airport->code }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">City:</td>
                                        <td>{{ $airport->city }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Country:</td>
                                        <td>
                                            <span class="flag-icon flag-icon-{{ strtolower($airport->country_code) }}"></span>
                                            {{ $airport->country }} ({{ $airport->country_code }})
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    @if($airport->latitude && $airport->longitude)
                                    <tr>
                                        <td class="fw-bold" width="120">Latitude:</td>
                                        <td>{{ number_format($airport->latitude, 6) }}°</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Longitude:</td>
                                        <td>{{ number_format($airport->longitude, 6) }}°</td>
                                    </tr>
                                    @endif
                                    @if($airport->timezone)
                                    <tr>
                                        <td class="fw-bold">Timezone:</td>
                                        <td>{{ $airport->timezone }}</td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td class="fw-bold">Created:</td>
                                        <td>{{ $airport->created_at->format('M d, Y g:i A') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Updated:</td>
                                        <td>{{ $airport->updated_at->format('M d, Y g:i A') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if($airport->address)
                        <div class="mt-3">
                            <h6 class="fw-bold">Address:</h6>
                            <p class="text-muted">{{ $airport->address }}</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Map Section -->
                @if($airport->latitude && $airport->longitude)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>Location Map
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="airport-map" style="height: 400px; border-radius: 8px;"></div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Quick Actions & Stats -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.airports.edit', $airport) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Airport
                            </a>
                            <button class="btn btn-info" onclick="copyCoordinates()">
                                <i class="fas fa-copy me-2"></i>Copy Coordinates
                            </button>
                            @if($airport->latitude && $airport->longitude)
                            <a href="https://www.google.com/maps?q={{ $airport->latitude }},{{ $airport->longitude }}"
                               target="_blank" class="btn btn-success">
                                <i class="fas fa-external-link-alt me-2"></i>View on Google Maps
                            </a>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Airport Statistics -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary mb-1">{{ $airport->total_bookings }}</h4>
                                    <small class="text-muted">Total Bookings</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success mb-1">{{ $airport->this_month_bookings }}</h4>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Booking statistics will appear here once this airport is used in bookings.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Airport Details -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>Details Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span class="small fw-bold">Code:</span>
                                    <span class="badge bg-primary">{{ $airport->code }}</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span class="small fw-bold">Location:</span>
                                    <span class="small">{{ $airport->city }}, {{ $airport->country_code }}</span>
                                </div>
                            </div>
                            @if($airport->timezone)
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span class="small fw-bold">Timezone:</span>
                                    <span class="small">{{ $airport->timezone }}</span>
                                </div>
                            </div>
                            @endif
                            @if($airport->latitude && $airport->longitude)
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span class="small fw-bold">Coordinates:</span>
                                    <span class="small" id="coordinates">
                                        {{ number_format($airport->latitude, 4) }}, {{ number_format($airport->longitude, 4) }}
                                    </span>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@if($airport->latitude && $airport->longitude)
<script>
    function initMap() {
        const airportLocation = {
            lat: {{ $airport->latitude }},
            lng: {{ $airport->longitude }}
        };

        const map = new google.maps.Map(document.getElementById("airport-map"), {
            zoom: 12,
            center: airportLocation,
        });

        const marker = new google.maps.Marker({
            position: airportLocation,
            map: map,
            title: "{{ $airport->name }}",
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#007bff" width="32" height="32">
                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
            }
        });

        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h6 style="margin: 0 0 5px 0; color: #007bff;">{{ $airport->name }}</h6>
                    <p style="margin: 0; font-size: 14px;">
                        <strong>Code:</strong> {{ $airport->code }}<br>
                        <strong>Location:</strong> {{ $airport->city }}, {{ $airport->country }}
                    </p>
                </div>
            `
        });

        marker.addListener("click", () => {
            infoWindow.open(map, marker);
        });
    }

    function copyCoordinates() {
        const coordinates = "{{ $airport->latitude }}, {{ $airport->longitude }}";
        navigator.clipboard.writeText(coordinates).then(function() {
            // Show success message
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            btn.classList.remove('btn-info');
            btn.classList.add('btn-success');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-info');
            }, 2000);
        });
    }
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.key') }}&callback=initMap"></script>
@else
<script>
    function copyCoordinates() {
        alert('No coordinates available for this airport.');
    }
</script>
@endif
@endsection
