<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display the settings page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get all settings from the database grouped by their group
        $settingsByGroup = Setting::all()->groupBy('group');

        // Get all settings as a key-value collection
        $settingsCollection = Setting::all()->keyBy('key');

        return view('admin.settings.index', compact('settingsCollection', 'settingsByGroup'));
    }

    /**
     * Update the specified settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $request->validate([
            // Company Settings
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|max:255',
            'company_phone' => 'required|string|max:255',
            'company_address' => 'required|string|max:255',

            // Pricing and Localization Settings
            'currency_code' => 'required|string|size:3',
            'currency_symbol' => 'required|string|max:10',
            'country_code' => 'required|string|size:2',
            'selected_country_name' => 'nullable|string|max:255',
            'distance_unit' => 'required|in:miles,kilometers',
            'timezone' => 'nullable|string|max:255',
            'date_format' => 'nullable|string|max:20',


            // Google Maps API Settings
            'google_maps_api_key' => 'nullable|string|max:255',
            'google_maps_default_zoom' => 'nullable|integer|min:1|max:20',
            'google_maps_libraries' => 'nullable|string|max:255',
            'google_maps_default_lat' => 'nullable|string|max:20',
            'google_maps_default_lng' => 'nullable|string|max:20',
            'google_maps_use_distance_matrix' => 'nullable|in:true,false',
            'google_maps_use_directions' => 'nullable|in:true,false',
            'google_maps_restrict_country' => 'nullable|in:true,false',
            'google_maps_country_code' => 'nullable|string|size:2',

            // Autocomplete Settings
            'autocomplete_enabled' => 'nullable|in:true,false',
            'autocomplete_restrict_country' => 'nullable|in:true,false',
            'autocomplete_country' => 'nullable|string|size:2',
            'autocomplete_types' => 'nullable|string|max:50',
            'autocomplete_bias_radius' => 'nullable|integer|min:0|max:500',
            'autocomplete_use_strict_bounds' => 'nullable|in:true,false',
            'autocomplete_fields' => 'nullable|string|max:50',

            // Payment Settings
            'paypal_client_id' => 'nullable|string|max:255',
            'paypal_secret' => 'nullable|string|max:255',
            'paypal_mode' => 'required|in:sandbox,live',
            'stripe_publishable_key' => 'nullable|string|max:255',
            'stripe_secret_key' => 'nullable|string|max:255',

            // Social Media Settings
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',

            // Content Settings
            'about_us' => 'nullable|string',
            'privacy_policy' => 'nullable|string',
            'terms_and_conditions' => 'nullable|string',

            // Booking Settings
            'advance_booking_time' => 'required|integer|min:1|max:90',
            'minimum_hourly_duration' => 'required|integer|min:1|max:12',
            'cancellation_time_window' => 'required|integer|min:1|max:72',
            'cancellation_fee_percentage' => 'required|integer|min:0|max:100',
            'allow_guest_bookings' => 'nullable|in:true,false',

            // Extra Services Settings
            'meet_and_greet_fee' => 'required|numeric|min:0|max:999.99',
            'child_seat_fee' => 'required|numeric|min:0|max:999.99',
            'wheelchair_fee' => 'required|numeric|min:0|max:999.99',
            'extra_luggage_fee' => 'required|numeric|min:0|max:999.99',
            'meet_and_greet_enabled' => 'nullable|in:true,false',
            'child_seat_enabled' => 'nullable|in:true,false',
            'wheelchair_enabled' => 'nullable|in:true,false',
            'extra_luggage_enabled' => 'nullable|in:true,false',

            // Driver Settings
            'driver_commission_percentage' => 'required|integer|min:50|max:95',
            'minimum_payout_amount' => 'required|numeric|min:10|max:200',
            'payout_schedule' => 'required|in:weekly,biweekly,monthly',
            'driver_verification_requirements' => 'nullable',
            'maximum_active_hours' => 'required|integer|min:4|max:16',

            // Email Settings
            'mail_driver' => 'required|string|max:255',
            'mail_host' => 'nullable|string|max:255',
            'mail_port' => 'nullable|integer',
            'mail_username' => 'nullable|string|max:255',
            'mail_password' => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|max:255',
            'mail_from_address' => 'nullable|email|max:255',
            'mail_from_name' => 'nullable|string|max:255',

            // Appearance Settings
            'primary_color' => 'required|string|max:7',
            'secondary_color' => 'required|string|max:7',
            'logo' => 'nullable|string|max:255',
            'favicon' => 'nullable|string|max:255',
            'default_language' => 'required|string|max:2',
        ]);

        // Group settings by their category
        $settingsGroups = [
            'company' => ['company_name', 'company_email', 'company_phone', 'company_address'],
            'pricing' => ['currency_code', 'currency_symbol'],
            'localization' => ['country_code', 'selected_country_name', 'distance_unit', 'timezone', 'date_format'],
            'api' => ['google_maps_api_key', 'google_maps_default_zoom', 'google_maps_libraries', 'google_maps_default_lat', 'google_maps_default_lng', 'google_maps_use_distance_matrix', 'google_maps_use_directions', 'google_maps_restrict_country', 'google_maps_country_code'],
            'autocomplete' => ['autocomplete_enabled', 'autocomplete_restrict_country', 'autocomplete_country', 'autocomplete_types', 'autocomplete_bias_radius', 'autocomplete_use_strict_bounds', 'autocomplete_fields'],
            'payment' => ['paypal_client_id', 'paypal_secret', 'paypal_mode', 'stripe_publishable_key', 'stripe_secret_key'],
            'social' => ['facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url'],
            'content' => ['about_us', 'privacy_policy', 'terms_and_conditions'],
            'booking' => ['advance_booking_time', 'minimum_hourly_duration', 'cancellation_time_window', 'cancellation_fee_percentage', 'allow_guest_bookings'],
            'extra_services' => ['meet_and_greet_fee', 'child_seat_fee', 'wheelchair_fee', 'extra_luggage_fee', 'meet_and_greet_enabled', 'child_seat_enabled', 'wheelchair_enabled', 'extra_luggage_enabled'],
            'driver' => ['driver_commission_percentage', 'minimum_payout_amount', 'payout_schedule', 'driver_verification_requirements', 'maximum_active_hours'],
            'email' => ['mail_driver', 'mail_host', 'mail_port', 'mail_username', 'mail_password', 'mail_encryption', 'mail_from_address', 'mail_from_name'],
            'appearance' => ['primary_color', 'secondary_color', 'logo', 'favicon', 'default_language'],
        ];

        // Update each setting in the database
        foreach ($request->except('_token') as $key => $value) {
            // Find the group for this setting
            $group = 'general';
            foreach ($settingsGroups as $groupName => $keys) {
                if (in_array($key, $keys)) {
                    $group = $groupName;
                    break;
                }
            }

            // Determine if this setting should be public
            $isPublic = !in_array($key, [
                'google_maps_api_key', 'paypal_client_id', 'paypal_secret', 'stripe_publishable_key', 'stripe_secret_key',
                'mail_driver', 'mail_host', 'mail_port', 'mail_username', 'mail_password', 'mail_encryption',
                'driver_commission_percentage', 'driver_verification_requirements'
            ]);

            // Determine the type of the setting
            $type = 'text';

            // Email fields
            if (in_array($key, ['company_email', 'mail_from_address'])) {
                $type = 'email';
            }
            // Number fields
            elseif (in_array($key, [
                'mail_port', 'advance_booking_time', 'minimum_hourly_duration', 'cancellation_time_window',
                'cancellation_fee_percentage', 'driver_commission_percentage', 'minimum_payout_amount',
                'maximum_active_hours', 'google_maps_default_zoom', 'meet_and_greet_fee', 'child_seat_fee',
                'wheelchair_fee', 'extra_luggage_fee'
            ])) {
                $type = 'number';
            }
            // URL fields
            elseif (in_array($key, ['facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url'])) {
                $type = 'url';
            }
            // Textarea fields
            elseif (in_array($key, ['about_us', 'privacy_policy', 'terms_and_conditions', 'company_address'])) {
                $type = 'textarea';
            }
            // Select fields
            elseif (in_array($key, ['paypal_mode', 'mail_driver', 'mail_encryption', 'payout_schedule', 'default_language', 'currency_code', 'country_code', 'distance_unit', 'timezone', 'date_format', 'google_maps_country_code'])) {
                $type = 'select';
            }
            // Color fields
            elseif (in_array($key, ['primary_color', 'secondary_color'])) {
                $type = 'color';
            }
            // File fields
            elseif (in_array($key, ['logo', 'favicon'])) {
                $type = 'file';
            }
            // Boolean fields
            elseif (in_array($key, ['allow_guest_bookings', 'google_maps_use_distance_matrix', 'google_maps_use_directions', 'google_maps_restrict_country', 'meet_and_greet_enabled', 'child_seat_enabled', 'wheelchair_enabled', 'extra_luggage_enabled'])) {
                $type = 'boolean';
            }
            // JSON fields
            elseif (in_array($key, ['driver_verification_requirements'])) {
                $type = 'json';
            }
            // Password fields
            elseif (in_array($key, ['mail_password'])) {
                $type = 'password';
            }

            // Set options for select fields
            $options = null;
            if ($key === 'paypal_mode') {
                $options = json_encode(['sandbox' => 'Sandbox', 'live' => 'Live']);
            } elseif ($key === 'mail_driver') {
                $options = json_encode(['smtp' => 'SMTP', 'sendmail' => 'Sendmail', 'mailgun' => 'Mailgun', 'ses' => 'Amazon SES']);
            } elseif ($key === 'mail_encryption') {
                $options = json_encode(['tls' => 'TLS', 'ssl' => 'SSL', 'none' => 'None']);
            } elseif ($key === 'payout_schedule') {
                $options = json_encode(['weekly' => 'Weekly', 'biweekly' => 'Bi-weekly', 'monthly' => 'Monthly']);
            } elseif ($key === 'default_language') {
                $options = json_encode(['en' => 'English', 'es' => 'Spanish', 'fr' => 'French', 'de' => 'German', 'it' => 'Italian']);
            } elseif ($key === 'currency_code') {
                $options = json_encode([
                    'USD' => 'US Dollar ($)',
                    'EUR' => 'Euro (€)',
                    'GBP' => 'British Pound (£)',
                    'CAD' => 'Canadian Dollar (C$)',
                    'AUD' => 'Australian Dollar (A$)',
                    'JPY' => 'Japanese Yen (¥)',
                    'INR' => 'Indian Rupee (₹)',
                    'CNY' => 'Chinese Yuan (¥)',
                    'AED' => 'UAE Dirham (د.إ)',
                    'SAR' => 'Saudi Riyal (﷼)'
                ]);
            } elseif (in_array($key, ['country_code', 'google_maps_country_code'])) {
                $options = json_encode([
                    'US' => 'United States',
                    'GB' => 'United Kingdom',
                    'CA' => 'Canada',
                    'AU' => 'Australia',
                    'DE' => 'Germany',
                    'FR' => 'France',
                    'IT' => 'Italy',
                    'ES' => 'Spain',
                    'IN' => 'India',
                    'AE' => 'United Arab Emirates',
                    'SA' => 'Saudi Arabia'
                ]);
            } elseif ($key === 'distance_unit') {
                $options = json_encode([
                    'miles' => 'Miles',
                    'kilometers' => 'Kilometers'
                ]);
            } elseif ($key === 'timezone') {
                $options = json_encode([
                    'UTC' => 'UTC',
                    'America/New_York' => 'Eastern Time (US & Canada)',
                    'America/Chicago' => 'Central Time (US & Canada)',
                    'America/Denver' => 'Mountain Time (US & Canada)',
                    'America/Los_Angeles' => 'Pacific Time (US & Canada)',
                    'Europe/London' => 'London',
                    'Europe/Paris' => 'Paris',
                    'Asia/Dubai' => 'Dubai',
                    'Asia/Kolkata' => 'India'
                ]);
            } elseif ($key === 'date_format') {
                $options = json_encode([
                    'Y-m-d' => 'YYYY-MM-DD (2023-12-31)',
                    'm/d/Y' => 'MM/DD/YYYY (12/31/2023)',
                    'd/m/Y' => 'DD/MM/YYYY (31/12/2023)',
                    'd.m.Y' => 'DD.MM.YYYY (31.12.2023)'
                ]);
            } elseif (in_array($key, ['advance_booking_time'])) {
                $options = json_encode(['min' => 1, 'max' => 90, 'step' => 1]);
            } elseif (in_array($key, ['minimum_hourly_duration'])) {
                $options = json_encode(['min' => 1, 'max' => 12, 'step' => 1]);
            } elseif (in_array($key, ['cancellation_time_window'])) {
                $options = json_encode(['min' => 1, 'max' => 72, 'step' => 1]);
            } elseif (in_array($key, ['cancellation_fee_percentage'])) {
                $options = json_encode(['min' => 0, 'max' => 100, 'step' => 5]);
            } elseif (in_array($key, ['driver_commission_percentage'])) {
                $options = json_encode(['min' => 50, 'max' => 95, 'step' => 5]);
            } elseif (in_array($key, ['minimum_payout_amount'])) {
                $options = json_encode(['min' => 10, 'max' => 200, 'step' => 10]);
            } elseif (in_array($key, ['maximum_active_hours'])) {
                $options = json_encode(['min' => 4, 'max' => 16, 'step' => 1]);
            } elseif (in_array($key, ['google_maps_default_zoom'])) {
                $options = json_encode(['min' => 1, 'max' => 20, 'step' => 1]);
            } elseif (in_array($key, ['meet_and_greet_fee', 'child_seat_fee', 'wheelchair_fee', 'extra_luggage_fee'])) {
                $options = json_encode(['min' => 0, 'max' => 999.99, 'step' => 0.01]);
            }

            // Update or create the setting
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => $group,
                    'type' => $type,
                    'options' => $options,
                    'is_public' => $isPublic,
                ]
            );
        }

        // Clear the settings cache
        Cache::forget('settings');

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully.');
    }
}
