@extends('layouts.guest')

@section('title', 'PayPal Test')

@section('styles')
<style>
    .test-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .test-title {
        margin-bottom: 30px;
        text-align: center;
    }

    .test-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
    }

    .test-section h3 {
        margin-bottom: 20px;
        color: #343a40;
    }

    .test-section pre {
        background-color: #343a40;
        color: #fff;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }

    .test-button {
        display: block;
        width: 100%;
        padding: 15px;
        background-color: #ee393d;
        color: #000;
        border: none;
        border-radius: 5px;
        font-weight: 600;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .test-button:hover {
        background-color: #e5b325;
    }

    #paypal-button-container {
        margin-top: 20px;
    }
</style>
@endsection

@section('content')
<div class="test-container">
    <h1 class="test-title">PayPal Integration Test</h1>

    <div class="test-section">
        <h3>Configuration</h3>
        <pre id="config-info">Loading configuration...</pre>
    </div>

    <div class="test-section">
        <h3>PayPal Button Test</h3>
        <p>Click the button below to test the PayPal integration:</p>
        <div id="paypal-button-container"></div>
    </div>

    <div class="test-section">
        <h3>Response</h3>
        <pre id="response-info">No response yet...</pre>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://www.paypal.com/sdk/js?client-id={{ env('PAYPAL_SANDBOX_CLIENT_ID') }}&currency=USD"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch configuration info
        fetch('/paypal-test')
            .then(response => response.json())
            .then(data => {
                document.getElementById('config-info').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('config-info').textContent = 'Error fetching configuration: ' + error.message;
            });

        // Initialize PayPal buttons
        if (paypal) {
            paypal.Buttons({
                // Set up the transaction
                createOrder: function(data, actions) {
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: '10.00'
                            }
                        }]
                    });
                },

                // Finalize the transaction
                onApprove: function(data, actions) {
                    return actions.order.capture().then(function(orderData) {
                        // Show a success message
                        document.getElementById('response-info').textContent = JSON.stringify(orderData, null, 2);
                    });
                },

                // Handle errors
                onError: function(err) {
                    document.getElementById('response-info').textContent = 'Error: ' + err;
                }
            }).render('#paypal-button-container');
        } else {
            document.getElementById('paypal-button-container').innerHTML = '<p class="text-danger">PayPal SDK failed to load. Check your client ID.</p>';
        }
    });
</script>
@endsection
