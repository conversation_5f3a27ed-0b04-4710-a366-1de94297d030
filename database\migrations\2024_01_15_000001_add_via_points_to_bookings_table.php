<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add via points support
            $table->json('via_points')->nullable()->after('dropoff_lng');
            $table->integer('via_count')->default(0)->after('via_points');
            $table->decimal('via_surcharge', 8, 2)->default(0)->after('via_count');
            
            // Add total distance and duration including via points
            $table->decimal('total_distance_with_via', 10, 2)->nullable()->after('via_surcharge');
            $table->integer('total_duration_with_via')->nullable()->after('total_distance_with_via');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn([
                'via_points',
                'via_count',
                'via_surcharge',
                'total_distance_with_via',
                'total_duration_with_via'
            ]);
        });
    }
};
