<?php $__env->startSection('title', 'Driver Profile'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }



    .profile-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .profile-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .profile-card .card-body {
        padding: 30px;
    }

    .profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
    }

    .profile-img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 20px;
    }

    .profile-name {
        margin-bottom: 5px;
    }

    .profile-role {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .profile-section {
        margin-bottom: 30px;
    }

    .profile-section h5 {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .profile-detail {
        margin-bottom: 15px;
    }

    .profile-detail-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .profile-detail-value {
        color: #6c757d;
    }

    .availability-status {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .status-available {
        background-color: #d4edda;
        color: #155724;
    }

    .status-unavailable {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>My Profile</h2>
                <div>
                    <a href="<?php echo e(route('driver.profile.availability')); ?>" class="btn btn-success me-2">
                        <i class="fas fa-toggle-on me-2"></i> Update Availability
                    </a>
                    <a href="<?php echo e(route('driver.profile.edit')); ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i> Edit Profile
                    </a>
                    <a href="<?php echo e(route('driver.profile.change-password')); ?>" class="btn btn-secondary">
                        <i class="fas fa-key me-2"></i> Change Password
                    </a>
                </div>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card profile-card">
                <div class="card-header">
                    <h4 class="mb-0">Profile Information</h4>
                </div>
                <div class="card-body">
                    <div class="profile-header">
                        <?php if($user->profile_photo): ?>
                            <img src="<?php echo e(asset('storage/' . $user->profile_photo)); ?>" class="profile-img" alt="<?php echo e($user->name); ?>">
                        <?php else: ?>
                            <img src="https://via.placeholder.com/100x100?text=Driver" class="profile-img" alt="<?php echo e($user->name); ?>">
                        <?php endif; ?>
                        <div>
                            <h3 class="profile-name"><?php echo e($user->name); ?></h3>
                            <p class="profile-role">
                                <?php echo e(ucfirst($user->role)); ?>

                                <span class="availability-status status-<?php echo e($user->is_available ? 'available' : 'unavailable'); ?>">
                                    <?php echo e($user->is_available ? 'Available' : 'Unavailable'); ?>

                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="profile-section">
                                <h5>Personal Information</h5>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Name</div>
                                    <div class="profile-detail-value"><?php echo e($user->name); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Email</div>
                                    <div class="profile-detail-value"><?php echo e($user->email); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Phone</div>
                                    <div class="profile-detail-value"><?php echo e($user->phone ?? 'Not provided'); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Address</div>
                                    <div class="profile-detail-value"><?php echo e($user->address ?? 'Not provided'); ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="profile-section">
                                <h5>Driver Information</h5>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">License Number</div>
                                    <div class="profile-detail-value"><?php echo e($user->license_number ?? 'Not provided'); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Vehicle Information</div>
                                    <div class="profile-detail-value"><?php echo e($user->vehicle_info ?? 'Not provided'); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Member Since</div>
                                    <div class="profile-detail-value"><?php echo e($user->created_at->format('M d, Y')); ?></div>
                                </div>
                                <div class="profile-detail">
                                    <div class="profile-detail-label">Last Updated</div>
                                    <div class="profile-detail-value"><?php echo e($user->updated_at->format('M d, Y')); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.driver', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\driver\profile\index.blade.php ENDPATH**/ ?>