<?php $__env->startSection('title', 'Payment'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .payment-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .payment-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .payment-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .payment-card .card-body {
        padding: 30px;
    }

    .booking-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .booking-detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .booking-detail-row:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .booking-detail-row span:first-child {
        font-weight: 600;
        color: #495057;
    }

    .payment-method-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .payment-method-card:hover {
        border-color: #ee393d;
        transform: translateY(-5px);
    }

    .payment-method-card.selected {
        border-color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
        transform: translateY(-5px);
    }

    .payment-method-card img {
        height: 40px;
        object-fit: contain;
    }

    .total-amount {
        font-size: 1.8rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 20px;
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #000;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e5b429;
        border-color: #e5b429;
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .alert {
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 20px;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        color: #856404;
    }

    /* Credit Card Form Styles */
    .card-payment-form {
        display: none;
        margin-top: 20px;
    }

    .card-payment-form.active {
        display: block;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        height: 50px;
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 10px 15px;
        font-size: 16px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.2rem rgba(238, 57, 61, 0.25);
    }

    .card-row {
        display: flex;
        gap: 15px;
    }

    .card-element {
        padding: 15px;
        border: 1px solid #ced4da;
        border-radius: 8px;
        background-color: white;
        height: 50px;
        transition: all 0.3s;
    }

    .card-element.StripeElement--focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.2rem rgba(238, 57, 61, 0.25);
    }

    .card-errors {
        color: #721c24;
        background-color: #f8d7da;
        padding: 10px;
        border-radius: 8px;
        margin-top: 10px;
        display: none;
    }

    .card-errors.visible {
        display: block;
    }

    .payment-success {
        color: #155724;
        background-color: #d4edda;
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
        text-align: center;
    }

    .payment-processing {
        display: none;
        text-align: center;
        margin-top: 20px;
    }

    .payment-processing.visible {
        display: block;
    }

    .spinner {
        display: inline-block;
        width: 30px;
        height: 30px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #ee393d;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="payment-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card payment-card">
                    <div class="card-header">
                        <h3 class="mb-0">Payment</h3>
                    </div>
                    <div class="card-body">
                        <?php if(session('error')): ?>
                            <div class="alert alert-danger">
                                <?php echo e(session('error')); ?>

                            </div>
                        <?php endif; ?>

                        <?php if(session('warning')): ?>
                            <div class="alert alert-warning">
                                <?php echo e(session('warning')); ?>

                            </div>
                        <?php endif; ?>

                        <?php if(session('success')): ?>
                            <div class="alert alert-success">
                                <?php echo e(session('success')); ?>

                            </div>
                        <?php endif; ?>

                        <div class="booking-details">
                            <h5 class="mb-3">Booking Details</h5>
                            <div class="booking-detail-row">
                                <span>Booking Number:</span>
                                <span><?php echo e($booking->booking_number); ?></span>
                            </div>
                            <div class="booking-detail-row">
                                <span>Vehicle:</span>
                                <span><?php echo e($booking->vehicle->name); ?></span>
                            </div>
                            <?php if($booking->booking_type === 'airport_transfer'): ?>
                                <div class="booking-detail-row">
                                    <span>Transfer Type:</span>
                                    <span><?php echo e(ucfirst(str_replace('_', ' ', $booking->airport_direction))); ?></span>
                                </div>
                                <?php if($booking->pickupAirport): ?>
                                    <div class="booking-detail-row">
                                        <span>From Airport:</span>
                                        <span><?php echo e($booking->pickupAirport->name); ?> (<?php echo e($booking->pickupAirport->code); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->dropoffAirport): ?>
                                    <div class="booking-detail-row">
                                        <span>To Airport:</span>
                                        <span><?php echo e($booking->dropoffAirport->name); ?> (<?php echo e($booking->dropoffAirport->code); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->airport_direction === 'to_airport'): ?>
                                    <div class="booking-detail-row">
                                        <span>Pickup:</span>
                                        <span><?php echo e($booking->pickup_address); ?></span>
                                    </div>
                                <?php else: ?>
                                    <div class="booking-detail-row">
                                        <span>Dropoff:</span>
                                        <span><?php echo e($booking->dropoff_address); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->airport_surcharge > 0): ?>
                                    <div class="booking-detail-row">
                                        <span>Airport Surcharge:</span>
                                        <span><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->airport_surcharge, 2)); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="booking-detail-row">
                                    <span>Pickup:</span>
                                    <span><?php echo e($booking->pickup_address); ?></span>
                                </div>
                                <?php if($booking->booking_type !== 'hourly'): ?>
                                    <div class="booking-detail-row">
                                        <span>Dropoff:</span>
                                        <span><?php echo e($booking->dropoff_address); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <div class="booking-detail-row">
                                <span>Date & Time:</span>
                                <span><?php echo e($booking->pickup_date->format('M d, Y h:i A')); ?></span>
                            </div>
                            <?php if($booking->booking_type === 'return' && $booking->return_date): ?>
                                <div class="booking-detail-row">
                                    <span>Return Date:</span>
                                    <span><?php echo e($booking->return_date->format('M d, Y h:i A')); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if($booking->booking_type === 'hourly' && $booking->duration_hours): ?>
                                <div class="booking-detail-row">
                                    <span>Duration:</span>
                                    <span><?php echo e($booking->duration_hours); ?> <?php echo e($booking->duration_hours > 1 ? 'hours' : 'hour'); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center mb-4">
                            <div class="total-amount">Total: <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->amount, 2)); ?></div>
                        </div>

                        <h5 class="mb-3">Select Payment Method</h5>

                        <div class="payment-method-card selected" data-payment-method="paypal">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <img src="https://www.paypalobjects.com/webstatic/mktg/logo/pp_cc_mark_37x23.jpg" alt="PayPal" class="img-fluid">
                                </div>
                                <div>
                                    <h5 class="mb-1">PayPal</h5>
                                    <p class="text-muted mb-0">Pay securely with your PayPal account</p>
                                </div>
                            </div>
                        </div>

                        <div class="payment-method-card" data-payment-method="cash">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">Cash Payment</h5>
                                    <p class="text-muted mb-0">Pay with cash to the driver</p>
                                </div>
                            </div>
                        </div>

                        <!-- PayPal Payment Form -->
                        <form id="paypal-form" action="<?php echo e(route('booking.process-payment', $booking->id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="payment_method" value="paypal">
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>Next: Proceed to PayPal
                                </button>
                                <a href="<?php echo e(route('booking.confirmation', $booking->id)); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Booking Details
                                </a>
                            </div>
                        </form>

                        <!-- Cash Payment Form -->
                        <form id="cash-form" style="display: none;" action="<?php echo e(route('booking.process-payment', $booking->id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="payment_method" value="cash">

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> By selecting cash payment, you agree to pay the driver in cash at the time of service.
                            </div>

                            <div class="form-group mb-4">
                                <label for="cash-notes" class="form-label">Special Instructions (Optional)</label>
                                <textarea id="cash-notes" name="notes" class="form-control" rows="3" placeholder="Any special instructions for the driver regarding payment..."></textarea>
                            </div>

                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check me-2"></i>Confirm Cash Payment
                                </button>
                                <a href="<?php echo e(route('booking.confirmation', $booking->id)); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Booking Details
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Function to load PayPal SDK with the correct currency
    function loadPayPalSDK() {
        const clientId = "<?php echo e(\App\Services\SettingsService::get('paypal_client_id')); ?>";
        const currency = "<?php echo e(\App\Services\SettingsService::getCurrencyCode()); ?>";

        // Remove any existing PayPal script
        const existingScript = document.querySelector('script[src*="paypal.com/sdk/js"]');
        if (existingScript) {
            existingScript.remove();
        }

        // Create and append the new script
        const script = document.createElement('script');
        script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=${currency}&components=buttons,card`;
        script.async = true;
        document.body.appendChild(script);

        return new Promise((resolve) => {
            script.onload = () => resolve();
        });
    }

    // Load PayPal SDK when the page loads
    loadPayPalSDK();
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment method selection
        const paymentMethodCards = document.querySelectorAll('.payment-method-card');
        const paypalForm = document.getElementById('paypal-form');
        const cashForm = document.getElementById('cash-form');

        paymentMethodCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                paymentMethodCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Show/hide appropriate form
                const paymentMethod = this.getAttribute('data-payment-method');
                if (paymentMethod === 'paypal') {
                    paypalForm.style.display = 'block';
                    cashForm.style.display = 'none';
                } else if (paymentMethod === 'cash') {
                    paypalForm.style.display = 'none';
                    cashForm.style.display = 'block';
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/booking/payment.blade.php ENDPATH**/ ?>