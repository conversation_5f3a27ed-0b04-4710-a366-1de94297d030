@extends('layouts.admin')

@section('title', 'User Details')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }

    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: calc(100vh - 76px);
        padding-top: 20px;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 10px 20px;
        margin-bottom: 5px;
        border-radius: 5px;
    }

    .sidebar .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
        color: #fff;
        background-color: #ee393d;
    }

    .sidebar .nav-link i {
        margin-right: 10px;
    }

    .user-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .user-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .user-card .card-body {
        padding: 30px;
    }

    .user-avatar {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 20px;
    }

    .user-info {
        margin-bottom: 30px;
    }

    .user-info-item {
        margin-bottom: 15px;
    }

    .user-info-label {
        font-weight: 600;
        color: #6c757d;
    }

    .user-info-value {
        font-weight: 500;
    }

    .role-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        margin-left: 10px;
    }

    .role-admin {
        background-color: #cff4fc;
        color: #055160;
    }

    .role-client {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .role-driver {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .status-active {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #842029;
    }

    .activity-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .activity-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .activity-card .card-body {
        padding: 0;
    }

    .activity-table {
        margin-bottom: 0;
    }

    .activity-table th,
    .activity-table td {
        padding: 15px 20px;
        vertical-align: middle;
    }

    .activity-table tbody tr {
        transition: background-color 0.3s;
    }

    .activity-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .stats-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
        padding: 20px;
        transition: transform 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-bottom: 15px;
        color: #ee393d;
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-label {
        color: #6c757d;
        font-weight: 500;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    Client Details
                    <span class="role-badge role-{{ $user->role }}">
                        {{ ucfirst($user->role) }}
                    </span>
                    <span class="status-badge status-{{ $user->is_active ? 'active' : 'inactive' }}">
                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </h2>
                <div>
                    <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i> Edit Client
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Clients
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card user-card">
                        <div class="card-body text-center">
                            @if ($user->profile_photo)
                                <img src="{{ asset('storage/' . $user->profile_photo) }}" class="user-avatar" alt="{{ $user->name }}">
                            @else
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=random&size=150" class="user-avatar" alt="{{ $user->name }}">
                            @endif

                            <h3 class="mb-1">{{ $user->name }}</h3>
                            <p class="text-muted">{{ $user->email }}</p>

                            <div class="d-flex justify-content-center mt-3">
                                <form action="{{ route('admin.users.toggle-active', $user->id) }}" method="POST" class="d-inline me-2">
                                    @csrf
                                    <button type="submit" class="btn btn-{{ $user->is_active ? 'warning' : 'success' }}">
                                        <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }} me-2"></i>
                                        {{ $user->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>

                                <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST" class="d-inline delete-user-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash me-2"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="card user-card">
                        <div class="card-header">
                            <h4 class="mb-0">User Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="user-info">
                                <div class="user-info-item">
                                    <div class="user-info-label">Member Since</div>
                                    <div class="user-info-value">{{ $user->created_at->format('F d, Y') }}</div>
                                </div>

                                <div class="user-info-item">
                                    <div class="user-info-label">Phone</div>
                                    <div class="user-info-value">{{ $user->phone ?? 'Not provided' }}</div>
                                </div>

                                <div class="user-info-item">
                                    <div class="user-info-label">Address</div>
                                    <div class="user-info-value">{{ $user->address ?? 'Not provided' }}</div>
                                </div>

                                @if ($user->role === 'driver')
                                    <div class="user-info-item">
                                        <div class="user-info-label">License Number</div>
                                        <div class="user-info-value">{{ $user->license_number ?? 'Not provided' }}</div>
                                    </div>

                                    <div class="user-info-item">
                                        <div class="user-info-label">Vehicle Information</div>
                                        <div class="user-info-value">{{ $user->vehicle_info ?? 'Not provided' }}</div>
                                    </div>

                                    <div class="user-info-item">
                                        <div class="user-info-label">Availability</div>
                                        <div class="user-info-value">
                                            <span class="status-badge status-{{ $user->is_available ? 'active' : 'inactive' }}">
                                                {{ $user->is_available ? 'Available' : 'Unavailable' }}
                                            </span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    @if ($user->role === 'client')
                        <!-- Client Stats -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-car"></i>
                                    </div>
                                    <div class="stats-value">{{ $user->bookings()->count() }}</div>
                                    <div class="stats-label">Total Bookings</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stats-value">{{ $user->bookings()->where('status', 'completed')->count() }}</div>
                                    <div class="stats-label">Completed Rides</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="stats-value">{{ $user->bookings()->sum('amount') }}</div>
                                    <div class="stats-label">Total Spent</div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Bookings -->
                        <div class="card activity-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">Recent Bookings</h4>
                                <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                @if (isset($data['bookings']) && $data['bookings']->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table activity-table">
                                            <thead>
                                                <tr>
                                                    <th>Booking #</th>
                                                    <th>Date</th>
                                                    <th>From</th>
                                                    <th>To</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($data['bookings'] as $booking)
                                                    <tr>
                                                        <td>{{ $booking->booking_number }}</td>
                                                        <td>{{ $booking->pickup_date->format('M d, Y') }}</td>
                                                        <td>{{ Str::limit($booking->pickup_address, 20) }}</td>
                                                        <td>{{ Str::limit($booking->dropoff_address, 20) }}</td>
                                                        <td>
                                                            <span class="badge bg-{{ $booking->status === 'completed' ? 'success' : ($booking->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                                {{ ucfirst($booking->status) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ $booking->amount }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-5">
                                        <p class="text-muted mb-0">No bookings found for this client.</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @elseif ($user->role === 'driver')
                        <!-- Driver Stats -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-car"></i>
                                    </div>
                                    <div class="stats-value">{{ $user->driverRides()->count() }}</div>
                                    <div class="stats-label">Total Rides</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stats-value">{{ $user->driverRides()->where('status', 'completed')->count() }}</div>
                                    <div class="stats-label">Completed Rides</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="stats-value">{{ isset($data['earnings']) ? $data['earnings'] : 0 }}</div>
                                    <div class="stats-label">Total Earnings</div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Rides -->
                        <div class="card activity-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">Recent Rides</h4>
                                <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                @if (isset($data['rides']) && $data['rides']->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table activity-table">
                                            <thead>
                                                <tr>
                                                    <th>Booking #</th>
                                                    <th>Date</th>
                                                    <th>Client</th>
                                                    <th>From</th>
                                                    <th>To</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($data['rides'] as $ride)
                                                    <tr>
                                                        <td>{{ $ride->booking_number }}</td>
                                                        <td>{{ $ride->pickup_date->format('M d, Y') }}</td>
                                                        <td>{{ $ride->user->name }}</td>
                                                        <td>{{ Str::limit($ride->pickup_address, 20) }}</td>
                                                        <td>{{ Str::limit($ride->dropoff_address, 20) }}</td>
                                                        <td>
                                                            <span class="badge bg-{{ $ride->status === 'completed' ? 'success' : ($ride->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                                {{ ucfirst($ride->status) }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-5">
                                        <p class="text-muted mb-0">No rides found for this driver.</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Confirm delete
        $('.delete-user-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);

            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the client. This action cannot be undone!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.off('submit').submit();
                }
            });
        });
    });
</script>
@endsection