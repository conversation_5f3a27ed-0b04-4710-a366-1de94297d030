<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Airport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AirportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Airport::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%");
            });
        }

        // Country filter
        if ($request->has('country') && $request->country) {
            $query->where('country_code', $request->country);
        }

        $airports = $query->orderBy('name')->paginate(15);
        $countries = Airport::select('country', 'country_code')
            ->distinct()
            ->orderBy('country')
            ->get();

        return view('admin.airports.index', compact('airports', 'countries'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.airports.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airports,code',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'country_code' => 'required|string|size:2',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
        ]);

        Airport::create($request->all());

        return redirect()->route('admin.airports.index')
            ->with('success', 'Airport created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Airport $airport)
    {
        return view('admin.airports.show', compact('airport'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Airport $airport)
    {
        return view('admin.airports.edit', compact('airport'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Airport $airport)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airports,code,' . $airport->id,
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'country_code' => 'required|string|size:2',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
        ]);

        $airport->update($request->all());

        return redirect()->route('admin.airports.index')
            ->with('success', 'Airport updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Airport $airport)
    {
        $airport->delete();

        return redirect()->route('admin.airports.index')
            ->with('success', 'Airport deleted successfully.');
    }
}
