<?php $__env->startSection('title', 'Booking History'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .booking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .booking-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .booking-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .booking-card .card-body {
        padding: 30px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #ee393d;
        background-color: rgba(248, 193, 44, 0.1);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto 15px;
    }

    .stat-title {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #343a40;
        margin-bottom: 0;
    }

    .filter-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .filter-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #343a40;
    }

    .booking-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-confirmed {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .status-in_progress {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .welcome-banner {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        color: white;
        padding: 30px;
        margin-bottom: 30px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(248, 193, 44, 0.05);
    }

    .table th {
        font-weight: 600;
        color: #343a40;
        border-top: none;
        border-bottom: 2px solid #ee393d;
    }

    .empty-state {
        text-align: center;
        padding: 50px 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 20px;
    }

    .empty-state h4 {
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        margin-right: 5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
    }

    .action-btn-view {
        background-color: rgba(13, 110, 253, 0.1);
        color: #ee393d;
    }

    .action-btn-view:hover {
        background-color: #ee393d;
        color: #fff;
    }

    .action-btn-review {
        background-color: rgba(248, 193, 44, 0.1);
        color: #ee393d;
    }

    .action-btn-review:hover {
        background-color: #ee393d;
        color: #fff;
    }

    .booking-item {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
        overflow: hidden;
    }

    .booking-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .booking-header {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .booking-body {
        padding: 20px;
    }

    .booking-footer {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .booking-number {
        font-weight: 600;
        color: #343a40;
    }

    .booking-date {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .booking-vehicle {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .booking-vehicle-img {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 5px;
        margin-right: 15px;
    }

    .booking-vehicle-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .booking-vehicle-type {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .booking-details {
        display: flex;
        margin-bottom: 15px;
    }

    .booking-detail {
        flex: 1;
        text-align: center;
        padding: 10px;
    }

    .booking-detail-label {
        color: #6c757d;
        font-size: 0.8rem;
        margin-bottom: 5px;
    }

    .booking-detail-value {
        font-weight: 600;
    }

    .booking-locations {
        margin-bottom: 15px;
    }

    .booking-location {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .booking-location-icon {
        width: 30px;
        height: 30px;
        background-color: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .booking-location-text {
        flex-grow: 1;
    }

    .booking-location-type {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .booking-location-address {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="welcome-banner" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">Booking History</h2>
            <p class="mb-0">View and manage all your past bookings</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?php echo e(route('client.bookings.index')); ?>" class="btn btn-light">
                <i class="fas fa-arrow-left me-2"></i> Back to Bookings
            </a>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Booking Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-title">Total Bookings</div>
            <div class="stat-value"><?php echo e($stats['total'] ?? 0); ?></div>
        </div>
    </div>
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="200">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-title">Completed</div>
            <div class="stat-value"><?php echo e($stats['completed'] ?? 0); ?></div>
        </div>
    </div>
    <div class="col-md-3 mb-4 mb-md-0" data-aos="fade-up" data-aos-delay="300">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-ban"></i>
            </div>
            <div class="stat-title">Cancelled</div>
            <div class="stat-value"><?php echo e($stats['cancelled'] ?? 0); ?></div>
        </div>
    </div>
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-title">Total Spent</div>
            <div class="stat-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($stats['total_amount'] ?? 0, 2)); ?></div>
        </div>
    </div>
</div>

<!-- Filter Options -->
<div class="filter-card" data-aos="fade-up" data-aos-delay="500">
    <div class="filter-title">
        <i class="fas fa-filter me-2"></i> Filter Options
    </div>
    <form action="<?php echo e(route('client.bookings.history')); ?>" method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="date_from" class="form-label">From Date</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
        </div>
        <div class="col-md-3">
            <label for="date_to" class="form-label">To Date</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
        </div>
        <div class="col-md-3">
            <label for="vehicle_id" class="form-label">Vehicle</label>
            <select class="form-select" id="vehicle_id" name="vehicle_id">
                <option value="">All Vehicles</option>
                <?php $__currentLoopData = $vehicles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($vehicle->id); ?>" <?php echo e(request('vehicle_id') == $vehicle->id ? 'selected' : ''); ?>><?php echo e($vehicle->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="col-md-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="">All Statuses</option>
                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                <option value="confirmed" <?php echo e(request('status') == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
            </select>
        </div>
        <div class="col-12 d-flex justify-content-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i> Apply Filters
            </button>
            <a href="<?php echo e(route('client.bookings.history')); ?>" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i> Reset
            </a>
        </div>
    </form>
</div>

<!-- Booking History -->
<div class="row" data-aos="fade-up" data-aos-delay="600">
    <?php if($bookings->isEmpty()): ?>
        <div class="col-12">
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h4>No booking records found</h4>
                <p>You haven't made any bookings yet or no bookings match your filter criteria.</p>
                <a href="<?php echo e(route('booking.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-car me-1"></i> Book a Ride
                </a>
            </div>
        </div>
    <?php else: ?>
        <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100 + 700); ?>">
                <div class="booking-item">
                    <div class="booking-header">
                        <div>
                            <div class="booking-number">Booking #<?php echo e($booking->booking_number); ?></div>
                            <div class="booking-date"><?php echo e($booking->created_at->format('M d, Y')); ?></div>
                        </div>
                        <div>
                            <span class="booking-status status-<?php echo e(strtolower($booking->status)); ?>">
                                <?php echo e(ucfirst($booking->status)); ?>

                            </span>
                        </div>
                    </div>
                    <div class="booking-body">
                        <div class="booking-vehicle">
                            <?php if($booking->vehicle->image): ?>
                                <img src="<?php echo e(asset('storage/' . $booking->vehicle->image)); ?>" class="booking-vehicle-img" alt="<?php echo e($booking->vehicle->name); ?>">
                            <?php else: ?>
                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center booking-vehicle-img">
                                    <i class="fas fa-car fa-2x"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <div class="booking-vehicle-name"><?php echo e($booking->vehicle->name); ?></div>
                                <div class="booking-vehicle-type"><?php echo e($booking->vehicle->type); ?></div>
                            </div>
                        </div>

                        <div class="booking-details">
                            <div class="booking-detail">
                                <div class="booking-detail-label">Date</div>
                                <div class="booking-detail-value"><?php echo e($booking->pickup_date->format('M d, Y')); ?></div>
                            </div>
                            <div class="booking-detail">
                                <div class="booking-detail-label">Time</div>
                                <div class="booking-detail-value"><?php echo e($booking->pickup_time); ?></div>
                            </div>
                            <div class="booking-detail">
                                <div class="booking-detail-label">Amount</div>
                                <div class="booking-detail-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->amount, 2)); ?></div>
                            </div>
                        </div>

                        <div class="booking-locations">
                            <div class="booking-location">
                                <div class="booking-location-icon">
                                    <i class="fas fa-map-marker-alt text-success"></i>
                                </div>
                                <div class="booking-location-text">
                                    <div class="booking-location-type">Pickup</div>
                                    <div class="booking-location-address"><?php echo e($booking->pickup_address); ?></div>
                                </div>
                            </div>
                            <div class="booking-location">
                                <div class="booking-location-icon">
                                    <i class="fas fa-map-marker-alt text-danger"></i>
                                </div>
                                <div class="booking-location-text">
                                    <div class="booking-location-type">Dropoff</div>
                                    <div class="booking-location-address"><?php echo e($booking->dropoff_address); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="booking-footer">
                        <div>
                            <?php if($booking->status === 'completed' && !$booking->review): ?>
                                <a href="<?php echo e(route('client.bookings.review', $booking->id)); ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-star me-1"></i> Leave Review
                                </a>
                            <?php endif; ?>
                        </div>
                        <div>
                            <a href="<?php echo e(route('client.bookings.show', $booking->id)); ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye me-1"></i> View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <div class="col-12">
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($bookings->links()); ?>

            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\client\bookings\history.blade.php ENDPATH**/ ?>