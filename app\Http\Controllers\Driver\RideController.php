<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Notifications\BookingStatusChanged;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RideController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Display a listing of available rides.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get available rides (confirmed bookings without a driver)
        $availableRides = Booking::where('status', 'confirmed')
            ->whereNull('driver_id')
            ->with(['vehicle', 'pickupAirport', 'dropoffAirport'])
            ->orderBy('pickup_date', 'asc')
            ->get();

        return view('driver.rides.index', compact('availableRides'));
    }

    /**
     * Display a listing of the driver's assigned rides.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function myRides(Request $request)
    {
        $driverId = Auth::id();

        // Get status filter
        $status = $request->input('status', 'all');

        // Build query for rides assigned to this driver
        $query = Booking::where('driver_id', $driverId);

        // Apply status filter if provided
        if ($status === 'upcoming') {
            $query->whereIn('status', ['assigned', 'in_progress']);
        } elseif ($status === 'completed') {
            $query->where('status', 'completed');
        } elseif ($status === 'cancelled') {
            $query->where('status', 'cancelled');
        }

        // Get rides with eager loading
        $myRides = $query->with(['vehicle', 'user', 'payment'])
            ->orderBy('pickup_date', 'desc')
            ->get();

        return view('driver.rides.my-rides', compact('myRides', 'status'));
    }

    /**
     * Display the specified ride.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $ride = Booking::with(['vehicle', 'user', 'payment'])
            ->findOrFail($id);

        // Check if the ride is available for acceptance or assigned to this driver
        $driverId = Auth::id();

        // Allow viewing if:
        // 1. The ride is confirmed and has no driver (available for acceptance)
        // 2. The ride is assigned to this driver (regardless of status)
        // 3. The ride was previously assigned to this driver (completed or cancelled)
        if (!(($ride->status === 'confirmed' && !$ride->driver_id) ||
              ($ride->driver_id === $driverId) ||
              ($ride->status === 'completed' && $ride->driver_id === $driverId))) {

            // Log the issue for debugging
            \Log::debug('Ride authorization failed', [
                'ride_id' => $ride->id,
                'ride_status' => $ride->status,
                'ride_driver_id' => $ride->driver_id,
                'current_driver_id' => $driverId
            ]);

            return redirect()->route('driver.rides.index')
                ->with('error', 'You are not authorized to view this ride.');
        }

        return view('driver.rides.show', compact('ride'));
    }

    /**
     * Accept a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function accept(Request $request, $id)
    {
        $ride = Booking::where('status', 'confirmed')
            ->whereNull('driver_id')
            ->findOrFail($id);

        $oldStatus = $ride->status;
        $ride->driver_id = Auth::id();
        $ride->status = 'assigned';
        $ride->save();

        // Add booking history
        $ride->addHistory('driver_assigned', [
            'driver_id' => Auth::id(),
            'driver_name' => Auth::user()->name,
            'old_status' => $oldStatus,
            'new_status' => 'assigned'
        ]);

        // Notify the client that a driver has been assigned
        $ride->user->notify(new BookingStatusChanged($ride, $oldStatus));

        return redirect()->route('driver.rides.my-rides')
            ->with('success', 'Ride accepted successfully.');
    }

    /**
     * Start a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function start(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'assigned')
            ->findOrFail($id);

        $oldStatus = $ride->status;
        $ride->status = 'in_progress';
        $ride->started_at = now();
        $ride->save();

        // Add booking history
        $ride->addHistory('status_changed', [
            'old_status' => $oldStatus,
            'new_status' => 'in_progress',
            'reason' => 'Ride started by driver',
            'started_at' => $ride->started_at
        ]);

        // Notify the client that the ride has started
        $ride->user->notify(new BookingStatusChanged($ride, $oldStatus));

        return redirect()->route('driver.rides.my-rides')
            ->with('success', 'Ride started successfully.');
    }

    /**
     * Complete a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function complete(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'in_progress')
            ->findOrFail($id);

        $oldStatus = $ride->status;
        $ride->status = 'completed';
        $ride->completed_at = now();
        $ride->save();

        // Calculate duration if started_at is set
        $duration = null;
        if ($ride->started_at) {
            $duration = $ride->started_at->diffInMinutes($ride->completed_at);
        }

        // Add booking history
        $ride->addHistory('status_changed', [
            'old_status' => $oldStatus,
            'new_status' => 'completed',
            'reason' => 'Ride completed by driver',
            'completed_at' => $ride->completed_at,
            'duration_minutes' => $duration
        ]);

        // Notify the client that the ride has been completed
        $ride->user->notify(new BookingStatusChanged($ride, $oldStatus));

        return redirect()->route('driver.rides.summary', $ride->id)
            ->with('success', 'Ride completed successfully.');
    }

    /**
     * Cancel a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->whereIn('status', ['assigned', 'in_progress'])
            ->findOrFail($id);

        $request->validate([
            'cancel_reason' => 'required|string|max:255', // Keep the form field name as is for backward compatibility
        ]);

        $oldStatus = $ride->status;
        $oldDriverId = $ride->driver_id;
        $ride->driver_id = null;
        $ride->status = 'confirmed'; // Reset to confirmed so another driver can accept
        $ride->cancellation_reason = $request->cancel_reason;
        $ride->cancelled_at = now();
        $ride->cancelled_by = 'driver';
        $ride->save();

        // Add booking history
        $ride->addHistory('driver_cancelled', [
            'driver_id' => $oldDriverId,
            'driver_name' => Auth::user()->name,
            'old_status' => $oldStatus,
            'new_status' => 'confirmed',
            'reason' => $request->cancel_reason
        ]);

        // Notify the client that the ride has been cancelled
        $ride->user->notify(new BookingStatusChanged($ride, $oldStatus));

        return redirect()->route('driver.rides.index')
            ->with('success', 'Ride cancelled successfully.');
    }

    /**
     * Display the driver's ride history.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function history()
    {
        $driverId = Auth::id();

        // Get completed rides for this driver with pagination
        $completedRides = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->with(['vehicle', 'user'])
            ->orderBy('completed_at', 'desc')
            ->paginate(10);

        // Log for debugging
        \Log::debug('Ride history', [
            'driver_id' => $driverId,
            'count' => $completedRides->count(),
            'total' => $completedRides->total()
        ]);

        return view('driver.rides.history', compact('completedRides'));
    }

    /**
     * Add a note to a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addNote(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->findOrFail($id);

        $request->validate([
            'note' => 'required|string|max:500',
        ]);

        // Add note to booking
        $ride->notes = $request->note;
        $ride->save();

        // Add to booking history
        $ride->addHistory('note_added', [
            'note' => $request->note,
        ]);

        return redirect()->back()
            ->with('success', 'Note added successfully.');
    }

    /**
     * Update the driver's location during a ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLocation(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'in_progress')
            ->findOrFail($id);

        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        // Store the location update in a separate table or as JSON in the booking
        // This is a simplified example - in a real app, you might want to store
        // location history in a separate table
        $locationUpdates = json_decode($ride->location_updates ?? '[]', true);
        $locationUpdates[] = [
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'timestamp' => now()->toIso8601String(),
        ];
        $ride->location_updates = json_encode($locationUpdates);
        $ride->save();

        return response()->json(['success' => true]);
    }

    /**
     * Display the ride tracking page.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function track($id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'in_progress')
            ->with(['vehicle', 'user'])
            ->findOrFail($id);

        return view('driver.rides.track', compact('ride'));
    }

    /**
     * Display the ride completion summary.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function summary($id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'completed')
            ->with(['vehicle', 'user', 'payment', 'history'])
            ->findOrFail($id);

        // Calculate ride duration
        $duration = null;
        if ($ride->started_at && $ride->completed_at) {
            $duration = $ride->started_at->diffInMinutes($ride->completed_at);
        } elseif ($ride->duration_value) {
            // Use the stored duration_value if available (in seconds, convert to minutes)
            $duration = ceil($ride->duration_value / 60);
        }

        // If distance is 0 or null, try to calculate it from Google Maps data
        if (!$ride->distance || $ride->distance <= 0) {
            if ($ride->distance_value) {
                // Convert distance_value from meters to kilometers
                $ride->distance = round($ride->distance_value / 1000, 2);
            } elseif ($ride->pickup_lat && $ride->pickup_lng && $ride->dropoff_lat && $ride->dropoff_lng) {
                // Calculate distance using coordinates
                $ride->distance = $this->calculateDistance(
                    $ride->pickup_lat,
                    $ride->pickup_lng,
                    $ride->dropoff_lat,
                    $ride->dropoff_lng
                );
            }
        }

        return view('driver.rides.summary', compact('ride', 'duration'));
    }

    /**
     * Calculate distance between two coordinates using Haversine formula
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float Distance in kilometers
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // Radius of the earth in km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c; // Distance in km

        return round($distance, 2);
    }

    /**
     * Download a PDF receipt for the completed ride.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function downloadReceipt($id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->where('status', 'completed')
            ->with(['vehicle', 'user', 'payment', 'history'])
            ->findOrFail($id);

        // Calculate ride duration
        $duration = null;
        if ($ride->started_at && $ride->completed_at) {
            $duration = $ride->started_at->diffInMinutes($ride->completed_at);
        } elseif ($ride->duration_value) {
            // Use the stored duration_value if available (in seconds, convert to minutes)
            $duration = ceil($ride->duration_value / 60);
        }

        // If distance is 0 or null, try to calculate it from Google Maps data
        if (!$ride->distance || $ride->distance <= 0) {
            if ($ride->distance_value) {
                // Convert distance_value from meters to kilometers
                $ride->distance = round($ride->distance_value / 1000, 2);
            } elseif ($ride->pickup_lat && $ride->pickup_lng && $ride->dropoff_lat && $ride->dropoff_lng) {
                // Calculate distance using coordinates
                $ride->distance = $this->calculateDistance(
                    $ride->pickup_lat,
                    $ride->pickup_lng,
                    $ride->dropoff_lat,
                    $ride->dropoff_lng
                );
            }
        }

        // Generate PDF receipt
        $pdf = \PDF::loadView('driver.rides.receipt', compact('ride', 'duration'));

        return $pdf->download('ride-receipt-' . $ride->booking_number . '.pdf');
    }

    /**
     * Report an issue with a completed ride.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reportIssue(Request $request, $id)
    {
        $ride = Booking::where('driver_id', Auth::id())
            ->findOrFail($id);

        $request->validate([
            'issue_type' => 'required|string|in:payment,fare,customer,route,other',
            'issue_description' => 'required|string|max:1000',
            'urgent' => 'sometimes|boolean',
        ]);

        // Create a support ticket
        $ticket = new \App\Models\SupportTicket();
        $ticket->user_id = Auth::id();
        $ticket->booking_id = $ride->id;
        $ticket->subject = 'Ride Issue: ' . ucfirst($request->issue_type) . ' - Booking #' . $ride->booking_number;
        $ticket->message = $request->issue_description;
        $ticket->status = 'open';
        $ticket->priority = $request->has('urgent') ? 'high' : 'medium';
        $ticket->type = 'driver_issue';
        $ticket->save();

        // Add to ride history
        $ride->history()->create([
            'user_id' => Auth::id(),
            'action' => 'issue_reported',
            'details' => json_encode([
                'issue_type' => $request->issue_type,
                'description' => $request->issue_description,
                'ticket_id' => $ticket->id
            ]),
            'status_before' => $ride->status,
            'status_after' => $ride->status
        ]);

        // Notify admin
        try {
            // Send notification to admin
            $admins = \App\Models\User::where('role', 'admin')->get();
            \Notification::send($admins, new \App\Notifications\NewSupportTicket($ticket));
        } catch (\Exception $e) {
            \Log::error('Failed to send admin notification: ' . $e->getMessage());
        }

        return redirect()->route('driver.rides.summary', $ride->id)
            ->with('success', 'Your issue has been reported. Our support team will review it shortly.');
    }
}
