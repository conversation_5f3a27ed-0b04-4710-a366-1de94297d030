{{-- Unified Autocomplete Meta Tags Component --}}
@php
    $metaTags = \App\Services\SettingsService::getAutocompleteMetaTags();
@endphp

<!-- Autocomplete Settings Meta Tags -->
@foreach($metaTags as $name => $content)
    <meta name="{{ $name }}" content="{{ $content }}">
@endforeach

<!-- Google Maps API Configuration -->
@php
    $googleMapsApiKey = \App\Services\SettingsService::get('google_maps_api_key', config('services.google_maps.key', ''));
    $jsConfig = \App\Services\SettingsService::getAutocompleteJavaScriptConfig();
@endphp

@if($googleMapsApiKey)
    <script>
        // Global autocomplete configuration
        window.autocompleteSettings = @json($jsConfig);
        window.googleMapsApiKey = '{{ $googleMapsApiKey }}';
        
        // Initialize Google Maps API callback
        function initGoogleMapsApi() {
            window.dispatchEvent(new Event('google-maps-loaded'));
        }
    </script>
    
    <!-- Load Google Maps API with Places library -->
    <script src="https://maps.googleapis.com/maps/api/js?key={{ $googleMapsApiKey }}&libraries=places&callback=initGoogleMapsApi" async defer></script>
    
    <!-- Load Unified Autocomplete Handler -->
    <script src="{{ asset('js/unified-autocomplete.js') }}" defer></script>
@else
    <script>
        console.error('Google Maps API key is not configured. Please set it in the admin settings.');
        
        // Show user-friendly message
        document.addEventListener('DOMContentLoaded', function() {
            const addressInputs = document.querySelectorAll('input[id*="address"], .address-autocomplete');
            addressInputs.forEach(input => {
                input.placeholder = 'Address autocomplete unavailable - API key not configured';
                input.style.borderColor = '#ffc107';
                input.style.backgroundColor = '#fff3cd';
            });
        });
    </script>
@endif
