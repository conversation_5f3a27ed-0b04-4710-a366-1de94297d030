<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\BlogPost;
use App\Helpers\SettingsHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $vehicles = Vehicle::where('is_active', true)->take(6)->get();

        return view('welcome', compact('vehicles'));
    }

    /**
     * Display the about page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function about()
    {
        $aboutContent = SettingsHelper::getAboutUs();
        return view('about', compact('aboutContent'));
    }

    /**
     * Display the services page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function services()
    {
        return view('services');
    }

    /**
     * Display the contact page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Process the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // In a real application, you would send an email here
        // For now, we'll just redirect with a success message

        return redirect()->back()->with('success', 'Your message has been sent successfully. We will get back to you soon.');
    }

    /**
     * Display the privacy policy page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function privacyPolicy()
    {
        $privacyContent = SettingsHelper::getPrivacyPolicy();
        return view('privacy-policy', compact('privacyContent'));
    }

    /**
     * Display the terms and conditions page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function termsAndConditions()
    {
        $termsContent = SettingsHelper::getTermsAndConditions();
        return view('terms-and-conditions', compact('termsContent'));
    }

    /**
     * Display the fleet page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function fleet()
    {
        $vehicles = Vehicle::where('is_active', true)->get();
        return view('fleet', compact('vehicles'));
    }



    /**
     * Display the FAQ page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function faq()
    {
        return view('faq');
    }

    /**
     * Process the corporate inquiry form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitCorporateInquiry(Request $request)
    {
        $request->validate([
            'company' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'message' => 'required|string',
        ]);

        // In a real application, you would:
        // 1. Save the inquiry to the database
        // 2. Send an email notification to the corporate team
        // 3. Send a confirmation email to the client

        // For now, we'll just redirect with a success message
        return redirect()->back()->with('success', 'Thank you for your corporate inquiry! Our team will contact you within 24 hours to discuss your transportation needs.');
    }
}
