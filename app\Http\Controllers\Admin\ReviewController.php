<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReviewController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the reviews.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $rating = $request->input('rating');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $search = $request->input('search');

        // Build query
        $query = Booking::whereNotNull('rating')
            ->whereNotNull('review')
            ->with(['user', 'vehicle', 'driver']);

        // Apply filters
        if ($rating) {
            $query->where('rating', $rating);
        }

        if ($dateFrom) {
            $query->whereDate('reviewed_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('reviewed_at', '<=', $dateTo);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('review', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('vehicle', function ($vehicleQuery) use ($search) {
                      $vehicleQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get reviews
        $reviews = $query->orderBy('reviewed_at', 'desc')->paginate(10);

        // Get review statistics
        $reviewStats = [
            'total' => Booking::whereNotNull('rating')->count(),
            'average_rating' => Booking::whereNotNull('rating')->avg('rating'),
            'rating_distribution' => $this->getRatingDistribution(),
        ];

        return view('admin.reviews.index', compact('reviews', 'reviewStats', 'rating', 'dateFrom', 'dateTo', 'search'));
    }

    /**
     * Display the specified review.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $booking = Booking::with(['user', 'vehicle', 'driver'])
            ->whereNotNull('rating')
            ->whereNotNull('review')
            ->findOrFail($id);

        return view('admin.reviews.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified review.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit($id)
    {
        $booking = Booking::with(['user', 'vehicle', 'driver'])
            ->whereNotNull('rating')
            ->whereNotNull('review')
            ->findOrFail($id);

        return view('admin.reviews.edit', compact('booking'));
    }

    /**
     * Update the specified review in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'required|string|max:500',
        ]);

        $booking = Booking::findOrFail($id);
        $booking->rating = $request->rating;
        $booking->review = $request->review;
        $booking->save();

        return redirect()->route('admin.reviews.show', $booking->id)
            ->with('success', 'Review updated successfully.');
    }

    /**
     * Remove the specified review from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $booking = Booking::findOrFail($id);
        $booking->rating = null;
        $booking->review = null;
        $booking->reviewed_at = null;
        $booking->save();

        return redirect()->route('admin.reviews.index')
            ->with('success', 'Review deleted successfully.');
    }

    /**
     * Get the rating distribution.
     *
     * @return array
     */
    private function getRatingDistribution()
    {
        $distribution = DB::table('bookings')
            ->select(DB::raw('rating, COUNT(*) as count'))
            ->whereNotNull('rating')
            ->groupBy('rating')
            ->orderBy('rating')
            ->get()
            ->pluck('count', 'rating')
            ->toArray();

        // Ensure all ratings 1-5 are represented
        $result = [];
        for ($i = 1; $i <= 5; $i++) {
            $result[$i] = $distribution[$i] ?? 0;
        }

        return $result;
    }
}
