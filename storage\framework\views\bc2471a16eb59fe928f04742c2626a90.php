<?php $__env->startSection('title', 'New Contact Form Submission'); ?>

<?php $__env->startSection('content'); ?>
<h2>New Contact Form Submission</h2>

<p>You have received a new contact form submission from your website.</p>

<div class="booking-details">
    <h3>Contact Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Name:</span>
        <span class="detail-value"><?php echo e($contactData['name'] ?? 'Not provided'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Email:</span>
        <span class="detail-value"><?php echo e($contactData['email'] ?? 'Not provided'); ?></span>
    </div>
    
    <?php if(isset($contactData['phone'])): ?>
    <div class="detail-row">
        <span class="detail-label">Phone:</span>
        <span class="detail-value"><?php echo e($contactData['phone']); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(isset($contactData['subject'])): ?>
    <div class="detail-row">
        <span class="detail-label">Subject:</span>
        <span class="detail-value"><?php echo e($contactData['subject']); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Submitted:</span>
        <span class="detail-value"><?php echo e(now()->format('F j, Y \a\t g:i A')); ?></span>
    </div>
</div>

<?php if(isset($contactData['message'])): ?>
<div style="margin: 20px 0;">
    <h3>Message:</h3>
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #ee393d;">
        <?php echo nl2br(e($contactData['message'])); ?>

    </div>
</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="mailto:<?php echo e($contactData['email'] ?? ''); ?>" class="btn">Reply to Customer</a>
</div>

<p><strong>Note:</strong> Please respond to this inquiry promptly to maintain excellent customer service.</p>

<p>Best regards,<br>
<?php echo e($companyName); ?> Website</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\contact\submission.blade.php ENDPATH**/ ?>