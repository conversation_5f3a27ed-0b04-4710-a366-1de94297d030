<?php

namespace Database\Seeders;

use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class BookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get client users
        $clients = User::where('role', 'client')->get();

        // Get drivers
        $drivers = User::where('role', 'driver')->get();

        // Get vehicles
        $vehicles = Vehicle::where('is_active', true)->get();

        // Sample pickup and dropoff locations
        $locations = [
            [
                'pickup' => '123 Main St, New York, NY 10001',
                'pickup_lat' => 40.7505,
                'pickup_lng' => -73.9934,
                'dropoff' => '456 Park Ave, New York, NY 10022',
                'dropoff_lat' => 40.7599,
                'dropoff_lng' => -73.9709,
                'distance' => 3.5,
            ],
            [
                'pickup' => 'Grand Central Terminal, New York, NY 10017',
                'pickup_lat' => 40.7527,
                'pickup_lng' => -73.9772,
                'dropoff' => 'Empire State Building, New York, NY 10001',
                'dropoff_lat' => 40.7484,
                'dropoff_lng' => -73.9857,
                'distance' => 1.8,
            ],
            [
                'pickup' => 'Times Square, New York, NY 10036',
                'pickup_lat' => 40.7580,
                'pickup_lng' => -73.9855,
                'dropoff' => 'Central Park, New York, NY 10024',
                'dropoff_lat' => 40.7812,
                'dropoff_lng' => -73.9665,
                'distance' => 2.7,
            ],
            [
                'pickup' => 'JFK Airport, Queens, NY 11430',
                'pickup_lat' => 40.6413,
                'pickup_lng' => -73.7781,
                'dropoff' => 'Manhattan, New York, NY 10001',
                'dropoff_lat' => 40.7505,
                'dropoff_lng' => -73.9934,
                'distance' => 21.5,
            ],
            [
                'pickup' => 'Brooklyn Bridge, New York, NY 10038',
                'pickup_lat' => 40.7061,
                'pickup_lng' => -73.9969,
                'dropoff' => 'Statue of Liberty, New York, NY 10004',
                'dropoff_lat' => 40.6892,
                'dropoff_lng' => -74.0445,
                'distance' => 5.2,
            ],
        ];

        // Booking statuses
        $statuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'];

        // Booking types
        $bookingTypes = ['one_way', 'return', 'hourly'];

        // Create 20 sample bookings
        for ($i = 0; $i < 20; $i++) {
            // Select random client, vehicle, and location
            $client = $clients->random();
            $vehicle = $vehicles->random();
            $location = $locations[array_rand($locations)];

            // Determine booking type
            $bookingType = $bookingTypes[array_rand($bookingTypes)];

            // Set pickup date (random time in the next 30 days)
            $pickupDate = Carbon::now()->addDays(rand(1, 30))->addHours(rand(1, 23));

            // Set return date if booking type is return
            $returnDate = null;
            if ($bookingType === 'return') {
                $returnDate = (clone $pickupDate)->addDays(rand(1, 5));
            }

            // Set duration hours if booking type is hourly
            $durationHours = null;
            if ($bookingType === 'hourly') {
                $durationHours = rand(1, 8);
            }

            // Calculate amount based on vehicle price and distance
            $amount = 0;
            if ($bookingType === 'one_way') {
                $amount = 5 + ($location['distance'] * $vehicle->price_per_km);
            } elseif ($bookingType === 'return') {
                $amount = 5 + ($location['distance'] * $vehicle->price_per_km * 2);
            } else { // hourly
                $amount = $vehicle->price_per_hour * $durationHours;
            }

            // Round amount to 2 decimal places
            $amount = round($amount, 2);

            // Determine status
            $status = $statuses[array_rand($statuses)];

            // Assign driver if status is not pending or cancelled
            $driverId = null;
            if (in_array($status, ['confirmed', 'in_progress', 'completed'])) {
                $driverId = $drivers->random()->id;
            }

            // Create booking
            $booking = Booking::create([
                'user_id' => $client->id,
                'vehicle_id' => $vehicle->id,
                'driver_id' => $driverId,
                'booking_number' => Booking::generateBookingNumber(),
                'booking_type' => $bookingType,
                'pickup_address' => $location['pickup'],
                'pickup_lat' => $location['pickup_lat'],
                'pickup_lng' => $location['pickup_lng'],
                'dropoff_address' => $location['dropoff'],
                'dropoff_lat' => $location['dropoff_lat'],
                'dropoff_lng' => $location['dropoff_lng'],
                'pickup_date' => $pickupDate,
                'return_date' => $returnDate,
                'duration_hours' => $durationHours,
                'distance' => $location['distance'],
                'amount' => $amount,
                'status' => $status,
                'payment_status' => $status === 'completed' ? 'completed' : ($status === 'cancelled' ? 'cancelled' : 'pending'),
                'notes' => 'Sample booking created by seeder.',
            ]);

            // Add booking history
            $booking->addHistory('booking_created', [
                'booking_type' => $booking->booking_type,
                'amount' => $booking->amount,
                'vehicle_id' => $booking->vehicle_id,
            ], $client->id);

            // Add status change history if not pending
            if ($status !== 'pending') {
                $booking->addHistory('status_changed', [
                    'old_status' => 'pending',
                    'new_status' => $status,
                    'reason' => 'Status updated by system',
                ], $client->id);
            }

            // Add driver assigned history if driver is assigned
            if ($driverId) {
                $booking->addHistory('driver_assigned', [
                    'driver_id' => $driverId,
                    'driver_name' => User::find($driverId)->name,
                ], $driverId);
            }
        }
    }
}
