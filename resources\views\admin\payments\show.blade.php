@extends('layouts.payment-logs')

@section('title', 'Payment Details')

@section('payment-logs-styles')
<style>
    .payment-info {
        margin-bottom: 30px;
    }

    .payment-info-item {
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .payment-info-item:hover {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 5px;
        transform: translateX(5px);
    }

    .payment-info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .payment-info-value {
        font-weight: 500;
        font-size: 1.1rem;
    }

    .booking-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        transition: all 0.3s;
    }

    .booking-details:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-5px);
    }

    .booking-details h5 {
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #ee393d;
        padding-bottom: 10px;
        display: inline-block;
    }

    .booking-info-item {
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .booking-info-item:hover {
        background-color: rgba(255,255,255,0.7);
        border-radius: 5px;
        padding: 5px;
    }

    .booking-info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .booking-info-value {
        font-weight: 500;
    }

    .payment-actions {
        margin-top: 30px;
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .payment-actions h5 {
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #ee393d;
        padding-bottom: 10px;
        display: inline-block;
    }

    .payment-details-json {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .payment-details-json pre {
        margin: 0;
        white-space: pre-wrap;
    }

    .transaction-id {
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        padding: 5px 10px;
        border-radius: 5px;
        border: 1px solid #e9ecef;
        font-size: 0.9rem;
    }

    .payment-amount {
        font-weight: 700;
        color: #198754;
        font-size: 1.2rem;
    }

    .payment-refunded {
        color: #0dcaf0;
    }

    .payment-failed {
        color: #dc3545;
    }

    .payment-pending {
        color: #ffc107;
    }

    .payment-header {
        background-color: #343a40;
        color: white;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }

    .payment-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .payment-header .payment-id {
        background-color: rgba(255,255,255,0.2);
        padding: 3px 10px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-left: 10px;
    }

    .action-buttons {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }

    .action-buttons .btn {
        flex: 1;
        transition: all 0.3s;
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
</style>
@endsection

@section('payment-logs-title', 'Payment Details')

@section('payment-logs-actions')
<div class="btn-group">
    @if($payment->status == 'completed')
    <a href="{{ route('admin.payments.invoice', $payment->id) }}" class="btn btn-outline-primary">
        <i class="fas fa-file-invoice me-1"></i> View Invoice
    </a>
    @endif
    <a href="{{ route('admin.bookings.show', $payment->booking->id) }}" class="btn btn-outline-info">
        <i class="fas fa-car me-1"></i> View Booking
    </a>
    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-1"></i> Print
    </button>
    <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to List
    </a>
</div>
@endsection

@section('payment-logs-content')
@if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<div class="row">
    <div class="col-md-8">
        <!-- Payment Information -->
        <div class="card payment-log-card">
            <div class="payment-header d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0 d-flex align-items-center">
                        Payment Information
                        <span class="payment-id">#{{ $payment->id }}</span>
                    </h4>
                </div>
                <span class="status-badge status-{{ $payment->status }}">
                    {{ ucfirst($payment->status) }}
                </span>
            </div>
            <div class="payment-log-body">
                <div class="payment-info">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Transaction ID</div>
                                <div class="payment-info-value">
                                    <span class="transaction-id">{{ $payment->transaction_id }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Payment Method</div>
                                <div class="payment-info-value">
                                    <span class="method-badge method-{{ $payment->payment_method }}">
                                        @if($payment->payment_method == 'paypal')
                                            <i class="fab fa-paypal me-1"></i>
                                        @elseif($payment->payment_method == 'credit_card')
                                            <i class="fas fa-credit-card me-1"></i>
                                        @elseif($payment->payment_method == 'cash')
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                        @elseif($payment->payment_method == 'bank_transfer')
                                            <i class="fas fa-university me-1"></i>
                                        @elseif($payment->payment_method == 'paypal_card')
                                            <i class="fab fa-paypal me-1"></i>
                                        @else
                                            <i class="fas fa-money-check me-1"></i>
                                        @endif
                                        {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Amount</div>
                                <div class="payment-info-value">
                                    <span class="payment-amount {{ $payment->status == 'refunded' ? 'payment-refunded' : ($payment->status == 'failed' ? 'payment-failed' : ($payment->status == 'pending' ? 'payment-pending' : '')) }}">
                                        {{ \App\Helpers\SettingsHelper::formatPrice($payment->amount) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Date</div>
                                <div class="payment-info-value">
                                    <i class="far fa-calendar-alt me-1 text-muted"></i>
                                    {{ $payment->created_at ? $payment->created_at->format('F d, Y H:i:s') : 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    @if ($payment->paid_at)
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Paid At</div>
                                <div class="payment-info-value">
                                    <i class="far fa-clock me-1 text-muted"></i>
                                    {{ $payment->paid_at ? $payment->paid_at->format('F d, Y H:i:s') : 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if ($payment->refunded_at)
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Refunded At</div>
                                <div class="payment-info-value">
                                    <i class="fas fa-undo-alt me-1 text-muted"></i>
                                    {{ $payment->refunded_at ? $payment->refunded_at->format('F d, Y H:i:s') : 'N/A' }}
                                </div>
                            </div>
                        </div>
                        @if($payment->refunded_amount)
                        <div class="col-md-6">
                            <div class="payment-info-item">
                                <div class="payment-info-label">Refunded Amount</div>
                                <div class="payment-info-value payment-refunded">
                                    {{ \App\Helpers\SettingsHelper::formatPrice($payment->refunded_amount) }}
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>

                <!-- Payment Actions -->
                <div class="payment-actions">
                    <h5>Payment Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <form action="{{ route('admin.payments.update-status', $payment->id) }}" method="POST" id="updateStatusForm">
                                @csrf
                                <div class="mb-3">
                                    <label for="status" class="form-label">Update Payment Status</label>
                                    <div class="input-group">
                                        <select class="form-select" name="status" id="status">
                                            <option value="pending" {{ $payment->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="processing" {{ $payment->status == 'processing' ? 'selected' : '' }}>Processing</option>
                                            <option value="completed" {{ $payment->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="failed" {{ $payment->status == 'failed' ? 'selected' : '' }}>Failed</option>
                                            <option value="refunded" {{ $payment->status == 'refunded' ? 'selected' : '' }}>Refunded</option>
                                            <option value="cancelled" {{ $payment->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                        </select>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-check me-1"></i> Update
                                        </button>
                                    </div>
                                    <div class="form-text">Changing the payment status will also update the booking payment status.</div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            @if ($payment->status === 'completed')
                                <form action="{{ route('admin.payments.refund', $payment->id) }}" method="POST" id="refundForm">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="refund_amount" class="form-label">Refund Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ \App\Helpers\SettingsHelper::getCurrencySymbol() }}</span>
                                            <input type="number" class="form-control" id="refund_amount" name="refund_amount" value="{{ $payment->amount }}" step="0.01" min="0" max="{{ $payment->amount }}" required>
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-undo me-1"></i> Process Refund
                                            </button>
                                        </div>
                                        <div class="form-text">Enter the amount to refund. This can be partial or full.</div>
                                    </div>
                                </form>
                            @endif
                        </div>
                    </div>

                    <div class="action-buttons">
                        <a href="{{ route('admin.bookings.show', $payment->booking->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-car me-1"></i> View Booking
                        </a>
                        @if($payment->status == 'completed')
                        <a href="{{ route('admin.payments.invoice', $payment->id) }}" class="btn btn-outline-info">
                            <i class="fas fa-file-invoice me-1"></i> View Invoice
                        </a>
                        @endif
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Details
                        </button>
                    </div>
                </div>

                <!-- Payment Details JSON -->
                @if ($payment->payment_details)
                    <div class="mt-4">
                        <h5>Payment Details</h5>
                        <div class="payment-details-json">
                            <pre>{{ json_encode(json_decode($payment->payment_details), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                        </div>
                        <div class="form-text mt-2">
                            <i class="fas fa-info-circle me-1"></i> This is the raw payment data received from the payment processor.
                        </div>
                    </div>
                @endif

                @if ($payment->refund_notes)
                    <div class="mt-4">
                        <h5>Refund Notes</h5>
                        <div class="payment-details-json">
                            <div class="p-3">{{ $payment->refund_notes }}</div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Booking Information -->
        <div class="card payment-log-card">
            <div class="payment-header">
                <h4 class="mb-0">Booking Information</h4>
            </div>
            <div class="payment-log-body">
                <div class="booking-info-item">
                    <div class="booking-info-label">Booking Number</div>
                    <div class="booking-info-value">
                        <a href="{{ route('admin.bookings.show', $payment->booking->id) }}" class="fw-bold">
                            {{ $payment->booking->booking_number }}
                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Client</div>
                    <div class="booking-info-value">
                        <a href="{{ route('admin.users.show', $payment->booking->user->id) }}">
                            <i class="fas fa-user me-1 text-muted"></i>
                            {{ $payment->booking->user->name }}
                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Email</div>
                    <div class="booking-info-value">
                        <a href="mailto:{{ $payment->booking->user->email }}">
                            <i class="fas fa-envelope me-1 text-muted"></i>
                            {{ $payment->booking->user->email }}
                        </a>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Vehicle</div>
                    <div class="booking-info-value">
                        <i class="fas fa-car me-1 text-muted"></i>
                        {{ $payment->booking->vehicle->name }}
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Pickup Date</div>
                    <div class="booking-info-value">
                        <i class="fas fa-calendar-alt me-1 text-muted"></i>
                        {{ $payment->booking->pickup_date ? $payment->booking->pickup_date->format('F d, Y H:i') : 'N/A' }}
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Pickup Location</div>
                    <div class="booking-info-value text-truncate" title="{{ $payment->booking->pickup_address }}">
                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                        {{ $payment->booking->pickup_address }}
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Dropoff Location</div>
                    <div class="booking-info-value text-truncate" title="{{ $payment->booking->dropoff_address }}">
                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                        {{ $payment->booking->dropoff_address }}
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Booking Status</div>
                    <div class="booking-info-value">
                        <span class="status-badge status-{{ $payment->booking->status }}">
                            {{ ucfirst($payment->booking->status) }}
                        </span>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Payment Status</div>
                    <div class="booking-info-value">
                        <span class="status-badge status-{{ $payment->booking->payment_status === 'paid' ? 'completed' : ($payment->booking->payment_status === 'refunded' ? 'refunded' : 'pending') }}">
                            {{ ucfirst($payment->booking->payment_status) }}
                        </span>
                    </div>
                </div>

                <div class="booking-info-item">
                    <div class="booking-info-label">Total Amount</div>
                    <div class="booking-info-value fw-bold">
                        {{ \App\Helpers\SettingsHelper::formatPrice($payment->booking->total_amount) }}
                    </div>
                </div>

                <div class="mt-4">
                    <a href="{{ route('admin.bookings.show', $payment->booking->id) }}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-external-link-alt me-1"></i> View Full Booking Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('payment-logs-scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle update status form submission
        const updateStatusForm = document.getElementById('updateStatusForm');
        if (updateStatusForm) {
            updateStatusForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const status = document.getElementById('status').value;
                let confirmMessage = 'You are about to update the payment status to ' + status + '.';
                let confirmIcon = 'info';

                if (status === 'refunded') {
                    confirmMessage = 'You are about to mark this payment as refunded. Consider using the refund form instead to process an actual refund.';
                    confirmIcon = 'warning';
                }

                Swal.fire({
                    title: 'Update Payment Status?',
                    text: confirmMessage,
                    icon: confirmIcon,
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, update status'
                }).then((result) => {
                    if (result.isConfirmed) {
                        updateStatusForm.removeEventListener('submit', arguments.callee);
                        updateStatusForm.submit();
                    }
                });
            });
        }

        // Handle refund form submission
        const refundForm = document.getElementById('refundForm');
        if (refundForm) {
            refundForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const refundAmount = parseFloat(document.getElementById('refund_amount').value);
                const maxAmount = parseFloat(document.getElementById('refund_amount').getAttribute('max'));

                if (isNaN(refundAmount) || refundAmount <= 0) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please enter a valid refund amount greater than zero',
                        icon: 'error'
                    });
                    return;
                }

                if (refundAmount > maxAmount) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Refund amount cannot exceed the original payment amount',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Process Refund?',
                    text: 'You are about to process a refund of ' + refundAmount.toFixed(2) + '. This action cannot be undone!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, process refund'
                }).then((result) => {
                    if (result.isConfirmed) {
                        refundForm.removeEventListener('submit', arguments.callee);
                        refundForm.submit();
                    }
                });
            });
        }

        // Format JSON for better readability
        const jsonPre = document.querySelector('.payment-details-json pre');
        if (jsonPre) {
            try {
                const jsonObj = JSON.parse(jsonPre.textContent);
                jsonPre.textContent = JSON.stringify(jsonObj, null, 2);
            } catch (e) {
                console.error('Error parsing JSON:', e);
            }
        }
    });
</script>
@endsection