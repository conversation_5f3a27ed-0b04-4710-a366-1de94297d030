@extends('emails.layouts.master')

@section('title', 'Booking Reminder')

@section('content')
<h2>Booking Reminder</h2>

<p>Dear {{ $user->name }},</p>

<p>This is a friendly reminder about your upcoming ride with {{ $companyName }}.</p>

<div class="booking-details">
    <h3>Your Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong>{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name ?? 'To be assigned' }}</span>
    </div>
    
    @if($driver)
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value">{{ $driver->name }}</span>
    </div>
    @if($driver->phone)
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value">{{ $driver->phone }}</span>
    </div>
    @endif
    @endif
</div>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #856404; margin-top: 0;">Important Reminders:</h3>
    <ul style="color: #856404; margin-bottom: 0;">
        <li>Please be ready at your pickup location 5 minutes before the scheduled time</li>
        <li>Our driver will contact you 15-30 minutes before arrival</li>
        <li>Have your booking confirmation number ready: <strong>#{{ $booking->booking_number }}</strong></li>
        @if($booking->payment_status === 'pending')
        <li style="color: #dc3545;"><strong>Payment Required:</strong> Please complete your payment before the ride</li>
        @endif
    </ul>
</div>

@if($booking->notes)
<div style="margin: 20px 0;">
    <strong>Special Instructions:</strong><br>
    {{ $booking->notes }}
</div>
@endif

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn">View Full Booking Details</a>
</div>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us as soon as possible:</p>
<ul>
    <li>Phone: {{ $companyPhone }}</li>
    <li>Email: {{ $companyEmail }}</li>
    <li>Or manage your booking online in your account</li>
</ul>

<p><strong>Cancellation Policy:</strong> Free cancellation up to 24 hours before pickup. Cancellations within 24 hours may incur a fee.</p>

<p>We look forward to serving you!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>
@endsection
