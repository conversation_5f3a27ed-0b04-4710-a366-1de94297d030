<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class VehicleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $vehicles = Vehicle::orderBy('created_at', 'desc')->get();
        return view('admin.vehicles.index', compact('vehicles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.vehicles.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'seats' => 'required|integer|min:1',
            'luggage_capacity' => 'required|integer|min:0',
            'transmission' => 'required|string|max:255',
            'price_per_km' => 'required|numeric|min:0',
            'price_per_hour' => 'required|numeric|min:0',
            'base_fare' => 'required|numeric|min:0',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'airport_surcharge' => 'required|numeric|min:0',
            'booking_fee' => 'required|numeric|min:0',
            'waiting_fee_per_minute' => 'required|numeric|min:0',
            'cancellation_fee' => 'required|numeric|min:0',
            'night_surcharge' => 'required|numeric|min:0',
            'weekend_surcharge' => 'required|numeric|min:0',
            'holiday_surcharge' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $vehicle = new Vehicle();
        $vehicle->name = $request->input('name');
        $vehicle->type = $request->input('type');
        $vehicle->model = $request->input('model');
        $vehicle->category = $request->input('category');
        $vehicle->seats = $request->input('seats');
        $vehicle->luggage_capacity = $request->input('luggage_capacity');
        $vehicle->transmission = $request->input('transmission');
        $vehicle->price_per_km = $request->input('price_per_km');
        $vehicle->price_per_hour = $request->input('price_per_hour');
        $vehicle->base_fare = $request->input('base_fare');
        $vehicle->tax_rate = $request->input('tax_rate');
        $vehicle->airport_surcharge = $request->input('airport_surcharge');
        $vehicle->booking_fee = $request->input('booking_fee');
        $vehicle->waiting_fee_per_minute = $request->input('waiting_fee_per_minute');
        $vehicle->cancellation_fee = $request->input('cancellation_fee');
        $vehicle->night_surcharge = $request->input('night_surcharge');
        $vehicle->weekend_surcharge = $request->input('weekend_surcharge');
        $vehicle->holiday_surcharge = $request->input('holiday_surcharge');
        $vehicle->description = $request->input('description');
        $vehicle->is_active = $request->boolean('is_active', true);

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('vehicles', $imageName, 'public');
            $vehicle->image = $path;
        }

        $vehicle->save();

        return redirect()->route('admin.vehicles.index')
            ->with('success', 'Vehicle created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $vehicle = Vehicle::findOrFail($id);
        return view('admin.vehicles.show', compact('vehicle'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $vehicle = Vehicle::findOrFail($id);
        return view('admin.vehicles.edit', compact('vehicle'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'seats' => 'required|integer|min:1',
            'luggage_capacity' => 'required|integer|min:0',
            'transmission' => 'required|string|max:255',
            'price_per_km' => 'required|numeric|min:0',
            'price_per_hour' => 'required|numeric|min:0',
            'base_fare' => 'required|numeric|min:0',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'airport_surcharge' => 'required|numeric|min:0',
            'booking_fee' => 'required|numeric|min:0',
            'waiting_fee_per_minute' => 'required|numeric|min:0',
            'cancellation_fee' => 'required|numeric|min:0',
            'night_surcharge' => 'required|numeric|min:0',
            'weekend_surcharge' => 'required|numeric|min:0',
            'holiday_surcharge' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $vehicle = Vehicle::findOrFail($id);
        $vehicle->name = $request->input('name');
        $vehicle->type = $request->input('type');
        $vehicle->model = $request->input('model');
        $vehicle->category = $request->input('category');
        $vehicle->seats = $request->input('seats');
        $vehicle->luggage_capacity = $request->input('luggage_capacity');
        $vehicle->transmission = $request->input('transmission');
        $vehicle->price_per_km = $request->input('price_per_km');
        $vehicle->price_per_hour = $request->input('price_per_hour');
        $vehicle->base_fare = $request->input('base_fare');
        $vehicle->tax_rate = $request->input('tax_rate');
        $vehicle->airport_surcharge = $request->input('airport_surcharge');
        $vehicle->booking_fee = $request->input('booking_fee');
        $vehicle->waiting_fee_per_minute = $request->input('waiting_fee_per_minute');
        $vehicle->cancellation_fee = $request->input('cancellation_fee');
        $vehicle->night_surcharge = $request->input('night_surcharge');
        $vehicle->weekend_surcharge = $request->input('weekend_surcharge');
        $vehicle->holiday_surcharge = $request->input('holiday_surcharge');
        $vehicle->description = $request->input('description');
        $vehicle->is_active = $request->boolean('is_active');

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($vehicle->image) {
                Storage::disk('public')->delete($vehicle->image);
            }

            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('vehicles', $imageName, 'public');
            $vehicle->image = $path;
        }

        $vehicle->save();

        return redirect()->route('admin.vehicles.index')
            ->with('success', 'Vehicle updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $vehicle = Vehicle::findOrFail($id);

        // Delete image if exists
        if ($vehicle->image) {
            Storage::disk('public')->delete($vehicle->image);
        }

        $vehicle->delete();

        return redirect()->route('admin.vehicles.index')
            ->with('success', 'Vehicle deleted successfully.');
    }
}
