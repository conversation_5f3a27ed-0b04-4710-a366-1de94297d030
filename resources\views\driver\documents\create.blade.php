@extends('layouts.driver')

@section('title', 'Upload Document')

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-upload me-2 text-primary"></i> Upload Document
        </h1>
        <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Documents
        </a>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">Document Information</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('driver.documents.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="document_type" class="form-label">Document Type <span class="text-danger">*</span></label>
                        <select class="form-select @error('document_type') is-invalid @enderror" id="document_type" name="document_type" required>
                            <option value="">Select Document Type</option>
                            <option value="Driver License" {{ old('document_type') == 'Driver License' ? 'selected' : '' }}>Driver License</option>
                            <option value="PCO License" {{ old('document_type') == 'PCO License' ? 'selected' : '' }}>PCO License</option>
                            <option value="Vehicle PCO License" {{ old('document_type') == 'Vehicle PCO License' ? 'selected' : '' }}>Vehicle PCO License</option>
                            <option value="Vehicle Insurance" {{ old('document_type') == 'Vehicle Insurance' ? 'selected' : '' }}>Vehicle Insurance</option>
                            <option value="MOT Certificate" {{ old('document_type') == 'MOT Certificate' ? 'selected' : '' }}>MOT Certificate</option>
                            <option value="V5C Logbook" {{ old('document_type') == 'V5C Logbook' ? 'selected' : '' }}>V5C Logbook</option>
                            <option value="Other" {{ old('document_type') == 'Other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('document_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="document_number" class="form-label">Document Number</label>
                        <input type="text" class="form-control @error('document_number') is-invalid @enderror" id="document_number" name="document_number" value="{{ old('document_number') }}">
                        @error('document_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">License number, certificate number, etc.</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date</label>
                        <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" id="expiry_date" name="expiry_date" value="{{ old('expiry_date') }}">
                        @error('expiry_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="document_file" class="form-label">Document File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control @error('document_file') is-invalid @enderror" id="document_file" name="document_file" required>
                        @error('document_file')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Accepted formats: PDF, JPG, PNG (Max: 5MB)</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Any additional information about this document</div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirm_authentic" name="confirm_authentic" required>
                        <label class="form-check-label" for="confirm_authentic">
                            I confirm that this document is authentic and valid
                        </label>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">Upload Document</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">Document Guidelines</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="mb-3">Document Requirements</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item px-0">
                            <i class="fas fa-check-circle text-success me-2"></i> Documents must be clear and legible
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-check-circle text-success me-2"></i> All information must be visible
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-check-circle text-success me-2"></i> Documents must be valid and not expired
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-check-circle text-success me-2"></i> File size must be under 5MB
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-3">Verification Process</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item px-0">
                            <i class="fas fa-info-circle text-primary me-2"></i> Documents will be reviewed within 1-2 business days
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-info-circle text-primary me-2"></i> You will be notified when your document is verified
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-info-circle text-primary me-2"></i> You may be asked to resubmit if the document is unclear
                        </li>
                        <li class="list-group-item px-0">
                            <i class="fas fa-info-circle text-primary me-2"></i> Keep your documents up to date to maintain your driver status
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
