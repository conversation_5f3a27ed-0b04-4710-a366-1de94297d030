<?php $__env->startSection('title', 'Sign In to Complete Your Booking'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/booking.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="guest-review-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="guest-review-card">
                    <div class="card-header">
                        <h3 class="mb-0">Sign In to Complete Your Booking</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-4">Sign In</h5>

                                <?php if($errors->any()): ?>
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><?php echo e($error); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <form method="POST" action="<?php echo e(route('booking.guest.process-login')); ?>" class="guest-form">
                                    <?php echo csrf_field(); ?>

                                    <div class="form-group mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input id="email" type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email', session('client_details.email', ''))); ?>" required autocomplete="email" autofocus>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="form-group mb-4">
                                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                        <input id="password" type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" required autocomplete="current-password">
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="remember">
                                            Remember Me
                                        </label>
                                    </div>

                                    <div class="booking-actions">
                                        <a href="<?php echo e(route('booking.client-details')); ?>" class="btn btn-back">
                                            <i class="fas fa-arrow-left me-2"></i> Back
                                        </a>
                                        <button type="submit" id="loginBtn" class="btn btn-next">
                                            <span class="button-text">Sign In <i class="fas fa-sign-in-alt ms-2"></i></span>
                                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                        </button>
                                    </div>

                                    <!-- Password reset functionality not implemented yet -->
                                    <!-- <div class="mt-3 text-center">
                                        <a href="#" class="text-muted">Forgot your password?</a>
                                    </div> -->
                                </form>

                                <div class="auth-divider my-4">
                                    <span class="auth-divider-text">OR</span>
                                </div>

                                <div class="text-center">
                                    <p class="mb-3">Don't have an account?</p>
                                    <a href="<?php echo e(route('booking.guest.register')); ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-user-plus me-2"></i> Create Account
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card bg-light p-4 mb-4">
                                    <h6 class="mb-3">Benefits of creating an account:</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Track your bookings easily</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Faster checkout for future bookings</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> View your booking history</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Receive special offers and discounts</li>
                                    </ul>
                                </div>

                                <div class="booking-summary">
                                    <h4>Booking Summary</h4>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Booking Type:</div>
                                        <div class="booking-summary-value">
                                            <?php if(session('guest_booking')['booking_type'] == 'one_way'): ?>
                                                One Way
                                            <?php elseif(session('guest_booking')['booking_type'] == 'return'): ?>
                                                Return
                                            <?php elseif(session('guest_booking')['booking_type'] == 'airport'): ?>
                                                Airport Transfer
                                            <?php else: ?>
                                                Hourly (<?php echo e(session('guest_booking')['duration_hours']); ?> hours)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Pickup:</div>
                                        <div class="booking-summary-value"><?php echo e(session('guest_booking')['pickup_address']); ?></div>
                                    </div>

                                    <?php if(session('guest_booking')['booking_type'] != 'hourly'): ?>
                                        <div class="booking-summary-item">
                                            <div class="booking-summary-label">Dropoff:</div>
                                            <div class="booking-summary-value"><?php echo e(session('guest_booking')['dropoff_address']); ?></div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="booking-summary-item">
                                        <div class="booking-summary-label">Date & Time:</div>
                                        <div class="booking-summary-value"><?php echo e(\Carbon\Carbon::parse(session('guest_booking')['pickup_date'])->format('M d, Y h:i A')); ?></div>
                                    </div>

                                    <div class="booking-total">
                                        <div class="booking-total-label">Total:</div>
                                        <div class="booking-total-value"><?php echo e(\App\Services\SettingsService::getCurrencySymbol()); ?><?php echo e(number_format(session('guest_booking')['amount'], 2)); ?></div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-4">
                                    <i class="fas fa-info-circle me-2"></i> Sign in to complete your booking and proceed to payment.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle login form submission
        const loginForm = document.querySelector('form[action="<?php echo e(route("booking.guest.process-login")); ?>"]');
        const loginBtn = document.getElementById('loginBtn');

        if (loginForm && loginBtn) {
            loginForm.addEventListener('submit', function(e) {
                // Show loading indicator
                const spinner = loginBtn.querySelector('.spinner-border');
                const buttonText = loginBtn.querySelector('.button-text');

                if (spinner && buttonText) {
                    spinner.classList.remove('d-none');
                    buttonText.innerHTML = 'Signing In...';
                    loginBtn.disabled = true;
                }

                // Form will submit normally
            });
        }

        // Add animation to benefits card
        const benefitsCard = document.querySelector('.card.bg-light');
        if (benefitsCard) {
            setTimeout(() => {
                benefitsCard.classList.add('animate__animated', 'animate__fadeIn');
            }, 300);
        }

        // Focus on email field
        const emailField = document.getElementById('email');
        if (emailField) {
            emailField.focus();
        }

        // Add auth divider styling
        const authDivider = document.querySelector('.auth-divider');
        if (authDivider) {
            authDivider.style.display = 'flex';
            authDivider.style.alignItems = 'center';
            authDivider.style.margin = '30px 0';
        }

        const authDividerText = document.querySelector('.auth-divider-text');
        if (authDividerText) {
            authDividerText.style.padding = '0 15px';
            authDividerText.style.color = 'var(--text-muted)';
            authDividerText.style.fontSize = '0.9rem';
        }

        if (authDivider) {
            // Add before and after pseudo-elements
            const style = document.createElement('style');
            style.textContent = `
                .auth-divider::before, .auth-divider::after {
                    content: '';
                    flex: 1;
                    border-bottom: 1px solid var(--border-color);
                }
            `;
            document.head.appendChild(style);
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\booking\guest-login.blade.php ENDPATH**/ ?>