<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EarningController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:driver']);
    }

    /**
     * Display the driver's earnings dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $driverId = Auth::id();

        // Get total earnings
        $totalEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->sum('amount');

        // Get earnings for current month
        $currentMonthEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereMonth('updated_at', Carbon::now()->month)
            ->whereYear('updated_at', Carbon::now()->year)
            ->sum('amount');

        // Get earnings for current week
        $currentWeekEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereBetween('updated_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->sum('amount');

        // Get total completed rides
        $totalRides = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->count();

        // Get monthly earnings for chart
        $monthlyEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereYear('updated_at', Carbon::now()->year)
            ->select(
                DB::raw('MONTH(updated_at) as month'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray(); // Convert to array

        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlyEarnings[$i])) {
                $monthlyEarnings[$i] = 0;
            }
        }

        // Sort by month
        ksort($monthlyEarnings);

        // Get recent earnings
        $recentEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->with('user')
            ->orderBy('updated_at', 'desc')
            ->take(10)
            ->get();

        return view('driver.earnings.index', compact(
            'totalEarnings',
            'currentMonthEarnings',
            'currentWeekEarnings',
            'totalRides',
            'monthlyEarnings',
            'recentEarnings'
        ));
    }

    /**
     * Display the driver's earnings history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function history(Request $request)
    {
        $driverId = Auth::id();

        // Get filter parameters
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query
        $query = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->with('user');

        // Apply date filters if provided
        if ($startDate) {
            $query->whereDate('updated_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('updated_at', '<=', $endDate);
        }

        // Get earnings with pagination
        $earnings = $query->orderBy('updated_at', 'desc')
            ->paginate(15);

        // Calculate total for filtered results
        $totalFiltered = $query->sum('amount');

        return view('driver.earnings.history', compact('earnings', 'totalFiltered', 'startDate', 'endDate'));
    }

    /**
     * Display the driver's earnings details for a specific month.
     *
     * @param  int  $month
     * @param  int  $year
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function details($month, $year)
    {
        $driverId = Auth::id();

        // Get earnings for the specified month
        $earnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereMonth('updated_at', $month)
            ->whereYear('updated_at', $year)
            ->with('user', 'vehicle')
            ->orderBy('updated_at', 'desc')
            ->get();

        // Calculate total earnings for the month
        $totalEarnings = $earnings->sum('amount');

        // Get month name
        $monthName = Carbon::createFromDate($year, $month, 1)->format('F');

        return view('driver.earnings.details', compact('earnings', 'totalEarnings', 'month', 'year', 'monthName'));
    }

    /**
     * Display the driver's earnings statement page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function statement(Request $request)
    {
        $driverId = Auth::id();

        // Get available months and years for statement generation
        $availableMonths = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->select(
                DB::raw('MONTH(updated_at) as month'),
                DB::raw('YEAR(updated_at) as year')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                $date = Carbon::createFromDate($item->year, $item->month, 1);
                return [
                    'month' => $item->month,
                    'year' => $item->year,
                    'name' => $date->format('F Y')
                ];
            });

        return view('driver.earnings.statement', compact('availableMonths'));
    }

    /**
     * Download the driver's earnings statement.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function downloadStatement(Request $request)
    {
        $request->validate([
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2000|max:2100',
        ]);

        $driverId = Auth::id();
        $month = $request->input('month');
        $year = $request->input('year');

        // Get earnings for the specified month
        $earnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereMonth('updated_at', $month)
            ->whereYear('updated_at', $year)
            ->with('user', 'vehicle')
            ->orderBy('updated_at', 'desc')
            ->get();

        // Calculate total earnings for the month
        $totalEarnings = $earnings->sum('amount');

        // Get month name
        $monthName = Carbon::createFromDate($year, $month, 1)->format('F');

        // Get driver details
        $driver = Auth::user();

        // Generate PDF
        $pdf = \PDF::loadView('driver.earnings.pdf.statement', compact(
            'earnings',
            'totalEarnings',
            'month',
            'year',
            'monthName',
            'driver'
        ));

        return $pdf->download("earnings-statement-{$monthName}-{$year}.pdf");
    }

    /**
     * Display the driver's earnings reports page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function reports()
    {
        $driverId = Auth::id();

        // Get available years for report generation
        $availableYears = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->select(DB::raw('YEAR(updated_at) as year'))
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get quarterly earnings for the current year
        $currentYear = date('Y');
        $quarterlyEarnings = [];

        for ($quarter = 1; $quarter <= 4; $quarter++) {
            $startMonth = ($quarter - 1) * 3 + 1;
            $endMonth = $quarter * 3;

            $startDate = Carbon::createFromDate($currentYear, $startMonth, 1)->startOfMonth();
            $endDate = Carbon::createFromDate($currentYear, $endMonth, 1)->endOfMonth();

            $amount = Booking::where('driver_id', $driverId)
                ->where('status', 'completed')
                ->whereBetween('updated_at', [$startDate, $endDate])
                ->sum('amount');

            $quarterlyEarnings[$quarter] = $amount;
        }

        return view('driver.earnings.reports', compact('availableYears', 'quarterlyEarnings', 'currentYear'));
    }

    /**
     * Display the driver's earnings analytics page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function analytics()
    {
        $driverId = Auth::id();

        // Get total earnings
        $totalEarnings = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->sum('amount');

        // Get earnings by booking type
        $earningsByType = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->select('booking_type', DB::raw('SUM(amount) as total'))
            ->groupBy('booking_type')
            ->get()
            ->pluck('total', 'booking_type')
            ->toArray();

        // Get earnings by day of week
        $earningsByDayOfWeek = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->select(
                DB::raw('DAYOFWEEK(updated_at) as day_of_week'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('day_of_week')
            ->get()
            ->pluck('total', 'day_of_week')
            ->toArray();

        // Map day of week numbers to names
        $dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $formattedEarningsByDay = [];
        foreach ($dayNames as $index => $name) {
            $dayNumber = $index + 1;
            $formattedEarningsByDay[$name] = $earningsByDayOfWeek[$dayNumber] ?? 0;
        }

        // Get earnings by hour of day
        $earningsByHour = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->select(
                DB::raw('HOUR(updated_at) as hour'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('hour')
            ->get()
            ->pluck('total', 'hour')
            ->toArray();

        // Format hours
        $formattedEarningsByHour = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $formattedHour = sprintf('%02d:00', $hour);
            $formattedEarningsByHour[$formattedHour] = $earningsByHour[$hour] ?? 0;
        }

        return view('driver.earnings.analytics', compact(
            'totalEarnings',
            'earningsByType',
            'formattedEarningsByDay',
            'formattedEarningsByHour'
        ));
    }

    /**
     * Display the driver's payment schedule page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function schedule()
    {
        $driverId = Auth::id();

        // Get upcoming payments (for demonstration purposes, we'll use completed bookings)
        $upcomingPayments = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereNull('payment_processed_at')  // Assuming this field exists to track payment processing
            ->orderBy('updated_at', 'desc')
            ->get();

        // Get payment history
        $paymentHistory = Booking::where('driver_id', $driverId)
            ->where('status', 'completed')
            ->whereNotNull('payment_processed_at')  // Assuming this field exists
            ->orderBy('payment_processed_at', 'desc')
            ->take(10)
            ->get();

        // Get next payment date (for demonstration, we'll use the first day of next month)
        $nextPaymentDate = Carbon::now()->addMonth()->startOfMonth();

        return view('driver.earnings.schedule', compact('upcomingPayments', 'paymentHistory', 'nextPaymentDate'));
    }
}
