<?php $__env->startSection('title', 'Email Preferences'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Preferences</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('client.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Email Preferences</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Manage Your Email Notifications</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Choose which email notifications you'd like to receive. You can always change these settings later.
                    </p>

                    <form method="POST" action="<?php echo e(route('client.email-preferences.update')); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <?php $__currentLoopData = $rolePreferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $preference = $preferences[$type] ?? ['enabled' => false, 'settings' => []];
                                    $isEssential = in_array($type, ['booking_confirmations', 'payment_confirmations', 'cancellation_notices']);
                                ?>
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card border <?php echo e($preference['enabled'] ? 'border-success' : 'border-light'); ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo e($label); ?></h6>
                                                <?php if($isEssential): ?>
                                                    <span class="badge bg-warning">Essential</span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <p class="card-text text-muted small">
                                                <?php switch($type):
                                                    case ('booking_confirmations'): ?>
                                                        Receive confirmation emails when you make a booking
                                                        <?php break; ?>
                                                    <?php case ('booking_reminders'): ?>
                                                        Get reminded about upcoming rides 24 hours in advance
                                                        <?php break; ?>
                                                    <?php case ('payment_confirmations'): ?>
                                                        Receive receipts and payment confirmations
                                                        <?php break; ?>
                                                    <?php case ('driver_assignments'): ?>
                                                        Get notified when a driver is assigned to your booking
                                                        <?php break; ?>
                                                    <?php case ('status_updates'): ?>
                                                        Receive updates when your booking status changes
                                                        <?php break; ?>
                                                    <?php case ('cancellation_notices'): ?>
                                                        Get notified about booking cancellations
                                                        <?php break; ?>
                                                    <?php case ('promotional_emails'): ?>
                                                        Receive special offers and promotional content
                                                        <?php break; ?>
                                                    <?php case ('system_notifications'): ?>
                                                        Important system updates and maintenance notices
                                                        <?php break; ?>
                                                    <?php case ('newsletter'): ?>
                                                        Monthly newsletter with company updates
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        Email notifications for <?php echo e(strtolower($label)); ?>

                                                <?php endswitch; ?>
                                            </p>

                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="preferences[<?php echo e($type); ?>][enabled]" 
                                                       id="pref_<?php echo e($type); ?>" 
                                                       value="1"
                                                       <?php echo e($preference['enabled'] ? 'checked' : ''); ?>

                                                       <?php echo e($isEssential ? 'disabled' : ''); ?>>
                                                <label class="form-check-label" for="pref_<?php echo e($type); ?>">
                                                    <?php echo e($preference['enabled'] ? 'Enabled' : 'Disabled'); ?>

                                                </label>
                                            </div>

                                            <?php if($isEssential): ?>
                                                <input type="hidden" name="preferences[<?php echo e($type); ?>][enabled]" value="1">
                                                <small class="text-warning">
                                                    <i class="fas fa-info-circle"></i> This is an essential notification and cannot be disabled.
                                                </small>
                                            <?php endif; ?>

                                            <?php if(!$isEssential && in_array($type, ['promotional_emails', 'newsletter', 'system_notifications'])): ?>
                                                <div class="mt-2 frequency-settings" style="<?php echo e($preference['enabled'] ? '' : 'display: none;'); ?>">
                                                    <label class="form-label small">Frequency:</label>
                                                    <select name="preferences[<?php echo e($type); ?>][frequency]" class="form-select form-select-sm">
                                                        <option value="immediate" <?php echo e(($preference['settings']['frequency'] ?? 'immediate') === 'immediate' ? 'selected' : ''); ?>>
                                                            Immediate
                                                        </option>
                                                        <option value="daily" <?php echo e(($preference['settings']['frequency'] ?? '') === 'daily' ? 'selected' : ''); ?>>
                                                            Daily Digest
                                                        </option>
                                                        <option value="weekly" <?php echo e(($preference['settings']['frequency'] ?? '') === 'weekly' ? 'selected' : ''); ?>>
                                                            Weekly Summary
                                                        </option>
                                                    </select>
                                                </div>
                                            <?php else: ?>
                                                <input type="hidden" name="preferences[<?php echo e($type); ?>][frequency]" value="immediate">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <div class="d-flex justify-content-between">
                            <form method="POST" action="<?php echo e(route('client.email-preferences.reset')); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-outline-secondary" 
                                        onclick="return confirm('Are you sure you want to reset all preferences to default?')">
                                    <i class="fas fa-undo"></i> Reset to Default
                                </button>
                            </form>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Email Preference Guide</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Essential Emails</h6>
                        <p class="mb-0">Some emails are marked as "Essential" and cannot be disabled. These include booking confirmations, payment receipts, and cancellation notices that are required for service delivery.</p>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Recommended Settings</h6>
                        <p class="mb-0">We recommend keeping booking reminders and status updates enabled to stay informed about your rides and ensure the best experience.</p>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Frequency Options</h6>
                        <p class="mb-0">For promotional and newsletter emails, you can choose to receive them immediately, in a daily digest, or as a weekly summary.</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Contact Preferences</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">Your current email address:</p>
                    <p class="fw-bold"><?php echo e(Auth::user()->email); ?></p>
                    
                    <a href="<?php echo e(route('client.profile.edit')); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Update Email Address
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle preference toggle changes
    const preferenceToggles = document.querySelectorAll('.form-check-input[type="checkbox"]');
    
    preferenceToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const card = this.closest('.card');
            const label = this.nextElementSibling;
            const frequencySettings = card.querySelector('.frequency-settings');
            
            if (this.checked) {
                card.classList.remove('border-light');
                card.classList.add('border-success');
                label.textContent = 'Enabled';
                if (frequencySettings) {
                    frequencySettings.style.display = '';
                }
            } else {
                card.classList.remove('border-success');
                card.classList.add('border-light');
                label.textContent = 'Disabled';
                if (frequencySettings) {
                    frequencySettings.style.display = 'none';
                }
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\client\email-preferences\index.blade.php ENDPATH**/ ?>