@extends('emails.layouts.master')

@section('title', 'Ride Receipt')

@section('content')
<h2>Ride Receipt</h2>

<p>Dear {{ $user->name }},</p>

<p>Thank you for choosing {{ $companyName }}! Here's your receipt for your completed ride.</p>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: #155724; margin-top: 0;">🧾 Ride Completed Successfully</h3>
    <p style="color: #155724; margin-bottom: 0;">
        Your ride has been completed. Thank you for using {{ $companyName }}!
    </p>
</div>

<div class="booking-details">
    <h3>Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#{{ $booking->booking_number }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Date & Time:</span>
        <span class="detail-value">{{ $booking->pickup_date->format('l, F j, Y \a\t g:i A') }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Location:</span>
        <span class="detail-value">{{ $booking->pickup_address }}</span>
    </div>
    
    @if($booking->dropoff_address)
    <div class="detail-row">
        <span class="detail-label">Dropoff Location:</span>
        <span class="detail-value">{{ $booking->dropoff_address }}</span>
    </div>
    @endif
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value">{{ $vehicle->name ?? 'N/A' }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value">{{ $driver->name ?? 'N/A' }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Distance:</span>
        <span class="detail-value">{{ $rideDistance ?? 'N/A' }} km</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Duration:</span>
        <span class="detail-value">{{ $rideDuration ?? 'N/A' }} minutes</span>
    </div>
</div>

<div class="booking-details">
    <h3>Payment Summary</h3>
    
    <div class="detail-row">
        <span class="detail-label">Base Fare:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($baseFare ?? 0, 2) }}</span>
    </div>
    
    @if(($distanceFare ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Distance Fare:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($distanceFare, 2) }}</span>
    </div>
    @endif
    
    @if(($timeFare ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Time Fare:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($timeFare, 2) }}</span>
    </div>
    @endif
    
    @if(($waitingTime ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Waiting Time:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($waitingTime, 2) }}</span>
    </div>
    @endif
    
    @if(($tolls ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Tolls & Fees:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($tolls, 2) }}</span>
    </div>
    @endif
    
    @if(($discount ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Discount:</span>
        <span class="detail-value">-{{ $currencySymbol }}{{ number_format($discount, 2) }}</span>
    </div>
    @endif
    
    @if(($tip ?? 0) > 0)
    <div class="detail-row">
        <span class="detail-label">Tip:</span>
        <span class="detail-value">{{ $currencySymbol }}{{ number_format($tip, 2) }}</span>
    </div>
    @endif
    
    <div class="detail-row" style="border-top: 2px solid #28a745; padding-top: 10px; margin-top: 10px;">
        <span class="detail-label"><strong>Total Amount:</strong></span>
        <span class="detail-value"><strong>{{ $currencySymbol }}{{ number_format($booking->amount, 2) }}</strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value">{{ $paymentMethod ?? 'Cash' }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Status:</span>
        <span class="detail-value">{{ $paymentStatus ?? 'Completed' }}</span>
    </div>
</div>

@if($driver)
<div class="booking-details">
    <h3>Driver Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Driver Name:</span>
        <span class="detail-value">{{ $driver->name }}</span>
    </div>
    
    @if($driver->phone)
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value">{{ $driver->phone }}</span>
    </div>
    @endif
    
    @if($vehicle->license_plate)
    <div class="detail-row">
        <span class="detail-label">Vehicle License:</span>
        <span class="detail-value">{{ $vehicle->license_plate }}</span>
    </div>
    @endif
    
    @if($driverRating ?? false)
    <div class="detail-row">
        <span class="detail-label">Your Rating:</span>
        <span class="detail-value">{{ $driverRating }} ⭐</span>
    </div>
    @endif
</div>
@endif

<h3>⭐ Rate Your Experience:</h3>
@if(!($driverRating ?? false))
<p>Help us improve our service by rating your ride experience:</p>
<div style="margin: 20px 0; text-align: center;">
    <a href="{{ route('client.bookings.review', $booking->id) }}" class="btn" style="background-color: #ffc107; color: #000;">
        Rate This Ride
    </a>
</div>
@else
<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <p style="color: #155724; margin-bottom: 0; text-align: center;">
        ✅ Thank you for rating this ride: {{ $driverRating }} ⭐
    </p>
</div>
@endif

<h3>📱 Download Our App:</h3>
<p>Get the {{ $companyName }} mobile app for easier booking and exclusive features:</p>
<div style="text-align: center; margin: 20px 0;">
    <a href="#" style="display: inline-block; margin: 0 10px;">
        <img src="https://via.placeholder.com/150x50/000000/FFFFFF?text=App+Store" alt="Download on App Store" style="height: 50px;">
    </a>
    <a href="#" style="display: inline-block; margin: 0 10px;">
        <img src="https://via.placeholder.com/150x50/000000/FFFFFF?text=Google+Play" alt="Get it on Google Play" style="height: 50px;">
    </a>
</div>

<h3>🎁 Special Offers:</h3>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <ul style="color: #856404; margin-bottom: 0;">
        <li><strong>Loyalty Program:</strong> Earn points with every ride</li>
        <li><strong>Referral Bonus:</strong> Get $10 credit for each friend you refer</li>
        <li><strong>Frequent Rider:</strong> Special discounts for regular customers</li>
        <li><strong>Weekend Special:</strong> 15% off weekend rides</li>
    </ul>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="{{ route('booking.index') }}" class="btn">Book Another Ride</a>
    <a href="{{ route('client.bookings.index') }}" class="btn" style="background-color: #28a745; margin-left: 10px;">View All Rides</a>
</div>

<h3>📞 Need Help?</h3>
<p>If you have any questions about this ride or need assistance:</p>
<ul>
    <li><strong>Customer Support:</strong> {{ $companyPhone }}</li>
    <li><strong>Email Support:</strong> {{ $companyEmail }}</li>
    <li><strong>Live Chat:</strong> Available on our website</li>
    <li><strong>Help Center:</strong> Visit our FAQ section</li>
</ul>

<h3>📄 Receipt Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p style="margin-bottom: 0; font-size: 14px; color: #6c757d;">
        <strong>Receipt Number:</strong> {{ $booking->booking_number }}<br>
        <strong>Generated:</strong> {{ now()->format('F j, Y \a\t g:i A') }}<br>
        <strong>Tax ID:</strong> {{ $companyTaxId ?? 'N/A' }}<br>
        <strong>Business License:</strong> {{ $companyLicense ?? 'N/A' }}
    </p>
</div>

<p>Thank you for choosing {{ $companyName }}. We appreciate your business and look forward to serving you again!</p>

<p>Best regards,<br>
The {{ $companyName }} Team</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 12px; color: #6c757d; text-align: center;">
    This receipt is for your records. Keep it for expense reporting or tax purposes.
    For questions about this receipt, contact us at {{ $companyEmail }}.
</p>
@endsection
