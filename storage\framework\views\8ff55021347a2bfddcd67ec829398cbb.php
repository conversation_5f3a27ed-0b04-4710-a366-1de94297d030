<?php $__env->startSection('title', 'Ride Receipt'); ?>

<?php $__env->startSection('content'); ?>
<h2>Ride Receipt</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>Thank you for choosing <?php echo e($companyName); ?>! Here's your receipt for your completed ride.</p>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: #155724; margin-top: 0;">🧾 Ride Completed Successfully</h3>
    <p style="color: #155724; margin-bottom: 0;">
        Your ride has been completed. Thank you for using <?php echo e($companyName); ?>!
    </p>
</div>

<div class="booking-details">
    <h3>Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Location:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Location:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name ?? 'N/A'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value"><?php echo e($driver->name ?? 'N/A'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Distance:</span>
        <span class="detail-value"><?php echo e($rideDistance ?? 'N/A'); ?> km</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Duration:</span>
        <span class="detail-value"><?php echo e($rideDuration ?? 'N/A'); ?> minutes</span>
    </div>
</div>

<div class="booking-details">
    <h3>Payment Summary</h3>
    
    <div class="detail-row">
        <span class="detail-label">Base Fare:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($baseFare ?? 0, 2)); ?></span>
    </div>
    
    <?php if(($distanceFare ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Distance Fare:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($distanceFare, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(($timeFare ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Time Fare:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($timeFare, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(($waitingTime ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Waiting Time:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($waitingTime, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(($tolls ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Tolls & Fees:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($tolls, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(($discount ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Discount:</span>
        <span class="detail-value">-<?php echo e($currencySymbol); ?><?php echo e(number_format($discount, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if(($tip ?? 0) > 0): ?>
    <div class="detail-row">
        <span class="detail-label">Tip:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($tip, 2)); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row" style="border-top: 2px solid #28a745; padding-top: 10px; margin-top: 10px;">
        <span class="detail-label"><strong>Total Amount:</strong></span>
        <span class="detail-value"><strong><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value"><?php echo e($paymentMethod ?? 'Cash'); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Status:</span>
        <span class="detail-value"><?php echo e($paymentStatus ?? 'Completed'); ?></span>
    </div>
</div>

<?php if($driver): ?>
<div class="booking-details">
    <h3>Driver Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Driver Name:</span>
        <span class="detail-value"><?php echo e($driver->name); ?></span>
    </div>
    
    <?php if($driver->phone): ?>
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value"><?php echo e($driver->phone); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if($vehicle->license_plate): ?>
    <div class="detail-row">
        <span class="detail-label">Vehicle License:</span>
        <span class="detail-value"><?php echo e($vehicle->license_plate); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if($driverRating ?? false): ?>
    <div class="detail-row">
        <span class="detail-label">Your Rating:</span>
        <span class="detail-value"><?php echo e($driverRating); ?> ⭐</span>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<h3>⭐ Rate Your Experience:</h3>
<?php if(!($driverRating ?? false)): ?>
<p>Help us improve our service by rating your ride experience:</p>
<div style="margin: 20px 0; text-align: center;">
    <a href="<?php echo e(route('client.bookings.review', $booking->id)); ?>" class="btn" style="background-color: #ffc107; color: #000;">
        Rate This Ride
    </a>
</div>
<?php else: ?>
<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <p style="color: #155724; margin-bottom: 0; text-align: center;">
        ✅ Thank you for rating this ride: <?php echo e($driverRating); ?> ⭐
    </p>
</div>
<?php endif; ?>

<h3>📱 Download Our App:</h3>
<p>Get the <?php echo e($companyName); ?> mobile app for easier booking and exclusive features:</p>
<div style="text-align: center; margin: 20px 0;">
    <a href="#" style="display: inline-block; margin: 0 10px;">
        <img src="https://via.placeholder.com/150x50/000000/FFFFFF?text=App+Store" alt="Download on App Store" style="height: 50px;">
    </a>
    <a href="#" style="display: inline-block; margin: 0 10px;">
        <img src="https://via.placeholder.com/150x50/000000/FFFFFF?text=Google+Play" alt="Get it on Google Play" style="height: 50px;">
    </a>
</div>

<h3>🎁 Special Offers:</h3>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <ul style="color: #856404; margin-bottom: 0;">
        <li><strong>Loyalty Program:</strong> Earn points with every ride</li>
        <li><strong>Referral Bonus:</strong> Get $10 credit for each friend you refer</li>
        <li><strong>Frequent Rider:</strong> Special discounts for regular customers</li>
        <li><strong>Weekend Special:</strong> 15% off weekend rides</li>
    </ul>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('booking.index')); ?>" class="btn">Book Another Ride</a>
    <a href="<?php echo e(route('client.bookings.index')); ?>" class="btn" style="background-color: #28a745; margin-left: 10px;">View All Rides</a>
</div>

<h3>📞 Need Help?</h3>
<p>If you have any questions about this ride or need assistance:</p>
<ul>
    <li><strong>Customer Support:</strong> <?php echo e($companyPhone); ?></li>
    <li><strong>Email Support:</strong> <?php echo e($companyEmail); ?></li>
    <li><strong>Live Chat:</strong> Available on our website</li>
    <li><strong>Help Center:</strong> Visit our FAQ section</li>
</ul>

<h3>📄 Receipt Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p style="margin-bottom: 0; font-size: 14px; color: #6c757d;">
        <strong>Receipt Number:</strong> <?php echo e($booking->booking_number); ?><br>
        <strong>Generated:</strong> <?php echo e(now()->format('F j, Y \a\t g:i A')); ?><br>
        <strong>Tax ID:</strong> <?php echo e($companyTaxId ?? 'N/A'); ?><br>
        <strong>Business License:</strong> <?php echo e($companyLicense ?? 'N/A'); ?>

    </p>
</div>

<p>Thank you for choosing <?php echo e($companyName); ?>. We appreciate your business and look forward to serving you again!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="font-size: 12px; color: #6c757d; text-align: center;">
    This receipt is for your records. Keep it for expense reporting or tax purposes.
    For questions about this receipt, contact us at <?php echo e($companyEmail); ?>.
</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\receipt.blade.php ENDPATH**/ ?>