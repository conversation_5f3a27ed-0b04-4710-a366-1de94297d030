<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'action' => $this->action,
            'formatted_action' => $this->formatted_action,
            'details' => $this->details,
            'status_before' => $this->status_before,
            'status_after' => $this->status_after,
            'action_icon' => $this->action_icon,
            'action_color' => $this->action_color,
            'created_at' => $this->created_at?->toISOString(),
            'relationships' => [
                'user' => new UserResource($this->whenLoaded('user')),
            ],
        ];
    }
}
