<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class StorageService
{
    /**
     * File type configurations
     */
    const FILE_TYPES = [
        'profile_photos' => [
            'disk' => 'public',
            'path' => 'profile-photos',
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
            'max_size' => 2048, // KB
            'dimensions' => ['max_width' => 1024, 'max_height' => 1024]
        ],
        'driver_documents' => [
            'disk' => 'public',
            'path' => 'driver-documents',
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size' => 5120, // KB
        ],
        'vehicles' => [
            'disk' => 'public',
            'path' => 'vehicles',
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
            'max_size' => 2048, // KB
            'dimensions' => ['max_width' => 1200, 'max_height' => 800]
        ],
        'blog_posts' => [
            'disk' => 'public',
            'path' => 'blog-posts',
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
            'max_size' => 2048, // KB
        ]
    ];

    /**
     * Store a file with proper validation and naming
     *
     * @param UploadedFile $file
     * @param string $type
     * @param string|null $customName
     * @return array
     */
    public static function storeFile(UploadedFile $file, string $type, string $customName = null): array
    {
        try {
            // Validate file type
            if (!isset(self::FILE_TYPES[$type])) {
                throw new Exception("Invalid file type: {$type}");
            }

            $config = self::FILE_TYPES[$type];
            
            // Validate file
            self::validateFile($file, $config);

            // Generate filename
            $filename = $customName ?: self::generateFilename($file, $type);
            
            // Store file
            $path = $file->storeAs($config['path'], $filename, $config['disk']);
            
            // Get URL
            $url = Storage::disk($config['disk'])->url($path);

            return [
                'success' => true,
                'path' => $path,
                'url' => $url,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Store multiple files
     *
     * @param array $files
     * @param string $type
     * @return array
     */
    public static function storeMultipleFiles(array $files, string $type): array
    {
        $results = [];
        
        foreach ($files as $index => $file) {
            if ($file instanceof UploadedFile && $file->isValid()) {
                $customName = time() . '_' . $index . '.' . $file->getClientOriginalExtension();
                $results[] = self::storeFile($file, $type, $customName);
            }
        }

        return $results;
    }

    /**
     * Delete a file
     *
     * @param string $path
     * @param string $disk
     * @return bool
     */
    public static function deleteFile(string $path, string $disk = 'public'): bool
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->delete($path);
            }
            return true; // File doesn't exist, consider it deleted
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get file URL
     *
     * @param string $path
     * @param string $disk
     * @return string|null
     */
    public static function getFileUrl(string $path, string $disk = 'public'): ?string
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->url($path);
            }
            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Validate uploaded file
     *
     * @param UploadedFile $file
     * @param array $config
     * @throws Exception
     */
    private static function validateFile(UploadedFile $file, array $config): void
    {
        // Check if file is valid
        if (!$file->isValid()) {
            throw new Exception('Invalid file upload');
        }

        // Check file size
        $fileSizeKB = $file->getSize() / 1024;
        if ($fileSizeKB > $config['max_size']) {
            throw new Exception("File size exceeds maximum allowed size of {$config['max_size']}KB");
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $config['allowed_extensions'])) {
            $allowedExtensions = implode(', ', $config['allowed_extensions']);
            throw new Exception("File type not allowed. Allowed types: {$allowedExtensions}");
        }

        // Check image dimensions if specified
        if (isset($config['dimensions']) && in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                $width = $imageInfo[0];
                $height = $imageInfo[1];
                
                if (isset($config['dimensions']['max_width']) && $width > $config['dimensions']['max_width']) {
                    throw new Exception("Image width exceeds maximum allowed width of {$config['dimensions']['max_width']}px");
                }
                
                if (isset($config['dimensions']['max_height']) && $height > $config['dimensions']['max_height']) {
                    throw new Exception("Image height exceeds maximum allowed height of {$config['dimensions']['max_height']}px");
                }
            }
        }
    }

    /**
     * Generate unique filename
     *
     * @param UploadedFile $file
     * @param string $type
     * @return string
     */
    private static function generateFilename(UploadedFile $file, string $type): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = time();
        $random = Str::random(8);
        
        return "{$type}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Ensure storage directories exist
     *
     * @return void
     */
    public static function ensureDirectoriesExist(): void
    {
        foreach (self::FILE_TYPES as $type => $config) {
            $disk = Storage::disk($config['disk']);
            if (!$disk->exists($config['path'])) {
                $disk->makeDirectory($config['path'], 0755, true);
            }
        }
    }

    /**
     * Get storage statistics
     *
     * @return array
     */
    public static function getStorageStats(): array
    {
        $stats = [];
        
        foreach (self::FILE_TYPES as $type => $config) {
            $disk = Storage::disk($config['disk']);
            $path = $config['path'];
            
            if ($disk->exists($path)) {
                $files = $disk->allFiles($path);
                $totalSize = 0;
                
                foreach ($files as $file) {
                    $totalSize += $disk->size($file);
                }
                
                $stats[$type] = [
                    'file_count' => count($files),
                    'total_size' => $totalSize,
                    'total_size_mb' => round($totalSize / 1024 / 1024, 2)
                ];
            } else {
                $stats[$type] = [
                    'file_count' => 0,
                    'total_size' => 0,
                    'total_size_mb' => 0
                ];
            }
        }
        
        return $stats;
    }

    /**
     * Clean up old files (older than specified days)
     *
     * @param string $type
     * @param int $days
     * @return int Number of files deleted
     */
    public static function cleanupOldFiles(string $type, int $days = 30): int
    {
        if (!isset(self::FILE_TYPES[$type])) {
            return 0;
        }

        $config = self::FILE_TYPES[$type];
        $disk = Storage::disk($config['disk']);
        $path = $config['path'];
        
        if (!$disk->exists($path)) {
            return 0;
        }

        $files = $disk->allFiles($path);
        $deletedCount = 0;
        $cutoffTime = time() - ($days * 24 * 60 * 60);

        foreach ($files as $file) {
            $lastModified = $disk->lastModified($file);
            if ($lastModified < $cutoffTime) {
                if ($disk->delete($file)) {
                    $deletedCount++;
                }
            }
        }

        return $deletedCount;
    }
}
