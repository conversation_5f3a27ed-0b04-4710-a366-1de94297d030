<?php $__env->startSection('title', 'Payment Confirmation'); ?>

<?php $__env->startSection('content'); ?>
<h2>Payment Confirmation</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>Thank you! We have successfully received your payment for booking #<?php echo e($booking->booking_number); ?>.</p>

<div class="booking-details">
    <h3>Payment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Transaction ID:</span>
        <span class="detail-value"><?php echo e($payment->transaction_id); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Method:</span>
        <span class="detail-value"><?php echo e(ucfirst($payment->payment_method)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Amount Paid:</span>
        <span class="detail-value"><strong><?php echo e($currencySymbol); ?><?php echo e(number_format($payment->amount, 2)); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Date:</span>
        <span class="detail-value"><?php echo e($payment->created_at->format('F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Status:</span>
        <span class="detail-value" style="color: #28a745;"><strong>Completed</strong></span>
    </div>
</div>

<div class="booking-details">
    <h3>Booking Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name); ?></span>
    </div>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">✓ Your Booking is Confirmed!</h3>
    <p style="color: #155724; margin-bottom: 0;">
        Your payment has been processed successfully and your booking is now fully confirmed. 
        You will receive a reminder email 24 hours before your pickup time.
    </p>
</div>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('client.bookings.show', $booking->id)); ?>" class="btn">View Booking Details</a>
    <a href="<?php echo e(route('client.payments.invoice', $payment->id)); ?>" class="btn" style="background-color: #6c757d; margin-left: 10px;">Download Invoice</a>
</div>

<h3>What's Next?</h3>
<ul>
    <li>You will receive a reminder email 24 hours before your pickup</li>
    <li>Our driver will contact you 15-30 minutes before arrival</li>
    <li>Please be ready at your pickup location at the scheduled time</li>
    <li>Keep your booking confirmation number handy: <strong>#<?php echo e($booking->booking_number); ?></strong></li>
</ul>

<h3>Need Help?</h3>
<p>If you have any questions about your payment or booking, please don't hesitate to contact us:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or visit your account dashboard online</li>
</ul>

<p>Thank you for choosing <?php echo e($companyName); ?>!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\payment\confirmation.blade.php ENDPATH**/ ?>