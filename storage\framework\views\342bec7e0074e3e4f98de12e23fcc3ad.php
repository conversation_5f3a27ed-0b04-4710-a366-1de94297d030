<?php $__env->startSection('title', 'Driver Management'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* Driver List Styles */
    .driver-list-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .driver-list-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .driver-list-card .card-body {
        padding: 0;
    }

    .driver-table {
        margin-bottom: 0;
    }

    .driver-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .driver-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .driver-table tr {
        transition: all 0.3s ease;
    }

    .driver-table tr:hover {
        background-color: rgba(238, 57, 61, 0.03);
    }

    .driver-profile-img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .driver-profile-placeholder {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #fff;
        background-color: #ee393d;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .driver-name {
        font-weight: 600;
        margin-bottom: 3px;
    }

    .driver-info {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .driver-info i {
        width: 18px;
        color: #ee393d;
    }

    .vehicle-info {
        font-size: 0.85rem;
    }

    .document-info {
        font-size: 0.85rem;
    }

    .document-badge {
        font-size: 0.7rem;
        padding: 3px 8px;
        border-radius: 20px;
    }

    .expiry-date {
        font-weight: 500;
    }

    .expiry-date.expired {
        color: #dc3545;
    }

    .expiry-date.expiring-soon {
        color: #ffc107;
    }

    .expiry-date.valid {
        color: #28a745;
    }

    .btn-group .btn {
        border-radius: 5px;
        margin-right: 3px;
        padding: 5px 10px;
    }

    .pagination {
        margin-top: 20px;
        justify-content: center;
    }

    .pagination .page-item .page-link {
        color: #ee393d;
        border-radius: 5px;
        margin: 0 3px;
    }

    .pagination .page-item.active .page-link {
        background-color: #ee393d;
        border-color: #ee393d;
    }

    /* Search Form */
    .search-form {
        position: relative;
    }

    .search-form .form-control {
        border-radius: 50px;
        padding-left: 20px;
        padding-right: 50px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border: none;
        height: 45px;
    }

    .search-form .btn {
        position: absolute;
        right: 5px;
        top: 5px;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .driver-table {
            min-width: 1000px;
        }

        .card-body {
            overflow-x: auto;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Driver Management</h1>
        <div>
            <a href="<?php echo e(route('admin.drivers.create')); ?>" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> Add New Driver
            </a>
            <a href="<?php echo e(route('admin.drivers.export')); ?>" class="btn btn-sm btn-success shadow-sm">
                <i class="fas fa-download fa-sm text-white-50"></i> Export Drivers
            </a>
        </div>
    </div>

    <div class="card driver-list-card mb-4">
        <div class="card-header d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-id-card me-2"></i> All Drivers
                <?php if(request('status') === 'active'): ?>
                    <span class="badge bg-success ms-2">Active</span>
                <?php elseif(request('status') === 'inactive'): ?>
                    <span class="badge bg-danger ms-2">Inactive</span>
                <?php endif; ?>

                <?php if(request('availability') === 'available'): ?>
                    <span class="badge bg-info ms-2">Available</span>
                <?php elseif(request('availability') === 'unavailable'): ?>
                    <span class="badge bg-warning ms-2">Unavailable</span>
                <?php endif; ?>

                <?php if(request('document_status') === 'expired'): ?>
                    <span class="badge bg-danger ms-2">Expired Documents</span>
                <?php elseif(request('document_status') === 'expiring_soon'): ?>
                    <span class="badge bg-warning ms-2">Documents Expiring Soon</span>
                <?php endif; ?>
            </h6>
            <div class="d-flex">
                <div class="dropdown me-2">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="filterDropdown">
                        <li><h6 class="dropdown-header">Filter By Status:</h6></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index')); ?>">All Drivers</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['status' => 'active'])); ?>">Active Drivers</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['status' => 'inactive'])); ?>">Inactive Drivers</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Filter By Availability:</h6></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['availability' => 'available'])); ?>">Available Drivers</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['availability' => 'unavailable'])); ?>">Unavailable Drivers</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Filter By Documents:</h6></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['document_status' => 'expired'])); ?>">Expired Documents</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.drivers.index', ['document_status' => 'expiring_soon'])); ?>">Documents Expiring Soon</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <form action="<?php echo e(route('admin.drivers.index')); ?>" method="GET" class="search-form">
                        <input type="text" class="form-control" name="search" placeholder="Search by name, email, license, vehicle..." value="<?php echo e(request('search')); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="table-responsive">

                <table class="table driver-table" id="driversTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Driver</th>
                            <th>Contact</th>
                            <th>Vehicle</th>
                            <th>Documents</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $drivers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($driver->profile_photo): ?>
                                        <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="driver-profile-img me-3">
                                    <?php else: ?>
                                        <div class="driver-profile-placeholder me-3">
                                            <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="driver-name"><?php echo e($driver->name); ?> <span class="text-muted">#<?php echo e($driver->id); ?></span></div>
                                        <div class="driver-info"><i class="fas fa-id-card"></i> <?php echo e($driver->license_number); ?></div>
                                        <div class="driver-info"><i class="fas fa-calendar-alt"></i> Joined: <?php echo e($driver->created_at->format('M d, Y')); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="driver-info"><i class="fas fa-envelope"></i> <?php echo e($driver->email); ?></div>
                                <div class="driver-info"><i class="fas fa-phone"></i> <?php echo e($driver->phone); ?></div>
                                <div class="driver-info text-truncate" style="max-width: 200px;"><i class="fas fa-map-marker-alt"></i> <?php echo e($driver->address); ?></div>
                            </td>
                            <td>
                                <?php if($driver->vehicle_make && $driver->vehicle_model): ?>
                                    <div class="vehicle-info"><i class="fas fa-car text-primary me-2"></i> <?php echo e($driver->vehicle_make); ?> <?php echo e($driver->vehicle_model); ?></div>
                                    <div class="vehicle-info"><i class="fas fa-palette text-primary me-2"></i> <?php echo e($driver->vehicle_color); ?></div>
                                    <div class="vehicle-info"><i class="fas fa-id-card text-primary me-2"></i> <?php echo e($driver->vehicle_reg_number); ?></div>
                                <?php else: ?>
                                    <span class="text-muted">No vehicle details</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                    $hasExpiredInsurance = $driver->insurance_expiry && $driver->insurance_expiry->isPast();
                                    $hasExpiredMOT = $driver->mot_expiry && $driver->mot_expiry->isPast();
                                    $insuranceExpiringSoon = $driver->insurance_expiry && !$driver->insurance_expiry->isPast() && $driver->insurance_expiry->diffInDays(now()) <= 30;
                                    $motExpiringSoon = $driver->mot_expiry && !$driver->mot_expiry->isPast() && $driver->mot_expiry->diffInDays(now()) <= 30;

                                    // Get document counts
                                    $documentCount = $driver->driverDocuments->count();
                                    $pendingDocuments = $driver->driverDocuments->where('is_verified', false)->count();
                                ?>

                                <div class="document-info mb-2">
                                    <i class="fas fa-file-alt text-primary me-2"></i>
                                    <span class="me-2">Documents: <?php echo e($documentCount); ?></span>
                                    <?php if($pendingDocuments > 0): ?>
                                        <span class="badge bg-warning document-badge"><?php echo e($pendingDocuments); ?> pending</span>
                                    <?php endif; ?>
                                </div>

                                <div class="document-info mb-1">
                                    <i class="fas fa-shield-alt text-primary me-2"></i> Insurance:
                                    <?php if($driver->insurance_expiry): ?>
                                        <span class="expiry-date <?php echo e($hasExpiredInsurance ? 'expired' : ($insuranceExpiringSoon ? 'expiring-soon' : 'valid')); ?>">
                                            <?php echo e($driver->insurance_expiry->format('M d, Y')); ?>

                                            <?php if($hasExpiredInsurance): ?>
                                                <i class="fas fa-exclamation-circle ms-1" title="Expired"></i>
                                            <?php elseif($insuranceExpiringSoon): ?>
                                                <i class="fas fa-exclamation-triangle ms-1" title="Expiring soon"></i>
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="expiry-date expired">Missing</span>
                                    <?php endif; ?>
                                </div>

                                <div class="document-info">
                                    <i class="fas fa-check-circle text-primary me-2"></i> MOT:
                                    <?php if($driver->mot_expiry): ?>
                                        <span class="expiry-date <?php echo e($hasExpiredMOT ? 'expired' : ($motExpiringSoon ? 'expiring-soon' : 'valid')); ?>">
                                            <?php echo e($driver->mot_expiry->format('M d, Y')); ?>

                                            <?php if($hasExpiredMOT): ?>
                                                <i class="fas fa-exclamation-circle ms-1" title="Expired"></i>
                                            <?php elseif($motExpiringSoon): ?>
                                                <i class="fas fa-exclamation-triangle ms-1" title="Expiring soon"></i>
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="expiry-date expired">Missing</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="mb-2">
                                    <span class="badge bg-<?php echo e($driver->is_active ? 'success' : 'danger'); ?> p-2 w-100">
                                        <i class="fas fa-<?php echo e($driver->is_active ? 'check-circle' : 'times-circle'); ?> me-1"></i>
                                        <?php echo e($driver->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </div>
                                <div>
                                    <span class="badge bg-<?php echo e($driver->is_available ? 'info' : 'warning'); ?> p-2 w-100">
                                        <i class="fas fa-<?php echo e($driver->is_available ? 'user-check' : 'user-clock'); ?> me-1"></i>
                                        <?php echo e($driver->is_available ? 'Available' : 'Unavailable'); ?>

                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column gap-2">
                                    <a href="<?php echo e(route('admin.drivers.show', $driver->id)); ?>" class="btn btn-sm btn-outline-info w-100">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                    <a href="<?php echo e(route('admin.drivers.edit', $driver->id)); ?>" class="btn btn-sm btn-outline-primary w-100">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                    <div class="btn-group w-100">
                                        <form action="<?php echo e(route('admin.drivers.toggle-active', $driver->id)); ?>" method="POST" class="w-50">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-<?php echo e($driver->is_active ? 'warning' : 'success'); ?> w-100" title="<?php echo e($driver->is_active ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($driver->is_active ? 'ban' : 'check'); ?> me-1"></i> <?php echo e($driver->is_active ? 'Deactivate' : 'Activate'); ?>

                                            </button>
                                        </form>
                                        <form action="<?php echo e(route('admin.drivers.toggle-availability', $driver->id)); ?>" method="POST" class="w-50">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-<?php echo e($driver->is_available ? 'secondary' : 'info'); ?> w-100" title="<?php echo e($driver->is_available ? 'Mark Unavailable' : 'Mark Available'); ?>">
                                                <i class="fas fa-<?php echo e($driver->is_available ? 'times' : 'user-check'); ?> me-1"></i> <?php echo e($driver->is_available ? 'Unavailable' : 'Available'); ?>

                                            </button>
                                        </form>
                                    </div>
                                    <form action="<?php echo e(route('admin.drivers.destroy', $driver->id)); ?>" method="POST" class="delete-form">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger w-100" title="Delete">
                                            <i class="fas fa-trash me-1"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No drivers found</h5>
                                    <p class="text-muted">Try adjusting your search or filter criteria</p>
                                    <a href="<?php echo e(route('admin.drivers.create')); ?>" class="btn btn-primary mt-2">
                                        <i class="fas fa-plus me-1"></i> Add New Driver
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($drivers->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Confirm delete
        $('.delete-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);

            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the driver. This action cannot be undone!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.off('submit').submit();
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\drivers\index.blade.php ENDPATH**/ ?>