@extends('layouts.admin')

@section('title', 'Vehicle Reports')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-car me-2 text-primary"></i> Vehicle Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-primary" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Vehicle Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Vehicles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalVehicles }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-car fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Vehicles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activeVehicles }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vehicle Type Distribution -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vehicle Type Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="vehicleTypeChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vehicle Types Summary</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle Type</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($vehicleTypes as $type)
                                <tr>
                                    <td>{{ ucfirst($type->type) }}</td>
                                    <td>{{ $type->count }}</td>
                                    <td>{{ number_format(($type->count / $totalVehicles) * 100, 1) }}%</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Vehicles by Bookings -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Most Popular Vehicles</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Total Bookings</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topVehiclesByBookings as $vehicle)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($vehicle->image)
                                                <img src="{{ asset('storage/' . $vehicle->image) }}" 
                                                     class="rounded me-2" width="40" height="30" style="object-fit: cover;">
                                            @else
                                                <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-weight-bold">{{ $vehicle->name }}</div>
                                                <small class="text-muted">{{ $vehicle->type }} - {{ $vehicle->model }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $vehicle->bookings_count }}</span>
                                    </td>
                                    <td>
                                        @if($vehicle->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Vehicles by Revenue -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Revenue Generating Vehicles</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Total Revenue</th>
                                    <th>Avg. per Booking</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topVehiclesByRevenue as $vehicle)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($vehicle->image)
                                                <img src="{{ asset('storage/' . $vehicle->image) }}" 
                                                     class="rounded me-2" width="40" height="30" style="object-fit: cover;">
                                            @else
                                                <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 30px;">
                                                    <i class="fas fa-car text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-weight-bold">{{ $vehicle->name }}</div>
                                                <small class="text-muted">{{ $vehicle->seats }} seats</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-success font-weight-bold">
                                            ${{ number_format($vehicle->bookings_sum_amount ?? 0, 2) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $completedBookings = $vehicle->bookings()->where('status', 'completed')->count();
                                            $avgPerBooking = $completedBookings > 0 ? ($vehicle->bookings_sum_amount ?? 0) / $completedBookings : 0;
                                        @endphp
                                        <span class="text-info">
                                            ${{ number_format($avgPerBooking, 2) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Vehicle Type Distribution Chart
const vehicleTypeCtx = document.getElementById('vehicleTypeChart').getContext('2d');
const vehicleTypeChart = new Chart(vehicleTypeCtx, {
    type: 'doughnut',
    data: {
        labels: @json($vehicleTypes->pluck('type')->map(function($type) { return ucfirst($type); })),
        datasets: [{
            data: @json($vehicleTypes->pluck('count')),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
@endpush
@endsection
