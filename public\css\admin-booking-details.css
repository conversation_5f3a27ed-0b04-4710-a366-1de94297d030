/* Admin Booking Details Styles */

/* Status Badges */
.booking-status {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.6px;
    box-shadow: 0 3px 8px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.booking-status::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-pending {
    background-color: #fff8e1;
    color: #f57c00;
    border: none;
}

.status-pending::before {
    background-color: #f57c00;
    box-shadow: 0 0 0 2px rgba(245, 124, 0, 0.2);
}

.status-confirmed {
    background-color: #e3f2fd;
    color: #1976d2;
    border: none;
}

.status-confirmed::before {
    background-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.status-assigned {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: none;
}

.status-assigned::before {
    background-color: #7b1fa2;
    box-shadow: 0 0 0 2px rgba(123, 31, 162, 0.2);
}

.status-in_progress {
    background-color: #e8f5e9;
    color: #388e3c;
    border: none;
}

.status-in_progress::before {
    background-color: #388e3c;
    box-shadow: 0 0 0 2px rgba(56, 142, 60, 0.2);
}

.status-completed {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: none;
}

.status-completed::before {
    background-color: #2e7d32;
    box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
}

.status-cancelled {
    background-color: #ffebee;
    color: #c62828;
    border: none;
}

.status-cancelled::before {
    background-color: #c62828;
    box-shadow: 0 0 0 2px rgba(198, 40, 40, 0.2);
}

/* Card Styling */
.booking-details-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.06);
    overflow: hidden;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.booking-details-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transform: translateY(-3px);
}

.booking-details-card .card-header {
    background-color: #ee393d;
    color: white;
    border-bottom: none;
    padding: 18px 22px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.booking-details-card .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
    transform: skewX(-30deg) translateX(50%);
}

.booking-details-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
}

.booking-details-card .card-header h5 i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.booking-details-card .card-body {
    padding: 22px;
    background-color: #ffffff;
}

/* Booking Info Styling */
.booking-info-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.booking-info-value {
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
    line-height: 1.5;
    font-size: 1rem;
}

/* Map Container */
.map-container {
    height: 300px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

/* Timeline Styling */
.timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    padding-bottom: 25px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-icon {
    position: absolute;
    left: -30px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #e9ecef;
    text-align: center;
    line-height: 18px;
    font-size: 0.7rem;
    color: #6c757d;
    z-index: 1;
}

.timeline-icon.active {
    background-color: #4e73df;
    border-color: #4e73df;
    color: #fff;
    box-shadow: 0 0 0 4px rgba(78, 115, 223, 0.25);
}

.timeline-content {
    background-color: #f8f9fc;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.03);
    border-left: 3px solid #4e73df;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transform: translateY(-2px);
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #4e73df;
}

.timeline-date {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.timeline-description {
    font-size: 0.9rem;
    color: #5a5c69;
}

/* Payment Badges */
.payment-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.payment-completed {
    background-color: #d1e7dd;
    color: #0f5132;
}

.payment-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.payment-processing {
    background-color: #cff4fc;
    color: #055160;
}

.payment-failed {
    background-color: #f8d7da;
    color: #842029;
}

.payment-refunded {
    background-color: #e2e3e5;
    color: #41464b;
}

/* Table Styling */
.bookings-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0 5px;
}

.bookings-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 15px;
    border-top: none;
    color: #5a5c69;
}

.bookings-table td {
    padding: 15px;
    vertical-align: middle;
    background-color: white;
}

.bookings-table tr {
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.bookings-table tr:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* Tabs Styling */
.booking-tabs {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.booking-tabs .nav-link {
    padding: 15px 20px;
    font-weight: 600;
    color: #6c757d;
    border: none;
    border-radius: 0;
    position: relative;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.booking-tabs .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: transparent;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.booking-tabs .nav-link.active {
    color: #ee393d;
    background-color: rgba(238, 57, 61, 0.05);
    border-bottom: none;
}

.booking-tabs .nav-link.active::after {
    background-color: #ee393d;
    transform: scaleX(1);
}

.booking-tabs .nav-link:hover {
    color: #ee393d;
    background-color: rgba(238, 57, 61, 0.03);
}

.booking-tabs .nav-link:hover::after {
    background-color: rgba(238, 57, 61, 0.3);
    transform: scaleX(0.5);
}

.tab-content {
    padding: 5px;
    transition: all 0.3s ease;
}

.tab-pane {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fare Breakdown */
.fare-breakdown {
    background-color: #f8f9fc;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.fare-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.fare-item:last-child {
    border-bottom: none;
}

.fare-label {
    font-weight: 500;
    color: #5a5c69;
}

.fare-value {
    font-weight: 600;
    color: #333;
}

.fare-total {
    display: flex;
    justify-content: space-between;
    padding-top: 15px;
    margin-top: 10px;
    border-top: 2px solid #e9ecef;
}

.fare-total .fare-label {
    font-weight: 700;
    font-size: 1.1rem;
    color: #333;
}

.fare-total .fare-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #4e73df;
}

/* Trip Card Styling */
.trip-card {
    border-radius: 10px;
    background-color: #f8f9fc;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.trip-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.pickup-icon, .dropoff-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1;
}

.pickup-icon i {
    color: #28a745;
}

.dropoff-icon i {
    color: #dc3545;
}

.trip-line {
    width: 2px;
    height: 50px;
    background-color: #dee2e6;
    margin: 5px 0;
}

.trip-locations {
    flex: 1;
}

.trip-details {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.trip-detail {
    text-align: center;
}

/* Client Card Styling */
.client-card {
    border-radius: 12px;
    background-color: #ffffff;
    padding: 18px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.04);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.client-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #ee393d 0%, #e5b429 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.client-card:hover {
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    transform: translateY(-3px);
}

.client-card:hover::after {
    transform: scaleX(1);
}

/* Vehicle Card Styling */
.vehicle-card {
    border-radius: 12px;
    background-color: #ffffff;
    padding: 18px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.04);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.vehicle-card::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    background-color: rgba(238, 57, 61, 0.05);
    border-radius: 50%;
    z-index: 0;
}

.vehicle-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.vehicle-features {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
}

.vehicle-features span {
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.vehicle-features span:hover {
    background-color: #ee393d;
    color: white;
    transform: translateY(-2px);
}

/* Driver Card */
.driver-card {
    border-radius: 12px;
    background-color: #ffffff;
    padding: 18px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.04);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.driver-card::before {
    content: '';
    position: absolute;
    bottom: -20px;
    left: -20px;
    width: 80px;
    height: 80px;
    background-color: rgba(229, 180, 41, 0.05);
    border-radius: 50%;
    z-index: 0;
}

.driver-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.driver-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 3px solid #fff;
    transition: all 0.3s ease;
}

.driver-card:hover .driver-avatar {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
}

.driver-info {
    text-align: center;
    position: relative;
    z-index: 1;
}

.driver-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: #333;
}

.driver-contact {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.driver-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

/* Progress Bar Styling */
.booking-progress {
    margin-top: 25px;
    margin-bottom: 35px;
    padding: 0 10px;
}

.progress {
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.progress-bar {
    background-color: #ee393d;
    transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 100%);
}

.progress-step {
    text-align: center;
    position: relative;
    width: 20%;
}

.progress-marker {
    width: 24px;
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 50%;
    margin: 0 auto 8px;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef, 0 2px 5px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1;
    transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-marker::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #fff;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.progress-step.active .progress-marker {
    background-color: #ee393d;
    box-shadow: 0 0 0 2px rgba(238, 57, 61, 0.3), 0 3px 8px rgba(238, 57, 61, 0.2);
    transform: scale(1.1);
}

.progress-step.active .progress-marker::after {
    opacity: 1;
    transform: scale(1);
}

.progress-step.active .progress-text {
    color: #ee393d;
    font-weight: 600;
}

/* Booking Header */
.booking-details-header {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.06);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border-left: 4px solid #ee393d;
}

.booking-details-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(238,57,61,0.05) 0%, rgba(238,57,61,0) 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
    z-index: 0;
}

.booking-details-header h2 {
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.booking-details-header p {
    color: #6c757d;
    position: relative;
    z-index: 1;
}

/* Booking Summary */
.booking-summary {
    margin-bottom: 25px;
}

.summary-item {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.summary-item:hover {
    background-color: rgba(248, 249, 250, 0.5);
    padding-left: 5px;
    padding-right: 5px;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.summary-value {
    font-weight: 600;
    color: #333;
    text-align: right;
    transition: all 0.2s ease;
}

.summary-item:hover .summary-value {
    transform: scale(1.05);
}

/* Route Stats */
.route-stat-card {
    transition: all 0.3s ease;
}

.route-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.route-stat-icon {
    color: #4e73df;
}

.route-stat-value {
    font-size: 1.2rem;
    color: #333;
}

.route-stat-label {
    color: #6c757d;
}

/* Print Show Class */
.print-show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        font-size: 12pt;
        color: #000;
        background: #fff;
    }

    .container-fluid {
        width: 100%;
        padding: 0;
        margin: 0;
    }

    .booking-details-header {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
        padding: 15px !important;
        margin-bottom: 20px !important;
    }

    .booking-details-card {
        box-shadow: none !important;
        margin-bottom: 20px !important;
        page-break-inside: avoid;
        border: 1px solid #dee2e6 !important;
    }

    .booking-details-card .card-header {
        background: #f8f9fa !important;
        color: #333 !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .booking-details-card .card-header h5 {
        color: #333 !important;
    }

    .booking-status {
        border: 1px solid #dee2e6 !important;
        background-color: #f8f9fa !important;
        color: #333 !important;
    }

    .tab-content > .tab-pane {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .nav-tabs {
        display: none !important;
    }

    .map-container {
        height: 200px !important;
        border: 1px solid #dee2e6 !important;
    }

    .timeline::before {
        background-color: #aaa !important;
    }

    .timeline-icon {
        border-color: #aaa !important;
        background-color: #fff !important;
        color: #333 !important;
    }

    .timeline-icon.active {
        background-color: #aaa !important;
        color: #fff !important;
    }

    .timeline-content {
        border-left-color: #aaa !important;
        background-color: #fff !important;
    }

    .fare-breakdown {
        border: 1px solid #dee2e6 !important;
    }

    .fare-item {
        border-bottom-color: #dee2e6 !important;
    }

    .fare-total {
        border-top-color: #333 !important;
    }

    .btn {
        display: none !important;
    }

    a[href]:after {
        content: none !important;
    }

    /* Add page breaks */
    .tab-pane {
        page-break-after: always;
    }

    .tab-pane:last-child {
        page-break-after: avoid;
    }

    /* Print header on each page */
    .booking-details-header {
        position: running(header);
    }

    @page {
        margin: 2cm;
        @top-center {
            content: element(header);
        }
        @bottom-center {
            content: "Page " counter(page) " of " counter(pages);
        }
    }

    /* Company info for invoice-like appearance */
    .booking-details-header:before {
        content: "Ynr Cars - Booking Details";
        display: block;
        text-align: center;
        font-size: 18pt;
        font-weight: bold;
        margin-bottom: 10px;
    }
}

/* Link Styling */
a {
    color: #4e73df;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: #224abe;
    text-decoration: none;
}

a.btn {
    font-weight: 500;
    letter-spacing: 0.3px;
    padding: 8px 16px;
    border-radius: 5px;
}

a.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

a.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
}

a.btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

a.btn-outline-primary:hover {
    background-color: #4e73df;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(78, 115, 223, 0.2);
}

a.btn-secondary {
    background-color: #858796;
    border-color: #858796;
    color: white;
}

a.btn-secondary:hover {
    background-color: #717384;
    border-color: #6b6d7d;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(133, 135, 150, 0.3);
}

a.btn-outline-secondary {
    color: #858796;
    border-color: #858796;
}

a.btn-outline-secondary:hover {
    background-color: #858796;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(133, 135, 150, 0.2);
}

a.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
    color: white;
}

a.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(54, 185, 204, 0.3);
}

a.btn-sm {
    padding: 5px 10px;
    font-size: 0.85rem;
}

/* Text links with underline effect */
a.text-link {
    position: relative;
    display: inline-block;
    padding-bottom: 2px;
}

a.text-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #4e73df;
    transition: width 0.3s ease;
}

a.text-link:hover::after {
    width: 100%;
}

/* Icon links */
a.icon-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

a.icon-link i {
    transition: transform 0.3s ease;
}

a.icon-link:hover i {
    transform: translateX(3px);
}

/* Booking Actions */
.booking-actions {
    margin-top: 20px;
}

.booking-actions .btn {
    margin-bottom: 10px;
}

.payment-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.payment-actions .btn {
    flex: 1;
}
