<?php

namespace App\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class LogChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        if (method_exists($notification, 'toMail')) {
            $message = $notification->toMail($notifiable);
            
            Log::info('Notification sent', [
                'to' => $notifiable->email ?? 'No email',
                'subject' => $message->subject ?? 'No subject',
                'notification' => get_class($notification),
                'notifiable' => get_class($notifiable),
            ]);
        } else {
            Log::info('Notification sent (no mail method)', [
                'notification' => get_class($notification),
                'notifiable' => get_class($notifiable),
            ]);
        }
    }
}
