<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailPreference extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'preference_type',
        'enabled',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'enabled' => 'boolean',
        'settings' => 'json',
    ];

    /**
     * Available email preference types
     */
    const PREFERENCE_TYPES = [
        'booking_confirmations' => 'Booking Confirmations',
        'booking_reminders' => 'Booking Reminders',
        'payment_confirmations' => 'Payment Confirmations',
        'driver_assignments' => 'Driver Assignments',
        'status_updates' => 'Status Updates',
        'cancellation_notices' => 'Cancellation Notices',
        'promotional_emails' => 'Promotional Emails',
        'system_notifications' => 'System Notifications',
        'newsletter' => 'Newsletter',
        'ride_requests' => 'Ride Requests (Drivers)',
        'earnings_reports' => 'Earnings Reports (Drivers)',
    ];

    /**
     * Get the user that owns the email preference
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if user has enabled a specific email preference
     *
     * @param int $userId
     * @param string $preferenceType
     * @return bool
     */
    public static function isEnabled($userId, $preferenceType)
    {
        $preference = self::where('user_id', $userId)
            ->where('preference_type', $preferenceType)
            ->first();

        // If no preference exists, default to enabled for essential emails
        if (!$preference) {
            return in_array($preferenceType, [
                'booking_confirmations',
                'payment_confirmations',
                'driver_assignments',
                'status_updates',
                'cancellation_notices',
                'ride_requests',
            ]);
        }

        return $preference->enabled;
    }

    /**
     * Set email preference for a user
     *
     * @param int $userId
     * @param string $preferenceType
     * @param bool $enabled
     * @param array $settings
     * @return EmailPreference
     */
    public static function setPreference($userId, $preferenceType, $enabled, $settings = [])
    {
        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'preference_type' => $preferenceType,
            ],
            [
                'enabled' => $enabled,
                'settings' => $settings,
            ]
        );
    }

    /**
     * Get all preferences for a user
     *
     * @param int $userId
     * @return array
     */
    public static function getUserPreferences($userId)
    {
        $preferences = self::where('user_id', $userId)->get()->keyBy('preference_type');
        $result = [];

        foreach (self::PREFERENCE_TYPES as $type => $label) {
            $preference = $preferences->get($type);
            $result[$type] = [
                'label' => $label,
                'enabled' => $preference ? $preference->enabled : self::getDefaultEnabled($type),
                'settings' => $preference ? $preference->settings : [],
            ];
        }

        return $result;
    }

    /**
     * Get default enabled status for a preference type
     *
     * @param string $preferenceType
     * @return bool
     */
    private static function getDefaultEnabled($preferenceType)
    {
        // Essential emails are enabled by default
        $essentialTypes = [
            'booking_confirmations',
            'payment_confirmations',
            'driver_assignments',
            'status_updates',
            'cancellation_notices',
            'ride_requests',
        ];

        return in_array($preferenceType, $essentialTypes);
    }

    /**
     * Get preferences for a specific user role
     *
     * @param string $role
     * @return array
     */
    public static function getPreferencesForRole($role)
    {
        $allPreferences = self::PREFERENCE_TYPES;

        switch ($role) {
            case 'client':
                return array_filter($allPreferences, function($key) {
                    return !in_array($key, ['ride_requests', 'earnings_reports']);
                }, ARRAY_FILTER_USE_KEY);

            case 'driver':
                return array_filter($allPreferences, function($key) {
                    return !in_array($key, ['booking_reminders', 'driver_assignments']);
                }, ARRAY_FILTER_USE_KEY);

            case 'admin':
                return $allPreferences;

            default:
                return [];
        }
    }

    /**
     * Bulk update preferences for a user
     *
     * @param int $userId
     * @param array $preferences
     * @return void
     */
    public static function updateUserPreferences($userId, array $preferences)
    {
        foreach ($preferences as $type => $data) {
            if (array_key_exists($type, self::PREFERENCE_TYPES)) {
                self::setPreference(
                    $userId,
                    $type,
                    $data['enabled'] ?? false,
                    $data['settings'] ?? []
                );
            }
        }
    }

    /**
     * Check if user can receive a specific email type
     *
     * @param User $user
     * @param string $emailType
     * @return bool
     */
    public static function canReceiveEmail(User $user, $emailType)
    {
        // Always allow essential emails
        $essentialTypes = [
            'booking_confirmations',
            'payment_confirmations',
            'cancellation_notices',
            'ride_requests',
        ];

        if (in_array($emailType, $essentialTypes)) {
            return true;
        }

        return self::isEnabled($user->id, $emailType);
    }

    /**
     * Get email frequency setting for a preference
     *
     * @param int $userId
     * @param string $preferenceType
     * @return string
     */
    public static function getFrequencySetting($userId, $preferenceType)
    {
        $preference = self::where('user_id', $userId)
            ->where('preference_type', $preferenceType)
            ->first();

        if (!$preference || !isset($preference->settings['frequency'])) {
            return 'immediate'; // Default frequency
        }

        return $preference->settings['frequency'];
    }
}
