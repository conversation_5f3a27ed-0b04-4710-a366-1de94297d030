@extends('layouts.admin')

@section('title', 'Edit Review')

@section('styles')
<style>
    .edit-review-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .edit-review-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        padding: 20px;
    }

    .edit-review-body {
        padding: 30px;
    }

    .edit-review-footer {
        background-color: #f8f9fa;
        padding: 20px;
        border-top: 1px solid #eee;
    }

    .booking-info-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .booking-info-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .booking-info-body {
        padding: 20px;
    }

    .booking-detail {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .booking-detail:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .booking-detail-label {
        font-weight: 600;
        color: #495057;
    }

    .star-rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        cursor: pointer;
        width: 40px;
        height: 40px;
        margin-right: 5px;
        position: relative;
        font-size: 30px;
    }

    .star-rating label:before {
        content: '\f005';
        font-family: 'Font Awesome 5 Free';
        font-weight: 400;
        position: absolute;
        top: 0;
        left: 0;
        color: #e0e0e0;
    }

    .star-rating input:checked ~ label:before,
    .star-rating label:hover:before,
    .star-rating label:hover ~ label:before {
        content: '\f005';
        font-weight: 900;
        color: #ffc107;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-edit me-2"></i> Edit Review</h2>
        <div>
            <a href="{{ route('admin.reviews.show', $booking->id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Review
            </a>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <!-- Edit Review Form -->
        <div class="col-md-8">
            <div class="edit-review-card">
                <div class="edit-review-header">
                    <h4 class="mb-2">Edit Review for Booking #{{ $booking->booking_number }}</h4>
                    <p class="mb-0">By {{ $booking->user->name }} on {{ $booking->reviewed_at->format('F d, Y h:i A') }}</p>
                </div>
                <div class="edit-review-body">
                    <form action="{{ route('admin.reviews.update', $booking->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label class="form-label">Rating</label>
                            <div class="star-rating mb-3">
                                <input type="radio" id="star5" name="rating" value="5" {{ $booking->rating == 5 ? 'checked' : '' }}>
                                <label for="star5" title="5 stars"></label>
                                
                                <input type="radio" id="star4" name="rating" value="4" {{ $booking->rating == 4 ? 'checked' : '' }}>
                                <label for="star4" title="4 stars"></label>
                                
                                <input type="radio" id="star3" name="rating" value="3" {{ $booking->rating == 3 ? 'checked' : '' }}>
                                <label for="star3" title="3 stars"></label>
                                
                                <input type="radio" id="star2" name="rating" value="2" {{ $booking->rating == 2 ? 'checked' : '' }}>
                                <label for="star2" title="2 stars"></label>
                                
                                <input type="radio" id="star1" name="rating" value="1" {{ $booking->rating == 1 ? 'checked' : '' }}>
                                <label for="star1" title="1 star"></label>
                            </div>
                            <div class="rating-value" id="rating-text">
                                {{ $booking->rating }} Star{{ $booking->rating != 1 ? 's' : '' }} - 
                                @if($booking->rating == 5)
                                    Excellent
                                @elseif($booking->rating == 4)
                                    Very Good
                                @elseif($booking->rating == 3)
                                    Good
                                @elseif($booking->rating == 2)
                                    Fair
                                @else
                                    Poor
                                @endif
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="review" class="form-label">Review Content</label>
                            <textarea class="form-control" id="review" name="review" rows="6" required>{{ old('review', $booking->review) }}</textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Update Review
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Booking Information -->
        <div class="col-md-4">
            <div class="booking-info-card">
                <div class="booking-info-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Information</h5>
                </div>
                <div class="booking-info-body">
                    <div class="booking-detail">
                        <div class="booking-detail-label">Client</div>
                        <div>{{ $booking->user->name }}</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Vehicle</div>
                        <div>{{ $booking->vehicle->name }} ({{ $booking->vehicle->type }})</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Booking Type</div>
                        <div>{{ ucfirst(str_replace('_', ' ', $booking->booking_type)) }}</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Pickup Date & Time</div>
                        <div>{{ $booking->pickup_date->format('M d, Y h:i A') }}</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Status</div>
                        <div><span class="badge bg-{{ $booking->status === 'completed' ? 'success' : 'primary' }}">{{ ucfirst($booking->status) }}</span></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Amount</div>
                        <div>{{ $currencySymbol ?? '£' }}{{ number_format($booking->amount, 2) }}</div>
                    </div>

                    @if($booking->driver)
                    <div class="booking-detail">
                        <div class="booking-detail-label">Driver</div>
                        <div>{{ $booking->driver->name }}</div>
                    </div>
                    @endif

                    <div class="mt-4">
                        <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-info w-100">
                            <i class="fas fa-calendar-check me-2"></i> View Full Booking Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update rating text when a star is selected
        const ratingInputs = document.querySelectorAll('.star-rating input');
        const ratingText = document.getElementById('rating-text');
        const ratingDescriptions = [
            '',
            '1 Star - Poor',
            '2 Stars - Fair',
            '3 Stars - Good',
            '4 Stars - Very Good',
            '5 Stars - Excellent'
        ];

        ratingInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = parseInt(this.value);
                ratingText.textContent = ratingDescriptions[value];
            });
        });
    });
</script>
@endsection
