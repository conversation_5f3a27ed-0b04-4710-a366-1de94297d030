<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'booking_number' => $this->booking_number,
            'booking_type' => $this->booking_type,
            'status' => $this->status,
            'payment_status' => $this->payment_status,
            'pickup_address' => $this->pickup_address,
            'pickup_coordinates' => [
                'lat' => $this->pickup_lat,
                'lng' => $this->pickup_lng,
            ],
            'dropoff_address' => $this->dropoff_address,
            'dropoff_coordinates' => [
                'lat' => $this->dropoff_lat,
                'lng' => $this->dropoff_lng,
            ],
            'pickup_date' => $this->pickup_date?->toISOString(),
            'return_date' => $this->return_date?->toISOString(),
            'duration_hours' => $this->duration_hours,
            'distance' => $this->distance,
            'distance_value' => $this->distance_value,
            'duration_value' => $this->duration_value,
            'amount' => $this->amount,
            'formatted_amount' => $this->formatted_amount,
            'notes' => $this->notes,
            'rating' => $this->rating,
            'review' => $this->review,
            'fare_details' => $this->fare_details,
            'timestamps' => [
                'created_at' => $this->created_at?->toISOString(),
                'updated_at' => $this->updated_at?->toISOString(),
                'started_at' => $this->started_at?->toISOString(),
                'completed_at' => $this->completed_at?->toISOString(),
                'cancelled_at' => $this->cancelled_at?->toISOString(),
                'reviewed_at' => $this->reviewed_at?->toISOString(),
                'payment_processed_at' => $this->payment_processed_at?->toISOString(),
                'reminder_sent_at' => $this->reminder_sent_at?->toISOString(),
            ],
            'cancellation' => [
                'reason' => $this->cancellation_reason,
                'cancelled_by' => $this->cancelled_by,
            ],
            'relationships' => [
                'user' => new UserResource($this->whenLoaded('user')),
                'vehicle' => new VehicleResource($this->whenLoaded('vehicle')),
                'driver' => new UserResource($this->whenLoaded('driver')),
                'payment' => new PaymentResource($this->whenLoaded('payment')),
                'payments' => PaymentResource::collection($this->whenLoaded('payments')),
                'history' => BookingHistoryResource::collection($this->whenLoaded('history')),
            ],
        ];
    }
}
