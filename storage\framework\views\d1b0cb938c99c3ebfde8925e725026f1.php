<?php $__env->startSection('title', 'Booking Confirmation'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .confirmation-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .confirmation-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .confirmation-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .confirmation-card .card-body {
        padding: 30px;
    }

    .success-icon {
        font-size: 5rem;
        color: #28a745;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .booking-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .booking-detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .booking-detail-row:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .booking-detail-row span:first-child {
        font-weight: 600;
        color: #495057;
    }

    .payment-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .payment-detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .payment-detail-row:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .payment-detail-row span:first-child {
        font-weight: 600;
        color: #495057;
    }

    .booking-status {
        display: inline-block;
        padding: 8px 20px;
        border-radius: 30px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .status-confirmed {
        background-color: #d4edda;
        color: #155724;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #000;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e5b429;
        border-color: #e5b429;
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    #map {
        border: 1px solid #ddd;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        border-radius: 10px;
        overflow: hidden;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="confirmation-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card confirmation-card">
                    <div class="card-header">
                        <h3 class="mb-0">Booking Confirmation</h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-check-circle success-icon"></i>
                            <h4 class="mb-3">Thank You for Your Booking!</h4>
                            <p class="mb-4">Your booking has been confirmed. We've sent a confirmation email to your registered email address.</p>

                            <div class="d-inline-block mb-4">
                                <span class="booking-status status-confirmed"><?php echo e(ucfirst($booking->status)); ?></span>
                            </div>
                        </div>

                        <div class="booking-details">
                            <h5 class="mb-3">Booking Details</h5>
                            <div class="booking-detail-row">
                                <span>Booking Number:</span>
                                <span><?php echo e($booking->booking_number); ?></span>
                            </div>
                            <div class="booking-detail-row">
                                <span>Vehicle:</span>
                                <span><?php echo e($booking->vehicle->name); ?></span>
                            </div>
                            <?php if($booking->booking_type === 'airport_transfer'): ?>
                                <div class="booking-detail-row">
                                    <span>Transfer Type:</span>
                                    <span><?php echo e(ucfirst(str_replace('_', ' ', $booking->airport_direction))); ?></span>
                                </div>
                                <?php if($booking->pickupAirport): ?>
                                    <div class="booking-detail-row">
                                        <span>From Airport:</span>
                                        <span><?php echo e($booking->pickupAirport->name); ?> (<?php echo e($booking->pickupAirport->code); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->dropoffAirport): ?>
                                    <div class="booking-detail-row">
                                        <span>To Airport:</span>
                                        <span><?php echo e($booking->dropoffAirport->name); ?> (<?php echo e($booking->dropoffAirport->code); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->airport_direction === 'to_airport'): ?>
                                    <div class="booking-detail-row">
                                        <span>Pickup:</span>
                                        <span><?php echo e($booking->pickup_address); ?></span>
                                    </div>
                                <?php else: ?>
                                    <div class="booking-detail-row">
                                        <span>Dropoff:</span>
                                        <span><?php echo e($booking->dropoff_address); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->airport_surcharge > 0): ?>
                                    <div class="booking-detail-row">
                                        <span>Airport Surcharge:</span>
                                        <span><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->airport_surcharge, 2)); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="booking-detail-row">
                                    <span>Pickup:</span>
                                    <span><?php echo e($booking->pickup_address); ?></span>
                                </div>
                                <?php if($booking->booking_type !== 'hourly'): ?>
                                    <div class="booking-detail-row">
                                        <span>Dropoff:</span>
                                        <span><?php echo e($booking->dropoff_address); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <div class="booking-detail-row">
                                <span>Date & Time:</span>
                                <span><?php echo e($booking->pickup_date->format('M d, Y h:i A')); ?></span>
                            </div>
                            <?php if($booking->booking_type === 'return' && $booking->return_date): ?>
                                <div class="booking-detail-row">
                                    <span>Return Date:</span>
                                    <span><?php echo e($booking->return_date->format('M d, Y h:i A')); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if($booking->booking_type === 'hourly' && $booking->duration_hours): ?>
                                <div class="booking-detail-row">
                                    <span>Duration:</span>
                                    <span><?php echo e($booking->duration_hours); ?> <?php echo e($booking->duration_hours > 1 ? 'hours' : 'hour'); ?></span>
                                </div>
                            <?php endif; ?>

                            <!-- Google Map -->
                            <?php if($booking->booking_type !== 'hourly'): ?>
                                <div class="mt-4">
                                    <h5 class="mb-3">Route Map</h5>
                                    <div id="map" style="height: 300px; width: 100%; border-radius: 8px;"></div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="payment-details">
                            <h5 class="mb-3">Payment Details</h5>
                            <?php if($booking->payment): ?>
                                <div class="payment-detail-row">
                                    <span>Payment Method:</span>
                                    <span><?php echo e(ucfirst($booking->payment->payment_method)); ?></span>
                                </div>
                                <div class="payment-detail-row">
                                    <span>Transaction ID:</span>
                                    <span><?php echo e($booking->payment->transaction_id); ?></span>
                                </div>
                                <div class="payment-detail-row">
                                    <span>Amount:</span>
                                    <span><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->payment->amount, 2)); ?></span>
                                </div>
                                <div class="payment-detail-row">
                                    <span>Status:</span>
                                    <span><?php echo e(ucfirst($booking->payment->status)); ?></span>
                                </div>
                            <?php else: ?>
                                <div class="payment-detail-row">
                                    <span>Payment Status:</span>
                                    <span>Pending</span>
                                </div>
                                <div class="payment-detail-row">
                                    <span>Amount Due:</span>
                                    <span><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->amount, 2)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <p class="mb-4">If you have any questions or need to make changes to your booking, please contact our customer service.</p>

                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <?php if($booking->payment && $booking->payment->status === 'completed'): ?>
                                    <a href="<?php echo e(route('booking.track', $booking->id)); ?>" class="btn btn-primary">
                                        <i class="fas fa-map-marker-alt me-2"></i>Track Your Booking
                                    </a>
                                    <a href="<?php echo e(route('client.dashboard')); ?>" class="btn btn-outline-secondary">Go to Dashboard</a>
                                <?php else: ?>
                                    <a href="<?php echo e(route('booking.payment', $booking->id)); ?>" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-2"></i>Next: Proceed to Payment
                                    </a>
                                    <a href="<?php echo e(route('client.dashboard')); ?>" class="btn btn-outline-secondary">Go to Dashboard</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Google Maps JavaScript API -->
<?php
    $googleMapsApiKey = \App\Services\SettingsService::getGoogleMapsApiKey();
?>
<?php if($googleMapsApiKey): ?>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&callback=initMap" async defer></script>
<?php else: ?>
<script>
    console.error('Google Maps API key is not configured. Please set it in the admin settings.');
</script>
<?php endif; ?>
<script>
    function initMap() {
        <?php if($booking->booking_type !== 'hourly' && $booking->pickup_lat && $booking->pickup_lng && $booking->dropoff_lat && $booking->dropoff_lng): ?>
            // Create map
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> }
            });

            // Create markers for pickup and dropoff
            const pickupMarker = new google.maps.Marker({
                position: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> },
                map: map,
                title: 'Pickup Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
                }
            });

            const dropoffMarker = new google.maps.Marker({
                position: { lat: <?php echo e($booking->dropoff_lat); ?>, lng: <?php echo e($booking->dropoff_lng); ?> },
                map: map,
                title: 'Dropoff Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
                }
            });

            // Create route
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            // Get directions
            directionsService.route({
                origin: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> },
                destination: { lat: <?php echo e($booking->dropoff_lat); ?>, lng: <?php echo e($booking->dropoff_lng); ?> },
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);

                    // Add info windows
                    const pickupInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Pickup:</strong> <?php echo e($booking->pickup_address); ?></div>'
                    });

                    const dropoffInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Dropoff:</strong> <?php echo e($booking->dropoff_address); ?></div>'
                    });

                    pickupMarker.addListener('click', function() {
                        pickupInfoWindow.open(map, pickupMarker);
                    });

                    dropoffMarker.addListener('click', function() {
                        dropoffInfoWindow.open(map, dropoffMarker);
                    });

                    // Open pickup info window by default
                    pickupInfoWindow.open(map, pickupMarker);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        <?php elseif($booking->booking_type !== 'hourly'): ?>
            // If we don't have coordinates, use the addresses
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: 51.5074, lng: -0.1278 } // Default to London
            });

            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            directionsService.route({
                origin: '<?php echo e($booking->pickup_address); ?>',
                destination: '<?php echo e($booking->dropoff_address); ?>',
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        <?php endif; ?>
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\booking\confirmation.blade.php ENDPATH**/ ?>