<?php $__env->startSection('title', 'Payment Logs'); ?>

<?php $__env->startSection('payment-logs-styles'); ?>
<style>
    .payment-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .payment-card:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .payment-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .payment-card .card-body {
        padding: 0;
    }

    .payment-table {
        margin-bottom: 0;
    }

    .payment-table th,
    .payment-table td {
        padding: 15px 20px;
        vertical-align: middle;
    }

    .payment-table tbody tr {
        transition: background-color 0.3s;
    }

    .payment-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .stats-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        padding: 20px;
        text-align: center;
        transition: transform 0.3s;
        background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-bottom: 15px;
        color: #ee393d;
        transition: transform 0.3s;
    }

    .stats-card:hover .stats-icon {
        transform: scale(1.2);
    }

    .stats-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-label {
        color: #6c757d;
        font-weight: 500;
    }

    .filter-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .filter-form .form-control,
    .filter-form .form-select {
        border-radius: 5px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .filter-form .form-control:focus,
    .filter-form .form-select:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
    }

    .filter-form .btn {
        border-radius: 5px;
        transition: all 0.3s;
    }

    .filter-form .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
    }

    .filter-form .btn-primary:hover {
        background-color: #d62f33;
        border-color: #d62f33;
    }

    .payment-actions {
        display: flex;
        gap: 5px;
    }

    .payment-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .empty-state-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .empty-state-text {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-title', 'Payment Logs'); ?>

<?php $__env->startSection('payment-logs-actions'); ?>
<div class="btn-group">
    <a href="<?php echo e(route('admin.payments.report')); ?>" class="btn btn-outline-primary">
        <i class="fas fa-chart-bar me-1"></i> Payment Reports
    </a>
    <a href="<?php echo e(route('admin.payments.export')); ?>" class="btn btn-outline-primary">
        <i class="fas fa-file-export me-1"></i> Export
    </a>
    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-1"></i> Print
    </button>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('payment-logs-content'); ?>
<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Payment Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stats-value"><?php echo e($stats['total_payments']); ?></div>
            <div class="stats-label">Total Payments</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($stats['total_amount'], 2)); ?></div>
            <div class="stats-label">Total Revenue</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-value"><?php echo e($stats['completed_payments']); ?></div>
            <div class="stats-label">Completed Payments</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-value"><?php echo e($stats['pending_payments']); ?></div>
            <div class="stats-label">Pending Payments</div>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="filter-form">
    <form action="<?php echo e(route('admin.payments.index')); ?>" method="GET">
        <div class="row">
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">All Methods</option>
                        <option value="paypal" <?php echo e(request('payment_method') == 'paypal' ? 'selected' : ''); ?>>PayPal</option>
                        <option value="paypal_card" <?php echo e(request('payment_method') == 'paypal_card' ? 'selected' : ''); ?>>PayPal Card</option>
                        <option value="credit_card" <?php echo e(request('payment_method') == 'credit_card' ? 'selected' : ''); ?>>Credit Card</option>
                        <option value="cash" <?php echo e(request('payment_method') == 'cash' ? 'selected' : ''); ?>>Cash</option>
                        <option value="bank_transfer" <?php echo e(request('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>Bank Transfer</option>
                        <option value="other" <?php echo e(request('payment_method') == 'other' ? 'selected' : ''); ?>>Other</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Processing</option>
                        <option value="failed" <?php echo e(request('status') == 'failed' ? 'selected' : ''); ?>>Failed</option>
                        <option value="refunded" <?php echo e(request('status') == 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                        <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="search" class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" placeholder="Search by transaction ID, booking number or client name" value="<?php echo e(request('search')); ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <div class="mb-3 w-100 d-flex gap-2">
                    <button type="submit" class="btn btn-primary flex-grow-1">
                        <i class="fas fa-filter me-2"></i> Apply Filters
                    </button>
                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Payments Table -->
<div class="card payment-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0">Payment Records</h4>
        <span>Total: <?php echo e($payments->total()); ?></span>
    </div>
    <div class="card-body">
        <?php if($payments->isEmpty()): ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h4 class="empty-state-title">No Payment Records Found</h4>
                <p class="empty-state-text">There are no payment records matching your search criteria.</p>
                <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-undo me-1"></i> Clear Filters
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table payment-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Transaction ID</th>
                            <th>Booking</th>
                            <th>Client</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($payment->id); ?></td>
                                <td><?php echo e($payment->transaction_id); ?></td>
                                <td><?php echo e($payment->booking->booking_number); ?></td>
                                <td><?php echo e($payment->booking->user->name); ?></td>
                                <td><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($payment->amount, 2)); ?></td>
                                <td>
                                    <span class="method-badge method-<?php echo e($payment->payment_method); ?>">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?>

                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo e($payment->status); ?>">
                                        <?php echo e(ucfirst($payment->status)); ?>

                                    </span>
                                </td>
                                <td><?php echo e($payment->created_at ? $payment->created_at->format('M d, Y H:i') : 'N/A'); ?></td>
                                <td>
                                    <div class="payment-actions">
                                        <a href="<?php echo e(route('admin.payments.show', $payment->id)); ?>" class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if($payment->status == 'completed'): ?>
                                            <a href="<?php echo e(route('admin.payments.invoice', $payment->id)); ?>" class="btn btn-sm btn-outline-primary" title="View Invoice">
                                                <i class="fas fa-file-invoice"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('admin.bookings.show', $payment->booking->id)); ?>" class="btn btn-sm btn-outline-secondary" title="View Booking">
                                            <i class="fas fa-car"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <?php echo e($payments->withQueryString()->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.payment-logs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/payments/index.blade.php ENDPATH**/ ?>