<?php

/**
 * YNR Cars Email System Test Script
 * 
 * This script tests the email system functionality
 * Run with: php test-email-system.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\EmailService;
use App\Models\EmailLog;
use App\Models\EmailPreference;
use App\Models\User;

echo "🚗📧 YNR Cars Email System Test\n";
echo "===============================\n\n";

// Test 1: Check if EmailService class exists
echo "1. Testing EmailService class...\n";
if (class_exists('App\Services\EmailService')) {
    echo "   ✅ EmailService class found\n";
} else {
    echo "   ❌ EmailService class not found\n";
    exit(1);
}

// Test 2: Check if EmailLog model exists
echo "2. Testing EmailLog model...\n";
if (class_exists('App\Models\EmailLog')) {
    echo "   ✅ EmailLog model found\n";
} else {
    echo "   ❌ EmailLog model not found\n";
    exit(1);
}

// Test 3: Check if EmailPreference model exists
echo "3. Testing EmailPreference model...\n";
if (class_exists('App\Models\EmailPreference')) {
    echo "   ✅ EmailPreference model found\n";
} else {
    echo "   ❌ EmailPreference model not found\n";
    exit(1);
}

// Test 4: Check database tables
echo "4. Testing database tables...\n";
try {
    $emailLogsCount = EmailLog::count();
    echo "   ✅ email_logs table accessible (records: $emailLogsCount)\n";
} catch (Exception $e) {
    echo "   ❌ email_logs table not accessible: " . $e->getMessage() . "\n";
}

try {
    $preferencesCount = EmailPreference::count();
    echo "   ✅ email_preferences table accessible (records: $preferencesCount)\n";
} catch (Exception $e) {
    echo "   ❌ email_preferences table not accessible: " . $e->getMessage() . "\n";
}

// Test 5: Check EmailService methods
echo "5. Testing EmailService methods...\n";
$methods = [
    'sendBookingConfirmation',
    'sendBookingReminder',
    'sendPaymentConfirmation',
    'sendDriverAssignment',
    'sendWelcomeEmail',
    'sendBookingStatusUpdate',
    'sendContactFormSubmission',
    'sendBulkEmail',
    'sendTestEmail'
];

foreach ($methods as $method) {
    if (method_exists('App\Services\EmailService', $method)) {
        echo "   ✅ $method method found\n";
    } else {
        echo "   ❌ $method method not found\n";
    }
}

// Test 6: Check Mail classes
echo "6. Testing Mail classes...\n";
$mailClasses = [
    'App\Mail\BookingConfirmation',
    'App\Mail\BookingReminder',
    'App\Mail\PaymentConfirmation',
    'App\Mail\DriverAssignment',
    'App\Mail\WelcomeEmail',
    'App\Mail\BookingStatusUpdate',
    'App\Mail\ContactFormSubmission',
    'App\Mail\GeneralEmail',
    'App\Mail\TestEmail'
];

foreach ($mailClasses as $class) {
    if (class_exists($class)) {
        echo "   ✅ $class found\n";
    } else {
        echo "   ❌ $class not found\n";
    }
}

// Test 7: Check email templates
echo "7. Testing email templates...\n";
$templates = [
    'resources/views/emails/layouts/master.blade.php',
    'resources/views/emails/booking/confirmation.blade.php',
    'resources/views/emails/booking/reminder-client.blade.php',
    'resources/views/emails/payment/confirmation.blade.php',
    'resources/views/emails/welcome/client.blade.php',
    'resources/views/emails/welcome/driver.blade.php',
    'resources/views/emails/welcome/admin.blade.php'
];

foreach ($templates as $template) {
    if (file_exists($template)) {
        echo "   ✅ $template found\n";
    } else {
        echo "   ❌ $template not found\n";
    }
}

// Test 8: Check admin views
echo "8. Testing admin views...\n";
$adminViews = [
    'resources/views/admin/emails/index.blade.php',
    'resources/views/admin/emails/show.blade.php',
    'resources/views/admin/emails/settings.blade.php',
    'resources/views/admin/emails/bulk.blade.php'
];

foreach ($adminViews as $view) {
    if (file_exists($view)) {
        echo "   ✅ $view found\n";
    } else {
        echo "   ❌ $view not found\n";
    }
}

// Test 9: Check console commands
echo "9. Testing console commands...\n";
$commands = [
    'app/Console/Commands/SendBookingReminders.php',
    'app/Console/Commands/CleanupEmailLogs.php',
    'app/Console/Commands/ProcessEmailQueue.php'
];

foreach ($commands as $command) {
    if (file_exists($command)) {
        echo "   ✅ $command found\n";
    } else {
        echo "   ❌ $command not found\n";
    }
}

// Test 10: Check event classes
echo "10. Testing event classes...\n";
$events = [
    'App\Events\BookingCreated',
    'App\Events\BookingStatusChanged',
    'App\Events\PaymentCompleted',
    'App\Events\DriverAssigned',
    'App\Events\UserRegistered'
];

foreach ($events as $event) {
    if (class_exists($event)) {
        echo "    ✅ $event found\n";
    } else {
        echo "    ❌ $event not found\n";
    }
}

// Test 11: Check listener classes
echo "11. Testing listener classes...\n";
$listeners = [
    'App\Listeners\SendBookingConfirmationEmail',
    'App\Listeners\SendBookingStatusUpdateEmail',
    'App\Listeners\SendPaymentConfirmationEmail',
    'App\Listeners\SendDriverAssignmentEmail',
    'App\Listeners\SendWelcomeEmail'
];

foreach ($listeners as $listener) {
    if (class_exists($listener)) {
        echo "    ✅ $listener found\n";
    } else {
        echo "    ❌ $listener not found\n";
    }
}

echo "\n🎉 Email System Test Complete!\n";
echo "===============================\n";
echo "If all tests passed, your email system is ready to use.\n";
echo "Next steps:\n";
echo "1. Configure SMTP settings in admin panel\n";
echo "2. Start queue workers: php artisan queue:work\n";
echo "3. Test with a real email using the admin panel\n\n";
