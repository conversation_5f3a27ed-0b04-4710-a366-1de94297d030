<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('preference_type'); // booking_confirmations, reminders, promotions, etc.
            $table->boolean('enabled')->default(true);
            $table->json('settings')->nullable(); // Additional settings for each preference type
            $table->timestamps();

            // Ensure one preference per user per type
            $table->unique(['user_id', 'preference_type']);
            
            // Index for better performance
            $table->index(['user_id', 'enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_preferences');
    }
};
