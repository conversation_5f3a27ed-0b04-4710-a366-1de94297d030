<?php $__env->startSection('title', 'Revenue Reports'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2 text-success"></i> Revenue Reports
        </h1>
        <div class="d-flex">
            <button class="btn btn-success" onclick="exportReport()">
                <i class="fas fa-download me-1"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.reports.revenue')); ?>">
                <div class="row">
                    <div class="col-md-4">
                        <label for="period">Period</label>
                        <select name="period" id="period" class="form-control" onchange="this.form.submit()">
                            <option value="daily" <?php echo e($period == 'daily' ? 'selected' : ''); ?>>Daily</option>
                            <option value="monthly" <?php echo e($period == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                            <option value="yearly" <?php echo e($period == 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                        </select>
                    </div>
                    <?php if($period == 'daily' || $period == 'monthly'): ?>
                    <div class="col-md-4">
                        <label for="year">Year</label>
                        <select name="year" id="year" class="form-control" onchange="this.form.submit()">
                            <?php for($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?php echo e($y); ?>" <?php echo e($year == $y ? 'selected' : ''); ?>><?php echo e($y); ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    <?php if($period == 'daily'): ?>
                    <div class="col-md-4">
                        <label for="month">Month</label>
                        <select name="month" id="month" class="form-control" onchange="this.form.submit()">
                            <?php for($m = 1; $m <= 12; $m++): ?>
                                <option value="<?php echo e($m); ?>" <?php echo e($month == $m ? 'selected' : ''); ?>>
                                    <?php echo e(date('F', mktime(0, 0, 0, $m, 1))); ?>

                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Revenue Trend</h6>
        </div>
        <div class="card-body">
            <canvas id="revenueChart" width="400" height="100"></canvas>
        </div>
    </div>

    <div class="row">
        <!-- Payment Methods -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Clients -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Clients by Revenue</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topClients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($client->user->name); ?></td>
                                    <td>$<?php echo e(number_format($client->total_spent, 2)); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($labels, 15, 512) ?>,
        datasets: [{
            label: 'Revenue ($)',
            data: <?php echo json_encode($revenueData, 15, 512) ?>,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Payment Methods Chart
const paymentCtx = document.getElementById('paymentMethodsChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($paymentMethods->pluck('payment_method'), 15, 512) ?>,
        datasets: [{
            data: <?php echo json_encode($paymentMethods->pluck('count'), 15, 512) ?>,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

function exportReport() {
    // Implementation for exporting report
    alert('Export functionality would be implemented here');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\reports\revenue.blade.php ENDPATH**/ ?>