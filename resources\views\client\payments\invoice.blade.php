@extends('layouts.client')

@section('title', 'Invoice #' . $payment->id)

@section('styles')
<style>
    .invoice-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .invoice-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .invoice-body {
        padding: 30px;
    }

    .invoice-footer {
        background-color: #f8f9fa;
        padding: 20px;
        border-top: 1px solid #dee2e6;
    }

    .invoice-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #343a40;
    }

    .invoice-subtitle {
        font-size: 1rem;
        color: #6c757d;
    }

    .invoice-logo {
        max-height: 60px;
    }

    .invoice-company {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .invoice-details {
        margin-bottom: 30px;
    }

    .invoice-details-title {
        font-weight: 600;
        margin-bottom: 10px;
        color: #343a40;
    }

    .invoice-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .invoice-total {
        font-size: 1.2rem;
        font-weight: 700;
    }

    .invoice-notes {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .invoice-payment-method {
        display: inline-block;
        padding: 5px 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    @media print {
        body {
            background-color: #fff;
        }

        .invoice-card {
            box-shadow: none;
        }

        .no-print {
            display: none !important;
        }

        .container {
            width: 100%;
            max-width: 100%;
        }
    }
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <h2>Invoice #{{ $payment->id }}</h2>
    <div>
        <button onclick="window.print()" class="btn btn-primary me-2">
            <i class="fas fa-print me-2"></i> Print Invoice
        </button>
        <a href="{{ route('client.payments.show', $payment->id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Payment
        </a>
    </div>
</div>

<div class="card invoice-card">
    <div class="invoice-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <img src="{{ asset('images/logo.png') }}" alt="Ynr Cars" class="invoice-logo">
                <div class="mt-3">
                    <div class="invoice-company">Ynr Cars</div>
                    <div>123 Main Street</div>
                    <div>New York, NY 10001</div>
                    <div>United States</div>
                    <div><EMAIL></div>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-subtitle">Invoice #{{ $payment->id }}</div>
                <div>Date: {{ $payment->created_at->format('F d, Y') }}</div>
                <div>Status: <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : 'warning' }}">{{ ucfirst($payment->status) }}</span></div>
            </div>
        </div>
    </div>

    <div class="invoice-body">
        <div class="row invoice-details">
            <div class="col-md-6">
                <div class="invoice-details-title">Bill To:</div>
                <div>{{ $payment->booking->user->name }}</div>
                <div>{{ $payment->booking->user->email }}</div>
                <div>{{ $payment->booking->user->phone ?? 'N/A' }}</div>
                <div>{{ $payment->booking->user->address ?? 'N/A' }}</div>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="invoice-details-title">Payment Details:</div>
                <div>Payment Method:
                    <span class="invoice-payment-method">
                        @if($payment->payment_method == 'paypal')
                            <i class="fab fa-paypal me-1"></i> PayPal
                        @elseif($payment->payment_method == 'credit_card')
                            <i class="far fa-credit-card me-1"></i> Credit Card
                        @elseif($payment->payment_method == 'cash')
                            <i class="fas fa-money-bill-wave me-1"></i> Cash
                        @else
                            <i class="fas fa-money-check me-1"></i> {{ ucfirst($payment->payment_method) }}
                        @endif
                    </span>
                </div>
                <div>Transaction ID: {{ $payment->transaction_id ?? 'N/A' }}</div>
                <div>Payment Date: {{ $payment->updated_at->format('F d, Y') }}</div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table invoice-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Booking Details</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong>{{ $payment->booking->vehicle->name }}</strong><br>
                            {{ $payment->booking->booking_type }} Booking<br>
                            Booking #{{ $payment->booking->booking_number }}
                        </td>
                        <td>
                            Pickup: {{ $payment->booking->pickup_date->format('M d, Y h:i A') }}<br>
                            @if($payment->booking->return_date)
                                Return: {{ $payment->booking->return_date->format('M d, Y h:i A') }}<br>
                            @endif
                            Duration: {{ $payment->booking->duration_hours }} hours<br>
                            @if($payment->booking->distance)
                                Distance: {{ $payment->booking->distance }} km
                            @endif
                        </td>
                        <td class="text-end">@currency(){{ number_format($payment->amount, 2) }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2" class="text-end">Subtotal:</td>
                        <td class="text-end">@currency(){{ number_format($payment->amount * 0.9, 2) }}</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="text-end">Tax (10%):</td>
                        <td class="text-end">@currency(){{ number_format($payment->amount * 0.1, 2) }}</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="text-end invoice-total">Total:</td>
                        <td class="text-end invoice-total">@currency(){{ number_format($payment->amount, 2) }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="invoice-notes mt-4">
            <strong>Notes:</strong>
            <p>Thank you for choosing Ynr Cars. This invoice serves as your official receipt for the services provided. Please retain this document for your records.</p>
        </div>
    </div>

    <div class="invoice-footer text-center">
        <p class="mb-0">If you have any questions about this invoice, please contact our customer <NAME_EMAIL></p>
    </div>
</div>
@endsection
