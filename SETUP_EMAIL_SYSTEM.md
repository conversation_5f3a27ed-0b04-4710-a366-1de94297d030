# YNR Cars Email System Setup Guide

## 🚀 Quick Setup Instructions

### 1. Run Database Migrations

```bash
# Run the email system migrations
php artisan migrate

# This will create:
# - email_logs table
# - email_preferences table
```

### 2. Configure Email Settings

#### Option A: Through Admin Panel (Recommended)
1. Login to admin panel: `/admin/dashboard`
2. Navigate to "Email Management" → "Settings"
3. Configure your SMTP settings:
   - **Gmail Example:**
     - Driver: SMTP
     - Host: smtp.gmail.com
     - Port: 587
     - Encryption: TLS
     - Username: <EMAIL>
     - Password: your-app-password
     - From Address: <EMAIL>
     - From Name: Your Company Name

#### Option B: Environment Variables
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="YNR Cars"
```

### 3. Set Up Queue Processing

#### For Development:
```bash
php artisan queue:work
```

#### For Production (using Supervisor):
Create `/etc/supervisor/conf.d/laravel-worker.conf`:
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
stopwaitsecs=3600
```

### 4. Schedule Email Commands (Optional)

Add to your `app/Console/Kernel.php`:
```php
protected function schedule(Schedule $schedule)
{
    // Send booking reminders every hour
    $schedule->command('email:send-booking-reminders')->hourly();
    
    // Clean up old email logs weekly
    $schedule->command('email:cleanup-logs --days=90')->weekly();
    
    // Process email queue status every 15 minutes
    $schedule->command('email:process-queue')->everyFifteenMinutes();
}
```

Then set up cron job:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### 5. Test Email Configuration

1. Go to Admin Panel → Email Management → Settings
2. Click "Send Test Email"
3. Enter your email address
4. Check if you receive the test email

## 📧 Available Email Types

### Automatic Emails (Event-Driven)
- ✅ **Booking Confirmation** - When booking is created
- ✅ **Payment Confirmation** - When payment is completed
- ✅ **Driver Assignment** - When driver is assigned
- ✅ **Status Updates** - When booking status changes
- ✅ **Cancellation Notices** - When booking is cancelled
- ✅ **Welcome Emails** - When user registers

### Manual/Scheduled Emails
- ✅ **Booking Reminders** - 24 hours before pickup
- ✅ **Bulk Campaigns** - Admin-initiated emails
- ✅ **Contact Form Submissions** - From website contact form

## 🎛️ Admin Features

### Email Management Dashboard
- **View all emails** with filtering and search
- **Email statistics** and delivery rates
- **Failed email management** with retry options
- **Bulk email campaigns** to multiple users
- **Email settings** configuration
- **Test email** functionality

### Access Points
- **Main Dashboard**: `/admin/emails`
- **Settings**: `/admin/emails/settings`
- **Bulk Email**: `/admin/emails/bulk`
- **Navigation**: Admin sidebar → "Email Management"

## 🔧 Console Commands

### Send Booking Reminders
```bash
# Send reminders 24 hours before pickup
php artisan email:send-booking-reminders

# Send reminders 2 hours before pickup
php artisan email:send-booking-reminders --hours=2
```

### Clean Up Email Logs
```bash
# Keep 90 days of logs (default)
php artisan email:cleanup-logs

# Keep 30 days of logs
php artisan email:cleanup-logs --days=30
```

### Process Email Queue
```bash
# Check and update email statuses
php artisan email:process-queue

# Run for 120 seconds
php artisan email:process-queue --timeout=120
```

## 🎨 Customizing Email Templates

### Template Locations
```
resources/views/emails/
├── layouts/master.blade.php          # Main email layout
├── booking/
│   ├── confirmation.blade.php        # Booking confirmation
│   ├── reminder-client.blade.php     # Client reminders
│   ├── reminder-driver.blade.php     # Driver reminders
│   └── status-update.blade.php       # Status updates
├── payment/
│   └── confirmation.blade.php        # Payment confirmations
└── welcome/
    ├── client.blade.php              # Client welcome
    └── driver.blade.php              # Driver welcome
```

### Customization Tips
1. **Company Branding**: Edit `master.blade.php` layout
2. **Colors**: Update CSS variables in the layout
3. **Content**: Modify individual template files
4. **Variables**: Use `{{ $variable }}` for dynamic content

## 🔒 User Email Preferences

### Available Preferences
- Booking Confirmations
- Booking Reminders
- Payment Confirmations
- Driver Assignments
- Status Updates
- Promotional Emails
- Newsletter

### Implementation
```php
// Check if user can receive email
EmailPreference::canReceiveEmail($user, 'booking_confirmations');

// Set user preference
EmailPreference::setPreference($userId, 'promotional_emails', false);

// Get all user preferences
EmailPreference::getUserPreferences($userId);
```

## 📊 Monitoring & Analytics

### Email Statistics
- **Total emails sent**
- **Delivery success rate**
- **Failed email count**
- **Template usage statistics**
- **Daily/weekly activity**

### Monitoring Tools
- **Admin Dashboard**: Real-time statistics
- **Email Logs**: Detailed delivery tracking
- **Error Logs**: Failed email investigation
- **Queue Monitoring**: Background job status

## 🚨 Troubleshooting

### Common Issues

#### 1. Emails Not Sending
**Symptoms**: Emails stuck in "queued" status
**Solutions**:
- Check queue worker is running: `php artisan queue:work`
- Verify SMTP settings in admin panel
- Test email configuration
- Check Laravel logs: `storage/logs/laravel.log`

#### 2. High Bounce Rate
**Symptoms**: Many emails marked as "failed"
**Solutions**:
- Verify SMTP credentials
- Check email addresses are valid
- Review email content for spam triggers
- Ensure proper SPF/DKIM records

#### 3. Slow Email Delivery
**Symptoms**: Long delays in email delivery
**Solutions**:
- Increase queue workers
- Optimize email templates
- Check SMTP server performance
- Monitor server resources

### Debug Commands
```bash
# Check queue status
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush

# Monitor queue in real-time
php artisan queue:work --verbose
```

## 🔐 Security Best Practices

1. **Use App Passwords**: For Gmail/Outlook, use app-specific passwords
2. **Secure SMTP**: Always use TLS/SSL encryption
3. **Rate Limiting**: Implement email rate limiting for bulk sends
4. **Validation**: Validate all email addresses before sending
5. **Logging**: Monitor email activity for suspicious patterns

## 📈 Performance Optimization

1. **Queue Workers**: Run multiple queue workers for high volume
2. **Database Indexing**: Email logs table is properly indexed
3. **Log Cleanup**: Regular cleanup of old email logs
4. **Template Caching**: Email templates are cached by Laravel
5. **Batch Processing**: Bulk emails are processed in chunks

## 🎯 Next Steps

1. **Configure SMTP** settings in admin panel
2. **Test email** functionality with test email feature
3. **Set up queue workers** for background processing
4. **Schedule commands** for automated reminders
5. **Customize templates** with your branding
6. **Monitor performance** through admin dashboard

Your YNR Cars email system is now ready to provide professional email communications! 🚗📧
