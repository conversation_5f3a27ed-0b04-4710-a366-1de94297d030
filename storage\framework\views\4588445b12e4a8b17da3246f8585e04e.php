<?php $__env->startSection('styles'); ?>
<style>
    .payment-log-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s;
        margin-bottom: 20px;
    }

    .payment-log-card:hover {
        transform: translateY(-5px);
    }

    .payment-log-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 15px 20px;
    }

    .payment-log-body {
        padding: 20px;
    }

    .payment-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-failed {
        background-color: #f8d7da;
        color: #721c24;
    }

    .status-refunded {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-processing {
        background-color: #e2e3e5;
        color: #383d41;
    }

    .status-cancelled {
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .payment-method-icon {
        font-size: 1.5rem;
        margin-right: 10px;
    }

    .method-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        background-color: #e9ecef;
        color: #495057;
    }

    .method-paypal {
        background-color: #0070ba;
        color: #fff;
    }

    .method-credit_card {
        background-color: #6c757d;
        color: #fff;
    }

    .method-cash {
        background-color: #198754;
        color: #fff;
    }

    .method-bank_transfer {
        background-color: #0dcaf0;
        color: #fff;
    }

    .method-paypal_card {
        background-color: #6610f2;
        color: #fff;
    }

    .payment-details-table th {
        width: 30%;
        font-weight: 600;
    }

    .payment-filter-form {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .payment-summary-card {
        background-color: #343a40;
        color: #fff;
        border-radius: 10px;
    }

    .payment-summary-card .card-body {
        padding: 20px;
    }

    .payment-summary-card .summary-icon {
        font-size: 2.5rem;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .payment-summary-card .summary-title {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 5px;
    }

    .payment-summary-card .summary-value {
        font-size: 1.8rem;
        font-weight: 600;
    }

    .date-range-picker {
        position: relative;
    }

    .date-range-picker .form-control {
        padding-right: 40px;
    }

    .date-range-picker .calendar-icon {
        position: absolute;
        right: 15px;
        top: 10px;
        color: #6c757d;
    }

    <?php echo $__env->yieldContent('payment-logs-styles'); ?>
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0"><?php echo $__env->yieldContent('payment-logs-title', 'Payment Logs'); ?></h2>
        <div>
            <?php echo $__env->yieldContent('payment-logs-actions'); ?>
        </div>
    </div>

    <?php echo $__env->yieldContent('payment-logs-content'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <?php echo $__env->yieldContent('payment-logs-scripts'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/layouts/payment-logs.blade.php ENDPATH**/ ?>