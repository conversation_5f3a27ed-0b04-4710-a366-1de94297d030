@extends('layouts.driver')

@section('title', 'Earnings Dashboard')

@section('styles')
<style>
    /* Earnings Styles */
    .earnings-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .earnings-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }

    .earnings-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .earnings-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .earnings-card .card-body {
        padding: 25px;
    }

    .earnings-summary {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #ee393d 0%, #d31a1e 100%);
        color: white;
        border-radius: 15px;
        margin-bottom: 30px;
    }

    .earnings-amount {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .earnings-period {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .earnings-stat-card {
        background-color: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        height: 100%;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .earnings-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    }

    .earnings-stat-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(238, 57, 61, 0.1);
        color: #ee393d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 1.5rem;
    }

    .earnings-stat-amount {
        font-size: 2rem;
        font-weight: 700;
        color: #343a40;
        margin: 10px 0;
    }

    .earnings-stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    .earnings-chart-container {
        height: 350px;
        margin-bottom: 20px;
    }

    .earnings-table {
        margin-bottom: 0;
    }

    .earnings-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .earnings-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .earnings-table tr {
        transition: all 0.3s ease;
    }

    .earnings-table tr:hover {
        background-color: rgba(238, 57, 61, 0.03);
    }

    .earnings-amount-cell {
        font-weight: 700;
        color: #ee393d;
    }

    .earnings-date {
        display: flex;
        flex-direction: column;
    }

    .earnings-date-day {
        font-weight: 600;
    }

    .earnings-date-time {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .earnings-client {
        display: flex;
        align-items: center;
    }

    .earnings-client-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 10px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        color: #6c757d;
    }

    .earnings-client-name {
        font-weight: 600;
    }

    .earnings-address {
        display: flex;
        flex-direction: column;
    }

    .earnings-address i {
        color: #ee393d;
        margin-right: 5px;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
    }

    .quick-action-card {
        flex: 1;
        background-color: #fff;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }

    .quick-action-icon {
        font-size: 2rem;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .quick-action-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .quick-action-desc {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 15px;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .earnings-card {
            margin-bottom: 20px;
        }

        .quick-actions {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-dollar-sign me-2 text-primary"></i> Earnings Dashboard
        </h1>
        <div class="d-flex">
            <a href="{{ route('driver.earnings.history') }}" class="btn btn-primary">
                <i class="fas fa-history me-1"></i> View Earnings History
            </a>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <div class="quick-action-card">
            <div class="quick-action-icon">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <h5 class="quick-action-title">Payment Reports</h5>
            <p class="quick-action-desc">Download your payment reports for tax purposes</p>
            <a href="{{ route('driver.earnings.reports') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-download me-1"></i> Download Reports
            </a>
        </div>

        <div class="quick-action-card">
            <div class="quick-action-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <h5 class="quick-action-title">Earnings Analytics</h5>
            <p class="quick-action-desc">View detailed analytics of your earnings</p>
            <a href="{{ route('driver.earnings.analytics') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-chart-bar me-1"></i> View Analytics
            </a>
        </div>

        <div class="quick-action-card">
            <div class="quick-action-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <h5 class="quick-action-title">Payment Schedule</h5>
            <p class="quick-action-desc">View your upcoming payment schedule</p>
            <a href="{{ route('driver.earnings.schedule') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-calendar me-1"></i> View Schedule
            </a>
        </div>
    </div>

    <!-- Earnings Summary -->
    <div class="earnings-card">
        <div class="card-body">
            <div class="earnings-summary">
                <div class="earnings-amount">@currency(){{ number_format($totalEarnings, 2) }}</div>
                <div class="earnings-period">Total Earnings</div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="earnings-stat-card">
                        <div class="earnings-stat-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="earnings-stat-amount">@currency(){{ number_format($currentWeekEarnings, 2) }}</div>
                        <div class="earnings-stat-label">This Week</div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="earnings-stat-card">
                        <div class="earnings-stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="earnings-stat-amount">@currency(){{ number_format($currentMonthEarnings, 2) }}</div>
                        <div class="earnings-stat-label">This Month</div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="earnings-stat-card">
                        <div class="earnings-stat-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="earnings-stat-amount">{{ $totalRides }}</div>
                        <div class="earnings-stat-label">Total Rides</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Earnings Chart -->
    <div class="earnings-card">
        <div class="card-header">
            <h5><i class="fas fa-chart-line me-2"></i> Monthly Earnings ({{ date('Y') }})</h5>
        </div>
        <div class="card-body">
            <div class="earnings-chart-container">
                <canvas id="earningsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Earnings -->
    <div class="earnings-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-history me-2"></i> Recent Earnings</h5>
            <a href="{{ route('driver.earnings.history') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-list me-1"></i> View All
            </a>
        </div>
        <div class="card-body">
            @if ($recentEarnings->isEmpty())
                <div class="text-center py-5">
                    <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Earnings Found</h5>
                    <p class="text-muted">You don't have any earnings yet. Complete rides to start earning.</p>
                </div>
            @else
                <div class="table-responsive">
                    <table class="table earnings-table">
                        <thead>
                            <tr>
                                <th>Booking #</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Route</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($recentEarnings as $earning)
                                <tr>
                                    <td>
                                        <span class="fw-bold">{{ $earning->booking_number }}</span>
                                    </td>
                                    <td>
                                        <div class="earnings-client">
                                            @if($earning->user->profile_photo)
                                                <img src="{{ asset('storage/' . $earning->user->profile_photo) }}" alt="{{ $earning->user->name }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                            @else
                                                <div class="earnings-client-avatar">
                                                    {{ strtoupper(substr($earning->user->name, 0, 1)) }}
                                                </div>
                                            @endif
                                            <span class="earnings-client-name">{{ $earning->user->name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="earnings-date">
                                            <span class="earnings-date-day">{{ $earning->completed_at ? $earning->completed_at->format('M d, Y') : 'N/A' }}</span>
                                            <span class="earnings-date-time">{{ $earning->completed_at ? $earning->completed_at->format('h:i A') : '' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="earnings-address">
                                            <span><i class="fas fa-map-marker-alt"></i> {{ Str::limit($earning->pickup_address, 20) }}</span>
                                            <span><i class="fas fa-map-pin"></i> {{ Str::limit($earning->dropoff_address, 20) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="earnings-amount-cell">@currency(){{ number_format($earning->amount, 2) }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('driver.rides.show', $earning->id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly earnings chart
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const monthlyData = [
            @foreach ($monthlyEarnings as $month => $amount)
                {{ $amount }},
            @endforeach
        ];

        const ctx = document.getElementById('earningsChart').getContext('2d');
        const earningsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthNames,
                datasets: [{
                    label: 'Earnings ({{ $currencySymbol }})',
                    data: monthlyData,
                    backgroundColor: 'rgba(238, 57, 61, 0.1)',
                    borderColor: '#ee393d',
                    borderWidth: 3,
                    pointBackgroundColor: '#ee393d',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#ee393d',
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '{{ $currencySymbol }}' + value;
                            },
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 14
                        },
                        padding: 15,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return 'Earnings: {{ $currencySymbol }}' + context.raw.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endsection
