<?php $__env->startSection('title', 'Admin Dashboard'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .dashboard-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.3s;
        height: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
    }

    .dashboard-icon {
        font-size: 2.5rem;
        color: #ee393d;
    }

    .stats-card {
        position: relative;
        overflow: hidden;
    }

    .stats-card .dashboard-icon {
        position: absolute;
        right: 20px;
        top: 20px;
        opacity: 0.2;
        font-size: 4rem;
    }

    .stats-card .card-body {
        position: relative;
        z-index: 1;
    }

    .stats-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .stats-card .card-text {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-card .percentage {
        font-size: 0.9rem;
        display: flex;
        align-items: center;
    }

    .stats-card .percentage.up {
        color: #28a745;
    }

    .stats-card .percentage.down {
        color: #dc3545;
    }

    .stats-card .percentage i {
        margin-right: 5px;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .booking-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .quick-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        border-radius: 10px;
        background-color: #f8f9fa;
        border: 1px solid #eee;
        transition: all 0.3s;
        text-decoration: none;
        color: #343a40;
    }

    .quick-action-btn:hover {
        background-color: #ee393d;
        color: #343a40;
        transform: translateY(-5px);
    }

    .quick-action-btn i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .quick-action-btn span {
        font-weight: 600;
        text-align: center;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Dashboard</h2>
    <div>
        <span class="text-muted me-2">Last updated: <?php echo e(now()->format('M d, Y H:i')); ?></span>
        <button class="btn btn-sm btn-outline-secondary" onclick="window.location.reload()">
            <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card stats-card">
            <div class="card-body">
                <h5 class="card-title">Total Users</h5>
                <h3 class="card-text"><?php echo e($userCount); ?></h3>
                <div class="percentage <?php echo e($userStats['client'] > 0 ? 'up' : 'down'); ?>">
                    <i class="fas fa-arrow-<?php echo e($userStats['client'] > 0 ? 'up' : 'down'); ?>"></i>
                    <?php echo e(number_format(abs($userStats['client'] / max(1, $userCount) * 100), 0)); ?>% clients
                </div>
                <i class="fas fa-users dashboard-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card stats-card">
            <div class="card-body">
                <h5 class="card-title">Total Vehicles</h5>
                <h3 class="card-text"><?php echo e($vehicleCount); ?></h3>
                <div class="percentage up">
                    <i class="fas fa-arrow-up"></i>
                    <?php echo e($vehicleCount > 0 ? number_format(($popularVehicles->count() / $vehicleCount) * 100, 0) : 0); ?>% active
                </div>
                <i class="fas fa-car dashboard-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card stats-card">
            <div class="card-body">
                <h5 class="card-title">Total Bookings</h5>
                <h3 class="card-text"><?php echo e($bookingCount); ?></h3>
                <div class="percentage <?php echo e($bookingStats['completed'] > $bookingStats['cancelled'] ? 'up' : 'down'); ?>">
                    <i class="fas fa-arrow-<?php echo e($bookingStats['completed'] > $bookingStats['cancelled'] ? 'up' : 'down'); ?>"></i>
                    <?php echo e($bookingCount > 0 ? number_format(($bookingStats['completed'] / $bookingCount) * 100, 0) : 0); ?>% completed
                </div>
                <i class="fas fa-calendar-check dashboard-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card stats-card">
            <div class="card-body">
                <h5 class="card-title">Total Revenue</h5>
                <h3 class="card-text"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($totalRevenue, 2)); ?></h3>
                <div class="percentage <?php echo e($revenueGrowth >= 0 ? 'up' : 'down'); ?>">
                    <i class="fas fa-arrow-<?php echo e($revenueGrowth >= 0 ? 'up' : 'down'); ?>"></i>
                    <?php echo e(number_format(abs($revenueGrowth), 0)); ?>% from last month
                </div>
                <i class="fas fa-dollar-sign dashboard-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card dashboard-card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Quick Actions</h5>
    </div>
    <div class="card-body">
        <div class="quick-actions">
            <a href="<?php echo e(route('admin.vehicles.create')); ?>" class="quick-action-btn">
                <i class="fas fa-car"></i>
                <span>Add Vehicle</span>
            </a>
            <a href="<?php echo e(route('admin.airports.index')); ?>" class="quick-action-btn">
                <i class="fas fa-plane"></i>
                <span>Manage Airports</span>
            </a>
            <a href="<?php echo e(route('admin.users.create')); ?>" class="quick-action-btn">
                <i class="fas fa-user-plus"></i>
                <span>Add Client</span>
            </a>

            <a href="<?php echo e(route('admin.settings.index')); ?>" class="quick-action-btn">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
            <a href="<?php echo e(route('admin.payments.report')); ?>" class="quick-action-btn">
                <i class="fas fa-chart-line"></i>
                <span>View Reports</span>
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Booking Status Chart -->
    <div class="col-md-6 mb-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">Booking Status</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="bookingStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- User Roles Chart -->
    <div class="col-md-6 mb-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">User Distribution</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="userRolesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Management Widget -->
<?php echo $__env->make('admin.dashboard.email-widget', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<div class="row">
    <!-- Document Alerts -->
    <div class="col-md-12 mb-4">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Driver Document Alerts</h5>
                <a href="<?php echo e(route('admin.drivers.index', ['document_status' => 'expired'])); ?>" class="btn btn-sm btn-outline-danger">View All Expired</a>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Expired Documents -->
                    <div class="col-md-6">
                        <h6 class="text-danger mb-3"><i class="fas fa-exclamation-circle"></i> Expired Documents</h6>
                        <?php if($driversWithExpiredDocuments->isEmpty()): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> No drivers with expired documents
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Driver</th>
                                            <th>Document</th>
                                            <th>Expired On</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $driversWithExpiredDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $hasExpiredInsurance = $driver->insurance_expiry && $driver->insurance_expiry->isPast();
                                                $hasExpiredMOT = $driver->mot_expiry && $driver->mot_expiry->isPast();
                                            ?>

                                            <?php if($hasExpiredInsurance): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if($driver->profile_photo): ?>
                                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="img-profile rounded-circle mr-2" style="width: 30px; height: 30px;">
                                                            <?php else: ?>
                                                                <div class="bg-primary rounded-circle mr-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                                    <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                                                                </div>
                                                            <?php endif; ?>
                                                            <span><?php echo e($driver->name); ?></span>
                                                        </div>
                                                    </td>
                                                    <td>Insurance</td>
                                                    <td class="text-danger"><?php echo e($driver->insurance_expiry->format('M d, Y')); ?></td>
                                                    <td>
                                                        <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">Update</a>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($hasExpiredMOT): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if($driver->profile_photo): ?>
                                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="img-profile rounded-circle mr-2" style="width: 30px; height: 30px;">
                                                            <?php else: ?>
                                                                <div class="bg-primary rounded-circle mr-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                                    <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                                                                </div>
                                                            <?php endif; ?>
                                                            <span><?php echo e($driver->name); ?></span>
                                                        </div>
                                                    </td>
                                                    <td>MOT Certificate</td>
                                                    <td class="text-danger"><?php echo e($driver->mot_expiry->format('M d, Y')); ?></td>
                                                    <td>
                                                        <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">Update</a>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Expiring Soon Documents -->
                    <div class="col-md-6">
                        <h6 class="text-warning mb-3"><i class="fas fa-exclamation-triangle"></i> Documents Expiring Soon</h6>
                        <?php if($driversWithExpiringDocuments->isEmpty()): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> No documents expiring soon
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Driver</th>
                                            <th>Document</th>
                                            <th>Expires On</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $driversWithExpiringDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $insuranceExpiringSoon = $driver->insurance_expiry && !$driver->insurance_expiry->isPast() && $driver->insurance_expiry->diffInDays(now()) <= 30;
                                                $motExpiringSoon = $driver->mot_expiry && !$driver->mot_expiry->isPast() && $driver->mot_expiry->diffInDays(now()) <= 30;
                                            ?>

                                            <?php if($insuranceExpiringSoon): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if($driver->profile_photo): ?>
                                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="img-profile rounded-circle mr-2" style="width: 30px; height: 30px;">
                                                            <?php else: ?>
                                                                <div class="bg-primary rounded-circle mr-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                                    <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                                                                </div>
                                                            <?php endif; ?>
                                                            <span><?php echo e($driver->name); ?></span>
                                                        </div>
                                                    </td>
                                                    <td>Insurance</td>
                                                    <td class="text-warning"><?php echo e($driver->insurance_expiry->format('M d, Y')); ?></td>
                                                    <td>
                                                        <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">Update</a>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($motExpiringSoon): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if($driver->profile_photo): ?>
                                                                <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="img-profile rounded-circle mr-2" style="width: 30px; height: 30px;">
                                                            <?php else: ?>
                                                                <div class="bg-primary rounded-circle mr-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                                    <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                                                                </div>
                                                            <?php endif; ?>
                                                            <span><?php echo e($driver->name); ?></span>
                                                        </div>
                                                    </td>
                                                    <td>MOT Certificate</td>
                                                    <td class="text-warning"><?php echo e($driver->mot_expiry->format('M d, Y')); ?></td>
                                                    <td>
                                                        <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">Update</a>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Recent Bookings -->
    <div class="col-md-6 mb-4">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Bookings</h5>
                <a href="<?php echo e(route('admin.bookings.index')); ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if($recentBookings->isEmpty()): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times text-muted fa-3x mb-3"></i>
                        <p class="text-muted">No bookings found</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Booking #</th>
                                    <th>Client</th>
                                    <th>Vehicle</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($booking->booking_number); ?></td>
                                        <td><?php echo e($booking->user->name); ?></td>
                                        <td><?php echo e($booking->vehicle->name); ?></td>
                                        <td><?php echo e($booking->pickup_date->format('M d, Y')); ?></td>
                                        <td>
                                            <span class="booking-status status-<?php echo e(strtolower($booking->status)); ?>">
                                                <?php echo e(ucfirst($booking->status)); ?>

                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="col-md-6 mb-4">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Users</h5>
                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if($recentUsers->isEmpty()): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users-slash text-muted fa-3x mb-3"></i>
                        <p class="text-muted">No users found</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($user->name); ?></td>
                                        <td><?php echo e($user->email); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo e($user->role == 'admin' ? 'danger' : ($user->role == 'driver' ? 'info' : 'success')); ?>">
                                                <?php echo e(ucfirst($user->role)); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($user->created_at->format('M d, Y')); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Reviews -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-star me-2"></i> Recent Reviews</h5>
                <a href="<?php echo e(route('admin.reviews.index')); ?>" class="btn btn-sm btn-outline-primary">View All Reviews</a>
            </div>
            <div class="card-body">
                <?php if($recentReviews->isEmpty()): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-star-half-alt text-muted fa-3x mb-3"></i>
                        <p class="text-muted">No reviews found</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php $__currentLoopData = $recentReviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo e($booking->user->name); ?></strong>
                                            <small class="text-muted">on <?php echo e($booking->reviewed_at->format('M d, Y')); ?></small>
                                        </div>
                                        <div class="text-warning">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <i class="<?php echo e($i <= $booking->rating ? 'fas' : 'far'); ?> fa-star"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text"><?php echo e(Str::limit($booking->review, 150)); ?></p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-car me-1"></i> <?php echo e($booking->vehicle->name); ?>

                                            </small>
                                            <a href="<?php echo e(route('admin.reviews.show', $booking->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Booking Status Chart
        const bookingStatusCtx = document.getElementById('bookingStatusChart');
        if (bookingStatusCtx) {
            new Chart(bookingStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Pending', 'Confirmed', 'Completed', 'Cancelled'],
                    datasets: [{
                        data: [
                            <?php echo e($bookingStats['pending']); ?>,
                            <?php echo e($bookingStats['confirmed']); ?>,
                            <?php echo e($bookingStats['completed']); ?>,
                            <?php echo e($bookingStats['cancelled']); ?>

                        ],
                        backgroundColor: [
                            '#fff3cd',
                            '#cff4fc',
                            '#d1e7dd',
                            '#f8d7da'
                        ],
                        borderColor: [
                            '#664d03',
                            '#055160',
                            '#0f5132',
                            '#842029'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // User Roles Chart
        const userRolesCtx = document.getElementById('userRolesChart');
        if (userRolesCtx) {
            new Chart(userRolesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Admins', 'Clients', 'Drivers'],
                    datasets: [{
                        data: [
                            <?php echo e($userStats['admin']); ?>,
                            <?php echo e($userStats['client']); ?>,
                            <?php echo e($userStats['driver']); ?>

                        ],
                        backgroundColor: [
                            '#dc3545',
                            '#198754',
                            '#0dcaf0'
                        ],
                        borderColor: [
                            '#b02a37',
                            '#146c43',
                            '#0aa2c0'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>