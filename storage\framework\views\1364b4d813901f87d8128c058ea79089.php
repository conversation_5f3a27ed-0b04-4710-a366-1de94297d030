<?php $__env->startSection('title', 'Manage Users'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }

    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: calc(100vh - 76px);
        padding-top: 20px;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 10px 20px;
        margin-bottom: 5px;
        border-radius: 5px;
    }

    .sidebar .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
        color: #fff;
        background-color: #ee393d;
    }

    .sidebar .nav-link i {
        margin-right: 10px;
    }

    .user-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .user-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .user-card .card-body {
        padding: 0;
    }

    .user-table {
        margin-bottom: 0;
    }

    .user-table th,
    .user-table td {
        padding: 15px 20px;
        vertical-align: middle;
    }

    .user-table tbody tr {
        transition: background-color 0.3s;
    }

    .user-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .role-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .role-admin {
        background-color: #cff4fc;
        color: #055160;
    }

    .role-client {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .role-driver {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-active {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #842029;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .search-form {
        margin-bottom: 20px;
    }

    .search-form .form-control {
        border-radius: 50px 0 0 50px;
        padding-left: 20px;
    }

    .search-form .btn {
        border-radius: 0 50px 50px 0;
    }

    .filter-dropdown {
        margin-right: 10px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Manage Clients</h2>
                <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add New Client
                </a>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Search and Filter -->
            <div class="d-flex justify-content-between mb-4">
                <div class="d-flex">
                    <div class="dropdown filter-dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="roleFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php if(request('role')): ?>
                                <?php echo e(ucfirst(request('role'))); ?>

                            <?php else: ?>
                                All Roles
                            <?php endif; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="roleFilterDropdown">
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.index')); ?>">All Roles</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.index', ['role' => 'admin'])); ?>">Admin</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.index', ['role' => 'client'])); ?>">Client</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.index', ['role' => 'driver'])); ?>">Driver</a></li>
                        </ul>
                    </div>
                </div>

                <form action="<?php echo e(route('admin.users.index')); ?>" method="GET" class="d-flex search-form">
                    <input type="hidden" name="role" value="<?php echo e(request('role')); ?>">
                    <input type="text" class="form-control" placeholder="Search clients..." name="search" value="<?php echo e(request('search')); ?>">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <div class="card user-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Clients</h4>
                    <span>Total: <?php echo e($users->total()); ?></span>
                </div>
                <div class="card-body">
                    <?php if($users->isEmpty()): ?>
                        <div class="text-center py-5">
                            <p class="text-muted mb-0">No clients found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table user-table">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th width="60">Avatar</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th width="200">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($user->id); ?></td>
                                            <td>
                                                <?php if($user->profile_photo): ?>
                                                    <img src="<?php echo e(asset('storage/' . $user->profile_photo)); ?>" class="user-avatar" alt="<?php echo e($user->name); ?>">
                                                <?php else: ?>
                                                    <img src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($user->name)); ?>&background=random" class="user-avatar" alt="<?php echo e($user->name); ?>">
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($user->name); ?></td>
                                            <td><?php echo e($user->email); ?></td>
                                            <td>
                                                <span class="role-badge role-<?php echo e($user->role); ?>">
                                                    <?php echo e(ucfirst($user->role)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo e($user->is_active ? 'active' : 'inactive'); ?>">
                                                    <?php echo e($user->is_active ? 'Active' : 'Inactive'); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($user->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('admin.users.show', $user->id)); ?>" class="btn btn-sm btn-outline-info me-1">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.users.edit', $user->id)); ?>" class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.users.toggle-active', $user->id)); ?>" method="POST" class="d-inline me-1">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-<?php echo e($user->is_active ? 'warning' : 'success'); ?>">
                                                        <i class="fas fa-<?php echo e($user->is_active ? 'ban' : 'check'); ?>"></i>
                                                    </button>
                                                </form>
                                                <form action="<?php echo e(route('admin.users.destroy', $user->id)); ?>" method="POST" class="d-inline delete-user-form">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($users->withQueryString()->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Confirm delete
        $('.delete-user-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);

            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the client. This action cannot be undone!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.off('submit').submit();
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\users\index.blade.php ENDPATH**/ ?>