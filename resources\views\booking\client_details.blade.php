@extends('layouts.guest')

@section('title', 'Enter Your Details - Ynr Cars')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/booking.css') }}">
<style>
    .client-details-form {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 8px;
    }

    .booking-summary {
        position: sticky;
        top: 100px;
    }

    .booking-steps .step {
        position: relative;
    }

    .booking-steps .step::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -15px;
        width: 30px;
        height: 2px;
        background-color: #ddd;
        transform: translateY(-50%);
    }

    .booking-steps .step:last-child::after {
        display: none;
    }

    .booking-steps .step.active .step-number {
        background-color: #ee393d;
        color: #fff;
    }

    .booking-steps .step.completed .step-number {
        background-color: #28a745;
        color: #fff;
    }
</style>
@endsection

@section('content')
<div class="booking-header">
    <div class="container">
        <h1>Enter Your Details</h1>
        <p>Please provide your contact information to proceed with the booking</p>
    </div>
</div>

<div class="booking-container">
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8">
                <!-- Booking Steps Indicator -->
                <div class="booking-steps mb-4" data-aos="fade-up">
                    <div class="step completed" id="step1-indicator">
                        <div class="step-number">1</div>
                        <div class="step-title">Trip Details</div>
                    </div>
                    <div class="step completed" id="step2-indicator">
                        <div class="step-number">2</div>
                        <div class="step-title">Vehicle Selection</div>
                    </div>
                    <div class="step active" id="step3-indicator">
                        <div class="step-number">3</div>
                        <div class="step-title">Your Details</div>
                    </div>
                    <div class="step" id="step4-indicator">
                        <div class="step-number">4</div>
                        <div class="step-title">Review & Payment</div>
                    </div>
                </div>

                <div class="client-details-form" data-aos="fade-up">
                    <h3 class="mb-4">Contact Information</h3>

                    @if ($errors->any())
                        <div class="alert alert-danger mb-4">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form id="clientDetailsForm" action="{{ route('booking.save-client-details') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="special_requests" class="form-label">Special Requests</label>
                                    <input type="text" class="form-control @error('special_requests') is-invalid @enderror" id="special_requests" name="special_requests" value="{{ old('special_requests') }}">
                                    @error('special_requests')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input @error('terms') is-invalid @enderror" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="{{ route('terms-and-conditions') }}" target="_blank">Terms and Conditions</a>
                                </label>
                                @error('terms')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back
                            </a>
                            <button type="submit" id="continueBtn" class="btn btn-primary">
                                <span class="button-text">Continue <i class="fas fa-arrow-right ms-2"></i></span>
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                            </button>
                        </div>

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i> After providing your details, you'll be able to either sign in to an existing account or create a new one to complete your booking.
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="booking-summary" data-aos="fade-up" data-aos-delay="100">
                    <h4>Booking Summary</h4>

                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Trip Type</div>
                        <div class="booking-summary-value" id="summary-booking-type">{{ session('guest_booking.booking_type') === 'one_way' ? 'One Way' : (session('guest_booking.booking_type') === 'return' ? 'Round Trip' : 'Hourly') }}</div>
                    </div>

                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Pickup</div>
                        <div class="booking-summary-value" id="summary-pickup">{{ session('guest_booking.pickup_address') }}</div>
                    </div>

                    @if(session('guest_booking.booking_type') !== 'hourly')
                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Dropoff</div>
                        <div class="booking-summary-value" id="summary-dropoff">{{ session('guest_booking.dropoff_address') }}</div>
                    </div>
                    @endif

                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Date & Time</div>
                        <div class="booking-summary-value" id="summary-datetime">{{ \Carbon\Carbon::parse(session('guest_booking.pickup_date'))->format('M d, Y h:i A') }}</div>
                    </div>

                    @if(session('guest_booking.booking_type') === 'return')
                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Return Date & Time</div>
                        <div class="booking-summary-value" id="summary-return-datetime">{{ \Carbon\Carbon::parse(session('guest_booking.return_date'))->format('M d, Y h:i A') }}</div>
                    </div>
                    @endif

                    @if(session('guest_booking.booking_type') === 'hourly')
                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Duration</div>
                        <div class="booking-summary-value" id="summary-hours">{{ session('guest_booking.duration_hours') }} hours</div>
                    </div>
                    @endif

                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Vehicle</div>
                        <div class="booking-summary-value" id="summary-vehicle">{{ \App\Models\Vehicle::find(session('guest_booking.vehicle_id'))->name }}</div>
                    </div>

                    <div class="booking-summary-item">
                        <div class="booking-summary-label">Total Fare</div>
                        <div class="booking-summary-value" id="summary-amount">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format(session('guest_booking.amount'), 2) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Prefill form if data exists in session
        @if(session()->has('client_details'))
            document.getElementById('name').value = "{{ session('client_details.name') }}";
            document.getElementById('email').value = "{{ session('client_details.email') }}";
            document.getElementById('phone').value = "{{ session('client_details.phone') }}";
            document.getElementById('special_requests').value = "{{ session('client_details.special_requests') }}";
        @endif

        // Handle form submission
        const clientDetailsForm = document.getElementById('clientDetailsForm');
        const continueBtn = document.getElementById('continueBtn');

        if (clientDetailsForm) {
            clientDetailsForm.addEventListener('submit', function(e) {
                // Show loading indicator
                if (continueBtn) {
                    const spinner = continueBtn.querySelector('.spinner-border');
                    const buttonText = continueBtn.querySelector('.button-text');

                    if (spinner && buttonText) {
                        spinner.classList.remove('d-none');
                        continueBtn.disabled = true;
                    }
                }

                // Form will submit normally
            });
        }
    });
</script>
@endsection
