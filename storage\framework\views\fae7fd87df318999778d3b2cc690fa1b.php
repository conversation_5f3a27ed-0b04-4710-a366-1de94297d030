<?php
    $emailStats = \App\Models\EmailLog::getStatistics();
    $recentEmails = \App\Models\EmailLog::getRecentActivity(5);
?>

<div class="row">
    <!-- Email Statistics Cards -->
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h3 class="mt-3"><?php echo e(number_format($emailStats['total'])); ?></h3>
                <p class="text-muted">Total Emails</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up"></i> All time
                </small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="mt-3"><?php echo e(number_format($emailStats['sent'])); ?></h3>
                <p class="text-muted">Emails Sent</p>
                <small class="text-success">
                    <?php echo e($emailStats['success_rate']); ?>% success rate
                </small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon text-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="mt-3"><?php echo e(number_format($emailStats['queued'])); ?></h3>
                <p class="text-muted">Queued</p>
                <small class="text-muted">
                    Pending delivery
                </small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="mt-3"><?php echo e(number_format($emailStats['failed'])); ?></h3>
                <p class="text-muted">Failed</p>
                <small class="text-danger">
                    Need attention
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Recent Email Activity -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title">Recent Email Activity</h4>
                    </div>
                    <div class="col-auto">
                        <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-outline-primary btn-sm">
                            View All
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if($recentEmails->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Template</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentEmails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $email): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($email->recipient_name ?: 'N/A'); ?></strong><br>
                                                <small class="text-muted"><?php echo e(Str::limit($email->recipient_email, 25)); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo e(Str::limit($email->subject, 30)); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $email->template))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo e($email->status_badge_class); ?>">
                                                <?php echo e($email->formatted_status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo e($email->created_at->diffForHumans()); ?>

                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-envelope-open-text fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent email activity</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Email Quick Actions -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Email Management</h4>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.emails.bulk')); ?>" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Bulk Email
                    </a>
                    <a href="<?php echo e(route('admin.emails.settings')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Email Settings
                    </a>
                    <a href="<?php echo e(route('admin.emails.index')); ?>" class="btn btn-outline-info">
                        <i class="fas fa-list"></i> View All Emails
                    </a>
                </div>

                <hr>

                <!-- Email Health Status -->
                <div class="mt-3">
                    <h6>Email System Health</h6>
                    <?php if($emailStats['success_rate'] >= 95): ?>
                        <div class="alert alert-success py-2">
                            <i class="fas fa-check-circle"></i> Excellent (<?php echo e($emailStats['success_rate']); ?>%)
                        </div>
                    <?php elseif($emailStats['success_rate'] >= 85): ?>
                        <div class="alert alert-warning py-2">
                            <i class="fas fa-exclamation-triangle"></i> Good (<?php echo e($emailStats['success_rate']); ?>%)
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger py-2">
                            <i class="fas fa-times-circle"></i> Needs Attention (<?php echo e($emailStats['success_rate']); ?>%)
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Quick Stats -->
                <div class="mt-3">
                    <h6>Today's Activity</h6>
                    <?php
                        $todayStats = \App\Models\EmailLog::whereDate('created_at', today())->count();
                        $todaySent = \App\Models\EmailLog::whereDate('created_at', today())->where('status', 'sent')->count();
                        $todayFailed = \App\Models\EmailLog::whereDate('created_at', today())->where('status', 'failed')->count();
                    ?>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-muted small">Total</div>
                            <div class="fw-bold"><?php echo e($todayStats); ?></div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted small">Sent</div>
                            <div class="fw-bold text-success"><?php echo e($todaySent); ?></div>
                        </div>
                        <div class="col-4">
                            <div class="text-muted small">Failed</div>
                            <div class="fw-bold text-danger"><?php echo e($todayFailed); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Templates -->
        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Popular Templates</h4>
            </div>
            <div class="card-body">
                <?php
                    $popularTemplates = \App\Models\EmailLog::selectRaw('template, COUNT(*) as count')
                        ->groupBy('template')
                        ->orderBy('count', 'desc')
                        ->limit(5)
                        ->get();
                ?>

                <?php if($popularTemplates->count() > 0): ?>
                    <?php $__currentLoopData = $popularTemplates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small"><?php echo e(ucfirst(str_replace('_', ' ', $template->template))); ?></span>
                            <span class="badge bg-light text-dark"><?php echo e(number_format($template->count)); ?></span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <p class="text-muted small">No templates used yet</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.badge.badge-success { background-color: #28a745 !important; }
.badge.badge-warning { background-color: #ffc107 !important; color: #212529 !important; }
.badge.badge-danger { background-color: #dc3545 !important; }
.badge.badge-secondary { background-color: #6c757d !important; }
</style>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/admin/dashboard/email-widget.blade.php ENDPATH**/ ?>