@extends('layouts.driver')

@section('title', 'Email Preferences')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Preferences</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('driver.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Email Preferences</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Manage Your Email Notifications</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Choose which email notifications you'd like to receive as a driver. You can always change these settings later.
                    </p>

                    <form method="POST" action="{{ route('driver.email-preferences.update') }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            @foreach($rolePreferences as $type => $label)
                                @php
                                    $preference = $preferences[$type] ?? ['enabled' => false, 'settings' => []];
                                    $isEssential = in_array($type, ['ride_requests', 'earnings_reports']);
                                @endphp
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card border {{ $preference['enabled'] ? 'border-success' : 'border-light' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ $label }}</h6>
                                                @if($isEssential)
                                                    <span class="badge bg-warning">Essential</span>
                                                @endif
                                            </div>
                                            
                                            <p class="card-text text-muted small">
                                                @switch($type)
                                                    @case('ride_requests')
                                                        Get notified when new ride requests are assigned to you
                                                        @break
                                                    @case('earnings_reports')
                                                        Receive weekly earnings summaries and performance reports
                                                        @break
                                                    @case('booking_confirmations')
                                                        Receive confirmations when bookings are made
                                                        @break
                                                    @case('payment_confirmations')
                                                        Get notified when payments are processed
                                                        @break
                                                    @case('status_updates')
                                                        Receive updates when booking status changes
                                                        @break
                                                    @case('cancellation_notices')
                                                        Get notified about ride cancellations
                                                        @break
                                                    @case('promotional_emails')
                                                        Receive special offers and promotional content
                                                        @break
                                                    @case('system_notifications')
                                                        Important system updates and maintenance notices
                                                        @break
                                                    @case('newsletter')
                                                        Monthly newsletter with company updates
                                                        @break
                                                    @default
                                                        Email notifications for {{ strtolower($label) }}
                                                @endswitch
                                            </p>

                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="preferences[{{ $type }}][enabled]" 
                                                       id="pref_{{ $type }}" 
                                                       value="1"
                                                       {{ $preference['enabled'] ? 'checked' : '' }}
                                                       {{ $isEssential ? 'disabled' : '' }}>
                                                <label class="form-check-label" for="pref_{{ $type }}">
                                                    {{ $preference['enabled'] ? 'Enabled' : 'Disabled' }}
                                                </label>
                                            </div>

                                            @if($isEssential)
                                                <input type="hidden" name="preferences[{{ $type }}][enabled]" value="1">
                                                <small class="text-warning">
                                                    <i class="fas fa-info-circle"></i> This is an essential notification for drivers and cannot be disabled.
                                                </small>
                                            @endif

                                            @if(!$isEssential && in_array($type, ['promotional_emails', 'newsletter', 'system_notifications', 'earnings_reports']))
                                                <div class="mt-2 frequency-settings" style="{{ $preference['enabled'] ? '' : 'display: none;' }}">
                                                    <label class="form-label small">Frequency:</label>
                                                    <select name="preferences[{{ $type }}][frequency]" class="form-select form-select-sm">
                                                        <option value="immediate" {{ ($preference['settings']['frequency'] ?? 'immediate') === 'immediate' ? 'selected' : '' }}>
                                                            Immediate
                                                        </option>
                                                        @if($type === 'earnings_reports')
                                                            <option value="weekly" {{ ($preference['settings']['frequency'] ?? '') === 'weekly' ? 'selected' : '' }}>
                                                                Weekly (Recommended)
                                                            </option>
                                                        @else
                                                            <option value="daily" {{ ($preference['settings']['frequency'] ?? '') === 'daily' ? 'selected' : '' }}>
                                                                Daily Digest
                                                            </option>
                                                            <option value="weekly" {{ ($preference['settings']['frequency'] ?? '') === 'weekly' ? 'selected' : '' }}>
                                                                Weekly Summary
                                                            </option>
                                                        @endif
                                                    </select>
                                                </div>
                                            @else
                                                <input type="hidden" name="preferences[{{ $type }}][frequency]" value="immediate">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="d-flex justify-content-between">
                            <form method="POST" action="{{ route('driver.email-preferences.reset') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-outline-secondary" 
                                        onclick="return confirm('Are you sure you want to reset all preferences to default?')">
                                    <i class="fas fa-undo"></i> Reset to Default
                                </button>
                            </form>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Driver Email Guide</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Essential Emails</h6>
                        <p class="mb-0">Some emails are marked as "Essential" and cannot be disabled. These include ride requests and earnings reports that are crucial for your driving activities.</p>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Recommended Settings</h6>
                        <p class="mb-0">We recommend keeping ride requests and earnings reports enabled to stay informed about your driving opportunities and income.</p>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Frequency Options</h6>
                        <p class="mb-0">For earnings reports, weekly frequency is recommended. For promotional emails, you can choose daily digest or weekly summary.</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Driver Benefits</h4>
                </div>
                <div class="card-body">
                    <h6>📧 Stay Informed:</h6>
                    <ul class="small">
                        <li>Get instant ride request notifications</li>
                        <li>Track your weekly earnings automatically</li>
                        <li>Receive important system updates</li>
                        <li>Learn about new driver benefits</li>
                    </ul>

                    <h6>💰 Maximize Earnings:</h6>
                    <ul class="small">
                        <li>Never miss a ride opportunity</li>
                        <li>Get notified about peak hour bonuses</li>
                        <li>Receive earnings optimization tips</li>
                        <li>Learn about referral bonuses</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Contact Information</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">Your current email address:</p>
                    <p class="fw-bold">{{ Auth::user()->email }}</p>
                    
                    <a href="{{ route('driver.profile.edit') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Update Email Address
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle preference toggle changes
    const preferenceToggles = document.querySelectorAll('.form-check-input[type="checkbox"]');
    
    preferenceToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const card = this.closest('.card');
            const label = this.nextElementSibling;
            const frequencySettings = card.querySelector('.frequency-settings');
            
            if (this.checked) {
                card.classList.remove('border-light');
                card.classList.add('border-success');
                label.textContent = 'Enabled';
                if (frequencySettings) {
                    frequencySettings.style.display = '';
                }
            } else {
                card.classList.remove('border-success');
                card.classList.add('border-light');
                label.textContent = 'Disabled';
                if (frequencySettings) {
                    frequencySettings.style.display = 'none';
                }
            }
        });
    });
});
</script>
@endsection
