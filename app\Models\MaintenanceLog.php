<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'vehicle_id',
        'driver_id',
        'date',
        'type',
        'description',
        'odometer',
        'cost',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'odometer' => 'integer',
        'cost' => 'decimal:2',
    ];

    /**
     * Get the vehicle that owns the maintenance log.
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the driver that created the maintenance log.
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }
}
