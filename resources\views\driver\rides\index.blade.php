@extends('layouts.driver')

@section('title', 'Available Rides')

@section('styles')
<style>
    /* Ride Card Styles */
    .ride-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .ride-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }

    .ride-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .ride-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .ride-card .card-body {
        padding: 25px;
    }

    .ride-card .card-footer {
        background-color: #fff;
        border-top: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .ride-detail {
        margin-bottom: 15px;
    }

    .ride-detail-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .ride-detail-value {
        color: #6c757d;
    }

    .vehicle-img {
        width: 100%;
        height: 140px;
        object-fit: cover;
        border-radius: 10px;
    }

    .vehicle-placeholder {
        width: 100%;
        height: 140px;
        background-color: #f8f9fa;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        font-size: 2rem;
    }

    .booking-badge {
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .badge-available {
        background-color: #cff4fc;
        color: #055160;
    }

    .ride-address {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .ride-address i {
        color: #ee393d;
        margin-right: 10px;
        margin-top: 3px;
    }

    .ride-address-content {
        flex: 1;
    }

    .ride-address-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 3px;
        font-size: 0.9rem;
    }

    .ride-address-value {
        color: #6c757d;
    }

    .ride-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .ride-info-item i {
        width: 20px;
        color: #ee393d;
        margin-right: 10px;
    }

    .ride-info-item span {
        color: #495057;
    }

    .ride-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 20px;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
    }

    .empty-state-text {
        color: #6c757d;
        margin-bottom: 20px;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .filter-bar {
        background-color: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .map-container {
        height: 200px;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 15px;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .ride-card {
            margin-bottom: 20px;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-search me-2 text-primary"></i> Available Rides
        </h1>
        <div class="d-flex">
            <a href="{{ route('driver.rides.my-rides') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-car me-1"></i> My Rides
            </a>
            <a href="{{ route('driver.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
            </a>
        </div>
    </div>

    <!-- Filter Bar -->
    <div class="filter-bar">
        <form action="{{ route('driver.rides.index') }}" method="GET">
            <div class="row">
                <div class="col-md-3 mb-3 mb-md-0">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" value="{{ request('date') }}">
                </div>
                <div class="col-md-3 mb-3 mb-md-0">
                    <label for="location" class="form-label">Location</label>
                    <input type="text" class="form-control" id="location" name="location" placeholder="Enter area or postcode" value="{{ request('location') }}">
                </div>
                <div class="col-md-3 mb-3 mb-md-0">
                    <label for="type" class="form-label">Booking Type</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">All Types</option>
                        <option value="one_way" {{ request('type') == 'one_way' ? 'selected' : '' }}>One Way</option>
                        <option value="round_trip" {{ request('type') == 'round_trip' ? 'selected' : '' }}>Round Trip</option>
                        <option value="airport" {{ request('type') == 'airport' ? 'selected' : '' }}>Airport Transfer</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="{{ route('driver.rides.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Available Rides -->
    @if($availableRides->isEmpty())
        <div class="card">
            <div class="card-body empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-car-side"></i>
                </div>
                <h3 class="empty-state-title">No Available Rides</h3>
                <p class="empty-state-text">There are no rides available at the moment. Please check back later or adjust your filter criteria.</p>
                <a href="{{ route('driver.dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt me-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
    @else
        <div class="row">
            @foreach($availableRides as $ride)
                <div class="col-lg-6">
                    <div class="ride-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>
                                <i class="fas fa-car me-2"></i> Booking #{{ $ride->booking_number }}
                            </h5>
                            <span class="booking-badge badge-available">
                                <i class="fas fa-check-circle me-1"></i> Available
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3 mb-md-0">
                                    @if($ride->vehicle->image)
                                        <img src="{{ asset('storage/' . $ride->vehicle->image) }}" class="vehicle-img" alt="{{ $ride->vehicle->name }}">
                                    @else
                                        <div class="vehicle-placeholder">
                                            <i class="fas fa-car"></i>
                                        </div>
                                    @endif
                                    <div class="mt-3">
                                        <div class="ride-detail-label">Vehicle</div>
                                        <div class="ride-detail-value">{{ $ride->vehicle->name }}</div>
                                    </div>
                                    <div class="mt-2">
                                        <div class="ride-price">@currency(){{ number_format($ride->amount, 2) }}</div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="ride-address">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <div class="ride-address-content">
                                            <div class="ride-address-label">Pickup Location</div>
                                            <div class="ride-address-value">{{ $ride->pickup_address }}</div>
                                        </div>
                                    </div>

                                    <div class="ride-address">
                                        <i class="fas fa-map-pin"></i>
                                        <div class="ride-address-content">
                                            <div class="ride-address-label">Dropoff Location</div>
                                            <div class="ride-address-value">{{ $ride->dropoff_address }}</div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-6">
                                            <div class="ride-info-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ $ride->pickup_date->format('M d, Y') }}</span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="ride-info-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{{ $ride->pickup_date->format('h:i A') }}</span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="ride-info-item">
                                                <i class="fas fa-route"></i>
                                                <span>{{ $ride->distance ? number_format($ride->distance, 1) . ' km' : 'N/A' }}</span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="ride-info-item">
                                                <i class="fas fa-tag"></i>
                                                <span>{{ ucfirst(str_replace('_', ' ', $ride->booking_type)) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex justify-content-between align-items-center">
                            <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i> View Details
                            </a>
                            <form action="{{ route('driver.rides.accept', $ride->id) }}" method="POST">
                                @csrf
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check-circle me-1"></i> Accept Ride
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
@endsection
