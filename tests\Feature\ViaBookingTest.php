<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Booking;
use App\Services\ViaBookingService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ViaBookingTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vehicle;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create([
            'name' => 'Test Vehicle',
            'base_fare' => 10.00,
            'price_per_km' => 2.00,
            'booking_fee' => 2.50
        ]);
    }

    /** @test */
    public function it_can_validate_via_points()
    {
        $viaPoints = [
            [
                'address' => '123 Test Street, London',
                'lat' => 51.5074,
                'lng' => -0.1278,
                'notes' => 'Test note'
            ]
        ];

        $errors = ViaBookingService::validateViaPoints($viaPoints);
        
        $this->assertEmpty($errors);
    }

    /** @test */
    public function it_rejects_invalid_via_points()
    {
        $viaPoints = [
            [
                'address' => '', // Empty address
                'lat' => null,
                'lng' => null,
                'notes' => ''
            ]
        ];

        $errors = ViaBookingService::validateViaPoints($viaPoints);
        
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('Address is required', implode(' ', $errors));
    }

    /** @test */
    public function it_can_format_via_points_for_storage()
    {
        $viaPoints = [
            [
                'address' => '123 Test Street, London',
                'lat' => '51.5074',
                'lng' => '-0.1278',
                'notes' => 'Test note'
            ]
        ];

        $formatted = ViaBookingService::formatViaPointsForStorage($viaPoints);
        
        $this->assertEquals('123 Test Street, London', $formatted[0]['address']);
        $this->assertEquals(51.5074, $formatted[0]['lat']);
        $this->assertEquals(-0.1278, $formatted[0]['lng']);
        $this->assertEquals('Test note', $formatted[0]['notes']);
    }

    /** @test */
    public function it_can_create_booking_with_via_points()
    {
        $this->actingAs($this->user);

        $viaPoints = [
            [
                'address' => '123 Test Street, London',
                'lat' => 51.5074,
                'lng' => -0.1278,
                'notes' => 'Test stop'
            ]
        ];

        $booking = Booking::create([
            'user_id' => $this->user->id,
            'vehicle_id' => $this->vehicle->id,
            'booking_number' => 'TEST123',
            'booking_type' => 'one_way',
            'pickup_address' => 'Start Address',
            'dropoff_address' => 'End Address',
            'pickup_date' => now()->addDay(),
            'amount' => 25.00,
            'status' => 'pending',
            'payment_status' => 'pending',
            'via_points' => ViaBookingService::formatViaPointsForStorage($viaPoints),
            'via_count' => 1,
            'via_surcharge' => 5.00
        ]);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'via_count' => 1,
            'via_surcharge' => 5.00
        ]);

        $this->assertTrue($booking->hasViaPoints());
        $this->assertEquals(1, $booking->getViaPointsCount());
    }

    /** @test */
    public function it_can_get_formatted_via_points()
    {
        $viaPoints = [
            [
                'address' => '123 Test Street, London',
                'lat' => 51.5074,
                'lng' => -0.1278,
                'notes' => 'Test stop'
            ]
        ];

        $booking = Booking::create([
            'user_id' => $this->user->id,
            'vehicle_id' => $this->vehicle->id,
            'booking_number' => 'TEST123',
            'booking_type' => 'one_way',
            'pickup_address' => 'Start Address',
            'dropoff_address' => 'End Address',
            'pickup_date' => now()->addDay(),
            'amount' => 25.00,
            'status' => 'pending',
            'payment_status' => 'pending',
            'via_points' => ViaBookingService::formatViaPointsForStorage($viaPoints),
            'via_count' => 1,
            'via_surcharge' => 5.00
        ]);

        $formattedViaPoints = $booking->getFormattedViaPoints();
        
        $this->assertCount(1, $formattedViaPoints);
        $this->assertEquals(1, $formattedViaPoints[0]['index']);
        $this->assertEquals('123 Test Street, London', $formattedViaPoints[0]['address']);
        $this->assertEquals('Test stop', $formattedViaPoints[0]['notes']);
    }

    /** @test */
    public function it_enforces_maximum_via_points_limit()
    {
        $tooManyViaPoints = [];
        for ($i = 0; $i < 6; $i++) { // More than the limit of 5
            $tooManyViaPoints[] = [
                'address' => "Address {$i}",
                'lat' => 51.5074,
                'lng' => -0.1278,
                'notes' => "Note {$i}"
            ];
        }

        $errors = ViaBookingService::validateViaPoints($tooManyViaPoints);
        
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('Maximum 5 via points allowed', implode(' ', $errors));
    }

    /** @test */
    public function it_can_calculate_via_surcharge()
    {
        $viaPointsCount = 3;
        $surchargePerPoint = 5.00;
        $maxSurcharge = 25.00;
        
        $totalSurcharge = ViaBookingService::calculateViaSurcharge($viaPointsCount, $surchargePerPoint, $maxSurcharge);
        
        $this->assertEquals(15.00, $totalSurcharge); // 3 × £5.00 = £15.00
    }

    /** @test */
    public function it_enforces_maximum_surcharge_limit()
    {
        $viaPointsCount = 6; // Would be £30.00 at £5.00 each
        $surchargePerPoint = 5.00;
        $maxSurcharge = 25.00;
        
        $totalSurcharge = ViaBookingService::calculateViaSurcharge($viaPointsCount, $surchargePerPoint, $maxSurcharge);
        
        $this->assertEquals(25.00, $totalSurcharge); // Capped at maximum
    }

    /** @test */
    public function booking_can_get_journey_summary_with_via_points()
    {
        $viaPoints = [
            [
                'address' => '123 Test Street, London',
                'lat' => 51.5074,
                'lng' => -0.1278,
                'notes' => 'Test stop'
            ]
        ];

        $booking = Booking::create([
            'user_id' => $this->user->id,
            'vehicle_id' => $this->vehicle->id,
            'booking_number' => 'TEST123',
            'booking_type' => 'one_way',
            'pickup_address' => 'Start Address',
            'dropoff_address' => 'End Address',
            'pickup_date' => now()->addDay(),
            'amount' => 25.00,
            'status' => 'pending',
            'payment_status' => 'pending',
            'via_points' => ViaBookingService::formatViaPointsForStorage($viaPoints),
            'via_count' => 1,
            'via_surcharge' => 5.00
        ]);

        $summary = $booking->getJourneySummary();
        
        $this->assertEquals('Start Address', $summary['pickup']);
        $this->assertEquals('End Address', $summary['dropoff']);
        $this->assertTrue($summary['has_via_points']);
        $this->assertEquals(5.00, $summary['via_surcharge']);
        $this->assertCount(1, $summary['via_points']);
    }
}
