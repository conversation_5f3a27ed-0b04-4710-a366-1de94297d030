<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add indexes to improve query performance
            $table->index('status');
            $table->index('pickup_date');
            $table->index('driver_id');
            $table->index('vehicle_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['status']);
            $table->dropIndex(['pickup_date']);
            $table->dropIndex(['driver_id']);
            $table->dropIndex(['vehicle_id']);
            $table->dropIndex(['user_id']);
        });
    }
};
