<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="currency-symbol" content="<?php echo e($currencySymbol ?? \App\Services\SettingsService::getCurrencySymbol()); ?>">
    <meta name="currency-code" content="<?php echo e($currencyCode ?? \App\Services\SettingsService::getCurrencyCode()); ?>">
    <meta name="distance-unit" content="<?php echo e(\App\Services\SettingsService::getDistanceUnit()); ?>">

    <!-- Autocomplete Settings -->
    <?php
        $autocompleteSettings = \App\Services\SettingsService::getAutocompleteSettings();
    ?>
    <meta name="autocomplete-enabled" content="<?php echo e($autocompleteSettings['enabled'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-restrict-country" content="<?php echo e($autocompleteSettings['restrict_country'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-country" content="<?php echo e($autocompleteSettings['country']); ?>">
    <meta name="autocomplete-types" content="<?php echo e($autocompleteSettings['types']); ?>">
    <meta name="autocomplete-bias-radius" content="<?php echo e($autocompleteSettings['bias_radius']); ?>">
    <meta name="autocomplete-use-strict-bounds" content="<?php echo e($autocompleteSettings['use_strict_bounds'] ? 'true' : 'false'); ?>">
    <meta name="autocomplete-fields" content="<?php echo e($autocompleteSettings['fields']); ?>">

    <title><?php echo $__env->yieldContent('title'); ?> - <?php echo e($companyName ?? config('app.name', 'Ynr Cars')); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo e(asset('images/favicon.png')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/pagination.css')); ?>">

    <style>
        :root {
            --primary-color: <?php echo e($primaryColor ?? '#ee393d'); ?>;
            --secondary-color: <?php echo e($secondaryColor ?? '#343a40'); ?>;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }

        /* Preloader */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .preloader.fade-out {
            opacity: 0;
            visibility: hidden;
        }

        .preloader-content {
            text-align: center;
        }

        .preloader-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }

        .navbar-nav .nav-link {
            color: black;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary-color);
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .sidebar {
            background-color: var(--secondary-color);
            color: #fff;
            min-height: calc(100vh - 76px);
            padding-top: 20px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: var(--primary-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .content-wrapper {
            padding: 20px;
        }

        /* Breadcrumb Styling */
        .breadcrumb {
            background-color: #fff;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            font-weight: 500;
        }

        .breadcrumb-item.active {
            color: #6c757d;
            font-weight: 500;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: #6c757d;
        }

        /* Mobile Responsiveness */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -100%;
                width: 80%;
                height: calc(100% - 56px);
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .content-wrapper {
                width: 100%;
                padding: 15px;
            }

            .navbar-toggler {
                display: block;
            }

            .mobile-sidebar-toggle {
                display: block;
            }
        }

        /* Footer Styling */
        .footer {
            background-color: var(--secondary-color);
            color: #fff;
            padding: 20px 0;
            margin-top: 30px;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.75);
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        .dashboard-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .dashboard-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
        }

        .booking-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-confirmed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-in_progress {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background-color: #c3e6cb;
            color: #155724;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: <?php echo e($colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -15)); ?>;
            border-color: <?php echo e($colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -15)); ?>;
            color: var(--secondary-color);
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        <?php echo $__env->yieldContent('styles'); ?>
    </style>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader">
        <div class="preloader-content">
            <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 20px;"><?php echo e($companyName ?? 'YNR CARS'); ?></h2>
            <div class="preloader-spinner"></div>
        </div>
    </div>

    <header>
        <nav class="navbar navbar-expand-md navbar-light">
            <div class="container-fluid">
<a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    <?php
                        $logoPath = \App\Services\SettingsService::get('logo');
                        $logoExists = $logoPath && file_exists(public_path('storage/' . $logoPath));
                    ?>

                    <?php if($logoExists): ?>
                        <img src="<?php echo e(asset('storage/' . $logoPath)); ?>" alt="<?php echo e(config('app.name', 'Ynr Cars')); ?>" height="40">
                    <?php else: ?>
                        <span style="font-weight: bold; font-size: 24px; color: var(--primary-color);"><?php echo e($companyName ?? 'YNR'); ?> <span style="color: #fff;">Cars</span></span>
                    <?php endif; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="<?php echo e(__('Toggle navigation')); ?>">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('client.dashboard')); ?>">Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('client.bookings.index')); ?>">My Bookings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('booking.index')); ?>">Book a Ride</a>
                        </li>
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Notifications -->
                        <li class="nav-item dropdown">
                            <a id="notificationsDropdown" class="nav-link" href="<?php echo e(route('notifications.index')); ?>" role="button">
                                <i class="fas fa-bell"></i>
                                <?php if(Auth::user()->unreadNotifications->count() > 0): ?>
                                    <span class="badge bg-danger"><?php echo e(Auth::user()->unreadNotifications->count()); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>

                        <li class="nav-item dropdown">
                            <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                <?php echo e(Auth::user()->name); ?>

                            </a>

                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <a class="dropdown-item" href="<?php echo e(route('client.profile.index')); ?>">
                                    <?php echo e(__('Profile')); ?>

                                </a>
                                <a class="dropdown-item" href="<?php echo e(route('notifications.index')); ?>">
                                    <?php echo e(__('Notifications')); ?>

                                    <?php if(Auth::user()->unreadNotifications->count() > 0): ?>
                                        <span class="badge bg-danger"><?php echo e(Auth::user()->unreadNotifications->count()); ?></span>
                                    <?php endif; ?>
                                </a>
                                <a class="dropdown-item" href="<?php echo e(route('logout')); ?>"
                                   onclick="event.preventDefault();
                                                 document.getElementById('logout-form').submit();">
                                    <?php echo e(__('Logout')); ?>

                                </a>

                                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                                    <?php echo csrf_field(); ?>
                                </form>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-2 sidebar">
                    <h5 class="px-3 mb-3">Client Panel</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('client.dashboard')); ?>">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.profile.*') ? 'active' : ''); ?>" href="<?php echo e(route('client.profile.index')); ?>">
                                <i class="fas fa-user"></i> Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.bookings.*') ? 'active' : ''); ?>" href="<?php echo e(route('client.bookings.index')); ?>">
                                <i class="fas fa-calendar-check"></i> My Bookings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.payments.*') ? 'active' : ''); ?>" href="<?php echo e(route('client.payments.index')); ?>">
                                <i class="fas fa-credit-card"></i> Payments
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.notifications.*') ? 'active' : ''); ?>" href="<?php echo e(route('client.notifications.settings')); ?>">
                                <i class="fas fa-bell"></i> Notifications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('client.email-preferences.*') ? 'active' : ''); ?>" href="<?php echo e(route('client.email-preferences.index')); ?>">
                                <i class="fas fa-envelope-open"></i> Email Preferences
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('booking.index')); ?>">
                                <i class="fas fa-plus-circle"></i> New Booking
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Main Content -->
                <div class="col-md-10 content-wrapper">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('client.dashboard')); ?>">Dashboard</a></li>
                            <?php if(request()->routeIs('client.profile.*')): ?>
                                <li class="breadcrumb-item <?php echo e(request()->routeIs('client.profile.index') ? 'active' : ''); ?>">
                                    <?php if(request()->routeIs('client.profile.index')): ?>
                                        Profile
                                    <?php else: ?>
                                        <a href="<?php echo e(route('client.profile.index')); ?>">Profile</a>
                                    <?php endif; ?>
                                </li>
                                <?php if(request()->routeIs('client.profile.edit')): ?>
                                    <li class="breadcrumb-item active">Edit Profile</li>
                                <?php elseif(request()->routeIs('client.profile.change-password')): ?>
                                    <li class="breadcrumb-item active">Change Password</li>
                                <?php endif; ?>
                            <?php elseif(request()->routeIs('client.bookings.*')): ?>
                                <li class="breadcrumb-item <?php echo e(request()->routeIs('client.bookings.index') ? 'active' : ''); ?>">
                                    <?php if(request()->routeIs('client.bookings.index')): ?>
                                        My Bookings
                                    <?php else: ?>
                                        <a href="<?php echo e(route('client.bookings.index')); ?>">My Bookings</a>
                                    <?php endif; ?>
                                </li>
                                <?php if(request()->routeIs('client.bookings.show')): ?>
                                    <li class="breadcrumb-item active">Booking Details</li>
                                <?php elseif(request()->routeIs('client.bookings.review')): ?>
                                    <li class="breadcrumb-item active">Leave Review</li>
                                <?php endif; ?>
                            <?php elseif(request()->routeIs('client.payments.*')): ?>
                                <li class="breadcrumb-item <?php echo e(request()->routeIs('client.payments.index') ? 'active' : ''); ?>">
                                    <?php if(request()->routeIs('client.payments.index')): ?>
                                        Payments
                                    <?php else: ?>
                                        <a href="<?php echo e(route('client.payments.index')); ?>">Payments</a>
                                    <?php endif; ?>
                                </li>
                                <?php if(request()->routeIs('client.payments.show')): ?>
                                    <li class="breadcrumb-item active">Payment Details</li>
                                <?php elseif(request()->routeIs('client.payments.invoice')): ?>
                                    <li class="breadcrumb-item active">Invoice</li>
                                <?php elseif(request()->routeIs('client.payments.history')): ?>
                                    <li class="breadcrumb-item active">Payment History</li>
                                <?php endif; ?>

                            <?php elseif(request()->routeIs('client.notifications.*')): ?>
                                <li class="breadcrumb-item active">Notification Settings</li>
                            <?php endif; ?>
                        </ol>
                    </nav>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                            <i class="fas fa-check-circle me-2"></i> <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i> <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('warning')): ?>
                        <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i> <?php echo e(session('warning')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('info')): ?>
                        <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
                            <i class="fas fa-info-circle me-2"></i> <?php echo e(session('info')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i> Please check the form for errors.
                            <ul class="mb-0 mt-2">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-dark text-white">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo e(date('Y')); ?> <?php echo e($companyName ?? config('app.name', 'Ynr Cars')); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="<?php echo e(url('/privacy-policy')); ?>" class="text-white me-3">Privacy Policy</a>
                    <a href="<?php echo e(url('/terms-of-service')); ?>" class="text-white me-3">Terms of Service</a>
                    <a href="<?php echo e(url('/contact')); ?>" class="text-white">Contact Us</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="<?php echo e(asset('js/autocomplete-settings.js')); ?>"></script>

    <!-- Google Maps API with Places Library -->
    <?php
        $googleMapsApiKey = \App\Services\SettingsService::getGoogleMapsApiKey();
    ?>
    <?php if($googleMapsApiKey): ?>
    <script>
        function initGoogleMapsApi() {
            // Dispatch event to notify other scripts that Google Maps API is loaded
            window.dispatchEvent(new Event('google-maps-loaded'));
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places&callback=initGoogleMapsApi" async defer></script>
    <?php endif; ?>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Preloader
        window.addEventListener('load', function() {
            const preloader = document.querySelector('.preloader');
            preloader.classList.add('fade-out');
            setTimeout(function() {
                preloader.style.display = 'none';
            }, 500);
        });

        // Mobile Sidebar Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const navbarToggler = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');

            if (navbarToggler && sidebar) {
                navbarToggler.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside
                document.addEventListener('click', function(event) {
                    if (!sidebar.contains(event.target) && !navbarToggler.contains(event.target) && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        });
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views/layouts/client.blade.php ENDPATH**/ ?>