<?php $__env->startSection('title', 'Driver Assigned to Your Booking'); ?>

<?php $__env->startSection('content'); ?>
<h2>Driver Assigned to Your Booking</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>Great news! We've assigned a professional driver to your upcoming ride.</p>

<div class="booking-details">
    <h3>Your Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><strong><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name); ?></span>
    </div>
</div>

<div class="booking-details">
    <h3>Your Driver Information</h3>
    
    <div class="detail-row">
        <span class="detail-label">Driver Name:</span>
        <span class="detail-value"><strong><?php echo e($driver->name); ?></strong></span>
    </div>
    
    <?php if($driver->phone): ?>
    <div class="detail-row">
        <span class="detail-label">Driver Phone:</span>
        <span class="detail-value"><?php echo e($driver->phone); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if($driver->license_number): ?>
    <div class="detail-row">
        <span class="detail-label">License Number:</span>
        <span class="detail-value"><?php echo e($driver->license_number); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if($vehicle->license_plate): ?>
    <div class="detail-row">
        <span class="detail-label">Vehicle License Plate:</span>
        <span class="detail-value"><?php echo e($vehicle->license_plate); ?></span>
    </div>
    <?php endif; ?>
    
    <?php if($vehicle->color): ?>
    <div class="detail-row">
        <span class="detail-label">Vehicle Color:</span>
        <span class="detail-value"><?php echo e($vehicle->color); ?></span>
    </div>
    <?php endif; ?>
</div>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #155724; margin-top: 0;">✓ Your Ride is Confirmed!</h3>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>Your driver has been notified and will contact you before pickup</li>
        <li>Please be ready at your pickup location 5 minutes early</li>
        <li>Your driver will arrive in a <?php echo e($vehicle->color ?? ''); ?> <?php echo e($vehicle->name); ?></li>
        <li>Keep your booking confirmation number handy: <strong>#<?php echo e($booking->booking_number); ?></strong></li>
    </ul>
</div>

<?php if($booking->notes): ?>
<div style="margin: 20px 0; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
    <strong style="color: #856404;">Special Instructions:</strong><br>
    <span style="color: #856404;"><?php echo e($booking->notes); ?></span>
</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('client.bookings.show', $booking->id)); ?>" class="btn">Track Your Ride</a>
</div>

<h3>What to Expect:</h3>
<ul>
    <li><strong>15-30 minutes before pickup:</strong> Your driver will contact you</li>
    <li><strong>At pickup time:</strong> Look for the <?php echo e($vehicle->color ?? ''); ?> <?php echo e($vehicle->name); ?></li>
    <li><strong>Identification:</strong> Your driver will confirm your name and destination</li>
    <li><strong>During the ride:</strong> Enjoy professional, safe transportation</li>
    <li><strong>After the ride:</strong> You'll receive a receipt and can rate your experience</li>
</ul>

<h3>Driver Contact Information:</h3>
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p><strong>Driver:</strong> <?php echo e($driver->name); ?></p>
    <?php if($driver->phone): ?>
    <p><strong>Phone:</strong> <a href="tel:<?php echo e($driver->phone); ?>"><?php echo e($driver->phone); ?></a></p>
    <?php endif; ?>
    <p><strong>Vehicle:</strong> <?php echo e($vehicle->color ?? ''); ?> <?php echo e($vehicle->name); ?></p>
    <?php if($vehicle->license_plate): ?>
    <p><strong>License Plate:</strong> <?php echo e($vehicle->license_plate); ?></p>
    <?php endif; ?>
</div>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us as soon as possible:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or manage your booking online in your account</li>
</ul>

<p>We're excited to provide you with excellent service!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\driver-assigned.blade.php ENDPATH**/ ?>