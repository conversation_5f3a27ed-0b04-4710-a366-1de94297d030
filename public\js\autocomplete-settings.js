/**
 * Autocomplete Settings JavaScript
 *
 * This file handles the autocomplete functionality for address fields
 * based on the settings configured in the admin panel.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get autocomplete settings from meta tags with safe defaults
    const autocompleteEnabled = document.querySelector('meta[name="autocomplete-enabled"]')?.content === 'true';
    const restrictCountry = document.querySelector('meta[name="autocomplete-restrict-country"]')?.content === 'true';
    const country = document.querySelector('meta[name="autocomplete-country"]')?.content || '';
    const types = document.querySelector('meta[name="autocomplete-types"]')?.content || 'geocode';
    const biasRadius = parseInt(document.querySelector('meta[name="autocomplete-bias-radius"]')?.content || '50');
    const useStrictBounds = document.querySelector('meta[name="autocomplete-use-strict-bounds"]')?.content === 'true';
    const fields = document.querySelector('meta[name="autocomplete-fields"]')?.content || 'formatted_address';

    // If autocomplete is not enabled, exit early
    if (!autocompleteEnabled) {
        console.log('Autocomplete is disabled in settings');
        return;
    }

    // Initialize autocomplete on all address input fields
    const addressInputs = document.querySelectorAll('.address-autocomplete');

    if (addressInputs.length === 0) {
        console.log('No address input fields found with class .address-autocomplete');
        return;
    }

    // Function to initialize Google Places Autocomplete
    function initAutocomplete() {
        addressInputs.forEach(input => {
            // Get any field-specific settings
            const fieldCountry = input.dataset.country || country;
            const fieldTypes = input.dataset.types || types;

            // Create options object for autocomplete
            const options = {
                fields: fields ? fields.split(',') : ['address_components', 'geometry', 'name'],
            };

            // Add restrictions if enabled
            if (restrictCountry && fieldCountry) {
                options.componentRestrictions = { country: fieldCountry };
            }

            // Add types if specified
            if (fieldTypes) {
                options.types = fieldTypes.split(',');
            }

            // Create the autocomplete object
            const autocomplete = new google.maps.places.Autocomplete(input, options);

            // Set bias if coordinates are available
            if (navigator.geolocation && biasRadius > 0) {
                navigator.geolocation.getCurrentPosition(position => {
                    const circle = new google.maps.Circle({
                        center: {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        },
                        radius: biasRadius
                    });

                    autocomplete.setBounds(circle.getBounds());

                    if (useStrictBounds) {
                        autocomplete.setOptions({ strictBounds: true });
                    }
                });
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();

                if (!place.geometry) {
                    console.log('No details available for this place');
                    return;
                }

                // Fill in the lat/lng fields if they exist
                const latField = document.getElementById(input.dataset.latField);
                const lngField = document.getElementById(input.dataset.lngField);

                if (latField && lngField) {
                    latField.value = place.geometry.location.lat();
                    lngField.value = place.geometry.location.lng();
                }

                // Trigger a custom event that other scripts can listen for
                const event = new CustomEvent('placeSelected', {
                    detail: {
                        place: place,
                        input: input
                    }
                });
                document.dispatchEvent(event);
            });
        });
    }

    // Check if Google Maps API is already loaded
    if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
        console.log('Google Maps API not loaded yet, waiting for it to load');
    } else {
        console.log('Google Maps API already loaded, initializing autocomplete');
        initAutocomplete();
    }

    // Listen for Google Maps API loaded event
    window.addEventListener('google-maps-loaded', function() {
        console.log('Google Maps API loaded, initializing autocomplete');
        initAutocomplete();
    });
});
