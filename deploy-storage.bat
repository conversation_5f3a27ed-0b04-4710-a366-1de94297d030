@echo off
REM YNR Cars - Storage Setup Script for Windows
REM This script sets up storage directories for Windows development/production

echo.
echo YNR Cars - Storage Setup Script for Windows
echo ==========================================
echo.

REM Get the current directory (should be the Laravel project root)
set PROJECT_ROOT=%cd%
echo Project root: %PROJECT_ROOT%
echo.

REM Step 1: Create storage directories
echo [STEP] Creating storage directories...

set STORAGE_DIRS=storage\app storage\app\public storage\app\public\profile-photos storage\app\public\driver-documents storage\app\public\vehicles storage\app\public\blog-posts storage\logs storage\framework storage\framework\cache storage\framework\cache\data storage\framework\sessions storage\framework\views bootstrap\cache

for %%d in (%STORAGE_DIRS%) do (
    if not exist "%%d" (
        mkdir "%%d" 2>nul
        echo [INFO] Created directory: %%d
    ) else (
        echo [INFO] Directory already exists: %%d
    )
)

echo.

REM Step 2: Create storage link
echo [STEP] Creating storage link...

set STORAGE_LINK=%PROJECT_ROOT%\public\storage
set STORAGE_TARGET=%PROJECT_ROOT%\storage\app\public

REM Remove existing link/directory if it exists
if exist "%STORAGE_LINK%" (
    rmdir /s /q "%STORAGE_LINK%" 2>nul
    del /q "%STORAGE_LINK%" 2>nul
)

REM Create junction (Windows equivalent of symlink)
mklink /J "%STORAGE_LINK%" "%STORAGE_TARGET%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Storage link created successfully
) else (
    echo [WARNING] Failed to create storage link. Trying alternative method...
    REM Try using PHP artisan command instead
    php artisan storage:link 2>nul
    if %errorlevel% equ 0 (
        echo [INFO] Storage link created using artisan command
    ) else (
        echo [ERROR] Failed to create storage link. Please run manually: php artisan storage:link
    )
)

echo.

REM Step 3: Create security files
echo [STEP] Creating security files...

REM Create .htaccess for storage/app (prevent direct access)
echo ^<IfModule mod_authz_core.c^> > storage\app\.htaccess
echo     Require all denied >> storage\app\.htaccess
echo ^</IfModule^> >> storage\app\.htaccess
echo ^<IfModule !mod_authz_core.c^> >> storage\app\.htaccess
echo     Order deny,allow >> storage\app\.htaccess
echo     Deny from all >> storage\app\.htaccess
echo ^</IfModule^> >> storage\app\.htaccess

REM Create .htaccess for storage/logs (prevent direct access)
echo ^<IfModule mod_authz_core.c^> > storage\logs\.htaccess
echo     Require all denied >> storage\logs\.htaccess
echo ^</IfModule^> >> storage\logs\.htaccess
echo ^<IfModule !mod_authz_core.c^> >> storage\logs\.htaccess
echo     Order deny,allow >> storage\logs\.htaccess
echo     Deny from all >> storage\logs\.htaccess
echo ^</IfModule^> >> storage\logs\.htaccess

echo [INFO] Security files created
echo.

REM Step 4: Test storage functionality
echo [STEP] Testing storage functionality...

REM Test if we can write to storage
echo Storage test > storage\app\public\test.txt 2>nul
if exist storage\app\public\test.txt (
    del storage\app\public\test.txt
    echo [INFO] Storage write test: PASSED
) else (
    echo [ERROR] Storage write test: FAILED
)

REM Test if storage link works
if exist public\storage (
    echo [INFO] Storage link test: PASSED
) else (
    echo [ERROR] Storage link test: FAILED
)

echo.

REM Step 5: Run Laravel storage setup command
echo [STEP] Running Laravel storage setup...

if exist artisan (
    php artisan storage:setup --force 2>nul
    if %errorlevel% equ 0 (
        echo [INFO] Laravel storage setup completed
    ) else (
        echo [WARNING] Laravel storage setup failed or not available
    )
) else (
    echo [WARNING] Laravel artisan not found. Skipping Laravel-specific setup.
)

echo.

REM Step 6: Display summary
echo [STEP] Setup Summary
echo ==============
echo ✓ Storage directories created
echo ✓ Storage link created
echo ✓ Security files created
echo.

echo [INFO] Storage setup completed successfully!
echo [INFO] Your application should now be able to store and serve files properly.
echo.

echo [WARNING] IMPORTANT NOTES:
echo 1. Ensure your web server has read/write access to storage directories
echo 2. Configure your .env file with correct APP_URL for file URLs
echo 3. Test file uploads in your application
echo 4. For production, consider cloud storage solutions
echo.

echo [INFO] For development on Windows:
echo - Make sure your web server (Apache/Nginx) can access the storage directories
echo - Check that the storage link is working properly
echo - Test file uploads through your application
echo.

pause
