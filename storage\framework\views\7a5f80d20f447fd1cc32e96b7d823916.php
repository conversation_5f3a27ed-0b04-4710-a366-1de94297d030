<?php $__env->startSection('title', 'Ride Cancellation Notice'); ?>

<?php $__env->startSection('content'); ?>
<h2>Ride Cancellation Notice</h2>

<p>Dear <?php echo e($driver->name); ?>,</p>

<p>We're writing to inform you that a ride assignment has been cancelled.</p>

<div class="booking-details">
    <h3>Cancelled Ride Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Client Name:</span>
        <span class="detail-value"><?php echo e($user->name); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Original Pickup Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Estimated Earnings:</span>
        <span class="detail-value"><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount * 0.8, 2)); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Cancellation Time:</span>
        <span class="detail-value"><?php echo e(now()->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
</div>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h3 style="color: #856404; margin-top: 0;">Important Information</h3>
    <ul style="color: #856404; margin-bottom: 0;">
        <li>This ride has been removed from your schedule</li>
        <li>You are now available for other ride assignments during this time slot</li>
        <li>No action is required from your side</li>
        <li>Your availability status remains unchanged</li>
    </ul>
</div>

<h3>What This Means for You:</h3>
<ul>
    <li><strong>Schedule Update:</strong> This time slot is now free in your schedule</li>
    <li><strong>Earnings:</strong> No earnings impact since the ride was cancelled before completion</li>
    <li><strong>Availability:</strong> You may receive new ride requests for this time period</li>
    <li><strong>Rating:</strong> This cancellation does not affect your driver rating</li>
</ul>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('driver.rides.index')); ?>" class="btn">View Available Rides</a>
</div>

<h3>Stay Active!</h3>
<p>Don't let this cancellation discourage you. Keep your availability status active to receive more ride requests. 
Remember, cancellations are a normal part of the business and don't reflect on your service quality.</p>

<h3>Need Support?</h3>
<p>If you have any questions about this cancellation or need assistance, please contact our driver support team:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or use the driver app support feature</li>
</ul>

<p>Thank you for your understanding and continued partnership with <?php echo e($companyName); ?>.</p>

<p>Safe driving,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\cancellation-driver.blade.php ENDPATH**/ ?>