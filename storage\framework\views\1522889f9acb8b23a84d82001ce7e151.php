<?php $__env->startSection('title', 'Earnings History'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .content-wrapper {
        padding: 20px;
    }



    .earnings-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .earnings-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .earnings-card .card-body {
        padding: 20px;
    }

    .filter-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .earnings-total {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .earnings-total-label {
        font-weight: 600;
    }

    .earnings-total-amount {
        font-size: 1.2rem;
        font-weight: 700;
        color: #28a745;
    }

    .earnings-table th,
    .earnings-table td {
        vertical-align: middle;
    }

    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Earnings History</h2>
                <a href="<?php echo e(route('driver.earnings.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i> Earnings Dashboard
                </a>
            </div>

            <div class="filter-form">
                <form action="<?php echo e(route('driver.earnings.history')); ?>" method="GET">
                    <div class="row">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate ?? ''); ?>">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate ?? ''); ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">Filter</button>
                            <a href="<?php echo e(route('driver.earnings.history')); ?>" class="btn btn-secondary">Reset</a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="earnings-total">
                <div class="earnings-total-label">Total Earnings<?php echo e($startDate || $endDate ? ' (Filtered)' : ''); ?>:</div>
                <div class="earnings-total-amount"><?php echo e($currencySymbol); ?><?php echo e(number_format($totalFiltered, 2)); ?></div>
            </div>

            <div class="card earnings-card">
                <div class="card-header">
                    <h5 class="mb-0">Earnings History</h5>
                </div>
                <div class="card-body">
                    <?php if($earnings->isEmpty()): ?>
                        <p class="text-center text-muted py-5">No earnings found for the selected period</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover earnings-table">
                                <thead>
                                    <tr>
                                        <th>Booking #</th>
                                        <th>Client</th>
                                        <th>Date</th>
                                        <th>Pickup</th>
                                        <th>Dropoff</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $earnings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $earning): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($earning->booking_number); ?></td>
                                            <td><?php echo e($earning->user->name); ?></td>
                                            <td><?php echo e($earning->completed_at ? $earning->completed_at->format('M d, Y') : 'N/A'); ?></td>
                                            <td><?php echo e(Str::limit($earning->pickup_address, 20)); ?></td>
                                            <td><?php echo e(Str::limit($earning->dropoff_address, 20)); ?></td>
                                            <td><?php echo e(ucfirst($earning->booking_type)); ?></td>
                                            <td><?php echo e($currencySymbol); ?><?php echo e(number_format($earning->amount, 2)); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <?php echo e($earnings->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.driver', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\driver\earnings\history.blade.php ENDPATH**/ ?>