/**
 * Via Booking JavaScript Handler
 * Manages via points/waypoints functionality in the booking system
 */

class ViaBookingManager {
    constructor() {
        this.viaPoints = [];
        this.maxViaPoints = 5;
        this.viaContainer = null;
        this.addViaButton = null;
        this.isInitialized = false;

        // Bind methods
        this.init = this.init.bind(this);
        this.addViaPoint = this.addViaPoint.bind(this);
        this.removeViaPoint = this.removeViaPoint.bind(this);
        this.updateViaPointsDisplay = this.updateViaPointsDisplay.bind(this);
        this.calculateFareWithVia = this.calculateFareWithVia.bind(this);
    }

    /**
     * Initialize via booking functionality
     */
    init() {
        if (this.isInitialized) return;

        // Load settings from meta tags or window object
        this.loadSettings();

        // Find container elements
        this.viaContainer = document.getElementById('via-points-container');
        this.addViaButton = document.getElementById('add-via-point');

        if (!this.viaContainer) {
            console.warn('Via points container not found');
            return;
        }

        // Set up event listeners
        this.setupEventListeners();

        // Initialize display
        this.updateViaPointsDisplay();
        this.updateAddButtonState();

        this.isInitialized = true;
        console.log('Via booking manager initialized');
    }

    /**
     * Load settings from various sources
     */
    loadSettings() {
        // Try to get settings from window object
        if (window.viaBookingSettings) {
            this.maxViaPoints = window.viaBookingSettings.maxViaPoints || 5;
        } else {
            // Load from meta tags
            const maxViaPointsMeta = document.querySelector('meta[name="max-via-points"]');

            if (maxViaPointsMeta) {
                this.maxViaPoints = parseInt(maxViaPointsMeta.content) || 5;
            }
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Add via point button
        if (this.addViaButton) {
            this.addViaButton.addEventListener('click', this.addViaPoint);
        }

        // Listen for address changes to recalculate fare
        document.addEventListener('autocomplete:place_changed', (event) => {
            if (event.detail.input.id.includes('via_address')) {
                this.handleViaAddressChange(event.detail);
            }
        });

        // Listen for booking type changes
        document.addEventListener('change', (event) => {
            if (event.target.name === 'booking_type' || event.target.id === 'bookingType') {
                this.handleBookingTypeChange();
            }
        });
    }

    /**
     * Add a new via point
     */
    addViaPoint() {
        if (this.viaPoints.length >= this.maxViaPoints) {
            this.showMessage(`Maximum ${this.maxViaPoints} via points allowed`, 'warning');
            return;
        }

        const viaPoint = {
            id: Date.now(),
            address: '',
            lat: null,
            lng: null,
            notes: ''
        };

        this.viaPoints.push(viaPoint);
        this.updateViaPointsDisplay();
        this.updateAddButtonState();
        this.updateHiddenFields();

        // Focus on the new address input
        setTimeout(() => {
            const newInput = document.getElementById(`via_address_${viaPoint.id}`);
            if (newInput) {
                newInput.focus();
            }
        }, 100);
    }

    /**
     * Remove a via point
     */
    removeViaPoint(viaId) {
        this.viaPoints = this.viaPoints.filter(point => point.id !== viaId);
        this.updateViaPointsDisplay();
        this.updateAddButtonState();
        this.updateHiddenFields();
        this.calculateFareWithVia();
    }

    /**
     * Update via points display
     */
    updateViaPointsDisplay() {
        if (!this.viaContainer) return;

        this.viaContainer.innerHTML = '';

        this.viaPoints.forEach((point, index) => {
            const viaPointHtml = this.createViaPointHtml(point, index);
            this.viaContainer.insertAdjacentHTML('beforeend', viaPointHtml);
        });

        // Initialize autocomplete for new inputs
        this.initializeViaAutocomplete();
    }

    /**
     * Create HTML for a via point
     */
    createViaPointHtml(point, index) {
        return `
            <div class="via-point-item" data-via-id="${point.id}">
                <div class="card border-primary mb-3">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Via Point ${index + 1}
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-light" onclick="viaBookingManager.removeViaPoint(${point.id})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="via_address_${point.id}" class="form-label">Address</label>
                                <input type="text"
                                       class="form-control address-autocomplete via-address"
                                       id="via_address_${point.id}"
                                       name="via_addresses[]"
                                       placeholder="Enter via point address"
                                       value="${point.address}"
                                       data-via-id="${point.id}"
                                       data-lat-field="via_lat_${point.id}"
                                       data-lng-field="via_lng_${point.id}">
                                <input type="hidden" id="via_lat_${point.id}" name="via_lats[]" value="${point.lat || ''}">
                                <input type="hidden" id="via_lng_${point.id}" name="via_lngs[]" value="${point.lng || ''}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <label for="via_notes_${point.id}" class="form-label">Notes (Optional)</label>
                                <input type="text"
                                       class="form-control"
                                       id="via_notes_${point.id}"
                                       name="via_notes[]"
                                       placeholder="Special instructions for this stop"
                                       value="${point.notes}"
                                       data-via-id="${point.id}"
                                       onchange="viaBookingManager.updateViaNotes(${point.id}, this.value)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Initialize autocomplete for via point inputs
     */
    initializeViaAutocomplete() {
        // Use the unified autocomplete system
        if (window.unifiedAutocomplete) {
            const viaInputs = document.querySelectorAll('.via-address');
            viaInputs.forEach(input => {
                if (!input.classList.contains('autocomplete-initialized')) {
                    window.unifiedAutocomplete.addInput(input);
                }
            });
        }
    }

    /**
     * Update add button state
     */
    updateAddButtonState() {
        if (this.addViaButton) {
            if (this.viaPoints.length >= this.maxViaPoints) {
                this.addViaButton.disabled = true;
                this.addViaButton.innerHTML = `<i class="fas fa-plus me-2"></i>Maximum ${this.maxViaPoints} via points`;
            } else {
                this.addViaButton.disabled = false;
                this.addViaButton.innerHTML = `<i class="fas fa-plus me-2"></i>Add Via Point (${this.viaPoints.length}/${this.maxViaPoints})`;
            }
        }
    }

    /**
     * Update hidden form fields
     */
    updateHiddenFields() {
        // Update via points data in hidden field
        const viaPointsField = document.getElementById('via_points_data');
        if (viaPointsField) {
            viaPointsField.value = JSON.stringify(this.viaPoints);
        }

        // Update via count
        const viaCountField = document.getElementById('via_count');
        if (viaCountField) {
            viaCountField.value = this.viaPoints.length;
        }
    }

    /**
     * Handle via address change
     */
    handleViaAddressChange(detail) {
        const input = detail.input;
        const place = detail.place;
        const viaId = parseInt(input.dataset.viaId);

        // Update via point data
        const viaPoint = this.viaPoints.find(point => point.id === viaId);
        if (viaPoint && place.geometry) {
            viaPoint.address = place.formatted_address || place.name;
            viaPoint.lat = place.geometry.location.lat();
            viaPoint.lng = place.geometry.location.lng();

            this.updateHiddenFields();
            this.calculateFareWithVia();
        }
    }

    // Stop duration functionality removed

    /**
     * Update via notes
     */
    updateViaNotes(viaId, notes) {
        const viaPoint = this.viaPoints.find(point => point.id === viaId);
        if (viaPoint) {
            viaPoint.notes = notes;
            this.updateHiddenFields();
        }
    }

    /**
     * Calculate fare with via points
     */
    calculateFareWithVia() {
        // Only calculate if we have valid pickup and dropoff
        const pickupAddress = document.getElementById('pickup_address')?.value;
        const dropoffAddress = document.getElementById('dropoff_address')?.value;
        const vehicleId = document.getElementById('selectedVehicle')?.value;

        if (!pickupAddress || !dropoffAddress || !vehicleId) {
            return;
        }

        // Trigger fare calculation with via points
        if (window.updateMapAndFare && typeof window.updateMapAndFare === 'function') {
            window.updateMapAndFare();
        }
    }

    /**
     * Handle booking type change
     */
    handleBookingTypeChange() {
        const bookingType = document.getElementById('bookingType')?.value;

        // Hide via points for hourly bookings
        if (bookingType === 'hourly') {
            this.hideViaPoints();
        } else {
            this.showViaPoints();
        }
    }

    /**
     * Show via points section
     */
    showViaPoints() {
        const viaSection = document.getElementById('via-points-section');
        if (viaSection) {
            viaSection.style.display = 'block';
        }
    }

    /**
     * Hide via points section
     */
    hideViaPoints() {
        const viaSection = document.getElementById('via-points-section');
        if (viaSection) {
            viaSection.style.display = 'none';
        }
    }

    /**
     * Get via points data for form submission
     */
    getViaPointsData() {
        return this.viaPoints.filter(point => point.address && point.lat && point.lng);
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('via-booking-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'via-booking-message';
            messageEl.className = 'alert alert-dismissible fade show';

            const viaSection = document.getElementById('via-points-section');
            if (viaSection) {
                viaSection.insertBefore(messageEl, viaSection.firstChild);
            }
        }

        messageEl.className = `alert alert-${type} alert-dismissible fade show`;
        messageEl.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (messageEl && messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    /**
     * Reset via points
     */
    reset() {
        this.viaPoints = [];
        this.updateViaPointsDisplay();
        this.updateAddButtonState();
        this.updateHiddenFields();
    }
}

// Create global instance
window.viaBookingManager = new ViaBookingManager();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.viaBookingManager.init();
    });
} else {
    window.viaBookingManager.init();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ViaBookingManager;
}
