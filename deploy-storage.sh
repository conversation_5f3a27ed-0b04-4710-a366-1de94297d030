#!/bin/bash

# YNR Cars - Storage Setup Script for Production
# This script sets up storage directories and permissions for production deployment

echo "🚀 YNR Cars - Storage Setup Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. This is not recommended for production."
fi

# Get the current directory (should be the Laravel project root)
PROJECT_ROOT=$(pwd)
print_status "Project root: $PROJECT_ROOT"

# Step 1: Create storage directories
print_step "Creating storage directories..."

STORAGE_DIRS=(
    "storage/app"
    "storage/app/public"
    "storage/app/public/profile-photos"
    "storage/app/public/driver-documents"
    "storage/app/public/vehicles"
    "storage/app/public/blog-posts"
    "storage/logs"
    "storage/framework"
    "storage/framework/cache"
    "storage/framework/cache/data"
    "storage/framework/sessions"
    "storage/framework/views"
    "bootstrap/cache"
)

for dir in "${STORAGE_DIRS[@]}"; do
    if [ ! -d "$PROJECT_ROOT/$dir" ]; then
        mkdir -p "$PROJECT_ROOT/$dir"
        print_status "Created directory: $dir"
    else
        print_status "Directory already exists: $dir"
    fi
done

# Step 2: Set proper permissions
print_step "Setting storage permissions..."

# Set directory permissions
find "$PROJECT_ROOT/storage" -type d -exec chmod 755 {} \;
find "$PROJECT_ROOT/bootstrap/cache" -type d -exec chmod 755 {} \;

# Set file permissions
find "$PROJECT_ROOT/storage" -type f -exec chmod 644 {} \;
find "$PROJECT_ROOT/bootstrap/cache" -type f -exec chmod 644 {} \;

print_status "Storage permissions set to 755 for directories and 644 for files"

# Step 3: Create storage link
print_step "Creating storage link..."

STORAGE_LINK="$PROJECT_ROOT/public/storage"
STORAGE_TARGET="$PROJECT_ROOT/storage/app/public"

if [ -L "$STORAGE_LINK" ]; then
    print_status "Storage link already exists"
elif [ -d "$STORAGE_LINK" ]; then
    print_warning "Storage directory exists instead of link. Removing..."
    rm -rf "$STORAGE_LINK"
    ln -sf "$STORAGE_TARGET" "$STORAGE_LINK"
    print_status "Storage link created"
else
    ln -sf "$STORAGE_TARGET" "$STORAGE_LINK"
    print_status "Storage link created"
fi

# Step 4: Set ownership (if running with appropriate permissions)
print_step "Setting ownership..."

# Detect web server user
WEB_USER=""
if id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
else
    print_warning "Could not detect web server user. Please set ownership manually."
fi

if [ ! -z "$WEB_USER" ]; then
    # Only change ownership if we have permission
    if [ -w "$PROJECT_ROOT/storage" ]; then
        chown -R "$WEB_USER:$WEB_USER" "$PROJECT_ROOT/storage"
        chown -R "$WEB_USER:$WEB_USER" "$PROJECT_ROOT/bootstrap/cache"
        print_status "Ownership set to $WEB_USER:$WEB_USER"
    else
        print_warning "No write permission. Run with sudo to set ownership:"
        print_warning "sudo chown -R $WEB_USER:$WEB_USER storage bootstrap/cache"
    fi
fi

# Step 5: Create .htaccess files for security
print_step "Creating security files..."

# Create .htaccess for storage/app (prevent direct access)
cat > "$PROJECT_ROOT/storage/app/.htaccess" << 'EOF'
<IfModule mod_authz_core.c>
    Require all denied
</IfModule>
<IfModule !mod_authz_core.c>
    Order deny,allow
    Deny from all
</IfModule>
EOF

# Create .htaccess for storage/logs (prevent direct access)
cat > "$PROJECT_ROOT/storage/logs/.htaccess" << 'EOF'
<IfModule mod_authz_core.c>
    Require all denied
</IfModule>
<IfModule !mod_authz_core.c>
    Order deny,allow
    Deny from all
</IfModule>
EOF

print_status "Security files created"

# Step 6: Test storage functionality
print_step "Testing storage functionality..."

# Test if we can write to storage
TEST_FILE="$PROJECT_ROOT/storage/app/public/test_$(date +%s).txt"
if echo "Storage test" > "$TEST_FILE" 2>/dev/null; then
    rm -f "$TEST_FILE"
    print_status "Storage write test: PASSED"
else
    print_error "Storage write test: FAILED"
fi

# Test if storage link works
if [ -L "$STORAGE_LINK" ] && [ -d "$STORAGE_LINK" ]; then
    print_status "Storage link test: PASSED"
else
    print_error "Storage link test: FAILED"
fi

# Step 7: Run Laravel storage setup command
print_step "Running Laravel storage setup..."

if [ -f "$PROJECT_ROOT/artisan" ]; then
    php "$PROJECT_ROOT/artisan" storage:setup --force
    print_status "Laravel storage setup completed"
else
    print_warning "Laravel artisan not found. Skipping Laravel-specific setup."
fi

# Step 8: Display summary
print_step "Setup Summary"
echo "=============="
echo "✓ Storage directories created"
echo "✓ Permissions set (755/644)"
echo "✓ Storage link created"
echo "✓ Security files created"

if [ ! -z "$WEB_USER" ]; then
    echo "✓ Ownership set to $WEB_USER"
else
    echo "⚠ Ownership not set (manual action required)"
fi

echo ""
print_status "Storage setup completed successfully!"
print_status "Your application should now be able to store and serve files properly."

echo ""
print_warning "IMPORTANT NOTES:"
echo "1. Ensure your web server has read/write access to storage directories"
echo "2. Configure your .env file with correct APP_URL for file URLs"
echo "3. Test file uploads in your application"
echo "4. Consider setting up automated backups for uploaded files"

echo ""
print_status "For production environments, also consider:"
echo "- Setting up cloud storage (S3, etc.) for better scalability"
echo "- Implementing file cleanup routines"
echo "- Monitoring storage usage"
echo "- Setting up proper backup strategies"

exit 0
