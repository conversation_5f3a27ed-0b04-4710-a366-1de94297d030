<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title'); ?> - <?php echo e(config('app.name', 'Ynr Cars')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .error-code {
            font-size: 120px;
            font-weight: 700;
            color: #ee393d;
            margin-bottom: 20px;
            line-height: 1;
        }
        
        .error-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #343a40;
        }
        
        .error-message {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .error-actions {
            margin-top: 30px;
        }
        
        .btn-primary {
            background-color: #ee393d;
            border-color: #ee393d;
            color: #343a40;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #e5b325;
            border-color: #e5b325;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-secondary {
            background-color: transparent;
            border: 2px solid #ee393d;
            color: #343a40;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-left: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background-color: #ee393d;
            color: #343a40;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .error-image {
            max-width: 100%;
            height: auto;
            margin-bottom: 30px;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #6c757d;
        }
        
        .footer a {
            color: #ee393d;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        <?php echo $__env->yieldContent('styles'); ?>
    </style>
</head>
<body>
    <div class="error-container">
        <?php echo $__env->yieldContent('content'); ?>
        
        <div class="error-actions">
            <a href="<?php echo e(route('home')); ?>" class="btn btn-primary">
                <i class="fas fa-home me-2"></i> Back to Home
            </a>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-secondary">
                <i class="fas fa-envelope me-2"></i> Contact Support
            </a>
        </div>
        
        <div class="footer">
            <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name', 'Ynr Cars')); ?>. All rights reserved.</p>
            <p>
                <a href="<?php echo e(route('privacy-policy')); ?>">Privacy Policy</a> | 
                <a href="<?php echo e(route('terms-and-conditions')); ?>">Terms and Conditions</a>
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\layouts\error.blade.php ENDPATH**/ ?>