#!/bin/bash

# Ynr Cars Setup Script
# This script sets up the Ynr Cars application for development or production

echo "🚗 Ynr Cars Setup Script"
echo "========================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📋 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. Please configure your environment variables."
else
    echo "✅ .env file already exists."
fi

# Install PHP dependencies
echo "📦 Installing PHP dependencies..."
composer install --optimize-autoloader

# Generate application key if not set
if grep -q "APP_KEY=$" .env; then
    echo "🔑 Generating application key..."
    php artisan key:generate
else
    echo "✅ Application key already set."
fi

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Build assets
echo "🏗️ Building frontend assets..."
npm run build

# Create storage link
echo "🔗 Creating storage link..."
php artisan storage:link

# Run database migrations
echo "🗄️ Running database migrations..."
php artisan migrate

# Seed the database with initial data
echo "🌱 Seeding database with initial data..."
php artisan db:seed

# Clear and cache configuration
echo "🧹 Clearing and caching configuration..."
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear
php artisan view:cache

# Set proper permissions (for Linux/Mac)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🔒 Setting proper permissions..."
    chmod -R 755 storage
    chmod -R 755 bootstrap/cache
    chown -R www-data:www-data storage bootstrap/cache 2>/dev/null || true
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Configure your .env file with:"
echo "   - Database credentials"
echo "   - Google Maps API key"
echo "   - PayPal credentials"
echo "   - Stripe credentials"
echo "   - Mail settings"
echo ""
echo "2. Create an admin user:"
echo "   php artisan tinker"
echo "   User::create(['name' => 'Admin', 'email' => '<EMAIL>', 'password' => bcrypt('password'), 'role' => 'admin']);"
echo ""
echo "3. Start the development server:"
echo "   php artisan serve"
echo ""
echo "4. Set up the task scheduler (for production):"
echo "   Add this to your crontab:"
echo "   * * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1"
echo ""
echo "🚗 Happy coding with Ynr Cars!"
