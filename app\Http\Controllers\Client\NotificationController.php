<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:client']);
    }

    /**
     * Display the notification settings page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function settings()
    {
        $userId = Auth::id();
        
        // Get the user's notification settings or create default ones
        $settings = NotificationSetting::firstOrCreate(
            ['user_id' => $userId],
            [
                'email_booking_updates' => true,
                'email_promotions' => true,
                'sms_booking_updates' => true,
                'sms_promotions' => false,
                'push_booking_updates' => true,
                'push_promotions' => false,
            ]
        );
        
        return view('client.notifications.settings', compact('settings'));
    }

    /**
     * Update the notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request)
    {
        $userId = Auth::id();
        
        $request->validate([
            'email_booking_updates' => 'boolean',
            'email_promotions' => 'boolean',
            'sms_booking_updates' => 'boolean',
            'sms_promotions' => 'boolean',
            'push_booking_updates' => 'boolean',
            'push_promotions' => 'boolean',
        ]);
        
        // Update or create notification settings
        NotificationSetting::updateOrCreate(
            ['user_id' => $userId],
            [
                'email_booking_updates' => $request->has('email_booking_updates'),
                'email_promotions' => $request->has('email_promotions'),
                'sms_booking_updates' => $request->has('sms_booking_updates'),
                'sms_promotions' => $request->has('sms_promotions'),
                'push_booking_updates' => $request->has('push_booking_updates'),
                'push_promotions' => $request->has('push_promotions'),
            ]
        );
        
        return redirect()->route('client.notifications.settings')
            ->with('success', 'Notification settings updated successfully.');
    }
}
