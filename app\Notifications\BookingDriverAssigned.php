<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingDriverAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The booking instance.
     *
     * @var \App\Models\Booking
     */
    protected $booking;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Booking  $booking
     * @return void
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = route('client.bookings.show', $this->booking->id);

        return (new MailMessage)
            ->subject('Driver Assigned to Your Booking #' . $this->booking->booking_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('A driver has been assigned to your booking.')
            ->line('Booking #: ' . $this->booking->booking_number)
            ->line('Driver: ' . $this->booking->driver->name)
            ->line('Pickup Date: ' . $this->booking->pickup_date->format('M d, Y h:i A'))
            ->line('Pickup Location: ' . $this->booking->pickup_address)
            ->line('Dropoff Location: ' . $this->booking->dropoff_address)
            ->line('Vehicle: ' . $this->booking->vehicle->name)
            ->action('View Booking Details', $url)
            ->line('Thank you for choosing our service!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'driver_name' => $this->booking->driver->name,
            'pickup_date' => $this->booking->pickup_date->format('M d, Y h:i A'),
            'pickup_address' => $this->booking->pickup_address,
            'dropoff_address' => $this->booking->dropoff_address,
            'vehicle' => $this->booking->vehicle->name,
            'message' => 'A driver has been assigned to your booking.',
            'url' => route('client.bookings.show', $this->booking->id)
        ];
    }
}
