<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new document types to the document_type enum if it doesn't exist
        // Since we're using a string field, we don't need to modify the schema
        // We'll just update the application code to handle these new document types

        // For reference, the new document types we'll support are:
        // - Driver License
        // - Driver PHD License
        // - Vehicle PHD License
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No schema changes to revert
    }
};
