@extends('layouts.driver')

@section('title', 'Document Details')

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-alt me-2 text-primary"></i> Document Details
        </h1>
        <div>
            <a href="{{ route('driver.documents.edit', $document->id) }}" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i> Edit Document
            </a>
            <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Back to Documents
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Document Preview</h5>
                </div>
                <div class="card-body text-center p-0">
                    @if(in_array(pathinfo($document->file_path, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif']))
                        <img src="{{ asset('storage/' . $document->file_path) }}" alt="{{ $document->type }}" class="img-fluid">
                    @elseif(pathinfo($document->file_path, PATHINFO_EXTENSION) == 'pdf')
                        <div class="ratio ratio-16x9">
                            <iframe src="{{ asset('storage/' . $document->file_path) }}" allowfullscreen></iframe>
                        </div>
                    @else
                        <div class="py-5">
                            <i class="fas fa-file fa-5x text-secondary mb-3"></i>
                            <p>Preview not available for this file type</p>
                            <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i> Open File
                            </a>
                        </div>
                    @endif
                </div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-calendar-alt me-1 text-muted"></i> Uploaded on {{ $document->created_at->format('M d, Y') }}
                        </span>
                        <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank" download>
                            <i class="fas fa-download me-1"></i> Download
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Document Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <h6 class="text-muted mb-2">Status</h6>
                            @if($document->status == 'verified')
                                <span class="badge bg-success">Verified</span>
                            @elseif($document->status == 'pending')
                                <span class="badge bg-warning">Pending Verification</span>
                            @elseif($document->status == 'rejected')
                                <span class="badge bg-danger">Rejected</span>
                            @else
                                <span class="badge bg-secondary">{{ ucfirst($document->status) }}</span>
                            @endif
                        </div>
                        @if($document->status == 'rejected' && $document->rejection_reason)
                            <div class="alert alert-danger mt-2 mb-0 py-2 px-3">
                                <small><strong>Reason:</strong> {{ $document->rejection_reason }}</small>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Document Type</h6>
                        <p class="mb-0">{{ $document->type }}</p>
                    </div>
                    
                    @if($document->document_number)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Document Number</h6>
                        <p class="mb-0">{{ $document->document_number }}</p>
                    </div>
                    @endif
                    
                    @if($document->expiry_date)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Expiry Date</h6>
                        <p class="mb-0 {{ $document->isExpired() ? 'text-danger' : ($document->isExpiringSoon() ? 'text-warning' : '') }}">
                            {{ $document->expiry_date->format('M d, Y') }}
                            @if($document->isExpired())
                                <span class="badge bg-danger ms-1">Expired</span>
                            @elseif($document->isExpiringSoon())
                                <span class="badge bg-warning ms-1">Expiring Soon</span>
                            @endif
                        </p>
                    </div>
                    @endif
                    
                    @if($document->notes)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Notes</h6>
                        <p class="mb-0">{{ $document->notes }}</p>
                    </div>
                    @endif
                    
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">File Type</h6>
                        <p class="mb-0 text-uppercase">{{ pathinfo($document->file_path, PATHINFO_EXTENSION) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('driver.documents.edit', $document->id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i> Edit Document
                        </a>
                        <a href="{{ asset('storage/' . $document->file_path) }}" class="btn btn-outline-primary" target="_blank" download>
                            <i class="fas fa-download me-1"></i> Download Document
                        </a>
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteDocumentModal">
                            <i class="fas fa-trash me-1"></i> Delete Document
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Document Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1" aria-labelledby="deleteDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteDocumentModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> Deleting required documents may affect your ability to accept rides.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('driver.documents.destroy', $document->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Document</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
