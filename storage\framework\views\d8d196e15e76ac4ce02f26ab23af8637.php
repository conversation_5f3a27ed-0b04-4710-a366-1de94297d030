<?php $__env->startSection('title', 'Driver Details'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* Driver Profile Styles */
    .driver-profile-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .driver-profile-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .driver-profile-card .card-header h6 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .driver-profile-card .card-body {
        padding: 30px;
    }

    .driver-profile-img {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 5px solid #fff;
    }

    .driver-profile-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 4rem;
        color: #fff;
        background-color: #ee393d;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 5px solid #fff;
        margin: 0 auto;
    }

    .driver-name {
        font-size: 1.5rem;
        font-weight: 700;
        margin-top: 20px;
        margin-bottom: 10px;
    }

    .driver-status-badges {
        margin-bottom: 15px;
    }

    .driver-status-badges .badge {
        padding: 8px 15px;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.8rem;
        margin: 0 5px;
    }

    .driver-info {
        margin-top: 30px;
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-item i {
        width: 30px;
        height: 30px;
        background-color: rgba(238, 57, 61, 0.1);
        color: #ee393d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 0.9rem;
    }

    .info-item span {
        color: #343a40;
        font-weight: 500;
    }

    .vehicle-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 30px;
    }

    .vehicle-info h6 {
        font-weight: 600;
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #ee393d;
        padding-bottom: 10px;
        display: inline-block;
    }

    .stat-item {
        text-align: center;
        background-color: #fff;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #ee393d;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* Document Styles */
    .document-card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }

    .document-card .card-header {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: none;
    }

    .document-card .card-header button {
        color: #343a40;
        font-weight: 600;
        text-decoration: none;
        width: 100%;
        text-align: left;
        padding: 0;
        position: relative;
    }

    .document-card .card-header button:hover {
        color: #ee393d;
    }

    .document-card .card-header button:after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.3s ease;
    }

    .document-card .card-header button.collapsed:after {
        transform: translateY(-50%) rotate(-90deg);
    }

    .document-card .card-body {
        padding: 20px;
    }

    .document-list-item {
        border-radius: 10px !important;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05) !important;
    }

    .document-list-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .document-list-item h6 {
        font-weight: 600;
        color: #343a40;
    }

    .document-list-item small {
        font-size: 0.8rem;
    }

    .document-badge {
        font-size: 0.7rem;
        padding: 3px 8px;
        border-radius: 20px;
    }

    /* Rides Table Styles */
    .rides-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .rides-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .rides-card .card-header h6 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .rides-table {
        margin-bottom: 0;
    }

    .rides-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .rides-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .rides-table tr {
        transition: all 0.3s ease;
    }

    .rides-table tr:hover {
        background-color: rgba(238, 57, 61, 0.03);
    }

    .booking-status {
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .status-assigned {
        background-color: #e2e3e5;
        color: #41464b;
    }

    .status-in_progress {
        background-color: #cfe2ff;
        color: #084298;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .action-buttons .btn {
        border-radius: 5px;
        padding: 8px 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .action-buttons .btn i {
        margin-right: 5px;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .rides-table {
            min-width: 800px;
        }

        .card-body {
            overflow-x: auto;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-id-card me-2 text-primary"></i> Driver Details
        </h1>
        <div class="action-buttons">
            <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Drivers
            </a>
            <a href="<?php echo e(route('admin.drivers.edit', $driver->id)); ?>" class="btn btn-outline-info">
                <i class="fas fa-edit"></i> Edit Driver
            </a>
            <form action="<?php echo e(route('admin.drivers.toggle-active', $driver->id)); ?>" method="POST" class="d-inline">
                <?php echo csrf_field(); ?>
                <button type="submit" class="btn btn-outline-<?php echo e($driver->is_active ? 'warning' : 'success'); ?>">
                    <i class="fas fa-<?php echo e($driver->is_active ? 'ban' : 'check'); ?>"></i>
                    <?php echo e($driver->is_active ? 'Deactivate' : 'Activate'); ?>

                </button>
            </form>
            <form action="<?php echo e(route('admin.drivers.toggle-availability', $driver->id)); ?>" method="POST" class="d-inline">
                <?php echo csrf_field(); ?>
                <button type="submit" class="btn btn-outline-<?php echo e($driver->is_available ? 'secondary' : 'info'); ?>">
                    <i class="fas fa-<?php echo e($driver->is_available ? 'times' : 'user-check'); ?>"></i>
                    <?php echo e($driver->is_available ? 'Mark Unavailable' : 'Mark Available'); ?>

                </button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-4 col-lg-5">
            <div class="driver-profile-card">
                <div class="card-header">
                    <h6 class="font-weight-bold"><i class="fas fa-user me-2"></i> Driver Profile</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php if($driver->profile_photo): ?>
                            <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="driver-profile-img">
                        <?php else: ?>
                            <div class="driver-profile-placeholder">
                                <?php echo e(strtoupper(substr($driver->name, 0, 1))); ?>

                            </div>
                        <?php endif; ?>
                        <h4 class="driver-name"><?php echo e($driver->name); ?></h4>
                        <div class="driver-status-badges">
                            <span class="badge bg-<?php echo e($driver->is_active ? 'success' : 'danger'); ?>">
                                <i class="fas fa-<?php echo e($driver->is_active ? 'check-circle' : 'times-circle'); ?> me-1"></i>
                                <?php echo e($driver->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                            <span class="badge bg-<?php echo e($driver->is_available ? 'info' : 'warning'); ?>">
                                <i class="fas fa-<?php echo e($driver->is_available ? 'user-check' : 'user-clock'); ?> me-1"></i>
                                <?php echo e($driver->is_available ? 'Available' : 'Unavailable'); ?>

                            </span>
                        </div>
                        <p class="text-muted"><i class="fas fa-calendar-alt me-1"></i> Driver since <?php echo e($driver->created_at->format('M d, Y')); ?></p>
                    </div>

                    <div class="driver-info">
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <span><?php echo e($driver->email); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span><?php echo e($driver->phone); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo e($driver->address); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-id-card"></i>
                            <span>License: <?php echo e($driver->license_number); ?></span>
                        </div>
                    </div>

                    <div class="vehicle-info mt-4">
                        <h6 class="font-weight-bold mb-3">Vehicle Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-car"></i>
                                    <span><?php echo e($driver->vehicle_make); ?> <?php echo e($driver->vehicle_model); ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-palette"></i>
                                    <span>Color: <?php echo e($driver->vehicle_color); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-id-card"></i>
                                    <span>Reg: <?php echo e($driver->vehicle_reg_number); ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-info-circle"></i>
                                    <span><?php echo e($driver->vehicle_info); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Insurance:
                                        <?php if($driver->insurance_expiry): ?>
                                            <span class="<?php echo e($driver->insurance_expiry->isPast() ? 'text-danger' : ($driver->insurance_expiry->diffInDays(now()) <= 30 ? 'text-warning' : 'text-success')); ?>">
                                                Expires <?php echo e($driver->insurance_expiry->format('M d, Y')); ?>

                                                <?php if($driver->insurance_expiry->isPast()): ?>
                                                    (Expired)
                                                <?php elseif($driver->insurance_expiry->diffInDays(now()) <= 30): ?>
                                                    (Expiring soon)
                                                <?php endif; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-danger">Not provided</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>MOT:
                                        <?php if($driver->mot_expiry): ?>
                                            <span class="<?php echo e($driver->mot_expiry->isPast() ? 'text-danger' : ($driver->mot_expiry->diffInDays(now()) <= 30 ? 'text-warning' : 'text-success')); ?>">
                                                Expires <?php echo e($driver->mot_expiry->format('M d, Y')); ?>

                                                <?php if($driver->mot_expiry->isPast()): ?>
                                                    (Expired)
                                                <?php elseif($driver->mot_expiry->diffInDays(now()) <= 30): ?>
                                                    (Expiring soon)
                                                <?php endif; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-danger">Not provided</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6 class="font-weight-bold">Driver Statistics</h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e($rides->where('status', 'completed')->count()); ?></div>
                                    <div class="stat-label">Completed Rides</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value">$<?php echo e(number_format($totalEarnings, 2)); ?></div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e($rides->where('status', 'assigned')->count()); ?></div>
                                    <div class="stat-label">Pending Rides</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e($rides->where('status', 'cancelled')->count()); ?></div>
                                    <div class="stat-label">Cancelled Rides</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="driver-profile-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="font-weight-bold"><i class="fas fa-file-alt me-2"></i> Documents</h6>
                    <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-upload me-1"></i> Upload Document
                    </a>
                </div>
                <div class="card-body">
                    <?php if($documents->isEmpty()): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-upload fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No documents uploaded yet</h5>
                            <p class="text-muted">Upload driver's documents to complete the profile</p>
                            <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-primary mt-2">
                                <i class="fas fa-upload me-1"></i> Upload Documents
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="accordion" id="documentsAccordion">
                            <!-- Driver License Documents -->
                            <div class="document-card">
                                <div class="card-header" id="headingDriverLicense">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDriverLicense" aria-expanded="true" aria-controls="collapseDriverLicense">
                                            <i class="fas fa-id-card me-2"></i> Driver License
                                            <?php
                                                $driverLicenseDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'Driver License';
                                                });
                                                $hasExpiredDriverLicense = $driverLicenseDocs->contains(function($doc) {
                                                    return $doc->isExpired();
                                                });
                                            ?>
                                            <?php if($hasExpiredDriverLicense): ?>
                                                <span class="badge bg-danger document-badge ms-2">Expired</span>
                                            <?php elseif($driverLicenseDocs->isEmpty()): ?>
                                                <span class="badge bg-warning document-badge ms-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseDriverLicense" class="collapse show" aria-labelledby="headingDriverLicense" data-bs-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($driverLicenseDocs->isEmpty()): ?>
                                            <p class="text-center text-muted">No driver license documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $driverLicenseDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action document-list-item">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                <i class="fas fa-calendar-alt me-1 text-primary"></i> Expires:
                                                                <span class="<?php echo e($document->isExpired() ? 'text-danger' : ($document->isExpiringSoon() ? 'text-warning' : 'text-success')); ?>">
                                                                    <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                    <?php if($document->isExpired()): ?>
                                                                        <span class="badge bg-danger document-badge">Expired</span>
                                                                    <?php elseif($document->isExpiringSoon()): ?>
                                                                        <span class="badge bg-warning document-badge">Expiring Soon</span>
                                                                    <?php endif; ?>
                                                                </span>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?> me-1"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Driver PHD License Documents -->
                            <div class="card">
                                <div class="card-header" id="headingDriverPHD">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseDriverPHD" aria-expanded="false" aria-controls="collapseDriverPHD">
                                            Driver PHD License
                                            <?php
                                                $driverPhdDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'Driver PHD License';
                                                });
                                                $hasExpiredDriverPhd = $driverPhdDocs->contains(function($doc) {
                                                    return $doc->isExpired();
                                                });
                                            ?>
                                            <?php if($hasExpiredDriverPhd): ?>
                                                <span class="badge badge-danger ml-2">Expired</span>
                                            <?php elseif($driverPhdDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseDriverPHD" class="collapse" aria-labelledby="headingDriverPHD" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($driverPhdDocs->isEmpty()): ?>
                                            <p class="text-center">No driver PHD license documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $driverPhdDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                Expires: <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                <?php if($document->isExpired()): ?>
                                                                    <span class="badge badge-danger">Expired</span>
                                                                <?php elseif($document->isExpiringSoon()): ?>
                                                                    <span class="badge badge-warning">Expiring Soon</span>
                                                                <?php endif; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Vehicle PHD License Documents -->
                            <div class="card">
                                <div class="card-header" id="headingVehiclePHD">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseVehiclePHD" aria-expanded="false" aria-controls="collapseVehiclePHD">
                                            Vehicle PHD License
                                            <?php
                                                $vehiclePhdDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'Vehicle PHD License';
                                                });
                                                $hasExpiredVehiclePhd = $vehiclePhdDocs->contains(function($doc) {
                                                    return $doc->isExpired();
                                                });
                                            ?>
                                            <?php if($hasExpiredVehiclePhd): ?>
                                                <span class="badge badge-danger ml-2">Expired</span>
                                            <?php elseif($vehiclePhdDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseVehiclePHD" class="collapse" aria-labelledby="headingVehiclePHD" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($vehiclePhdDocs->isEmpty()): ?>
                                            <p class="text-center">No vehicle PHD license documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $vehiclePhdDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                Expires: <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                <?php if($document->isExpired()): ?>
                                                                    <span class="badge badge-danger">Expired</span>
                                                                <?php elseif($document->isExpiringSoon()): ?>
                                                                    <span class="badge badge-warning">Expiring Soon</span>
                                                                <?php endif; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Insurance Documents -->
                            <div class="card">
                                <div class="card-header" id="headingInsurance">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseInsurance" aria-expanded="false" aria-controls="collapseInsurance">
                                            Insurance
                                            <?php
                                                $insuranceDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'Insurance';
                                                });
                                                $hasExpiredInsurance = $insuranceDocs->contains(function($doc) {
                                                    return $doc->isExpired();
                                                });
                                            ?>
                                            <?php if($hasExpiredInsurance): ?>
                                                <span class="badge badge-danger ml-2">Expired</span>
                                            <?php elseif($insuranceDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseInsurance" class="collapse" aria-labelledby="headingInsurance" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($insuranceDocs->isEmpty()): ?>
                                            <p class="text-center">No insurance documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $insuranceDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                Expires: <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                <?php if($document->isExpired()): ?>
                                                                    <span class="badge badge-danger">Expired</span>
                                                                <?php elseif($document->isExpiringSoon()): ?>
                                                                    <span class="badge badge-warning">Expiring Soon</span>
                                                                <?php endif; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- MOT Documents -->
                            <div class="card">
                                <div class="card-header" id="headingMOT">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseMOT" aria-expanded="false" aria-controls="collapseMOT">
                                            MOT Certificate
                                            <?php
                                                $motDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'MOT Certificate';
                                                });
                                                $hasExpiredMOT = $motDocs->contains(function($doc) {
                                                    return $doc->isExpired();
                                                });
                                            ?>
                                            <?php if($hasExpiredMOT): ?>
                                                <span class="badge badge-danger ml-2">Expired</span>
                                            <?php elseif($motDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseMOT" class="collapse" aria-labelledby="headingMOT" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($motDocs->isEmpty()): ?>
                                            <p class="text-center">No MOT certificate documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $motDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                Expires: <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                <?php if($document->isExpired()): ?>
                                                                    <span class="badge badge-danger">Expired</span>
                                                                <?php elseif($document->isExpiringSoon()): ?>
                                                                    <span class="badge badge-warning">Expiring Soon</span>
                                                                <?php endif; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- V5C Logbook Documents -->
                            <div class="card">
                                <div class="card-header" id="headingV5C">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseV5C" aria-expanded="false" aria-controls="collapseV5C">
                                            V5C Logbook
                                            <?php
                                                $v5cDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'V5C Logbook';
                                                });
                                            ?>
                                            <?php if($v5cDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseV5C" class="collapse" aria-labelledby="headingV5C" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($v5cDocs->isEmpty()): ?>
                                            <p class="text-center">No V5C logbook documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $v5cDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Vehicle Photos -->
                            <div class="card">
                                <div class="card-header" id="headingVehiclePhotos">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseVehiclePhotos" aria-expanded="false" aria-controls="collapseVehiclePhotos">
                                            Vehicle Photos
                                            <?php
                                                $vehiclePhotoDocs = $documents->filter(function($doc) {
                                                    return $doc->document_type == 'Vehicle Photos';
                                                });
                                            ?>
                                            <?php if($vehiclePhotoDocs->isEmpty()): ?>
                                                <span class="badge badge-warning ml-2">Missing</span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseVehiclePhotos" class="collapse" aria-labelledby="headingVehiclePhotos" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($vehiclePhotoDocs->isEmpty()): ?>
                                            <p class="text-center">No vehicle photos uploaded.</p>
                                        <?php else: ?>
                                            <div class="row">
                                                <?php $__currentLoopData = $vehiclePhotoDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-3">
                                                        <div class="card">
                                                            <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank">
                                                                <img src="<?php echo e(asset('storage/' . $document->file_path)); ?>" class="card-img-top" alt="Vehicle Photo">
                                                            </a>
                                                            <div class="card-body">
                                                                <p class="card-text">
                                                                    <small class="text-muted">Uploaded: <?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                                </p>
                                                                <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                                    <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                                    <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Other Documents -->
                            <div class="card">
                                <div class="card-header" id="headingOther">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseOther" aria-expanded="false" aria-controls="collapseOther">
                                            Other Documents
                                            <?php
                                                $otherDocs = $documents->filter(function($doc) {
                                                    return !in_array($doc->document_type, [
                                                        'Driver License',
                                                        'Driver PHD License',
                                                        'Vehicle PHD License',
                                                        'Insurance',
                                                        'MOT Certificate',
                                                        'V5C Logbook',
                                                        'Vehicle Photos'
                                                    ]);
                                                });
                                            ?>
                                            <?php if($otherDocs->isNotEmpty()): ?>
                                                <span class="badge badge-info ml-2"><?php echo e($otherDocs->count()); ?></span>
                                            <?php endif; ?>
                                        </button>
                                    </h2>
                                </div>
                                <div id="collapseOther" class="collapse" aria-labelledby="headingOther" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        <?php if($otherDocs->isEmpty()): ?>
                                            <p class="text-center">No other documents uploaded.</p>
                                        <?php else: ?>
                                            <div class="list-group">
                                                <?php $__currentLoopData = $otherDocs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(asset('storage/' . $document->file_path)); ?>" target="_blank" class="list-group-item list-group-item-action">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1"><?php echo e($document->document_type); ?></h6>
                                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small>
                                                        </div>
                                                        <?php if($document->expiry_date): ?>
                                                            <p class="mb-1">
                                                                Expires: <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                                                <?php if($document->isExpired()): ?>
                                                                    <span class="badge badge-danger">Expired</span>
                                                                <?php elseif($document->isExpiringSoon()): ?>
                                                                    <span class="badge badge-warning">Expiring Soon</span>
                                                                <?php endif; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <small class="text-<?php echo e($document->is_verified ? 'success' : 'warning'); ?>">
                                                            <i class="fas fa-<?php echo e($document->is_verified ? 'check-circle' : 'clock'); ?>"></i>
                                                            <?php echo e($document->is_verified ? 'Verified' : 'Pending Verification'); ?>

                                                        </small>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <a href="<?php echo e(route('admin.drivers.documents', $driver->id)); ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-file"></i> Manage Documents
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-xl-8 col-lg-7">
            <div class="rides-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="font-weight-bold"><i class="fas fa-car me-2"></i> Recent Rides</h6>
                    <a href="<?php echo e(route('admin.bookings.index', ['driver_id' => $driver->id])); ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list me-1"></i> View All Rides
                    </a>
                </div>
                <div class="card-body">
                    <?php if($rides->isEmpty()): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-car-side fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No rides assigned to this driver yet</h5>
                            <p class="text-muted">Assign bookings to this driver to see them here</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table rides-table">
                                <thead>
                                    <tr>
                                        <th>Booking #</th>
                                        <th>Client</th>
                                        <th>Date</th>
                                        <th>Vehicle</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $rides; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ride): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <span class="fw-bold"><?php echo e($ride->booking_number); ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($ride->user->profile_photo): ?>
                                                        <img src="<?php echo e(asset('storage/' . $ride->user->profile_photo)); ?>" alt="<?php echo e($ride->user->name); ?>" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                            <?php echo e(strtoupper(substr($ride->user->name, 0, 1))); ?>

                                                        </div>
                                                    <?php endif; ?>
                                                    <span><?php echo e($ride->user->name); ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div><i class="fas fa-calendar-alt text-primary me-1"></i> <?php echo e($ride->pickup_date->format('M d, Y')); ?></div>
                                                <div><i class="fas fa-clock text-primary me-1"></i> <?php echo e($ride->pickup_date->format('h:i A')); ?></div>
                                            </td>
                                            <td>
                                                <i class="fas fa-car text-primary me-1"></i> <?php echo e($ride->vehicle->name); ?>

                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">$<?php echo e(number_format($ride->amount, 2)); ?></span>
                                            </td>
                                            <td>
                                                <span class="booking-status status-<?php echo e($ride->status); ?>">
                                                    <?php echo e(ucfirst($ride->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('admin.bookings.show', $ride->id)); ?>" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="rides-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="font-weight-bold"><i class="fas fa-calendar-alt me-2"></i> Schedule</h6>
                    <a href="<?php echo e(route('admin.drivers.schedule', $driver->id)); ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit me-1"></i> Manage Schedule
                    </a>
                </div>
                <div class="card-body">
                    <?php if($schedules->isEmpty()): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No schedule entries found for this driver</h5>
                            <p class="text-muted">Set up a schedule to manage driver availability</p>
                            <a href="<?php echo e(route('admin.drivers.schedule', $driver->id)); ?>" class="btn btn-primary mt-2">
                                <i class="fas fa-plus me-1"></i> Add Schedule
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table rides-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Day</th>
                                        <th>Time</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($schedule->isToday() ? 'table-primary' : ''); ?>">
                                            <td>
                                                <span class="fw-bold"><?php echo e($schedule->formatted_date); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark"><?php echo e($schedule->day_of_week); ?></span>
                                            </td>
                                            <td>
                                                <div><i class="fas fa-clock text-primary me-1"></i> <?php echo e($schedule->formatted_start_time); ?> - <?php echo e($schedule->formatted_end_time); ?></div>
                                                <div class="text-muted small"><?php echo e($schedule->getDuration()); ?> hours</div>
                                            </td>
                                            <td>
                                                <?php if($schedule->is_time_off): ?>
                                                    <span class="booking-status status-cancelled">
                                                        <i class="fas fa-ban me-1"></i> Time Off
                                                    </span>
                                                <?php else: ?>
                                                    <span class="booking-status status-<?php echo e($schedule->is_available ? 'completed' : 'pending'); ?>">
                                                        <i class="fas fa-<?php echo e($schedule->is_available ? 'check-circle' : 'clock'); ?> me-1"></i>
                                                        <?php echo e($schedule->is_available ? 'Available' : 'Unavailable'); ?>

                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($schedule->notes): ?>
                                                    <i class="fas fa-sticky-note text-primary me-1"></i> <?php echo e($schedule->notes); ?>

                                                <?php else: ?>
                                                    <span class="text-muted">No notes</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\drivers\show.blade.php ENDPATH**/ ?>