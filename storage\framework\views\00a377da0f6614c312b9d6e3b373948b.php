<?php $__env->startSection('title', 'Booking Confirmation'); ?>

<?php $__env->startSection('content'); ?>
<h2>Booking Confirmation</h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>Thank you for choosing <?php echo e($companyName); ?>! Your booking has been confirmed and we're excited to serve you.</p>

<div class="booking-details">
    <h3>Booking Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Booking Number:</span>
        <span class="detail-value">#<?php echo e($booking->booking_number); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Date & Time:</span>
        <span class="detail-value"><?php echo e($booking->pickup_date->format('l, F j, Y \a\t g:i A')); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Pickup Address:</span>
        <span class="detail-value"><?php echo e($booking->pickup_address); ?></span>
    </div>
    
    <?php if($booking->dropoff_address): ?>
    <div class="detail-row">
        <span class="detail-label">Dropoff Address:</span>
        <span class="detail-value"><?php echo e($booking->dropoff_address); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Vehicle:</span>
        <span class="detail-value"><?php echo e($vehicle->name ?? 'To be assigned'); ?></span>
    </div>
    
    <?php if($driver): ?>
    <div class="detail-row">
        <span class="detail-label">Driver:</span>
        <span class="detail-value"><?php echo e($driver->name); ?></span>
    </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">Total Amount:</span>
        <span class="detail-value"><strong><?php echo e($currencySymbol); ?><?php echo e(number_format($booking->amount, 2)); ?></strong></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Payment Status:</span>
        <span class="detail-value"><?php echo e(ucfirst($booking->payment_status)); ?></span>
    </div>
</div>

<?php if($booking->notes): ?>
<div style="margin: 20px 0;">
    <strong>Special Instructions:</strong><br>
    <?php echo e($booking->notes); ?>

</div>
<?php endif; ?>

<div style="margin: 30px 0; text-align: center;">
    <a href="<?php echo e(route('client.bookings.show', $booking->id)); ?>" class="btn">View Booking Details</a>
</div>

<h3>What's Next?</h3>
<ul>
    <li>You will receive a reminder email 24 hours before your pickup time</li>
    <li>Our driver will contact you 15-30 minutes before arrival</li>
    <li>Please be ready at your pickup location at the scheduled time</li>
    <?php if($booking->payment_status === 'pending'): ?>
    <li>Complete your payment to confirm your booking</li>
    <?php endif; ?>
</ul>

<h3>Need to Make Changes?</h3>
<p>If you need to modify or cancel your booking, please contact us at least 24 hours in advance:</p>
<ul>
    <li>Phone: <?php echo e($companyPhone); ?></li>
    <li>Email: <?php echo e($companyEmail); ?></li>
    <li>Or manage your booking online in your account</li>
</ul>

<p>Thank you for choosing <?php echo e($companyName); ?>. We look forward to providing you with excellent service!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\booking\confirmation.blade.php ENDPATH**/ ?>