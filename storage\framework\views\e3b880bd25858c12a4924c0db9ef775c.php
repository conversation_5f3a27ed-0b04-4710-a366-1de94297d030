<?php $__env->startSection('title', 'Manage Driver Schedule'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .schedule-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .schedule-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }
    
    .schedule-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #4e73df;
    }
    
    .schedule-card .card-body {
        padding: 25px;
    }
    
    .schedule-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .schedule-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .schedule-table td {
        padding: 15px;
        border-bottom: 1px solid #e3e6f0;
        vertical-align: middle;
    }
    
    .schedule-table tr:last-child td {
        border-bottom: none;
    }
    
    .schedule-table .day-column {
        width: 15%;
        font-weight: 600;
        color: #4e73df;
        background-color: #f8f9fa;
    }
    
    .schedule-table .time-column {
        width: 25%;
    }
    
    .schedule-table .status-column {
        width: 20%;
    }
    
    .schedule-table .notes-column {
        width: 40%;
    }
    
    .schedule-entry {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border-left: 4px solid #4e73df;
    }
    
    .schedule-entry:last-child {
        margin-bottom: 0;
    }
    
    .schedule-entry.available {
        border-left-color: #1cc88a;
    }
    
    .schedule-entry.unavailable {
        border-left-color: #f6c23e;
    }
    
    .schedule-entry.time-off {
        border-left-color: #e74a3b;
    }
    
    .schedule-entry .time {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .schedule-entry .status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.7rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .schedule-entry .status.available {
        background-color: #e6f8f1;
        color: #1cc88a;
    }
    
    .schedule-entry .status.unavailable {
        background-color: #fff8e6;
        color: #f6c23e;
    }
    
    .schedule-entry .status.time-off {
        background-color: #fbeae9;
        color: #e74a3b;
    }
    
    .schedule-entry .notes {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .schedule-form {
        margin-top: 20px;
    }
    
    .schedule-form .form-group {
        margin-bottom: 15px;
    }
    
    .schedule-form label {
        font-weight: 600;
        color: #4e73df;
    }
    
    .schedule-form .form-control {
        border-radius: 10px;
        border: 1px solid #e3e6f0;
        padding: 10px 15px;
    }
    
    .schedule-form .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    
    .schedule-form .form-check-input:checked {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    
    .schedule-form .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    
    .schedule-form .btn-primary:hover {
        background-color: #2e59d9;
        border-color: #2653d4;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #e3e6f0;
        margin-bottom: 20px;
    }
    
    .nav-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        font-weight: 600;
        padding: 10px 20px;
    }
    
    .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #4e73df;
    }
    
    .nav-tabs .nav-link.active {
        border-color: #4e73df;
        color: #4e73df;
        background-color: transparent;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .add-schedule-btn {
        border-radius: 10px;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border: 1px dashed #4e73df;
        color: #4e73df;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .add-schedule-btn:hover {
        background-color: #eaecf4;
    }
    
    .add-schedule-btn i {
        margin-right: 5px;
    }
    
    .today-indicator {
        background-color: #e8f4ff;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calendar-alt me-2 text-primary"></i> Manage Driver Schedule
        </h1>
        <a href="<?php echo e(route('admin.drivers.show', $driver->id)); ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Driver
        </a>
    </div>

    <!-- Driver Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Driver Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2 text-center">
                    <?php if($driver->profile_photo): ?>
                        <img src="<?php echo e(asset('storage/' . $driver->profile_photo)); ?>" alt="<?php echo e($driver->name); ?>" class="img-profile rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                    <?php else: ?>
                        <div class="img-profile rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; margin: 0 auto;">
                            <span style="font-size: 2.5rem;"><?php echo e(strtoupper(substr($driver->name, 0, 1))); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-5">
                    <h5 class="font-weight-bold"><?php echo e($driver->name); ?></h5>
                    <p class="mb-1"><i class="fas fa-envelope me-2 text-primary"></i> <?php echo e($driver->email); ?></p>
                    <p class="mb-1"><i class="fas fa-phone me-2 text-primary"></i> <?php echo e($driver->phone); ?></p>
                    <p class="mb-1"><i class="fas fa-id-card me-2 text-primary"></i> <?php echo e($driver->license_number); ?></p>
                </div>
                <div class="col-md-5">
                    <p class="mb-1"><i class="fas fa-car me-2 text-primary"></i> <?php echo e($driver->vehicle_make); ?> <?php echo e($driver->vehicle_model); ?> (<?php echo e($driver->vehicle_color); ?>)</p>
                    <p class="mb-1"><i class="fas fa-hashtag me-2 text-primary"></i> <?php echo e($driver->vehicle_reg_number); ?></p>
                    <p class="mb-1">
                        <i class="fas fa-user-check me-2 text-primary"></i> 
                        Status: 
                        <?php if($driver->is_active): ?>
                            <span class="badge bg-success text-white">Active</span>
                        <?php else: ?>
                            <span class="badge bg-danger text-white">Inactive</span>
                        <?php endif; ?>
                        
                        <?php if($driver->is_available): ?>
                            <span class="badge bg-info text-white">Available</span>
                        <?php else: ?>
                            <span class="badge bg-warning text-white">Unavailable</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Tabs -->
    <ul class="nav nav-tabs" id="scheduleTab" role="tablist">
        <?php $__currentLoopData = $weeklySchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $week): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo e($index === 0 ? 'active' : ''); ?>" 
                        id="week<?php echo e($index + 1); ?>-tab" 
                        data-bs-toggle="tab" 
                        data-bs-target="#week<?php echo e($index + 1); ?>" 
                        type="button" 
                        role="tab" 
                        aria-controls="week<?php echo e($index + 1); ?>" 
                        aria-selected="<?php echo e($index === 0 ? 'true' : 'false'); ?>">
                    <?php echo e($week['label']); ?>

                </button>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

    <!-- Schedule Tab Content -->
    <div class="tab-content" id="scheduleTabContent">
        <?php $__currentLoopData = $weeklySchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $week): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="tab-pane fade <?php echo e($index === 0 ? 'show active' : ''); ?>" 
                 id="week<?php echo e($index + 1); ?>" 
                 role="tabpanel" 
                 aria-labelledby="week<?php echo e($index + 1); ?>-tab">
                
                <div class="schedule-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><?php echo e($week['label']); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addScheduleModal" data-week="<?php echo e($index); ?>">
                            <i class="fas fa-plus me-1"></i> Add Schedule
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="schedule-table">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Time</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $week['days']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dayIndex => $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e(Carbon\Carbon::now()->format('Y-m-d') === $day['date'] ? 'today-indicator' : ''); ?>">
                                            <td class="day-column">
                                                <?php echo e($day['day_name']); ?><br>
                                                <small><?php echo e(\Carbon\Carbon::parse($day['date'])->format('M d, Y')); ?></small>
                                            </td>
                                            <td class="time-column">
                                                <?php if(count($day['schedules']) > 0): ?>
                                                    <?php $__currentLoopData = $day['schedules']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="time"><?php echo e(\Carbon\Carbon::parse($schedule->start_time)->format('h:i A')); ?> - <?php echo e(\Carbon\Carbon::parse($schedule->end_time)->format('h:i A')); ?></div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No schedule</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="status-column">
                                                <?php if(count($day['schedules']) > 0): ?>
                                                    <?php $__currentLoopData = $day['schedules']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($schedule->is_time_off): ?>
                                                            <span class="status time-off">Time Off</span>
                                                        <?php elseif($schedule->is_available): ?>
                                                            <span class="status available">Available</span>
                                                        <?php else: ?>
                                                            <span class="status unavailable">Unavailable</span>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="notes-column">
                                                <?php if(count($day['schedules']) > 0): ?>
                                                    <?php $__currentLoopData = $day['schedules']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($schedule->notes): ?>
                                                            <div class="notes"><?php echo e($schedule->notes); ?></div>
                                                        <?php else: ?>
                                                            <span class="text-muted">No notes</span>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if(count($day['schedules']) > 0): ?>
                                                    <?php $__currentLoopData = $day['schedules']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-schedule-btn" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#editScheduleModal"
                                                                data-schedule-id="<?php echo e($schedule->id); ?>"
                                                                data-date="<?php echo e($schedule->date); ?>"
                                                                data-start-time="<?php echo e($schedule->start_time); ?>"
                                                                data-end-time="<?php echo e($schedule->end_time); ?>"
                                                                data-is-available="<?php echo e($schedule->is_available ? 1 : 0); ?>"
                                                                data-is-time-off="<?php echo e($schedule->is_time_off ? 1 : 0); ?>"
                                                                data-notes="<?php echo e($schedule->notes); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-primary add-schedule-btn" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#addScheduleModal"
                                                            data-date="<?php echo e($day['date']); ?>">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Add Schedule Modal -->
<div class="modal fade" id="addScheduleModal" tabindex="-1" aria-labelledby="addScheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addScheduleModalLabel">Add Schedule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo e(route('admin.drivers.update-schedule', $driver->id)); ?>" method="POST" class="schedule-form">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="date">Date</label>
                        <input type="date" class="form-control" id="date" name="date[]" required>
                    </div>
                    <div class="form-group">
                        <label for="start_time">Start Time</label>
                        <input type="time" class="form-control" id="start_time" name="start_time[]" required>
                    </div>
                    <div class="form-group">
                        <label for="end_time">End Time</label>
                        <input type="time" class="form-control" id="end_time" name="end_time[]" required>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_available" name="is_available[]" value="1" checked>
                            <label class="form-check-label" for="is_available">
                                Available for rides
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_time_off" name="is_time_off[]" value="1">
                            <label class="form-check-label" for="is_time_off">
                                Time off (no rides)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes[]" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Schedule</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Schedule Modal -->
<div class="modal fade" id="editScheduleModal" tabindex="-1" aria-labelledby="editScheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editScheduleModalLabel">Edit Schedule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo e(route('admin.drivers.update-schedule', $driver->id)); ?>" method="POST" class="schedule-form">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="edit_schedule_id" name="schedule_id[]">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_date">Date</label>
                        <input type="date" class="form-control" id="edit_date" name="date[]" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_start_time">Start Time</label>
                        <input type="time" class="form-control" id="edit_start_time" name="start_time[]" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_end_time">End Time</label>
                        <input type="time" class="form-control" id="edit_end_time" name="end_time[]" required>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_available" name="is_available[]" value="1">
                            <label class="form-check-label" for="edit_is_available">
                                Available for rides
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_time_off" name="is_time_off[]" value="1">
                            <label class="form-check-label" for="edit_is_time_off">
                                Time off (no rides)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit_notes">Notes</label>
                        <textarea class="form-control" id="edit_notes" name="notes[]" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Schedule</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Schedule Modal
        const addScheduleModal = document.getElementById('addScheduleModal');
        addScheduleModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const date = button.getAttribute('data-date');
            
            if (date) {
                document.getElementById('date').value = date;
            }
        });
        
        // Edit Schedule Modal
        const editScheduleModal = document.getElementById('editScheduleModal');
        editScheduleModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const scheduleId = button.getAttribute('data-schedule-id');
            const date = button.getAttribute('data-date');
            const startTime = button.getAttribute('data-start-time');
            const endTime = button.getAttribute('data-end-time');
            const isAvailable = button.getAttribute('data-is-available') === '1';
            const isTimeOff = button.getAttribute('data-is-time-off') === '1';
            const notes = button.getAttribute('data-notes');
            
            document.getElementById('edit_schedule_id').value = scheduleId;
            document.getElementById('edit_date').value = date;
            document.getElementById('edit_start_time').value = startTime;
            document.getElementById('edit_end_time').value = endTime;
            document.getElementById('edit_is_available').checked = isAvailable;
            document.getElementById('edit_is_time_off').checked = isTimeOff;
            document.getElementById('edit_notes').value = notes || '';
        });
        
        // Time off checkbox logic
        document.getElementById('is_time_off').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('is_available').checked = false;
            }
        });
        
        document.getElementById('is_available').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('is_time_off').checked = false;
            }
        });
        
        document.getElementById('edit_is_time_off').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('edit_is_available').checked = false;
            }
        });
        
        document.getElementById('edit_is_available').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('edit_is_time_off').checked = false;
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\admin\drivers\schedule.blade.php ENDPATH**/ ?>