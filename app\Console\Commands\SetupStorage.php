<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use App\Services\StorageService;

class SetupStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:setup {--force : Force recreation of directories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup storage directories and fix permissions for local and live environments';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Setting up storage directories and permissions...');

        // Create storage link if it doesn't exist
        $this->createStorageLink();

        // Create required directories
        $this->createDirectories();

        // Fix permissions
        $this->fixPermissions();

        // Verify setup
        $this->verifySetup();

        $this->info('Storage setup completed successfully!');
        return 0;
    }

    /**
     * Create storage link
     */
    private function createStorageLink()
    {
        $this->info('Creating storage link...');

        $publicStoragePath = public_path('storage');
        $storageAppPublicPath = storage_path('app/public');

        // Remove existing link if force option is used
        if ($this->option('force') && File::exists($publicStoragePath)) {
            if (is_link($publicStoragePath)) {
                unlink($publicStoragePath);
                $this->info('Removed existing storage link.');
            } elseif (is_dir($publicStoragePath)) {
                File::deleteDirectory($publicStoragePath);
                $this->info('Removed existing storage directory.');
            }
        }

        // Create link if it doesn't exist
        if (!File::exists($publicStoragePath)) {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows: Create junction
                $command = "mklink /J \"$publicStoragePath\" \"$storageAppPublicPath\"";
                exec($command, $output, $returnCode);
                
                if ($returnCode === 0) {
                    $this->info('Storage link created successfully (Windows junction).');
                } else {
                    $this->error('Failed to create storage link on Windows.');
                    $this->info('Please run: php artisan storage:link');
                }
            } else {
                // Unix/Linux: Create symbolic link
                if (symlink($storageAppPublicPath, $publicStoragePath)) {
                    $this->info('Storage link created successfully (symbolic link).');
                } else {
                    $this->error('Failed to create storage link.');
                    $this->info('Please run: php artisan storage:link');
                }
            }
        } else {
            $this->info('Storage link already exists.');
        }
    }

    /**
     * Create required directories
     */
    private function createDirectories()
    {
        $this->info('Creating storage directories...');

        $directories = [
            'storage/app/public',
            'storage/app/public/profile-photos',
            'storage/app/public/driver-documents',
            'storage/app/public/vehicles',
            'storage/app/public/blog-posts',
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
        ];

        foreach ($directories as $directory) {
            $fullPath = base_path($directory);
            
            if (!File::exists($fullPath)) {
                File::makeDirectory($fullPath, 0755, true);
                $this->info("Created directory: $directory");
            } else {
                $this->info("Directory already exists: $directory");
            }
        }

        // Use StorageService to ensure directories exist
        StorageService::ensureDirectoriesExist();
    }

    /**
     * Fix permissions for storage directories
     */
    private function fixPermissions()
    {
        $this->info('Fixing storage permissions...');

        if (PHP_OS_FAMILY !== 'Windows') {
            $directories = [
                storage_path(),
                storage_path('app'),
                storage_path('app/public'),
                storage_path('logs'),
                storage_path('framework'),
                storage_path('framework/cache'),
                storage_path('framework/sessions'),
                storage_path('framework/views'),
            ];

            foreach ($directories as $directory) {
                if (File::exists($directory)) {
                    chmod($directory, 0755);
                    $this->info("Fixed permissions for: " . basename($directory));
                }
            }

            // Fix file permissions in storage/app/public
            $publicPath = storage_path('app/public');
            if (File::exists($publicPath)) {
                $files = File::allFiles($publicPath);
                foreach ($files as $file) {
                    chmod($file->getPathname(), 0644);
                }
                $this->info('Fixed file permissions in storage/app/public');
            }
        } else {
            $this->info('Skipping permission fixes on Windows.');
        }
    }

    /**
     * Verify storage setup
     */
    private function verifySetup()
    {
        $this->info('Verifying storage setup...');

        $checks = [
            'Storage link exists' => File::exists(public_path('storage')),
            'Storage app/public exists' => File::exists(storage_path('app/public')),
            'Profile photos directory' => File::exists(storage_path('app/public/profile-photos')),
            'Driver documents directory' => File::exists(storage_path('app/public/driver-documents')),
            'Vehicles directory' => File::exists(storage_path('app/public/vehicles')),
            'Blog posts directory' => File::exists(storage_path('app/public/blog-posts')),
            'Logs directory' => File::exists(storage_path('logs')),
        ];

        $allPassed = true;
        foreach ($checks as $check => $passed) {
            if ($passed) {
                $this->info("✓ $check");
            } else {
                $this->error("✗ $check");
                $allPassed = false;
            }
        }

        if ($allPassed) {
            $this->info('All storage checks passed!');
        } else {
            $this->error('Some storage checks failed. Please review the setup.');
        }

        // Test file operations
        $this->testFileOperations();
    }

    /**
     * Test basic file operations
     */
    private function testFileOperations()
    {
        $this->info('Testing file operations...');

        try {
            // Test writing to storage
            $testFile = 'test_' . time() . '.txt';
            $testContent = 'Storage test file created at ' . now();
            
            Storage::disk('public')->put($testFile, $testContent);
            
            if (Storage::disk('public')->exists($testFile)) {
                $this->info('✓ File write test passed');
                
                // Test reading
                $content = Storage::disk('public')->get($testFile);
                if ($content === $testContent) {
                    $this->info('✓ File read test passed');
                } else {
                    $this->error('✗ File read test failed');
                }
                
                // Test URL generation
                $url = Storage::disk('public')->url($testFile);
                if ($url) {
                    $this->info('✓ URL generation test passed');
                } else {
                    $this->error('✗ URL generation test failed');
                }
                
                // Clean up test file
                Storage::disk('public')->delete($testFile);
                $this->info('✓ File deletion test passed');
                
            } else {
                $this->error('✗ File write test failed');
            }
            
        } catch (\Exception $e) {
            $this->error('File operation test failed: ' . $e->getMessage());
        }
    }
}
