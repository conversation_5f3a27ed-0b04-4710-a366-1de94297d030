@extends('layouts.admin')

@section('title', 'Add New Vehicle')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }

    .form-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .form-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .form-card .card-body {
        padding: 30px;
    }

    .preview-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 10px;
        display: none;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Add New Vehicle</h2>
                <a href="{{ route('admin.vehicles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Vehicles
                </a>
            </div>

            <div class="card form-card">
                <div class="card-header">
                    <h4 class="mb-0">Vehicle Information</h4>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.vehicles.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Vehicle Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="type" class="form-label">Vehicle Type</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="Sedan" {{ old('type') == 'Sedan' ? 'selected' : '' }}>Sedan</option>
                                    <option value="SUV" {{ old('type') == 'SUV' ? 'selected' : '' }}>SUV</option>
                                    <option value="Limousine" {{ old('type') == 'Limousine' ? 'selected' : '' }}>Limousine</option>
                                    <option value="Van" {{ old('type') == 'Van' ? 'selected' : '' }}>Van</option>
                                    <option value="Bus" {{ old('type') == 'Bus' ? 'selected' : '' }}>Bus</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="model" class="form-label">Model</label>
                                <input type="text" class="form-control" id="model" name="model" value="{{ old('model') }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="Economy" {{ old('category') == 'Economy' ? 'selected' : '' }}>Economy</option>
                                    <option value="Business" {{ old('category') == 'Business' ? 'selected' : '' }}>Business</option>
                                    <option value="Luxury" {{ old('category') == 'Luxury' ? 'selected' : '' }}>Luxury</option>
                                    <option value="Premium" {{ old('category') == 'Premium' ? 'selected' : '' }}>Premium</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="seats" class="form-label">Number of Seats</label>
                                <input type="number" class="form-control" id="seats" name="seats" value="{{ old('seats') }}" min="1" required>
                            </div>
                            <div class="col-md-4">
                                <label for="luggage_capacity" class="form-label">Luggage Capacity</label>
                                <input type="number" class="form-control" id="luggage_capacity" name="luggage_capacity" value="{{ old('luggage_capacity') }}" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label for="transmission" class="form-label">Transmission</label>
                                <select class="form-select" id="transmission" name="transmission" required>
                                    <option value="">Select Transmission</option>
                                    <option value="Automatic" {{ old('transmission') == 'Automatic' ? 'selected' : '' }}>Automatic</option>
                                    <option value="Manual" {{ old('transmission') == 'Manual' ? 'selected' : '' }}>Manual</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price_per_km" class="form-label">Price Per {{ \App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Mile' : 'KM' }} ($)</label>
                                <input type="number" class="form-control" id="price_per_km" name="price_per_km" value="{{ old('price_per_km') }}" min="0" step="0.01" required>
                            </div>
                            <div class="col-md-6">
                                <label for="price_per_hour" class="form-label">Price Per Hour ($)</label>
                                <input type="number" class="form-control" id="price_per_hour" name="price_per_hour" value="{{ old('price_per_hour') }}" min="0" step="0.01" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="base_fare" class="form-label">Base Fare ($)</label>
                                <input type="number" class="form-control" id="base_fare" name="base_fare" value="{{ old('base_fare', 5.00) }}" min="0" step="0.01" required>
                                <small class="text-muted">Minimum fare for any ride with this vehicle</small>
                            </div>
                            <div class="col-md-6">
                                <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="{{ old('tax_rate', 0.00) }}" min="0" max="100" step="0.01" required>
                                <small class="text-muted">Tax percentage applied to rides with this vehicle</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Vehicle Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                            <div class="mt-2">
                                <img id="preview" class="preview-image" src="#" alt="Vehicle Preview">
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {{ old('is_active') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">Active</label>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('admin.vehicles.index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Vehicle</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function previewImage(input) {
        var preview = document.getElementById('preview');

        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
        }
    }
</script>
@endsection
