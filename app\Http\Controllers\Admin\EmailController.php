<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailLog;
use App\Models\User;
use App\Services\EmailService;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class EmailController extends Controller
{
    /**
     * Display email logs and statistics
     */
    public function index(Request $request)
    {
        $query = EmailLog::with(['user', 'booking']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('template')) {
            $query->where('template', $request->template);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('recipient_email', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        // Handle export request
        if ($request->filled('export') && $request->export === 'csv') {
            return $this->exportEmailLogs($query);
        }

        $emailLogs = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get statistics
        $statistics = EmailService::getEmailStatistics();

        // Get template options for filter
        $templates = EmailLog::distinct()->pluck('template')->filter()->sort();

        return view('admin.emails.index', compact('emailLogs', 'statistics', 'templates'));
    }

    /**
     * Show email details
     */
    public function show(EmailLog $emailLog)
    {
        $emailLog->load(['user', 'booking', 'payment']);
        return view('admin.emails.show', compact('emailLog'));
    }

    /**
     * Show bulk email form
     */
    public function bulkEmailForm()
    {
        $userRoles = ['client', 'driver', 'admin'];
        $templates = ['general', 'announcement', 'promotion', 'maintenance'];

        return view('admin.emails.bulk', compact('userRoles', 'templates'));
    }

    /**
     * Send bulk email
     */
    public function sendBulkEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recipient_type' => 'required|in:all,role,specific',
            'user_role' => 'required_if:recipient_type,role|in:client,driver,admin',
            'user_ids' => 'required_if:recipient_type,specific|array',
            'user_ids.*' => 'exists:users,id',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'template' => 'required|string|in:general,announcement,promotion,maintenance',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get user IDs based on recipient type
        $userIds = [];

        switch ($request->recipient_type) {
            case 'all':
                $userIds = User::whereNotNull('email')->pluck('id')->toArray();
                break;

            case 'role':
                $userIds = User::where('role', $request->user_role)
                    ->whereNotNull('email')
                    ->pluck('id')
                    ->toArray();
                break;

            case 'specific':
                $userIds = $request->user_ids;
                break;
        }

        if (empty($userIds)) {
            return redirect()->back()
                ->with('error', 'No users found matching the selected criteria.');
        }

        // Send bulk email
        $results = EmailService::sendBulkEmail(
            $userIds,
            $request->subject,
            $request->message,
            $request->template
        );

        $message = "Bulk email queued successfully! ";
        $message .= "Success: {$results['success']}, Failed: {$results['failed']}";

        if (!empty($results['errors'])) {
            $message .= " Errors: " . implode(', ', array_slice($results['errors'], 0, 3));
            if (count($results['errors']) > 3) {
                $message .= " and " . (count($results['errors']) - 3) . " more...";
            }
        }

        return redirect()->route('admin.emails.index')
            ->with('success', $message);
    }

    /**
     * Show email settings form
     */
    public function settings()
    {
        $emailSettings = SettingsService::getEmailSettings();
        return view('admin.emails.settings', compact('emailSettings'));
    }

    /**
     * Update email settings
     */
    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_driver' => 'required|in:smtp,sendmail,mailgun,ses',
            'mail_host' => 'required_if:mail_driver,smtp|string',
            'mail_port' => 'required_if:mail_driver,smtp|integer|min:1|max:65535',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|in:tls,ssl,none',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update settings
        foreach ($request->only([
            'mail_driver', 'mail_host', 'mail_port', 'mail_username',
            'mail_password', 'mail_encryption', 'mail_from_address', 'mail_from_name'
        ]) as $key => $value) {
            \App\Models\Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        // Clear settings cache
        \Illuminate\Support\Facades\Cache::forget('settings');

        // Clear all settings-related cache keys
        \Illuminate\Support\Facades\Cache::forget('settings.mail_driver');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_host');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_port');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_username');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_password');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_encryption');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_from_address');
        \Illuminate\Support\Facades\Cache::forget('settings.mail_from_name');

        // Apply the new email settings immediately
        \App\Services\SettingsService::applySettings();

        // Refresh the mail configuration
        $this->refreshMailConfiguration();

        return redirect()->back()
            ->with('success', 'Email settings updated successfully!');
    }

    /**
     * Test email configuration and templates
     */
    public function testEmail(Request $request)
    {
        // Handle template test emails from templates page
        if ($request->has('template')) {
            return $this->sendTemplateTestEmail($request);
        }

        // Handle configuration test emails from settings page
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid email address.'
            ]);
        }

        try {
            // Use the enhanced EmailConfigService
            $result = \App\Services\EmailConfigService::testEmailConfiguration($request->test_email);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending test email: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send template test email
     */
    private function sendTemplateTestEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'template' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid email address and template.'
            ]);
        }

        try {
            $templateName = $request->template;
            $subject = 'Test Email - ' . ucfirst(str_replace('_', ' ', $templateName));

            // Create sample data for testing
            $sampleData = $this->getSampleDataForTemplate($templateName);

            // Send test email with sample data
            Mail::send('emails.test', $sampleData, function ($message) use ($request, $subject) {
                $message->to($request->email)
                    ->subject($subject);
            });

            // Log the test email
            EmailLog::create([
                'recipient_email' => $request->email,
                'recipient_name' => 'Test User',
                'subject' => $subject,
                'template' => $templateName,
                'status' => 'sent',
                'sent_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);
        } catch (\Exception $e) {
            // Log failed test email
            EmailLog::create([
                'recipient_email' => $request->email,
                'recipient_name' => 'Test User',
                'subject' => $subject ?? 'Test Email',
                'template' => $templateName ?? 'test_email',
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'failed_at' => now(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get sample data for template testing
     */
    private function getSampleDataForTemplate($templateName)
    {
        $baseData = [
            'user' => (object) [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+****************',
            ],
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'current_year' => now()->year,
            'template_name' => $templateName,
        ];

        // Add template-specific sample data
        switch ($templateName) {
            case 'booking_confirmation':
            case 'booking_cancelled':
            case 'booking_reminder':
            case 'driver_assigned':
                $baseData['booking'] = (object) [
                    'id' => 'BK-' . rand(1000, 9999),
                    'pickup_location' => '123 Main Street, New York, NY',
                    'dropoff_location' => 'JFK Airport, Terminal 1',
                    'pickup_datetime' => now()->addDay()->format('M j, Y g:i A'),
                    'total_amount' => '$85.00',
                    'status' => 'confirmed',
                ];
                $baseData['driver'] = (object) [
                    'name' => 'Mike Johnson',
                    'phone' => '+****************',
                    'vehicle' => 'Toyota Camry - ABC123',
                ];
                break;

            case 'payment_confirmation':
                $baseData['payment'] = (object) [
                    'amount' => '$85.00',
                    'method' => 'Credit Card',
                    'transaction_id' => 'TXN-' . rand(100000, 999999),
                    'date' => now()->format('M j, Y g:i A'),
                ];
                break;

            case 'password_reset':
                $baseData['reset_url'] = config('app.url') . '/password/reset/sample-token';
                break;

            case 'email_verification':
                $baseData['verification_url'] = config('app.url') . '/email/verify/sample-token';
                break;
        }

        return $baseData;
    }

    /**
     * Get email configuration status
     */
    public function getConfigurationStatus()
    {
        try {
            $status = \App\Services\EmailConfigService::getConfigurationStatus();
            return response()->json($status);
        } catch (\Exception $e) {
            return response()->json([
                'configured' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-configure email provider
     */
    public function autoConfigureProvider(Request $request)
    {
        $request->validate([
            'provider' => 'required|string',
            'username' => 'required|email',
            'password' => 'required|string',
            'from_name' => 'nullable|string|max:255',
        ]);

        try {
            $success = \App\Services\EmailConfigService::autoConfigureProvider(
                $request->provider,
                $request->username,
                $request->password,
                $request->from_name ?: 'YNR Cars'
            );

            if ($success) {
                return redirect()->back()->with('success', 'Email provider configured successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to configure email provider.');
            }
        } catch (\Exception $e) {
            Log::error('Auto-configure email provider failed', [
                'error' => $e->getMessage(),
                'provider' => $request->provider
            ]);

            return redirect()->back()->with('error', 'Configuration failed: ' . $e->getMessage());
        }
    }

    /**
     * Get email provider configurations
     */
    public function getEmailProviders()
    {
        try {
            $providers = \App\Services\EmailConfigService::getEmailProviders();
            return response()->json($providers);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Refresh mail configuration after settings update
     */
    private function refreshMailConfiguration()
    {
        try {
            // Get the mail manager instance
            $mailManager = app('mail.manager');

            // Purge the default mailer to force reconfiguration
            $mailManager->purge();

            // Force Laravel to reload the mail configuration
            app()->forgetInstance('mail.manager');

            // Clear any cached mail configuration
            if (method_exists($mailManager, 'forgetMailers')) {
                $mailManager->forgetMailers();
            }

            // Apply new configuration from database
            \App\Services\EmailConfigService::configureFromDatabase();

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::warning('Failed to refresh mail configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get email statistics for dashboard
     */
    public function statistics(Request $request)
    {
        $filters = $request->only(['date_from', 'date_to', 'template', 'status']);
        $statistics = EmailService::getEmailStatistics($filters);

        return response()->json($statistics);
    }

    /**
     * Resend failed email
     */
    public function resend(EmailLog $emailLog)
    {
        if ($emailLog->status !== 'failed') {
            return redirect()->back()
                ->with('error', 'Only failed emails can be resent.');
        }

        // Mark as queued and clear error
        $emailLog->update([
            'status' => 'queued',
            'error_message' => null,
            'failed_at' => null,
        ]);

        // Here you would typically re-queue the email
        // For now, we'll just mark it as queued

        return redirect()->back()
            ->with('success', 'Email has been queued for resending.');
    }

    /**
     * Delete email log
     */
    public function destroy(EmailLog $emailLog)
    {
        $emailLog->delete();

        return redirect()->back()
            ->with('success', 'Email log deleted successfully.');
    }

    /**
     * Bulk delete email logs
     */
    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'exists:email_logs,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->with('error', 'Invalid email logs selected.');
        }

        $count = EmailLog::whereIn('id', $request->ids)->delete();

        return redirect()->route('admin.emails.index')
            ->with('success', "Successfully deleted {$count} email log(s).");
    }

    /**
     * Bulk resend failed emails
     */
    public function bulkResend(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'exists:email_logs,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->with('error', 'Invalid email logs selected.');
        }

        $count = EmailLog::whereIn('id', $request->ids)
            ->where('status', 'failed')
            ->update([
                'status' => 'queued',
                'error_message' => null,
                'failed_at' => null,
            ]);

        return redirect()->route('admin.emails.index')
            ->with('success', "Successfully queued {$count} failed email(s) for resending.");
    }

    /**
     * Show email templates management
     */
    public function templates()
    {
        return view('admin.emails.templates');
    }

    /**
     * Load template content for editing
     */
    public function loadTemplate($templateName)
    {
        try {
            // Get template content from database or use defaults
            $template = \App\Models\EmailTemplate::where('name', $templateName)->first();

            if ($template) {
                return response()->json([
                    'success' => true,
                    'subject' => $template->subject,
                    'content' => $template->content
                ]);
            } else {
                // Get default template from model
                $defaults = \App\Models\EmailTemplate::getDefaultTemplates();
                if (isset($defaults[$templateName])) {
                    return response()->json([
                        'success' => true,
                        'subject' => $defaults[$templateName]['subject'],
                        'content' => $defaults[$templateName]['content']
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Template not found, using defaults'
                    ]);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading template: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Save template content
     */
    public function saveTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template' => 'required|string',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please fill in all required fields.'
            ]);
        }

        try {
            // Save or update template
            $template = \App\Models\EmailTemplate::updateOrCreate(
                ['name' => $request->template],
                [
                    'subject' => $request->subject,
                    'content' => $request->content,
                    'updated_by' => auth()->id()
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Template saved successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving template: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show email analytics dashboard
     */
    public function analytics(Request $request)
    {
        $dateFrom = $request->input('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->input('date_to', now()->format('Y-m-d'));
        $template = $request->input('template');

        // Base query with date filter
        $query = EmailLog::whereBetween('created_at', [$dateFrom, $dateTo]);

        if ($template) {
            $query->where('template', $template);
        }

        // Get analytics data
        $analytics = [
            'total_emails' => $query->count(),
            'sent_emails' => $query->where('status', 'sent')->count(),
            'queued_emails' => $query->where('status', 'queued')->count(),
            'failed_emails' => $query->where('status', 'failed')->count(),
        ];

        // Calculate success rate
        $analytics['success_rate'] = $analytics['total_emails'] > 0
            ? ($analytics['sent_emails'] / $analytics['total_emails']) * 100
            : 0;

        // Calculate average send time (mock data for now)
        $analytics['avg_send_time'] = 2.3;

        // Volume chart data (last 30 days)
        $volumeData = EmailLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [now()->subDays(30), now()])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $analytics['volume_chart'] = [
            'labels' => $volumeData->pluck('date')->map(fn($date) => \Carbon\Carbon::parse($date)->format('M j'))->toArray(),
            'data' => $volumeData->pluck('count')->toArray()
        ];

        // Template distribution
        $templateData = EmailLog::selectRaw('template, COUNT(*) as count')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->whereNotNull('template')
            ->groupBy('template')
            ->orderByDesc('count')
            ->limit(6)
            ->get();

        $analytics['template_chart'] = [
            'labels' => $templateData->pluck('template')->map(fn($t) => ucfirst(str_replace('_', ' ', $t)))->toArray(),
            'data' => $templateData->pluck('count')->toArray()
        ];

        // Top recipients
        $topRecipients = EmailLog::selectRaw('
                recipient_email,
                recipient_name,
                COUNT(*) as count,
                SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_count
            ')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('recipient_email', 'recipient_name')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->map(function($recipient) {
                $recipient->success_rate = $recipient->count > 0
                    ? ($recipient->sent_count / $recipient->count) * 100
                    : 0;
                return [
                    'email' => $recipient->recipient_email,
                    'name' => $recipient->recipient_name,
                    'count' => $recipient->count,
                    'success_rate' => $recipient->success_rate
                ];
            });

        $analytics['top_recipients'] = $topRecipients;

        // Recent failed emails
        $analytics['recent_failed'] = EmailLog::where('status', 'failed')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->orderByDesc('failed_at')
            ->limit(10)
            ->get();

        // Get template options for filter
        $templates = EmailLog::distinct()->pluck('template')->filter()->sort();

        return view('admin.emails.analytics', compact('analytics', 'templates'));
    }

    /**
     * Export email logs to CSV
     */
    private function exportEmailLogs($query)
    {
        $emailLogs = $query->get();

        $filename = 'email_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($emailLogs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Recipient Name',
                'Recipient Email',
                'Subject',
                'Template',
                'Status',
                'Created At',
                'Sent At',
                'Failed At',
                'Error Message'
            ]);

            // CSV data
            foreach ($emailLogs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->recipient_name,
                    $log->recipient_email,
                    $log->subject,
                    $log->template,
                    $log->status,
                    $log->created_at ? $log->created_at->format('Y-m-d H:i:s') : '',
                    $log->sent_at ? $log->sent_at->format('Y-m-d H:i:s') : '',
                    $log->failed_at ? $log->failed_at->format('Y-m-d H:i:s') : '',
                    $log->error_message
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get email configuration diagnostics
     */
    public function diagnostics()
    {
        $diagnostics = [
            'mail_configuration' => [
                'default_mailer' => config('mail.default'),
                'smtp_host' => config('mail.mailers.smtp.host'),
                'smtp_port' => config('mail.mailers.smtp.port'),
                'smtp_encryption' => config('mail.mailers.smtp.encryption'),
                'smtp_username' => config('mail.mailers.smtp.username') ? '***configured***' : 'not set',
                'smtp_password' => config('mail.mailers.smtp.password') ? '***configured***' : 'not set',
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
            ],
            'database_settings' => [
                'mail_driver' => \App\Services\SettingsService::get('mail_driver'),
                'mail_host' => \App\Services\SettingsService::get('mail_host'),
                'mail_port' => \App\Services\SettingsService::get('mail_port'),
                'mail_encryption' => \App\Services\SettingsService::get('mail_encryption'),
                'mail_username' => \App\Services\SettingsService::get('mail_username') ? '***configured***' : 'not set',
                'mail_password' => \App\Services\SettingsService::get('mail_password') ? '***configured***' : 'not set',
                'mail_from_address' => \App\Services\SettingsService::get('mail_from_address'),
                'mail_from_name' => \App\Services\SettingsService::get('mail_from_name'),
            ],
            'environment_variables' => [
                'MAIL_MAILER' => env('MAIL_MAILER'),
                'MAIL_HOST' => env('MAIL_HOST'),
                'MAIL_PORT' => env('MAIL_PORT'),
                'MAIL_USERNAME' => env('MAIL_USERNAME') ? '***configured***' : 'not set',
                'MAIL_PASSWORD' => env('MAIL_PASSWORD') ? '***configured***' : 'not set',
                'MAIL_ENCRYPTION' => env('MAIL_ENCRYPTION'),
                'MAIL_FROM_ADDRESS' => env('MAIL_FROM_ADDRESS'),
                'MAIL_FROM_NAME' => env('MAIL_FROM_NAME'),
            ],
            'system_checks' => [
                'settings_table_exists' => \Illuminate\Support\Facades\Schema::hasTable('settings'),
                'email_logs_table_exists' => \Illuminate\Support\Facades\Schema::hasTable('email_logs'),
                'mail_manager_available' => class_exists('Illuminate\Mail\MailManager'),
                'test_email_class_exists' => class_exists('App\Mail\TestEmail'),
                'email_service_exists' => class_exists('App\Services\EmailService'),
            ]
        ];

        return response()->json($diagnostics);
    }
}
