<?php $__env->startSection('title', $emailSubject); ?>

<?php $__env->startSection('content'); ?>
<h2><?php echo e($emailSubject); ?></h2>

<p>Dear <?php echo e($user->name); ?>,</p>

<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
    <h3 style="color: #856404; margin-top: 0;">🔧 Scheduled Maintenance Notice</h3>
    <p style="color: #856404; margin-bottom: 0;">
        We're performing scheduled maintenance to improve your <?php echo e($companyName); ?> experience.
    </p>
</div>

<div style="margin: 20px 0; line-height: 1.6; font-size: 16px;">
    <?php echo nl2br(e($emailMessage)); ?>

</div>

<h3>What to Expect:</h3>
<ul>
    <li><strong>Minimal Disruption:</strong> We've scheduled this maintenance during low-traffic hours</li>
    <li><strong>Improved Performance:</strong> These updates will enhance system speed and reliability</li>
    <li><strong>New Features:</strong> You may notice some exciting new features after maintenance</li>
    <li><strong>Better Security:</strong> We're implementing the latest security enhancements</li>
</ul>

<div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h4 style="color: #155724; margin-top: 0;">✅ What We're Improving:</h4>
    <ul style="color: #155724; margin-bottom: 0;">
        <li>System performance and speed optimizations</li>
        <li>Enhanced security measures</li>
        <li>Bug fixes and stability improvements</li>
        <li>New features and functionality</li>
        <li>Database optimizations</li>
    </ul>
</div>

<h3>During Maintenance:</h3>
<ul>
    <li>🌐 <strong>Website:</strong> May be temporarily unavailable</li>
    <li>📱 <strong>Mobile Access:</strong> Limited functionality possible</li>
    <li>📞 <strong>Phone Bookings:</strong> Available as usual</li>
    <li>🚗 <strong>Existing Rides:</strong> Will continue normally</li>
    <li>💳 <strong>Payments:</strong> May be temporarily affected</li>
</ul>

<h3>Alternative Booking Options:</h3>
<p>If you need to make a booking during maintenance, please use these alternatives:</p>
<ul>
    <li><strong>Phone:</strong> Call us at <?php echo e($companyPhone); ?></li>
    <li><strong>Email:</strong> Send booking requests to <?php echo e($companyEmail); ?></li>
    <li><strong>Emergency:</strong> Our emergency line remains available 24/7</li>
</ul>

<div style="margin: 30px 0; text-align: center;">
    <a href="tel:<?php echo e($companyPhone); ?>" class="btn">Call for Bookings</a>
</div>

<h3>After Maintenance:</h3>
<p>Once maintenance is complete, you can expect:</p>
<ul>
    <li>✅ Faster website loading times</li>
    <li>✅ Improved booking process</li>
    <li>✅ Enhanced security features</li>
    <li>✅ Better mobile experience</li>
    <li>✅ New features and improvements</li>
</ul>

<div style="background-color: #e3f2fd; border: 1px solid #bbdefb; border-radius: 5px; padding: 15px; margin: 20px 0;">
    <h4 style="color: #1976d2; margin-top: 0;">📧 Stay Updated</h4>
    <p style="color: #1976d2; margin-bottom: 0;">
        We'll send you another email once maintenance is complete and all systems are fully operational.
        Follow us on social media for real-time updates during the maintenance window.
    </p>
</div>

<h3>Questions or Concerns?</h3>
<p>If you have any questions about this maintenance or need immediate assistance:</p>
<ul>
    <li><strong>Phone:</strong> <?php echo e($companyPhone); ?> (Available 24/7)</li>
    <li><strong>Email:</strong> <?php echo e($companyEmail); ?></li>
    <li><strong>Emergency Line:</strong> Available for urgent ride requests</li>
</ul>

<p>We apologize for any inconvenience and appreciate your patience as we work to improve your <?php echo e($companyName); ?> experience.</p>

<p>Thank you for your understanding!</p>

<p>Best regards,<br>
The <?php echo e($companyName); ?> Technical Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\public_html\resources\views\emails\general\maintenance.blade.php ENDPATH**/ ?>