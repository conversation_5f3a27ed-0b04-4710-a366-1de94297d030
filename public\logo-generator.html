<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YNR Cars Logo Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .logo-container {
            width: 300px;
            height: 100px;
            background-color: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        .logo-text {
            font-size: 36px;
            font-weight: bold;
            color: #ee393d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .logo-text span {
            color: #343a40;
        }
        .logo-subtitle {
            position: absolute;
            bottom: 10px;
            font-size: 14px;
            color: #343a40;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        .controls {
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #ee393d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #d32f2f;
        }
    </style>
</head>
<body>
    <div>
        <div class="logo-container" id="logo">
            <div class="logo-text">YNR <span>Cars</span></div>
            <div class="logo-subtitle">Ynr Cars Taxi Services</div>
        </div>
        <div class="controls">
            <button id="download">Download Logo</button>
        </div>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('download').addEventListener('click', function() {
            html2canvas(document.getElementById('logo'), {
                backgroundColor: null
            }).then(function(canvas) {
                // Convert canvas to PNG image
                const image = canvas.toDataURL('image/png');
                
                // Create download link
                const link = document.createElement('a');
                link.download = 'ynr-cars-logo.png';
                link.href = image;
                link.click();
            });
        });
    </script>
</body>
</html>
